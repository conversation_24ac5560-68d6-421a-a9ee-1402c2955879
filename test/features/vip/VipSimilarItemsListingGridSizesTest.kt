package features.vip

import com.gumtree.mobile.features.vip.VipSimilarItemsListingGridSizes
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class VipSimilarItemsListingGridSizesTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return double grid size in portrait mode`() {
        runUnitTest(robot) {
            When { getPortraitGridSize() }
            Then { checkGridSize(2) }
        }
    }

    @Test
    fun `should return zero grid size in landscape mode (NO landscape UI)`() {
        runUnitTest(robot) {
            When { getLandscapeGridSize() }
            Then { checkGridSize(0) }
        }
    }

    private class Robot: BaseRobot {

        private lateinit var tested: VipSimilarItemsListingGridSizes
        private var actualGridSize: Int = 0

        override fun setup() {
            tested = VipSimilarItemsListingGridSizes()
        }

        fun getPortraitGridSize() {
            actualGridSize = tested.getPortraitGridSize()
        }

        fun getLandscapeGridSize() {
            actualGridSize = tested.getLandscapeGridSize()
        }

        fun checkGridSize(expected: Int) {
            assertEquals(expected, actualGridSize)
        }
    }
}