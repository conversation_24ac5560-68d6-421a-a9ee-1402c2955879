ext {

    gumtreeCustomGradle = 'Custom Gumtree'

    /***********
     * Custom Gradle Tasks depends on tasks
     ***********/

    prTasks = [
            'check',
            'assemble'
    ]


    /***********
     * Custom Gradle Tasks descriptions
     ***********/

    prTaskDescription = 'Runs all unit tests, Runs all Kotlin code style checks, Generates code coverage, Assembles the mobile BFF project (.jar file)'
    contractsTaskDescription = 'Read all Mobile BFF contracts and update the list in CONTRACTS.md (depends on wrapper task). Will be executed everytime when BFF application run'

}