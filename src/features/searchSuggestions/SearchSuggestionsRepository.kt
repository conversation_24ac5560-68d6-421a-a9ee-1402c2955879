package com.gumtree.mobile.features.searchSuggestions

import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import kotlinx.coroutines.withContext

interface SearchSuggestionsRepository {
    /**
     * Read the search suggestions by search term
     * @param searchTerm - the search term
     * @param platform - the platform of app
     * @return - a list with all search suggestions per the search term look up
     */
    suspend fun getSearchResults(
        searchTerm: String,
        platform: String,
    ): List<SearchSuggestionResultDto>
}

class DefaultSearchSuggestionsRepository(
    private val capiSearchApi: CapiSearchApi,
    private val searchSuggestionsMapper: SearchSuggestionsMapper,
    private val searchSuggestionsAnalyticsProvider: SearchSuggestionsAnalyticsProvider,
    private val dispatcherProvider: DispatcherProvider,
) : SearchSuggestionsRepository {

    override suspend fun getSearchResults(
        searchTerm: String,
        platform: String,
    ): List<SearchSuggestionResultDto> {
        val analyticsEventData = AnalyticsEventData(
            eventName = searchSuggestionsAnalyticsProvider.getSubmitSuggestedListingSearchEvent(),
            parameters = searchSuggestionsAnalyticsProvider.getScreenParams(searchTerm),
        )
        val rawCapiResponse =
            withContext(dispatcherProvider.io) {
                capiSearchApi.getSearchSuggestions(
                    searchQuery = searchTerm,
                    platform = platform,
                    useFiveEightFlag = true,
                )
            }

        return searchSuggestionsMapper.map(
            rawCapiResponse,
            searchTerm,
            analyticsEventData,
        ).take(SearchSuggestionsConfiguration.MAX_RESULTS)
    }
}
