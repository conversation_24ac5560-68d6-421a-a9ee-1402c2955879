package com.gumtree.mobile.api.capi

/**
 * All CAPI endpoint URLs
 */
object CapiEndpointUrls {

    @Suppress("MaxLineLength")
    private const val SEARCH_IN_PROPERTIES = "title,description,price,price-frequency,highest-price,ad-status,ad-type,ad-address.city,ad-address.state,ad-address.latitude,ad-address.longitude,pictures,category,locations,creation-date-time,start-date-time,user-id,rank,view-ad-count,features-active,link,phone,ad-source-id,user-logos,ad-channel-id,attributes,external-partner-listing,extended-info,contact-methods,skills"

    internal const val GET_CATEGORIES_HISTOGRAMS_ENDPOINT = "ads?ad-status=ACTIVE&page=0&histogramsExpand=category&size=0&pictureRequired=false"

    internal const val INCREMENT_AD_COUNTER_ENDPOINT = "counters/ad/{adId}"
    internal const val INCREMENT_AD_COUNTER_BY_TYPE_ENDPOINT = "counters/ads/{counterType}/{adId}"
    internal const val GET_AUTO_RENEWAL_STATUS_ENDPOINT = "ads/{adId}/extended-info/automatic_repost_ad"
    internal const val FLAG_AD_ENDPOINT = "flags/ads/{adId}"
    internal const val GET_ADS_SEARCH_SUGGESTIONS_ENDPOINT = "suggestions/ads"
    internal const val GET_NEWEST_ADS_IN_SEARCH_ENDPOINT = "ads?_in=id&includeTopAds=false&page=0&size=1"
    internal const val SEARCH_ADS_ENDPOINT = "ads?_in=$SEARCH_IN_PROPERTIES"
    internal const val GET_AD_DETAILS_ENDPOINT = "ads/{adId}"
    internal const val RE_POST_AD_FOR_FREE_ENDPOINT = "ads/{advertId}/repostForFree"
    internal const val GET_AD_DELETION_REASONS_ENDPOINT = "ads/delete-reasons"
    internal const val GET_AD_STATS_ENDPOINT = "users/{username}/stats"

    internal const val GET_RAW_REPLY_TEMPLATE_ENDPOINT = "replies/reply-to-ad/metadata"
    internal const val ADD_REPLY_ATTACHMENT_ENDPOINT = "replies/reply-file"
    internal const val EMAIL_REPLY_ENDPOINT = "replies/reply-to-ad"

    internal const val USER_AD_ENDPOINT = "users/{username}/ads/{adId}"
    internal const val UPDATE_AD_STATUS_ENDPOINT = "users/{username}/ads/{status}/{adId}"

    @Suppress("MaxLineLength")
    internal const val GET_FAVORITE_ADS_ENDPOINT = "users/{username}/watchlist?_in=title,description,price,highest-price,ad-type,ad-address.city,ad-address.state,ad-address.latitude,ad-address.longitude,pictures,category,locations,start-date-time,features-active,phone,attributes,ad-status,extended-info"
    internal const val ADD_FAVORITE_AD_ENDPOINT = "users/{username}/watchlist/{adId}?_in=id"
    internal const val DELETE_FAVORITE_AD_ENDPOINT = "users/{username}/watchlist/{adId}"

    @Suppress("MaxLineLength")
    internal const val ALL_USERS_ADS_ENDPOINT = "users/{username}/ads?_in=title,description,price,highest-price,pictures,category,locations,creation-date-time,start-date-time,end-date-time,ad-status,ad-type,rank,view-ad-count,phone-click-count,map-view-count,features-active,feature-group-active,modification-date-time,phone,attributes,extended-info&size=20"

    @Suppress("MaxLineLength")
    internal const val OTHER_USER_ADS_ENDPOINT = "users/{username}/ads?_in=title,description,price,highest-price,pictures,category,locations,creation-date-time,start-date-time,end-date-time,ad-status,ad-type,rank,view-ad-count,phone-click-count,map-view-count,features-active,feature-group-active,modification-date-time,phone,attributes,extended-info"

    @Suppress("MaxLineLength")
    internal const val OTHER_USER_AD_DETAILS_ENDPOINT = "users/{username}/ads/{adId}/?_in=title,price,highest-price,description,category,locations,ad-address,attributes,creation-date-time,start-date-time,end-date-time,pictures,ad-status,ad-type,phone,link,features-active,feature-group-active,rank,view-ad-count,phone-click-count,map-view-count,poster-contact-name,extended-info&_expanded=true"
    internal const val GET_USERS_AD_COUNT_ENDPOINT = "ads?_in=id"

    internal const val GET_PURCHASABLE_FEATURES_ENDPOINT = "features/ad"

    internal const val SAVED_SEARCH_ENDPOINT = "alerts"
    internal const val DELETE_SAVED_SEARCH_ENDPOINT = "alerts/{alertId}"

    internal const val GET_ATTRIBUTES_ENDPOINT = "attributes/metadata/{categoryId}/{attribute}"
    internal const val GET_ATTRIBUTES_DEFAULT_VALUES_ENDPOINT = "attributes/metadata/{categoryId}/defaults"

    internal const val GET_USER_CONVERSATIONS_ENDPOINT = "users/{username}/conversations"
    internal const val GET_USER_CONVERSATION_DETAILS_ENDPOINT = "users/{username}/conversations/{conversationId}"
    internal const val DELETE_USER_CONVERSATIONS_ENDPOINT = "users/{username}/conversations/{conversationId}"
    internal const val REPLY_TO_AD_CONVERSATION_ENDPOINT = "replies/reply-to-ad-conversation"

    internal const val GET_SELECTED_SKILLS_ENDPOINT = "skill/selected/{accountId}/{categoryId}"
}
