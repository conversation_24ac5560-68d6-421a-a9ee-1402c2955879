package api.similarItems.api

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.similarItems.SimilarItemsApiParams
import com.gumtree.mobile.api.similarItems.SimilarItemsUrls
import com.gumtree.mobile.api.similarItems.models.RawPapiSimilarItems
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.Query

fun interface SimilarItemsApi {

    @GET(SimilarItemsUrls.SIMILAR_ITEMS_ENDPOINT)
    suspend fun getSimilarItems(
        @HeaderMap authorisationHeaders: ApiHeaders,
        @Query(SimilarItemsApiParams.LIMIT) limit: Int,
        @Query(SimilarItemsApiParams.AD_ID) adId: String,
    ): RawPapiSimilarItems
}
