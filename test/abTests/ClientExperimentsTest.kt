package abTests

import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.http.Headers
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.ClientExperimentsFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUiTest
import tools.runUnitTest

@ParallelTest
class ClientExperimentsTest {

    private val robot = Robot()

    @Test
    fun `should return experiment with A variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.A,
                    )
                )
            }
            When { isA(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should NOT return experiment with A variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.B,
                    )
                )
            }
            When { isA(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(false) }

            Given { stubClientExperiments(ClientExperimentsFactory.createNoExperiments()) }
            When { isA(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(false) }
        }
    }

    @Test
    fun `should return experiment with B variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.B,
                    )
                )
            }
            When { isB(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should NOT return experiment with B variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.A,
                    )
                )
            }
            When { isB(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(false) }
        }
    }

    @Test
    fun `should return experiment with C variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.C,
                    )
                )
            }
            When { isC(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should NOT return experiment with C variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.D,
                    )
                )
            }
            When { isC(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(false) }
        }
    }

    @Test
    fun `should return experiment with D variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.D,
                    )
                )
            }
            When { isD(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should NOT return experiment with D variant`() {
        runUiTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_4006 to Variant.C,
                    )
                )
            }
            When { isD(Experiment.GTNA_4006) }
            Then { checkIsExperimentVariant(false) }
        }
    }

    @Test
    fun `should return null if home feed categories experiment is unknown for Android and iOS`() {
        runUnitTest(robot) {
            Given { createHeader("GTB-263", "") }
            When { getHomeFeedCategoriesExperimentValue() }
            Then { checkHomeFeedCategoriesExperimentVariant(null) }
        }
    }

    @Test
    fun `should return Variant if exists in Android and not iOS`() {
        runUnitTest(robot) {
            Given { createHeader("GTB-263", "C") }
            When { getHomeFeedCategoriesExperimentValue() }
            Then { checkHomeFeedCategoriesExperimentVariant(Variant.C) }
        }
    }

    @Test
    fun `should return Variant if exists in ioS and not Android`() {
        runUnitTest(robot) {
            Given { createHeader("GTB-262", "C") }
            When { getHomeFeedCategoriesExperimentValue() }
            Then { checkHomeFeedCategoriesExperimentVariant(Variant.C) }
        }
    }

    @Test
    fun `should convert Experiments into GAM format`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_1 to Variant.D,
                        Experiment.GTNA_2 to Variant.B,
                    )
                )
            }
            When { toWebExperiments() }
            Then { checkWebExperimentsString("${Experiment.GTNA_1.key}-${Variant.D},${Experiment.GTNA_2.key}-${Variant.B}") }
        }
    }

    @Test
    fun `should convert Experiments into Bing format`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.GTNA_1 to Variant.D,
                        Experiment.GTNA_2 to Variant.B,
                    )
                )
            }
            When { toPartnershipExperimentsString() }
            Then { checkPartnershipsExperimentsString("${Experiment.GTNA_1.key}_${Variant.D},${Experiment.GTNA_2.key}_${Variant.B}") }
        }
    }

    @Test
    fun `should return experiment with SWX_UI_OPT_TEST variant B`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.SERVICE_NEW_UI to Variant.B,
                    )
                )
            }
            When { isB(Experiment.SERVICE_NEW_UI) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should NOT return experiment with SWX_UI_OPT_TEST variant B when variant A`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.SERVICE_NEW_UI to Variant.A,
                    )
                )
            }
            When { isB(Experiment.SERVICE_NEW_UI) }
            Then { checkIsExperimentVariant(false) }
        }
    }

    @Test
    fun `should return experiment with SWX_UI_OPT_TEST variant A`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.SERVICE_NEW_UI to Variant.A,
                    )
                )
            }
            When { isA(Experiment.SERVICE_NEW_UI) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should return experiment with SWX_UI_OPT_TEST variant C`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.SERVICE_NEW_UI to Variant.C,
                    )
                )
            }
            When { isC(Experiment.SERVICE_NEW_UI) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    @Test
    fun `should return experiment with SWX_UI_OPT_TEST variant D`() {
        runUnitTest(robot) {
            Given {
                stubClientExperiments(
                    ClientExperimentsFactory.createExperiments(
                        Experiment.SERVICE_NEW_UI to Variant.D,
                    )
                )
            }
            When { isD(Experiment.SERVICE_NEW_UI) }
            Then { checkIsExperimentVariant(true) }
        }
    }

    private class Robot : BaseRobot {
        private var actualIsExperimentVariantResult: Boolean = false
        private var actualHomeFeedCategoriesExperimentVariant: Variant? = null
        private var actualWebExperimentsString: String? = null
        private var actualPartnershipsExperimentsString: String? = null
        private lateinit var testSubject: ClientExperiments
        private lateinit var headers: Headers

        fun createHeader(experimentKey: String, experimentValue: String) {
            headers = Headers.build {
                append(ApiHeaderParams.EXPERIMENTS, "$experimentKey.$experimentValue")
            }
        }

        fun stubClientExperiments(clientExperiments: ClientExperiments) {
            testSubject = clientExperiments
        }

        fun isA(experiment: Experiment) {
            actualIsExperimentVariantResult = testSubject.isA(experiment)
        }

        fun isB(experiment: Experiment) {
            actualIsExperimentVariantResult = testSubject.isB(experiment)
        }

        fun isC(experiment: Experiment) {
            actualIsExperimentVariantResult = testSubject.isC(experiment)
        }

        fun isD(experiment: Experiment) {
            actualIsExperimentVariantResult = testSubject.isD(experiment)
        }

        fun getHomeFeedCategoriesExperimentValue() {
            actualHomeFeedCategoriesExperimentVariant = ClientExperiments.getHomeFeedCategoriesExperimentValue(headers)
        }

        fun toWebExperiments() {
            actualWebExperimentsString = testSubject.toGamExperimentsString()
        }

        fun toPartnershipExperimentsString() {
            actualPartnershipsExperimentsString = testSubject.toPartnershipExperimentsString()
        }

        fun checkWebExperimentsString(expected: String?) {
            assertEquals(expected, actualWebExperimentsString)
        }

        fun checkPartnershipsExperimentsString(expected: String?) {
            assertEquals(expected, actualPartnershipsExperimentsString)
        }

        fun checkIsExperimentVariant(expected: Boolean) {
            assertEquals(expected, actualIsExperimentVariantResult)
        }

        fun checkHomeFeedCategoriesExperimentVariant(expected: Variant?) {
            assertEquals(expected, actualHomeFeedCategoriesExperimentVariant)
        }
    }
}
