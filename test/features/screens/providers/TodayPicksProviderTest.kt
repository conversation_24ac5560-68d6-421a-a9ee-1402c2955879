package features.screens.providers

import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.TodayPicksCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.providers.TODAY_PICKS_TEXT
import com.gumtree.mobile.features.screens.providers.TodayPicksProvider
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawLocationFactory

class TodayPicksProviderTest {

    private val robot = Robot()

    @Test
    fun `should return today picks row with 1 row layout item`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubDistance(Distance.FIVE) }
            When { getTodayPicksRow() }
            Then { checkTodayPicksRowLayoutsSize(1) }
        }
    }

    @Test
    fun `should return today picks row with today picks row layout type`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubDistance(Distance.FIVE) }
            When { getTodayPicksRow() }
            Then { checkTodayPicksRowLayoutTypeAtPosition(RowLayoutType.TODAY_PICKS_ROW) }
        }
    }

    @Test
    fun `should return expected TodayPicksCardDto`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubDistance(Distance.FIVE) }
            Given { stubLocationDto(DataFactory.SOME_LOCATION_NAME) }
            When { getTodayPicksRow() }
            Then {
                checkTodayPicksCard(
                    TodayPicksCardDto(
                        TODAY_PICKS_TEXT,
                        TodayPicksCardDto.Location(
                            DataFactory.SOME_LOCATION_NAME_SHORT,
                            DestinationRoute.LOCATION_OVERVIEW.build(
                                ApiQueryParams.LOCATION_ID to DataFactory.SOME_LOCATION_ID,
                                ApiQueryParams.LOCATION_TYPE to LocationType.LOCATION.name,
                                ApiQueryParams.LOCATION_NAME to DataFactory.SOME_LOCATION_NAME,
                                ApiQueryParams.LATITUDE to DataFactory.SOME_LOCATION_LAT,
                                ApiQueryParams.LONGITUDE to DataFactory.SOME_LOCATION_LNG,
                                ApiQueryParams.DISTANCE to Distance.FIVE.name
                            )
                        )
                    )
                )
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualTodayPicksRowResult: RowLayout<UiItem>

        private lateinit var locationId: String
        private lateinit var locationType: LocationType
        private lateinit var distance: Distance
        private val rawLocationFetcher: RawLocationFetcher = mockk(relaxed = true)

        private var testSubject = TodayPicksProvider(rawLocationFetcher)

        fun stubLocationId(locationId: String) {
            this.locationId = locationId
        }

        fun stubLocationType(locationType: LocationType) {
            this.locationType = locationType
        }

        fun stubDistance(distance: Distance) {
            this.distance = distance
        }

        fun stubLocationDto(locationName: String) {
            val locationDto = RawLocationFactory.createRawLocation(
                id = DataFactory.SOME_LOCATION_ID.toInt(),
                name = locationName,
                type = RawLocation.Type.location,
                locationId = DataFactory.SOME_LOCATION_ID.toInt(),
                latitude = DataFactory.SOME_LOCATION_LAT_DOUBLE,
                longitude = DataFactory.SOME_LOCATION_LON_DOUBLE
            )
            coEvery { rawLocationFetcher.fetchByLocationIdAndType(any(), any()) } returns locationDto
        }

        suspend fun getTodayPicksRow() {
            actualTodayPicksRowResult = testSubject.getTodayPicksRow(locationId, locationType, distance.name)
        }

        fun checkTodayPicksRowLayoutsSize(expected: Int) {
            assertEquals(expected, actualTodayPicksRowResult.data.size)
        }

        fun checkTodayPicksRowLayoutTypeAtPosition(expected: RowLayoutType) {
            assertEquals(expected, actualTodayPicksRowResult.type)
        }

        fun checkTodayPicksCard(expected: TodayPicksCardDto) {
            assertEquals(expected, actualTodayPicksRowResult.data[0])
        }
    }
}
