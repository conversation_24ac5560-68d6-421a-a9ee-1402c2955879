package com.gumtree.mobile.api.locations

import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.routes.DEFAULT_LOCATION_ID
import com.gumtree.mobile.routes.DEFAULT_UK_LATITUDE
import com.gumtree.mobile.routes.DEFAULT_UK_LONGITUDE
import com.gumtree.mobile.utils.LocationDefaults

/**
 * Fetcher for Raw Locations
 */
fun interface RawLocationFetcher {
    /**
     * Fetch raw Location data by location ID and location type
     * @param locationId - the location id
     * @param locationType - the location type
     * @return - the data about the Raw Location
     */
    suspend fun fetchByLocationIdAndType(
        locationId: String,
        locationType: LocationType,
    ): RawLocation

    companion object {
        fun createAllUKRawLocationData(): RawLocation {
            return RawLocation(
                id = LocationDefaults.ALL_UK.id.toInt(),
                name = LocationDefaults.ALL_UK.text,
                type = RawLocation.Type.location,
                latitude = DEFAULT_UK_LATITUDE.toDouble(),
                longitude = DEFAULT_UK_LONGITUDE.toDouble(),
            )
        }
    }
}

class DefaultRawLocationFetcher(private val locationsApi: LocationsApi) : RawLocationFetcher {

    override suspend fun fetchByLocationIdAndType(
        locationId: String,
        locationType: LocationType,
    ): RawLocation {
        require(locationId.isNotBlank()) { "Location Id cannot be blank" }

        return if (locationId == DEFAULT_LOCATION_ID) {
            RawLocationFetcher.createAllUKRawLocationData()
        } else {
            when (locationType) {
                LocationType.LOCATION -> locationsApi.getLocationById(locationId)
                LocationType.POSTCODE -> locationsApi.getLocationPostcodeById(locationId)
                LocationType.OUTCODE -> locationsApi.getLocationOutcodeById(locationId)
            }
        }
    }
}
