package com.gumtree.mobile.features.phoneVerification

import com.gumtree.mobile.di.CAPI_HEADERS_PROVIDER
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

val phoneVerificationModule = module {

    single { PhoneVerificationMapper() }

    single { PhoneVerificationAnalyticsProvider(commonAnalyticsProvider = getFromKoin()) }
    single<PhoneVerificationRepository> {
        DefaultPhoneVerificationRepository(
            phoneVerificationMapper = getFromKoin(),
            phoneVerificationService = getFromKoin(),
            dispatcherProvider = getFromKoin(),
            capiHeadersProvider = getFromKoin(CAPI_HEADERS_PROVIDER),
            analyticsProvider = getFromKoin(),
        )
    }
    single {
        PhoneVerificationService(
            phoneNumberAuthenticatorApi = getFromKoin(),
            userServiceApi = getFromKoin(),
            coreChatAuthApi = getFromKoin(),
            capiAdsApi = getFromKoin(),
        )
    }
}
