package com.gumtree.mobile.features.registration

import com.gumtree.mobile.api.userService.bodies.UserServiceActivateBody

/**
 * Creator of RawAccountActivationBody data instances when activating new user registration
 */
class RegistrationActivationBodyCreator {

    /**
     * Create RawAccountActivationBody data instance to be passed as a body to the POST registration activation request in CAPI
     * @param activateRegistrationRequest - the registration activation body payload
     * @return - the instance of RawAccountActivationBody data
     */
    fun create(activateRegistrationRequest: ActivateRegistrationRequest) = UserServiceActivateBody(
        token = activateRegistrationRequest.activationToken,
        username = activateRegistrationRequest.userName,
    )
}
