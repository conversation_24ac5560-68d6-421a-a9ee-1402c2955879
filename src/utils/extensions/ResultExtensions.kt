package com.gumtree.mobile.utils.extensions

import io.ktor.util.logging.error
import org.slf4j.LoggerFactory

// this cannot be private as it is inside a public inline extension function
val runCatchingLogger = LoggerFactory.getLogger("runCatching")

/**
 * Calls the specified function block and returns its encapsulated result if invocation was successful,
 * catching any Throwable exception that was thrown from the block function execution and encapsulating it as a failure.
 * This also logs the failure
 */
@SinceKotlin("1.3")
inline fun <T, R> T.runCatchingWithLog(errorPrefix: String? = null, block: T.() -> R): Result<R> {
    return try {
        Result.success(block())
    } catch (e: Throwable) {
        if (errorPrefix.isNotNull()) {
            runCatchingLogger.error("$errorPrefix -> error: $e")
        } else {
            runCatchingLogger.error(e)
        }
        Result.failure(e)
    }
}

/**
 * Calls the specified function [block] and returns its encapsulated result if invocation was successful,
 * catching any [Throwable] exception that was thrown from the [block] function execution and encapsulating it as a failure.
 * This also logs the failure
 */
@SinceKotlin("1.3")
inline fun <R> runCatchingWithLog(errorPrefix: String? = null, block: () -> R): Result<R> {
    return try {
        Result.success(block())
    } catch (e: Throwable) {
        if (errorPrefix.isNotNull()) {
            runCatchingLogger.error("$errorPrefix -> error: $e")
        } else {
            runCatchingLogger.error(e)
        }
        Result.failure(e)
    }
}

/**
 * Calls the specified function [block] and returns its encapsulated result if invocation was successful,
 * catching any [Throwable] exception that was thrown from the [block] function execution and encapsulating it as a failure.
 * This does NOT log the failure
 */
@SinceKotlin("1.3")
inline fun <R> runCatchingWithoutLog(block: () -> R): Result<R> {
    return try {
        Result.success(block())
    } catch (e: Throwable) {
        Result.failure(e)
    }
}
