package com.gumtree.mobile.features.srp

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdList
import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.common.analytics.AD_PREFIX
import com.gumtree.mobile.common.analytics.LISTING_PREFIX
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoriesTreeCache.isVehicles
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.ContactButtonRowDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.wrapScreenDataWithRowLayouts
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.data.toLargeListingCardDto
import com.gumtree.mobile.utils.extensions.data.toListingCardDto
import com.gumtree.mobile.utils.extensions.data.toSingleListingCardDto

/**
 * Mapping RawCapiAdList to different SRP ListingCardDto's
 */

class SrpListingMapper(
    private val srpAnalyticsProvider: SrpAnalyticsProvider,
    private val srpTimeAgoFormatter: SrpTimeAgoFormatter,
) {

    fun map(
        rawAdsData: RawCapiAdList,
        listingGridSizes: GridSizes,
        categoryId: String,
        isVehiclesSingleColumnLayout: Boolean,
        queryParams: QueryParams,
        serviceButtonsMap: Map<String, List<ContactButtonRowDto>?>? = null,
        searchLocationData: RawLocation? = null,
        experiments: ClientExperiments? = null,
    ): Pair<PortraitData, LandscapeData?> {
        val rawAds = rawAdsData.rawAds.orEmpty()
        return when {
            isVehicles(categoryId) && isVehiclesSingleColumnLayout -> mapCarsListings(
                rawAds,
                listingGridSizes,
                queryParams,
            )

            CategoriesTreeCache.isServices(categoryId) -> {
                when {
                    experiments?.isB(Experiment.SERVICE_NEW_UI) == true -> mapServicesListings(
                        rawAds,
                        listingGridSizes,
                        queryParams,
                        serviceButtonsMap,
                        searchLocationData,
                    )

                    else -> mapDefaultListings(
                        rawAds,
                        listingGridSizes,
                        queryParams,
                    ) // base (A) and other variants use default layout
                }
            }

            else -> mapDefaultListings(rawAds, listingGridSizes, queryParams)
        }
    }

    private fun mapCarsListings(
        rawAds: List<RawCapiAd>,
        listingGridSizes: GridSizes,
        queryParams: QueryParams,
    ): Pair<PortraitData, LandscapeData?> {
        val searchResultCarListingCards = rawAds.map {
            it.toLargeListingCardDto(
                srpAnalyticsProvider.getClickListingEvent(AD_PREFIX, it),
                srpTimeAgoFormatter,
                srpAnalyticsProvider.getCommonListingParams(AD_PREFIX, it),
                queryParams,
            )
        }
        return searchResultCarListingCards.wrapScreenDataWithRowLayouts<UiItem>(listingGridSizes)
    }

    private fun mapServicesListings(
        rawAds: List<RawCapiAd>,
        listingGridSizes: GridSizes,
        queryParams: QueryParams,
        serviceButtonsMap: Map<String, List<ContactButtonRowDto>?>?,
        searchLocationData: RawLocation? = null,
    ): Pair<PortraitData, LandscapeData?> {
        val searchResultServiceListingCards = rawAds.map { rawAd ->
            val buttons = serviceButtonsMap?.get(rawAd.id)
            rawAd.toSingleListingCardDto(
                analyticsEventData = srpAnalyticsProvider.getClickListingEvent(AD_PREFIX, rawAd),
                analyticsParameters = srpAnalyticsProvider.getEnhancedCommonListingParams(
                    LISTING_PREFIX,
                    rawAd,
                    queryParams[ApiQueryParams.REQUEST_ID],
                ),
                queryParams = queryParams,
                buttons = buttons,
                searchLocationData = searchLocationData,
            )
        }
        return searchResultServiceListingCards.wrapScreenDataWithRowLayouts<UiItem>(
            listingGridSizes,
            RowLayoutType.SINGLE_LISTING_ROW,
        )
    }

    private fun mapDefaultListings(
        rawAds: List<RawCapiAd>,
        listingGridSizes: GridSizes,
        queryParams: QueryParams,
    ): Pair<PortraitData, LandscapeData?> {
        val searchResultListingCards = rawAds.map { rawAd ->
            rawAd.toListingCardDto(
                srpAnalyticsProvider.getClickListingEvent(AD_PREFIX, rawAd),
                srpAnalyticsProvider.getCommonListingParams(AD_PREFIX, rawAd),
                queryParams,
            )
        }
        return searchResultListingCards.wrapScreenDataWithRowLayouts<UiItem>(listingGridSizes)
    }
}
