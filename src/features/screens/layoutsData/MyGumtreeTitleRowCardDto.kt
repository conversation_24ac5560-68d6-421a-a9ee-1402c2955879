package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.api.common.EMPTY_STRING
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("MY_GUMTREE_TITLE_CARD")
data class MyGumtreeTitleRowCardDto(
    val title: String,
    val selectedFilter: MyGumtreeFilter,
    val filters: Map<MyGumtreeFilter, String>,
) : UiItem

@Serializable
enum class MyGumtreeFilter(
    val text: String,
    val queryParams: String,
) {
    ALL_ADS("All ads", "EXPIRED,DELETED_USER,DELETED_CS"),
    ACTIVE("Active", EMPTY_STRING),
    ;

    /**
     * Convert optional String to MyGumtreeFilter
     * @return - MyGumtreeFilter matching the String value,
     * If the String is NULL return as default MyGumtreeFilter.ALL_ADS
     */
    companion object {
        fun fromString(string: String?): MyGumtreeFilter {
            return when (string) {
                ACTIVE.name -> ACTIVE
                else -> ALL_ADS
            }
        }
    }
}
