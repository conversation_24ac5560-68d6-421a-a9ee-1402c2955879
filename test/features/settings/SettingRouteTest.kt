package features.settings

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.settings.SETTINGS_SCREEN_PATH
import com.gumtree.mobile.features.settings.SettingsRepository
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class SettingRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET settings screen request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When {
                getSettingsScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_TOKEN,
                    appVersion = DataFactory.SOME_APP_VERSION,
                    isSavedSearchPushEnabled = true,
                    isChatPushEnabled = true,
                    isMarketingPushEnabled = true
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET settings screen request with success when email and token NOT sent`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When {
                getSettingsScreen(
                    userEmail = EMPTY_STRING,
                    userToken = EMPTY_STRING,
                    appVersion = DataFactory.SOME_APP_VERSION,
                    isSavedSearchPushEnabled = true,
                    isChatPushEnabled = true,
                    isMarketingPushEnabled = true
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET settings screen request with success when push preference params NOT sent`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When {
                getSettingsScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_TOKEN,
                    appVersion = DataFactory.SOME_APP_VERSION,
                    isSavedSearchPushEnabled = null,
                    isChatPushEnabled = null,
                    isMarketingPushEnabled = null
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET settings screen request with error`() = runRouteTestForException(module, InternalError::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(InternalError()) }
            When {
                getSettingsScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_TOKEN,
                    appVersion = DataFactory.SOME_APP_VERSION,
                    isSavedSearchPushEnabled = false,
                    isChatPushEnabled = false,
                    isMarketingPushEnabled = false
                )
            }
        }
    }

    private class Robot: BaseRouteRobot() {

        val repository: SettingsRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse
        private lateinit var screenPortraitData: List<RowLayout<UiItem>>
        private var screenLandscapeData: List<RowLayout<UiItem>>? = null

        fun stubScreenPortraitData() {
            screenPortraitData = emptyList()
        }

        fun stubScreenLandscapeData() {
            screenLandscapeData = null
        }

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any(), any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery {
                repository.readScreen(any(), any(), any(), any())
            } throws error
        }

        suspend fun getSettingsScreen(
            userEmail: String,
            userToken: String,
            appVersion: String,
            isSavedSearchPushEnabled: Boolean? = null,
            isChatPushEnabled: Boolean? = null,
            isMarketingPushEnabled: Boolean? = null
        ) {
            actualResponse = client.get(SETTINGS_SCREEN_PATH) {
                headers { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, userEmail) }
                headers { append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, userToken) }
                headers { append(ApiHeaderParams.APP_VERSION, appVersion) }
                parameter(ApiQueryParams.IS_SAVED_SEARCH_PUSH_ENABLED, isSavedSearchPushEnabled)
                parameter(ApiQueryParams.IS_CHAT_PUSH_ENABLED, isChatPushEnabled)
                parameter(ApiQueryParams.IS_MARKETING_PUSH_ENABLED, isMarketingPushEnabled)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }
}