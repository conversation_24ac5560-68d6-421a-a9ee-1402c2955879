package features.conversations

import api.capi.bodies.RawReplyToAdConversation
import api.capi.models.RawConversation
import com.gumtree.mobile.features.conversations.CreateConversationBodyCreator
import com.gumtree.mobile.features.conversations.CreateConversationRequest
import io.swagger.codegen.v3.service.exception.BadRequestException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import kotlin.test.assertEquals
import tools.runUnitTestForException

class CreateConversationBodyCreatorTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should create conversation body payload with request containing text, adId and username`() {
        runUnitTest(robot) {
            Given {
                stubCreateConversationRequest(
                    DataFactory.SOME_COMMENT,
                    null
                )
            }
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkAdId(DataFactory.SOME_AD_ID) }
            Then { checkEmail(DataFactory.SOME_USER_EMAIL) }
            Then { checkMessage(DataFactory.SOME_COMMENT) }
            Then { checkReplyDirection(RawConversation.TO_OWNER) }
        }
    }

    @Test
    fun `should create conversation body payload with request containing imageURL, adId and username`() {
        runUnitTest(robot) {
            Given {
                stubCreateConversationRequest(
                    null,
                    DataFactory.SOME_AD_IMAGE_URL
                )
            }
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkAdId(DataFactory.SOME_AD_ID) }
            Then { checkEmail(DataFactory.SOME_USER_EMAIL) }
            Then { checkMessage(DataFactory.SOME_AD_IMAGE_URL) }
        }
    }

    @Test
    fun `should throw BadRequestException when create conversation body payload with request (containing NO imageURL or text), adId and username`() {
        runUnitTestForException(robot, BadRequestException::class) {
            Given {
                stubCreateConversationRequest(
                    null,
                    null
                )
            }
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var rawReplyToAdConversationBody: RawReplyToAdConversation
        private lateinit var createConversationRequest: CreateConversationRequest
        private lateinit var adId: String
        private lateinit var userName: String

        private lateinit var testSubject: CreateConversationBodyCreator

        override fun setup() {
            testSubject = CreateConversationBodyCreator()
        }

        fun stubCreateConversationRequest(
            text: String?,
            imageURL: String?
        ) {
            createConversationRequest = CreateConversationRequest(
                message = CreateConversationRequest.Message(
                    text = text,
                    imageURL = imageURL
                )
            )
        }

        fun stubAdId(adId: String) {
            this.adId = adId
        }

        fun stubUsername(userName: String) {
            this.userName= userName
        }

        fun create() {
            rawReplyToAdConversationBody = testSubject.create(
                adId,
                userName,
                createConversationRequest
            )
        }

        fun checkAdId(expected: String) {
            assertEquals(expected, rawReplyToAdConversationBody.adId)
        }

        fun checkEmail(expected: String) {
            assertEquals(expected, rawReplyToAdConversationBody.email)
        }

        fun checkMessage(expected: String) {
            assertEquals(expected, rawReplyToAdConversationBody.message)
        }

        fun checkReplyDirection(expected: String) {
            assertEquals(expected, rawReplyToAdConversationBody.direction.value)
        }
    }
}