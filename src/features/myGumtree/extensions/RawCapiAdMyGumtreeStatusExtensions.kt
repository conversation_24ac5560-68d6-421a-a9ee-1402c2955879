package com.gumtree.mobile.features.myGumtree.extensions

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.myGumtree.v2.extensions.DELAYED
import com.gumtree.mobile.features.screens.layoutsData.HyperlinkMeta
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeStatus
import com.gumtree.mobile.features.screens.layoutsData.StatusLabel

// todo: Need to find out what all the different statuses that come from Capi are called - Review, removed by CS, Needs Editing, Draft
fun RawCapiAd.getMyGumtreeStatus(): MyGumtreeStatus? {
    val rawStatus = status.value
    val status = when (rawStatus) {
        PENDING -> processing
        EXPIRED -> expired
        DELETED -> deleted
        DELAYED -> underReview // is this correct
        REMOVED -> adRemoved
        PAYABLE, CREATED -> requiresPayment
        PAUSED -> requiresEditing
        else -> null
    }
    // todo: legacy app also has inactive, paused, delayed, uploading, archived and created

    return status
}

private val processing = MyGumtreeStatus(
    text = "Processing...",
    toolTip = "We moderate listings in order to follow safety practices, which can sometimes take a few hours",
)

private val underReview = MyGumtreeStatus(
    text = "Under review...",
    toolTip = "We moderate listings in order to follow safety practices, which can sometimes take a few hours",
)

private val adRemoved = MyGumtreeStatus(
    text = "Ad has been removed",
    toolTip = "Your ad has been removed from the site because it breaks the posting rules. We have emailed you to explain why",
    toolTipHyperlink = HyperlinkMeta("posting rules", "gumtree.com/rules"),
    label = StatusLabel.REMOVED,
)

private val deleted = MyGumtreeStatus(label = StatusLabel.DELETED)

private val expired = MyGumtreeStatus(label = StatusLabel.EXPIRED)

private val requiresPayment = MyGumtreeStatus()

private val requiresEditing = MyGumtreeStatus()
