package com.gumtree.mobile.api.capi

import com.gumtree.mobile.api.common.CustomConverterFactory
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type

class CapiConverterFactoryAdapter(private val default: Converter.Factory) : Converter.Factory() {

    private val factories = mutableListOf<CustomConverterFactory>()

    fun addCustomFactory(factory: CustomConverterFactory) {
        factories.add(factory)
    }

    @Suppress("ReturnCount")
    override fun responseBodyConverter(
        type: Type,
        annotations: Array<Annotation>,
        retrofit: Retrofit,
    ): Converter<ResponseBody, *>? {
        if (unknownType(type)) {
            return null
        }
        factories.firstOrNull { it.validType(type) }?.let { factory ->
            return factory.getFactory().responseBodyConverter(type, annotations, retrofit)
        }
        return default.responseBodyConverter(type, annotations, retrofit)
    }

    @Suppress("ReturnCount")
    override fun requestBodyConverter(
        type: Type,
        parameterAnnotations: Array<Annotation>,
        methodAnnotations: Array<Annotation>,
        retrofit: Retrofit,
    ): Converter<*, RequestBody>? {
        if (unknownType(type)) {
            return null
        }
        factories.firstOrNull { it.validType(type) }?.let { factory ->
            return factory.getFactory().requestBodyConverter(type, parameterAnnotations, methodAnnotations, retrofit)
        }
        return default.requestBodyConverter(type, parameterAnnotations, methodAnnotations, retrofit)
    }

    private fun unknownType(type: Type): Boolean = type !is Class<*>
}
