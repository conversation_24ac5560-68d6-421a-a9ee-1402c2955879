package com.gumtree.mobile.utils.extensions.data

import api.capi.models.FEATURE_AD_INSERTION
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.common.ImageType
import com.gumtree.mobile.utils.DefaultPriceFormatter
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.extensions.runCatchingWithLog

fun RawFlatAd.isPrivateSeller(): Boolean {
    return attribute
        ?.get(RawFlatAd.ATTRIBUTE_SELLER_TYPE)
        ?.equals("private", true)
        ?: false
}

fun RawFlatAd.isTradeSeller(): Boolean {
    return attribute
        ?.get(RawFlatAd.ATTRIBUTE_SELLER_TYPE)
        ?.equals("trade", true)
        ?: false
}

fun RawFlatAd.getFirstPictureImages(imageType: ImageType = ImageType.THUMBNAIL_SQUARE): List<Image>? {
    return primaryImageUrl?.toListWithImages(imageType)
}

fun RawFlatAd.getCategory(): RawFlatAd.AdCategory {
    require(categories.isNotEmpty()) { "Missing Flat Ad category data" }
    return categories
        .firstOrNull { it.primary }
        ?: categories.last()
}

fun RawFlatAd.getLocation(): RawFlatAd.AdLocation {
    require(locations.isNotEmpty()) { "Missing Flat Ad location data" }
    return locations
        .firstOrNull { it.primary }
        ?: locations.last()
}

fun RawFlatAd.getDescriptiveLocation(): String {
    return runCatchingWithLog("getDescriptiveLocation on FlatAd error: locations.size = ${locations.size}") {
        locations.takeLast(2).joinToString { it.name }
    }.getOrDefault(EMPTY_STRING)
}

fun RawFlatAd.getLocationHierarchy(): String {
    return when {
        locations.isNotEmpty() -> "L${locations.size - 1}"
        else -> EMPTY_STRING
    }
}

fun RawFlatAd.getImagesCount(): Int {
    return when {
        primaryImageUrl.isNotNullOrEmpty() -> (additionalImageUrls?.size ?: ZERO) + 1
        else -> ZERO
    }
}

fun RawFlatAd.hasActiveFeature(feature: String): Boolean {
    return features
        ?.firstOrNull { it.product == feature }
        .isNotNull()
}

@Suppress("MagicNumber")
fun RawFlatAd.getFormattedPrice(): String? {
    return DefaultPriceFormatter.formatPrice(
        amount = attribute
            ?.get(RawFlatAd.ATTRIBUTE_PRICE)
            ?.toLongOrNull()
            ?.let { "${it / 100}" } // RawFlatAd price is in pence
            ?.toString(),
        frequency = attribute?.get(RawFlatAd.ATTRIBUTE_PRICE_FREQUENCY),
    )
}

fun RawFlatAd.isActive(): Boolean = AdStatus.fromString(status) == AdStatus.ACTIVE

fun RawFlatAd.getPromotions(): List<String> {
    return features?.map { it.product } ?: emptyList()
}

fun RawFlatAd.isFromPaidCategory(): Boolean {
    return features
        ?.firstOrNull { it.product == FEATURE_AD_INSERTION }
        .isNotNull()
}

fun RawFlatAd.hasLocationData(): Boolean = centroid.isNotNull()

fun RawFlatAd.getBreedAttribute(): String? {
    return attribute?.get(
        attribute.keys.firstOrNull { it.endsWith("_breed", ignoreCase = true) },
    )
}
