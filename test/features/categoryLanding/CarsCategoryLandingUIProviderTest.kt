package features.categoryLanding

import com.gumtree.mobile.IMAGE_ASSET_URL_ENV_VARIABLE
import com.gumtree.mobile.common.ANY_KEY
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.common.FilterAttributeDefault
import com.gumtree.mobile.features.categoryLanding.ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME
import com.gumtree.mobile.features.categoryLanding.CarsCategoryLandingAnalyticsProvider
import com.gumtree.mobile.features.categoryLanding.CarsCategoryLandingUIProvider
import com.gumtree.mobile.features.categoryLanding.CategoryLandingScreenUiConfiguration
import com.gumtree.mobile.features.categoryLanding.CategoryLandingSimilarItemsMapper
import com.gumtree.mobile.features.filters.FiltersScreenUiConfiguration
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.ButtonsFactory
import com.gumtree.mobile.features.screens.factories.DropdownFactory
import com.gumtree.mobile.features.screens.factories.ImageFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.ButtonCardDto
import com.gumtree.mobile.features.screens.layoutsData.DoubleDropdownCardDto
import com.gumtree.mobile.features.screens.layoutsData.DropdownCardDto
import com.gumtree.mobile.features.screens.layoutsData.ImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VehicleBodyTypeCardDto
import com.gumtree.mobile.responses.ScreenType
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.CategoryDefaults
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.AnalyticsEventFactory
import tools.layoutsDataFactory.FiltersCategoryAttributesFactory
import tools.runUnitTest
import kotlin.test.assertEquals
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory.SOME_LOCAL_IMAGE_ASSET_URL
import tools.rawDataFactory.RawLocationFactory

class CarsCategoryLandingUIProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return image row`() {
        runUnitTest(robot) {
            Given { stubImageAssetUrl(SOME_LOCAL_IMAGE_ASSET_URL) }
            When { createImageRow() }
            Then { checkRowLayoutType(RowLayoutType.IMAGE_ROW) }
            Then { checkImageCardDtoImagesSize(4) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return search section title row`() {
        runUnitTest(robot) {
            When { createSearchSectionTitleRow() }
            Then { checkRowLayoutType(RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoText(CategoryLandingScreenUiConfiguration.FIND_YOUR_DREAM_CAR_TEXT) }
            Then { checkTitleCardDtoSize(TitleCardDto.Size.MEDIUM) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return vehicle make dropdown row with default selected option`() {
        runUnitTest(robot) {
            Given { stubSelectedVehicleMake(null) }
            When { createVehicleMakeDropdownRow() }
            Then { checkRowLayoutType(RowLayoutType.DROPDOWN_ROW) }
            Then { checkDropdownCardDtoTitle(CategoryLandingScreenUiConfiguration.MAKE_DROPDOWN_TITLE_TEXT) }
            Then { checkDropdownCardDtoParam(ApiQueryParams.VEHICLE_MAKE) }
            Then { checkDropdownCardDtoSelectedOption(FilterAttributeDefault.ANY_MAKE.key) }
            Then { checkDropdownCardDtoOptionsSize(5) }
            Then { checkDropdownCardDtoChangeAction(DropdownCardDto.Action.REFRESH) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return vehicle make dropdown row with the selected option`() {
        runUnitTest(robot) {
            Given { stubSelectedVehicleMake("alfa_romeo") }
            When { createVehicleMakeDropdownRow() }
            Then { checkRowLayoutType(RowLayoutType.DROPDOWN_ROW) }
            Then { checkDropdownCardDtoTitle(CategoryLandingScreenUiConfiguration.MAKE_DROPDOWN_TITLE_TEXT) }
            Then { checkDropdownCardDtoParam(ApiQueryParams.VEHICLE_MAKE) }
            Then { checkDropdownCardDtoSelectedOption("alfa_romeo") }
            Then { checkDropdownCardDtoOptionsSize(5) }
            Then { checkDropdownCardDtoChangeAction(DropdownCardDto.Action.REFRESH) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return vehicle model dropdown row with default selected option`() {
        runUnitTest(robot) {
            Given { stubSelectedVehicleModel(null) }
            Given { stubSelectedVehicleMake("audi") }
            When { createVehicleModelDropdownRow() }
            Then { checkRowLayoutType(RowLayoutType.DROPDOWN_ROW) }
            Then { checkDropdownCardDtoTitle(CategoryLandingScreenUiConfiguration.MODEL_DROPDOWN_TITLE_TEXT) }
            Then { checkDropdownCardDtoParam(ApiQueryParams.VEHICLE_MODEL) }
            Then { checkDropdownCardDtoSelectedOption(FilterAttributeDefault.ANY_MODEL.key) }
            Then { checkDropdownCardDtoOptionsSize(4) }//ANY is there too
            Then { checkDropdownCardDtoChangeAction(null) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return vehicle model dropdown row with the selected option`() {
        runUnitTest(robot) {
            Given { stubSelectedVehicleModel("A5") }
            Given { stubSelectedVehicleMake("audi") }
            When { createVehicleModelDropdownRow() }
            Then { checkRowLayoutType(RowLayoutType.DROPDOWN_ROW) }
            Then { checkDropdownCardDtoTitle(CategoryLandingScreenUiConfiguration.MODEL_DROPDOWN_TITLE_TEXT) }
            Then { checkDropdownCardDtoParam(ApiQueryParams.VEHICLE_MODEL) }
            Then { checkDropdownCardDtoSelectedOption("A5") }
            Then { checkDropdownCardDtoOptionsSize(4) }//ANY is there too
            Then { checkDropdownCardDtoChangeAction(null) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return price title row`() {
        runUnitTest(robot) {
            When { createPriceTitleRow() }
            Then { checkRowLayoutType(RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoText(CategoryLandingScreenUiConfiguration.PRICE_DROPDOWN_TITLE_TEXT) }
            Then { checkTitleCardDtoSize(TitleCardDto.Size.XX_SMALL) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return price double dropdown row with default selected options`() {
        runUnitTest(robot) {
            Given { stubMinPrice(ANY_KEY) }
            Given { stubMaxPrice(ANY_KEY) }
            When { createPriceDoubleDropdownRow() }
            Then { checkRowLayoutType(RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkDoubleDropdownCardDtoTitle1(FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleDropdownCardDtoParam1(ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption1(ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions1Size(49) }
            Then { checkDoubleDropdownCardDtoTitle2(FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleDropdownCardDtoParam2(ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption2(ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions2Size(49) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return price double dropdown row with the selected options`() {
        runUnitTest(robot) {
            Given { stubMinPrice("1000") }
            Given { stubMaxPrice("10000") }
            When { createPriceDoubleDropdownRow() }
            Then { checkRowLayoutType(RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkDoubleDropdownCardDtoTitle1(FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleDropdownCardDtoParam1(ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption1("1000") }
            Then { checkDoubleDropdownCardDtoOptions1Size(49) }
            Then { checkDoubleDropdownCardDtoTitle2(FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleDropdownCardDtoParam2(ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption2("10000") }
            Then { checkDoubleDropdownCardDtoOptions2Size(49) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return search button row`() {
        runUnitTest(robot) {
            Given { stubAnalyticsEvent(DataFactory.SOME_ANALYTICS_EVENT_NAME) }
            When { createSearchButtonRow() }
            Then { checkRowLayoutType(RowLayoutType.BUTTON_ROW) }
            Then { checkButtonCardDtoText(CategoryLandingScreenUiConfiguration.SEARCH_TEXT) }
            Then { checkButtonCardDtoSize(ButtonCardDto.Size.LARGE) }
            Then { checkButtonCardDtoDestinationRoute("${DestinationRoute.SRP.screenName}?${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}") }
            Then { checkButtonCardDtoDestinationAnalyticsEvent(AnalyticsEventData(eventName = DataFactory.SOME_ANALYTICS_EVENT_NAME)) }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @Test
    fun `should return vehicle body types row`() {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubDistance(DataFactory.SOME_DISTANCE) }
            When { createVehicleBodyTypeRow() }
            Then { checkRowLayoutType(RowLayoutType.VEHICLE_BODY_TYPE_ROW) }
            Then { checkVehicleBodyTypeCardDtoTitle(CategoryLandingScreenUiConfiguration.CARS_BY_TYPE_TEXT) }
            Then { checkVehicleBodyTypeCardDtoBodyTypeSize(8) }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(0, "Hatchback") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(1, "Saloon") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(2, "Coupe (2 doors)") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(3, "SUV") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(4, "Pick up") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(5, "Convertible") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(6, "Van") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(7, "Estate") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(0, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=hatchback&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(1, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=saloon&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(2, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=coupe&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(3, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=light_4x4_utility&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(4, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=pick_up&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(5, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=convertible&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(6, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=window_van&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
            Then { checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(7, "${DestinationRoute.SRP.screenName}?${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.CARS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${ApiQueryParams.LOCATION_TYPE}=${LocationType.LOCATION}&${ApiQueryParams.VEHICLE_BODY_TYPE}=estate&${ApiQueryParams.SCREEN_TYPE}=${ScreenType.BRP}&${ApiQueryParams.DISTANCE}=${DataFactory.SOME_DISTANCE}") }
        }
    }

    @Test
    fun `should return vehicle body types row with analytics event data`() {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubDistance(DataFactory.SOME_DISTANCE) }
            When {
                createVehicleBodyTypeRow(
                    mapOf(
                        AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                    )
                )
            }
            Then { checkRowLayoutType(RowLayoutType.VEHICLE_BODY_TYPE_ROW) }
            Then { checkVehicleBodyTypeCardDtoTitle(CategoryLandingScreenUiConfiguration.CARS_BY_TYPE_TEXT) }
            Then { checkVehicleBodyTypeCardDtoBodyTypeSize(8) }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 0,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "hatchback",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 1,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "saloon",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 2,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "coupe",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 3,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "light_4x4_utility",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 4,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "pick_up",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 5,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "convertible",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 6,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "window_van",
                        )
                    ),
                )
            }
            Then {
                checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
                    position = 7,
                    expected = AnalyticsEventData(
                        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                        parameters = mapOf(
                            AnalyticsParams.Search.LOCATION to DataFactory.SOME_AD_LOCATION_ID,
                            AnalyticsParams.Search.BODY_TYPE to "estate",
                        )
                    ),
                )
            }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualRowLayoutResult: RowLayout<UiItem>

        private var selectedVehicleMake: String? = null
        private var selectedVehicleModel: String? = null
        private var minPrice: String? = null
        private var maxPrice: String? = null
        private var locationId: String? = null
        private lateinit var distance: String
        private lateinit var locationType: LocationType
        private lateinit var analyticsEventData: AnalyticsEventData

        private val testSubject = CarsCategoryLandingUIProvider(
            TitleFactory(),
            ImageFactory(),
            DropdownFactory(),
            ButtonsFactory(),
            CategoryLandingSimilarItemsMapper(
                CarsCategoryLandingAnalyticsProvider(
                    CommonAnalyticsProviderFactory.createInstance(),
                    RawLocationFactory.createRawLocationFetcher(),
                )
            ),
        )

        override fun setup() {
            mockkObject(ApplicationEnvironment)
        }

        override fun tearsDown() {
            unmockkObject(ApplicationEnvironment)
        }

        fun stubImageAssetUrl(url: String?) {
            every { ApplicationEnvironment.getProperty(IMAGE_ASSET_URL_ENV_VARIABLE) } returns url
        }

        fun stubSelectedVehicleMake(make: String?) {
            selectedVehicleMake = make
        }

        fun stubSelectedVehicleModel(model: String?) {
            selectedVehicleModel = model
        }

        fun stubMinPrice(price: String?) {
            minPrice = price
        }

        fun stubMaxPrice(price: String?) {
            maxPrice = price
        }

        fun stubLocationId(locationId: String?) {
            this.locationId = locationId
        }

        fun stubLocationType(locationType: LocationType) {
            this.locationType = locationType
        }

        fun stubAnalyticsEvent(eventName: String) {
            analyticsEventData = AnalyticsEventFactory.createAnalyticsEvent(
                eventName = eventName
            )
        }

        fun stubDistance(distance: String) {
            this.distance = distance
        }

        fun createImageRow() {
            actualRowLayoutResult = testSubject.createImageRow()
        }

        fun createSearchSectionTitleRow() {
            actualRowLayoutResult = testSubject.createSearchSectionTitleRow()
        }

        fun createVehicleMakeDropdownRow() {
            actualRowLayoutResult = testSubject.createVehicleMakeDropdownRow(selectedVehicleMake, FiltersCategoryAttributesFactory.vehicleMakeAttribute)
        }

        fun createVehicleModelDropdownRow() {
            actualRowLayoutResult = testSubject.createVehicleModelDropdownRow(selectedVehicleModel, selectedVehicleMake, FiltersCategoryAttributesFactory.vehicleModelAttribute)
        }

        fun createPriceTitleRow() {
            actualRowLayoutResult = testSubject.createPriceTitleRow()
        }

        fun createPriceDoubleDropdownRow() {
            actualRowLayoutResult = testSubject.createPriceDoubleDropdownRow(minPrice!!, maxPrice!!)
        }

        fun createSearchButtonRow() {
            actualRowLayoutResult = testSubject.createSearchButtonRow(analyticsEventData)
        }

        fun createVehicleBodyTypeRow(commonAnalyticsParams: Map<String, String> = emptyMap()) {
            actualRowLayoutResult = testSubject.createVehicleBodyTypeRow(
                vehicleBodyTypeAttribute = FiltersCategoryAttributesFactory.vehicleBodyTypeAttribute,
                locationId = locationId,
                locationType = locationType,
                commonAnalyticsParams = commonAnalyticsParams,
                distance = distance,
            )
        }

        fun checkRowLayoutBottomDivider(expected: Boolean?) {
            assertEquals(expected, actualRowLayoutResult.bottomDivider)
        }

        fun checkRowLayoutType(expected: RowLayoutType) {
            assertEquals(expected, actualRowLayoutResult.type)
        }

        fun checkImageCardDtoImagesSize(expected: Int) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as ImageCardDto).images?.size)
        }

        fun checkTitleCardDtoText(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as TitleCardDto).text)
        }

        fun checkTitleCardDtoSize(expected: TitleCardDto.Size) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as TitleCardDto).size)
        }

        fun checkDropdownCardDtoTitle(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DropdownCardDto).title)
        }

        fun checkDropdownCardDtoParam(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DropdownCardDto).param)
        }

        fun checkDropdownCardDtoSelectedOption(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DropdownCardDto).selectedOption)
        }

        fun checkDropdownCardDtoOptionsSize(expected: Int) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DropdownCardDto).options.size)
        }

        fun checkDropdownCardDtoChangeAction(expected: DropdownCardDto.Action?) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DropdownCardDto).changeAction)
        }

        fun checkDoubleDropdownCardDtoTitle1(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).title1)
        }

        fun checkDoubleDropdownCardDtoParam1(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).param1)
        }

        fun checkDoubleDropdownCardDtoSelectedOption1(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).selectedOption1)
        }

        fun checkDoubleDropdownCardDtoOptions1Size(expected: Int) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).options1.size)
        }

        fun checkDoubleDropdownCardDtoTitle2(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).title2)
        }

        fun checkDoubleDropdownCardDtoParam2(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).param2)
        }

        fun checkDoubleDropdownCardDtoSelectedOption2(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).selectedOption2)
        }

        fun checkDoubleDropdownCardDtoOptions2Size(expected: Int) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as DoubleDropdownCardDto).options2.size)
        }

        fun checkButtonCardDtoText(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as ButtonCardDto.Primary).text)
        }

        fun checkButtonCardDtoSize(expected: ButtonCardDto.Size) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as ButtonCardDto.Primary).size)
        }

        fun checkButtonCardDtoDestinationRoute(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as ButtonCardDto.Primary).destination.route)
        }

        fun checkButtonCardDtoDestinationAnalyticsEvent(expected: AnalyticsEventData) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as ButtonCardDto.Primary).destination.analyticsEventData)
        }

        fun checkVehicleBodyTypeCardDtoTitle(expected: String) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as VehicleBodyTypeCardDto).title)
        }

        fun checkVehicleBodyTypeCardDtoBodyTypeSize(expected: Int) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as VehicleBodyTypeCardDto).bodyTypes.size)
        }

        fun checkVehicleBodyTypeCardDtoBodyTypeTextAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as VehicleBodyTypeCardDto).bodyTypes[position].text)
        }

        fun checkVehicleBodyTypeCardDtoBodyTypeDestinationRouteAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as VehicleBodyTypeCardDto).bodyTypes[position].destination.route)
        }

        fun checkVehicleBodyTypeCardDtoBodyTypeDestinationAnalyticsEventAtPosition(
            position: Int,
            expected: AnalyticsEventData
        ) {
            assertEquals(expected, (actualRowLayoutResult.data[0] as VehicleBodyTypeCardDto).bodyTypes[position].destination.analyticsEventData)
        }
    }
}