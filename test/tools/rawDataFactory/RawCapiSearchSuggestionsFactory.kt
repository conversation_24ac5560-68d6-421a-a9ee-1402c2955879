package tools.rawDataFactory

import api.capi.models.RawCapiSearchSingleSuggestion
import api.capi.models.RawCapiSearchSuggestionCategory
import api.capi.models.RawCapiSearchSuggestions
import tools.DataFactory

object RawCapiSearchSuggestionsFactory {

    fun createRawSearchSuggestions(
        suggestions: List<RawCapiSearchSingleSuggestion> = listOf(
            createRawSearchSuggestion(
                DataFactory.SOME_AD_KEYWORD, null
            )
        )
    ): RawCapiSearchSuggestions =
        RawCapiSearchSuggestions().apply { rawSearchSuggestionList = suggestions }

    fun createRawSearchSuggestion(
        keyword: String,
        category: RawCapiSearchSuggestionCategory?
    ): RawCapiSearchSingleSuggestion =
        RawCapiSearchSingleSuggestion().apply {
            this.keyword = keyword
            this.categories = category?.let { arrayListOf(it) } ?: arrayListOf<RawCapiSearchSuggestionCategory>()
        }

    fun createRawSearchSuggestionCategory(id: String, name: String): RawCapiSearchSuggestionCategory =
        RawCapiSearchSuggestionCategory().apply {
            this.categoryId = id
            this.localizedName = name
        }
}
