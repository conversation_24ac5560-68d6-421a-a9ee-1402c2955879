package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("HPI_CHECK_CARD")
data class HPICardDto(
    @SerialName("collapseItemCount")
    val collapseItemCount: Int,
    @SerialName("checks")
    val checks: List<Check>,
    @SerialName("descriptionSummary")
    val descriptionSummary: String?,
    @SerialName("question")
    val question: String?,
    @SerialName("answer")
    val answer: String?,
) : UiItem {

    @Serializable
    data class Check(
        @SerialName("label")
        val label: String,
        @SerialName("value")
        val value: String,
        @SerialName("status")
        val status: Status,
        @SerialName("description")
        val description: String?,
    ) {
        enum class Status {
            PASS,
            ADVISORY,
            ;

            fun toDisplayText(): String {
                return when (this) {
                    PASS -> "Pass"
                    ADVISORY -> "Advisory"
                }
            }

            /**
             * When the status is PASS we shouldn't return the check description
             * When the status is ADVISORY we should return the check description (if the check has description)
             */
            fun toDisplayDescription(description: String?): String? {
                return when (this) {
                    PASS -> null
                    ADVISORY -> description
                }
            }
        }
    }
}
