package com.gumtree.mobile.features.reportListing

import com.gumtree.mobile.common.DispatcherProvider
import kotlinx.coroutines.withContext

fun interface ReportListingRepository {
    /**
     * Report a listing
     * @param email - if user is logged in, email is taken from the headers
     * @param request - the ReportListingRequest object contains ad id, reason and comment
     */
    suspend fun createReportedListing(
        email: String?,
        request: ReportListingRequest,
    )
}

class DefaultReportListingRepository(
    private val reportListingService: ReportListingService,
    private val bodyCreator: ReportListingBodyCreator,
    private val dispatcherProvider: DispatcherProvider,
) : ReportListingRepository {
    override suspend fun createReportedListing(
        email: String?,
        request: ReportListingRequest,
    ) {
        val body = bodyCreator.create(request, email)
        withContext(dispatcherProvider.io) { reportListingService.reportListing(request.adId, body) }
    }
}
