package features.myGumtree.v2

import api.capi.models.RawAdStatList
import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.common.analytics.ANALYTICS_CLICK_LISTING_EVENT_NAME
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeAdsMapper
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeAnalyticsProvider
import com.gumtree.mobile.features.myGumtree.v2.extensions.MyGumtreeButton
import com.gumtree.mobile.features.myGumtree.v2.extensions.getMyGumtreeListingOptions
import com.gumtree.mobile.features.myGumtree.v2.extensions.getMyGumtreeStatus
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import tools.layoutsDataFactory.MyGumtreeDataFactory
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.DataFactory.SOME_AD_ID
import tools.DataFactory.SOME_AD_IMAGE_URL
import tools.DataFactory.SOME_AD_LOCATION
import tools.DataFactory.SOME_AD_PRICE
import tools.DataFactory.SOME_AD_TITLE
import tools.DataFactory.SOME_AD_VIEWS
import tools.DataFactory.SOME_ANALYTICS_EVENT_NAME
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.rawDataFactory.RawAdStatListFactory.createAdStat
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

@ParallelTest
class MyGumtreeAdsMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should map RawCapiAdList to MyGumtreeListingCard`() {
        runUnitTest(robot) {
            Given { stubRawAdAndViews(SOME_AD_ID, SOME_AD_TITLE, SOME_AD_IMAGE_URL, SOME_AD_PRICE, SOME_AD_VIEWS) }
            Given { stubDeleteAnalyticsEvent() }
            Given { stubImmediateRepostEvent() }
            Given { stubClickListingEvent() }
            When { map() }
            Then {
                val expiredAd = RawCapiAdsFactory.createRawCapiAd(status = AdStatus.EXPIRED)
                checkMappedValue(
                    MyGumtreeDataFactory.createMyGumtreeListing(
                        adId = SOME_AD_ID,
                        images = DataFactory.squareThumbnails(),
                        title = SOME_AD_TITLE,
                        price = "£$SOME_AD_PRICE",
                        views = "$SOME_AD_VIEWS ad views",
                        destination = DestinationRoute.VIP.build(
                            queryParams = mapOf(ApiQueryParams.AD_ID to SOME_AD_ID),
                            analyticsEvent = AnalyticsEventData(eventName = ANALYTICS_CLICK_LISTING_EVENT_NAME, emptyMap()),
                        ),
                        options = expiredAd.getMyGumtreeListingOptions(
                            mapOf(
                                SOME_AD_ID to listOf(createAdStat(id = SOME_AD_ID, value = SOME_AD_VIEWS)),
                            ),
                            images = DataFactory.squareThumbnails(),
                            SOME_AD_PRICE,
                            SOME_AD_LOCATION,
                            AnalyticsEventData(SOME_ANALYTICS_EVENT_NAME),
                        ),
                        status = expiredAd.getMyGumtreeStatus(),
//                        button = MyGumtreeButton.REPOST_FREE.toButtonCard(
//                            SOME_AD_ID,
//                            analyticsEventData = AnalyticsEventData(SOME_ANALYTICS_EVENT_NAME),
//                        ),
                        //TODO Revert the button verification once the immediate repost is back
                        button = MyGumtreeButton.REPOST.toButtonCard(
                            SOME_AD_ID,
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should map RawCapiAdList to MyGumtreeAd without destination for Delayed status`() {
        runUnitTest(robot) {
            Given { stubRawAdAndViews(SOME_AD_ID, SOME_AD_TITLE, SOME_AD_IMAGE_URL, SOME_AD_PRICE, SOME_AD_VIEWS, AdStatus.DELAYED) }
            When { map() }
            Then {
                checkDestinationIsNull()
            }
        }
    }

    @Test
    fun `should map RawCapiAdList to MyGumtreeAd without destination for Pending status`() {
        runUnitTest(robot) {
            Given { stubRawAdAndViews(SOME_AD_ID, SOME_AD_TITLE, SOME_AD_IMAGE_URL, SOME_AD_PRICE, SOME_AD_VIEWS, AdStatus.PENDING) }
            When { map() }
            Then {
                checkDestinationIsNull()
            }
        }
    }

    @Test
    fun `should map RawCapiAdList to MyGumtreeAd without destination for Removed status`() {
        runUnitTest(robot) {
            Given { stubRawAdAndViews(SOME_AD_ID, SOME_AD_TITLE, SOME_AD_IMAGE_URL, SOME_AD_PRICE, SOME_AD_VIEWS, AdStatus.DELETED_CS) }
            When { map() }
            Then {
                checkDestinationIsNull()
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualRowLayoutsResult: List<RowLayout<UiItem>>

        private val myGumtreeAnalyticsProvider: MyGumtreeAnalyticsProvider = mockk(relaxed = true)
        private var rawCapiAdList = RawCapiAdList()
        private var rawPapiAdStatList = RawAdStatList()

        private lateinit var testSubject: MyGumtreeAdsMapper

        override fun setup() {
            testSubject = MyGumtreeAdsMapper(myGumtreeAnalyticsProvider)
        }

        fun stubRawAdAndViews(id: String, title: String, imageUrl: String, price: String, views: String, status: AdStatus = AdStatus.EXPIRED) {
            rawCapiAdList.rawAds = listOf(
                RawCapiAdsFactory.createRawCapiAd(
                    adId = id,
                    title = title,
                    imageUrl = imageUrl,
                    price = price,
                    status = status,
                )
            )
            rawPapiAdStatList.stats = listOf(createAdStat(id, value = views))
        }

        fun stubImmediateRepostEvent() {
            every { myGumtreeAnalyticsProvider.getAdPostedAnalyticsEvent(any(), any(), any(), any()) } returns AnalyticsEventData(SOME_ANALYTICS_EVENT_NAME)
        }

        fun stubClickListingEvent() {
            every { myGumtreeAnalyticsProvider.getClickListingEvent(any(), any<RawCapiAd>()) } returns AnalyticsEventData(eventName = ANALYTICS_CLICK_LISTING_EVENT_NAME, emptyMap())
        }

        fun stubDeleteAnalyticsEvent() {
            every { myGumtreeAnalyticsProvider.getDeleteAdAnalyticsEvent(any()) } returns AnalyticsEventData(SOME_ANALYTICS_EVENT_NAME)
        }

        fun map() {
            actualRowLayoutsResult = testSubject.map(rawCapiAdList, rawPapiAdStatList, ClientSemantics(platform = ClientPlatform.ANDROID))
        }

        fun checkMappedValue(expectedUiItem: MyGumtreeListingCardDto) {
            val expected = RowLayout(type = RowLayoutType.MY_GUMTREE_LISTING_ROW, data = listOf(expectedUiItem))
            assertEquals(expected, actualRowLayoutsResult.first())
        }

        fun checkDestinationIsNull() {
            val listingCard = actualRowLayoutsResult.first().data.first() as? MyGumtreeListingCardDto
            assertNull(listingCard?.destination)
        }
    }
}
