package utils.extensions.data

import api.capi.models.RawCapiAdSlot
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.features.screens.layoutsData.PartnershipAdvertDto
import com.gumtree.mobile.features.vip.ANALYTICS_BANNER_TYPE_COMPACT
import com.gumtree.mobile.features.vip.ANALYTICS_PARTNERSHIP_AD_CLICK_EVENT_NAME
import com.gumtree.mobile.features.vip.VipPartnershipAnalyticsProvider
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.utils.extensions.data.APP_VERSION_QUERY_PARAM
import com.gumtree.mobile.utils.extensions.data.toPartnershipAdvertCard
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import kotlin.test.assertEquals
import kotlin.test.assertNull

class RawCapiAdSlotExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should return partnership advert card with expected data and append app version query param in the url`() {
        runUnitTest(robot) {
            Given {
                stubAnalyticsEvent(DataFactory.anyAnalyticsEventWithParams())
                stubRawAdSlot(
                    RawCapiAdSlot().apply {
                        vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                            id = DataFactory.ANOTHER_PARTNERSHIP_ID
                            labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                            labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                            targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                        }
                    }
                )
            }
            When { toPartnershipAdvertCard(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkPartnershipAdvertIconUrl(DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL) }
            Then { checkPartnershipAdvertText(DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT) }
            Then { checkPartnershipAdvertWebUrl(DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL+"&$APP_VERSION_QUERY_PARAM=${DataFactory.SOME_APP_VERSION}") }
        }
    }

    @Test
    fun `should return partnership advert card with expected data and append empty app version query param in the url`() {
        runUnitTest(robot) {
            Given {
                stubAnalyticsEvent(DataFactory.anyAnalyticsEventWithParams())
                stubRawAdSlot(
                    RawCapiAdSlot().apply {
                        vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                            id = DataFactory.ANOTHER_PARTNERSHIP_ID
                            labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                            labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                            targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                        }
                    }
                )
            }
            When { toPartnershipAdvertCard(ClientSemantics(appVersion = EMPTY_STRING, platform = ClientPlatform.ANDROID)) }
            Then { checkPartnershipAdvertIconUrl(DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL) }
            Then { checkPartnershipAdvertText(DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT) }
            Then { checkPartnershipAdvertWebUrl(DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL+"&$APP_VERSION_QUERY_PARAM=$EMPTY_STRING") }
        }
    }

    @Test
    fun `should NOT return partnership advert card if label icon is NULL`() {
        runUnitTest(robot) {
            Given {
                stubAnalyticsEvent(DataFactory.anyAnalyticsEventWithParams())
                stubRawAdSlot(
                    RawCapiAdSlot().apply {
                        vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                            id = DataFactory.ANOTHER_PARTNERSHIP_ID
                            labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                            targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                        }
                    }
                )
            }
            When { toPartnershipAdvertCard(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkPartnershipAdvertCardIsNull() }
        }
    }

    @Test
    fun `should NOT return partnership advert card if label text is NULL`() {
        runUnitTest(robot) {
            Given {
                stubAnalyticsEvent(DataFactory.anyAnalyticsEventWithParams())
                stubRawAdSlot(
                    RawCapiAdSlot().apply {
                        vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                            id = DataFactory.SOME_PARTNERSHIP_ID
                            labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                            targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                        }
                    }
                )
            }
            When { toPartnershipAdvertCard(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkPartnershipAdvertCardIsNull() }
        }
    }

    @Test
    fun `should NOT return partnership advert card if target url is NULL`() {
        runUnitTest(robot) {
            Given {
                stubAnalyticsEvent(DataFactory.anyAnalyticsEventWithParams())
                stubRawAdSlot(
                    RawCapiAdSlot().apply {
                        vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                            id = DataFactory.SOME_PARTNERSHIP_ID
                            labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                            labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                        }
                    }
                )
            }
            When { toPartnershipAdvertCard(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkPartnershipAdvertCardIsNull() }
        }
    }

    @Test
    fun `should return partnership advert card with expected analytics data`() {
        val analyticsEvent = DataFactory.anyAnalyticsEventWithParams()
        runUnitTest(robot) {
            Given {
                stubAnalyticsEvent(analyticsEvent)
                stubRawAdSlot(
                    RawCapiAdSlot().apply {
                        vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                            id = DataFactory.SOME_PARTNERSHIP_ID
                            labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                            labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                            targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                        }
                    }
                )
            }
            When { toPartnershipAdvertCard(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkPartnershipAdvertAnalyticsData(analyticsEvent) }
        }
    }

    private class Robot: BaseRobot {
        private var actualPartnershipAdvertCardResult: PartnershipAdvertDto? = null
        private lateinit var analyticsEvent: AnalyticsEventData

        private lateinit var testSubject: RawCapiAdSlot

        fun stubRawAdSlot(adSlot: RawCapiAdSlot) {
            testSubject = adSlot
        }

        fun toPartnershipAdvertCard(clientSemantics: ClientSemantics) {
            actualPartnershipAdvertCardResult = testSubject.toPartnershipAdvertCard(
                analyticsEvent,
                clientSemantics,
            )
        }

        fun stubAnalyticsEvent(event: AnalyticsEventData) {
            analyticsEvent = event
        }

        fun checkPartnershipAdvertIconUrl(expected: String) {
            assertEquals(expected, actualPartnershipAdvertCardResult?.iconUrl)
        }

        fun checkPartnershipAdvertText(expected: String) {
            assertEquals(expected, actualPartnershipAdvertCardResult?.text)
        }

        fun checkPartnershipAdvertWebUrl(expected: String) {
            assertEquals(expected, actualPartnershipAdvertCardResult?.webUrl)
        }

        fun checkPartnershipAdvertAnalyticsData(expected: AnalyticsEventData?) {
            assertEquals(expected, actualPartnershipAdvertCardResult?.analyticsEventData)
        }

        fun checkPartnershipAdvertCardIsNull() {
            assertNull(actualPartnershipAdvertCardResult)
        }
    }
}