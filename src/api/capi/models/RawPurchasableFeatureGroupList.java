package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.NamespaceList;
import org.simpleframework.xml.Root;

import java.util.List;

@Root(name = "feature-groups", strict = false)
@NamespaceList({
        @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT),
        @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
})
public class RawPurchasableFeatureGroupList {

    @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
    @ElementList(required = false, name = "feature-group", inline = true)
    public List<RawPurchasableFeatureGroup> supportedFeatureGroups;
}
