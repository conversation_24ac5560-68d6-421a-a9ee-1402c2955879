package com.gumtree.mobile.plugins

import com.gumtree.mobile.utils.annotations.NotCovered
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationStarted
import io.ktor.server.application.ApplicationStarting
import io.ktor.server.application.ApplicationStopped
import io.ktor.server.application.ApplicationStopping
import io.ktor.server.application.log
import org.koin.ktor.plugin.KoinApplicationStarted
import org.koin.ktor.plugin.KoinApplicationStopPreparing
import org.koin.ktor.plugin.KoinApplicationStopped

@NotCovered
fun Application.initializeEvents() {
    environment.monitor.subscribe(ApplicationStarting) { log.info("Application starting...") }
    environment.monitor.subscribe(ApplicationStarted) { log.info("Application started") }
    environment.monitor.subscribe(ApplicationStopping) { log.info("Application stopping...") }
    environment.monitor.subscribe(ApplicationStopped) { log.info("Application stopped") }
    environment.monitor.subscribe(KoinApplicationStarted) { log.info("Koin started") }
    environment.monitor.subscribe(KoinApplicationStopPreparing) { log.info("Koin stopping...") }
    environment.monitor.subscribe(KoinApplicationStopped) { log.info("<PERSON>in stopped.") }
}
