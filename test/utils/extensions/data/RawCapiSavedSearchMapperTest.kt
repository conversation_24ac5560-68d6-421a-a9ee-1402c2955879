package utils.extensions.data

import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.CapiAttrParams
import com.gumtree.mobile.api.capi.models.RawCapiSavedSearch
import com.gumtree.mobile.api.capi.models.SavedSearchStatus
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.locations.models.RawLocationSuggestions
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.savedSearches.SavedSearchDto
import com.gumtree.mobile.features.savedSearches.SavedSearchResponseDto
import com.gumtree.mobile.features.screens.layoutsData.HomeFeedSavedSearchCardDto
import com.gumtree.mobile.features.screens.layoutsData.SavedSearchCardDto
import com.gumtree.mobile.features.screens.layoutsData.SearchSuggestionSavedSearchCardDto
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.utils.LocationDefaults
import com.gumtree.mobile.utils.extensions.data.RawCapiSavedSearchMapper
import com.gumtree.mobile.utils.extensions.toPrice
import com.gumtree.mobile.utils.extensions.toVehicleSubtitle
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiSavedSearchesFactory
import tools.rawDataFactory.RawLocationFactory

class RawCapiSavedSearchMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should map ACTIVE raw saved search into saved search card with expected data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.ACTIVE,
                    DataFactory.SOME_AD_TITLE,
                    DataFactory.SAVED_SEARCH_LINK_WITH_THREE_FILTERS
                )
            }
            When { toSavedSearchCard(true) }
            Then { checkSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkSavedSearchCardSubtitle("") }
            Then { checkSavedSearchCardDestinationRoute("srp?locationId=268&distance=ZERO&categoryId=10201&locationType=LOCATION") }
            Then { checkSavedSearchCardDisplayAlert(true) }

            When { toSavedSearchCard(false) }
            Then { checkSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkSavedSearchCardSubtitle("") }
            Then { checkSavedSearchCardDestinationRoute("srp?locationId=268&distance=ZERO&categoryId=10201&locationType=LOCATION") }
            Then { checkSavedSearchCardDisplayAlert(false) }

            When { toSavedSearchCard(null) }
            Then { checkSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkSavedSearchCardSubtitle("") }
            Then { checkSavedSearchCardDestinationRoute("srp?locationId=268&distance=ZERO&categoryId=10201&locationType=LOCATION") }
            Then { checkSavedSearchCardDisplayAlert(null) }
        }
    }

    @Test
    fun `should map INACTIVE raw saved search into saved search card with expected data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.INACTIVE,
                    DataFactory.SOME_AD_TITLE,
                    DataFactory.SAVED_SEARCH_LINK_WITH_ONE_FILTER
                )
            }
            When { toSavedSearchCard(true) }
            Then { checkSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkSavedSearchCardSubtitle("") }
            Then { checkSavedSearchCardDestinationRoute("srp?locationId=268&locationType=LOCATION") }
            Then { checkSavedSearchCardDisplayAlert(true) }

            When { toSavedSearchCard(false) }
            Then { checkSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkSavedSearchCardSubtitle("") }
            Then { checkSavedSearchCardDestinationRoute("srp?locationId=268&locationType=LOCATION") }
            Then { checkSavedSearchCardDisplayAlert(false) }

            When { toSavedSearchCard(null) }
            Then { checkSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkSavedSearchCardSubtitle("") }
            Then { checkSavedSearchCardDestinationRoute("srp?locationId=268&locationType=LOCATION") }
            Then { checkSavedSearchCardDisplayAlert(null) }
        }
    }

    @Test
    fun `should map ACTIVE raw saved search into search suggestion saved search card with expected data`() = runTest {
        runUnitTest(robot) {
            Given { stubCategoryName("Property") }
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.ACTIVE,
                    DataFactory.ANOTHER_SAVED_SEARCH_DESCRIPTION,
                    DataFactory.SAVED_SEARCH_LINK_WITH_THREE_FILTERS
                )
            }
            When { toSearchSuggestionSavedSearchCard() }
            Then { checkSearchSuggestionSavedSearchCardTitle("Property") }
            Then { checkSearchSuggestionSavedSearchCardSubtitle(DataFactory.ANOTHER_SAVED_SEARCH_DESCRIPTION) }
            Then { checkSearchSuggestionSavedSearchCardParams("locationId=${DataFactory.SOME_AD_LOCATION_ID}&distance=${DataFactory.SOME_DISTANCE}&categoryId=${DataFactory.SOME_AD_CATEGORY_ID}&locationType=LOCATION") }
            Then { checkSearchSuggestionSavedSearchCardDestinationRoute("srp?locationId=${DataFactory.SOME_AD_LOCATION_ID}&distance=${DataFactory.SOME_DISTANCE}&categoryId=${DataFactory.SOME_AD_CATEGORY_ID}&locationType=LOCATION") }
        }
    }

    @Test
    fun `should map INACTIVE raw saved search into search suggestion saved search card with expected data`() = runTest {
        runUnitTest(robot) {
            Given { stubCategoryName("Jobs") }
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.INACTIVE,
                    DataFactory.SOME_SAVED_SEARCH_DESCRIPTION,
                    DataFactory.SAVED_SEARCH_LINK_WITH_THREE_FILTERS_AND_KEYWORD
                )
            }
            When { toSearchSuggestionSavedSearchCard() }
            Then { checkSearchSuggestionSavedSearchCardTitle(DataFactory.SOME_KEYWORD) }
            Then { checkSearchSuggestionSavedSearchCardSubtitle(DataFactory.SOME_SAVED_SEARCH_DESCRIPTION) }
            Then { checkSearchSuggestionSavedSearchCardParams("q=${DataFactory.SOME_KEYWORD}&locationId=${DataFactory.SOME_AD_LOCATION_ID}&distance=${DataFactory.SOME_DISTANCE}&categoryId=${DataFactory.SOME_AD_CATEGORY_ID}&locationType=LOCATION") }
            Then { checkSearchSuggestionSavedSearchCardDestinationRoute("srp?q=${DataFactory.SOME_KEYWORD}&locationId=${DataFactory.SOME_AD_LOCATION_ID}&distance=${DataFactory.SOME_DISTANCE}&categoryId=${DataFactory.SOME_AD_CATEGORY_ID}&locationType=LOCATION") }
        }
    }

    @Test
    fun `should map raw saved search into home feed saved search card with expected data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.ACTIVE,
                    DataFactory.SOME_AD_TITLE,
                    DataFactory.SAVED_SEARCH_LINK_WITH_THREE_FILTERS
                )
            }
            When { toHomeFeedSavedSearchCard() }
            Then { checkHomeFeedSavedSearchCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkHomeFeedSavedSearchCardDestination(DestinationDto("srp?locationId=268&distance=ZERO&categoryId=10201&locationType=LOCATION")) }
            Then { checkHomeFeedSavedSearchCardSubtitle("3 filters applied") }
        }
    }

    @Test
    fun `should map raw saved search into saved search response with expected data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.ACTIVE,
                    DataFactory.SOME_AD_TITLE
                )
            }
            When { toSavedSearchResponse() }
            Then { checkSavedSearchResponseId(DataFactory.SOME_SAVED_SEARCH_ID) }
        }
    }

    @Test
    fun `should NOT map raw saved search into home feed saved search card when search description is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.ACTIVE,
                    null,
                    DataFactory.SAVED_SEARCH_LINK_WITH_NO_FILTERS
                )
            }
            When { toHomeFeedSavedSearchCard() }
            Then { checkHomeFeedSavedSearchCardIsNull() }
        }
    }

    @Test
    fun `should return price text with min and max price when query contains both parameters`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchPriceQueryParams("100", "200") }
            When { toPriceTextForSavedSearchCard() }
            Then { checkSavedSearchPriceText("Price: £100 - £200") }
        }
    }

    @Test
    fun `should return price text with min price when query contains min price`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchPriceQueryParams("100", null) }
            When { toPriceTextForSavedSearchCard() }
            Then { checkSavedSearchPriceText("Price: min £100") }
        }
    }

    @Test
    fun `should return price text with max price when query contains max price`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchPriceQueryParams(null, "200") }
            When { toPriceTextForSavedSearchCard() }
            Then { checkSavedSearchPriceText("Price: max £200") }
        }
    }

    @Test
    fun `should return null price text when query contains no min or max price`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchPriceQueryParams(null, null) }
            When { toPriceTextForSavedSearchCard() }
            Then { checkSavedSearchPriceTextIsNull() }
        }
    }

    @Test
    fun `should return null price text when query contains empty min and max price`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchPriceQueryParams("", "") }
            When { toPriceTextForSavedSearchCard() }
            Then { checkSavedSearchPriceTextIsNull() }
        }
    }

    @Test
    fun `should return null subtitle text when query contains no params`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams(null, null, null, null) }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains vehicle make`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams("Audi", null, null, null) }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("Audi") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains vehicle make and model`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams("Audi", "A1", null, null) }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("Audi / Model: A1") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains vehicle transmission`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams(null, null, "Automatic", null) }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("Automatic") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains vehicle doors`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams(null, null, null, "2") }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("2 Doors") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains vehicle doors and transmission`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams(null, null, "Automatic", "2") }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("Automatic • 2 Doors") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains all vehicle params except for model`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams("Audi", null, "Automatic", "2") }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("Audi • Automatic • 2 Doors") }
        }
    }

    @Test
    fun `should return expected subtitle text when query contains all vehicle params`() = runTest {
        runUnitTest(robot) {
            Given { stubSavedSearchVehicleQueryParams("Audi", "A1", "Automatic", "2") }
            When { toSubtitleTextForSavedSearchCard() }
            Then { checkSavedSearchCardSubtitleText("Audi / Model: A1 • Automatic • 2 Doors") }
        }
    }

    @Test
    fun `should remove zipcode, latitude and longitude`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParam(
                    mapOf(
                        ApiQueryParams.ZIPCODE to DataFactory.SOME_ZIP_CODE,
                        ApiQueryParams.LATITUDE to DataFactory.SOME_LOCATION_LAT,
                        ApiQueryParams.LONGITUDE to DataFactory.SOME_LOCATION_LNG,
                    )
                )
            }
            When { addLocationIdAndTypeToQueryParams() }
            Then { checkQueryParams(mapOf(ApiQueryParams.LOCATION_TYPE to "LOCATION")) }
        }
    }

    @Test
    fun `should swap zipcode for location`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParam(mapOf(ApiQueryParams.ZIPCODE to DataFactory.SOME_ZIP_CODE)) }
            Given {
                stubLocationApiResponse(
                    RawLocationFactory.createRawLocation(
                        id = DataFactory.SOME_LOCATION_ID.toInt(),
                        type = RawLocation.Type.postcode
                    )
                )
            }
            When { addLocationIdAndTypeToQueryParams() }
            Then {
                checkQueryParams(
                    mapOf(
                        ApiQueryParams.LOCATION_TYPE to "POSTCODE",
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_LOCATION_ID
                    )
                )
                checkLocationSuggestionsCalled(DataFactory.SOME_ZIP_CODE)
            }
        }
    }

    @Test
    fun `should swap ALL_UK zipcode for ALL_UK raw location without calling the location suggestion API`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParam(mapOf(ApiQueryParams.ZIPCODE to "UK")) }
            When { addLocationIdAndTypeToQueryParams() }
            Then {
                checkQueryParams(
                    mapOf(
                        ApiQueryParams.LOCATION_TYPE to "LOCATION",
                        ApiQueryParams.LOCATION_ID to LocationDefaults.ALL_UK.id,
                    )
                )
                checkLocationSuggestionsNotCalled()
            }
        }
    }

    @Test
    fun `should map to SavedSearchDto`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawSavedSearch(
                    SavedSearchStatus.ACTIVE,
                    DataFactory.SOME_AD_TITLE,
                    DataFactory.SAVED_SEARCH_LINK_WITH_ONE_FILTER
                )
            }
            When { toSavedSearchDto() }
            Then {
                checkSavedSearchDto(
                    SavedSearchDto(
                        DataFactory.SOME_SAVED_SEARCH_ID,
                        mapOf(
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                            ApiQueryParams.LOCATION_TYPE to LocationType.LOCATION.name
                        )
                    )
                )
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualSavedSearchCardResult: SavedSearchCardDto
        private lateinit var actualSearchSuggestionSavedSearchCardResult: SearchSuggestionSavedSearchCardDto
        private lateinit var actualQueryParamsResult: QueryParams
        private lateinit var actualSavedSearchDto: SavedSearchDto
        private var actualHomeFeedSavedSearchCardResult: HomeFeedSavedSearchCardDto? = null
        private var queryParams: QueryParams = mutableMapOf()
        private var rawSavedSearchPriceQueryParams: Map<String, String?> = emptyMap()
        private var rawSavedSearchVehicleQueryParams: Map<String, String?> = emptyMap()
        private lateinit var rawCapiSavedSearch: RawCapiSavedSearch
        private var actualSavedSearchPriceTextResult: String? = null
        private var actualSavedSearchSubtitleTextResult: String? = null
        private var actualSavedSearchResponseResult: SavedSearchResponseDto? = null

        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val testSubject = RawCapiSavedSearchMapper(locationsApi)

        override fun setup() {
            mockkObject(CategoriesTreeCache)
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubRawSavedSearch(
            status: SavedSearchStatus,
            searchDescription: String?,
            searchLink: String? = null
        ) {
            rawCapiSavedSearch = RawCapiSavedSearchesFactory.createRawCapiSavedSearch(
                status,
                searchDescription,
                searchLink
            )
        }

        fun stubQueryParam(params: QueryParams) {
            this.queryParams = params
        }

        fun stubLocationApiResponse(location: RawLocation) {
            coEvery { locationsApi.getLocationSuggestions(any()) } returns RawLocationSuggestions(listOf(location))
        }

        fun stubSavedSearchPriceQueryParams(
            minPrice: String?,
            maxPrice: String?
        ) {
            rawSavedSearchPriceQueryParams = mapOf(
                CapiApiParams.MIN_PRICE to minPrice,
                CapiApiParams.MAX_PRICE to maxPrice
            )
        }

        fun stubSavedSearchVehicleQueryParams(
            make: String?,
            model: String?,
            transmission: String?,
            doors: String?
        ) {
            rawSavedSearchVehicleQueryParams = mapOf(
                "attr[${CapiAttrParams.VEHICLE_MAKE}]" to make,
                "attr[${CapiAttrParams.VEHICLE_MODEL}]" to model,
                "attr[${CapiAttrParams.VEHICLE_TRANSMISSION}]" to transmission,
                "attr[${CapiAttrParams.VEHICLE_DOORS}]" to doors
            )
        }

        fun stubCategoryName(categoryName: String) {
            every { CategoriesTreeCache.findItemByIdOrDefault(any()) } returns CategoryDto(
                DataFactory.anyString(),
                categoryName,
                DataFactory.anyString(),
                DataFactory.anyString(),
            )
        }

        suspend fun toSavedSearchDto() {
            actualSavedSearchDto = testSubject.toSavedSearchDto(rawCapiSavedSearch)
        }

        suspend fun toSavedSearchCard(displayAlert:Boolean?) {
            actualSavedSearchCardResult = testSubject.toSavedSearchCard(rawCapiSavedSearch, displayAlert)
        }

        suspend fun toSearchSuggestionSavedSearchCard() {
            actualSearchSuggestionSavedSearchCardResult =
                testSubject.toSearchSuggestionSavedSearchCard(rawCapiSavedSearch)
        }

        fun toSavedSearchResponse() {
            actualSavedSearchResponseResult = testSubject.toSavedSearchResponse(rawCapiSavedSearch)
        }

        suspend fun addLocationIdAndTypeToQueryParams() {
            actualQueryParamsResult = testSubject.addLocationIdAndTypeToQueryParams(queryParams)
        }

        suspend fun toHomeFeedSavedSearchCard() {
            actualHomeFeedSavedSearchCardResult = testSubject.toHomeFeedSavedSearchCard(rawCapiSavedSearch)
        }

        fun toPriceTextForSavedSearchCard() {
            actualSavedSearchPriceTextResult = rawSavedSearchPriceQueryParams.toPrice()
        }

        fun toSubtitleTextForSavedSearchCard() {
            actualSavedSearchSubtitleTextResult = rawSavedSearchVehicleQueryParams.toVehicleSubtitle()
        }

        fun checkSavedSearchResponseId(id: String) {
            assertEquals(id, actualSavedSearchResponseResult?.id)
        }

        fun checkSavedSearchCardTitle(expected: String) {
            assertEquals(expected, actualSavedSearchCardResult.title)
        }

        fun checkSavedSearchCardDestinationRoute(expected: String) {
            assertEquals(expected, actualSavedSearchCardResult.destination.route)
        }

        fun checkSavedSearchCardSubtitle(expected: String) {
            assertEquals(expected, actualSavedSearchCardResult.subtitle)
        }

        fun checkSavedSearchCardDisplayAlert(expected: Boolean?) {
            assertEquals(expected, actualSavedSearchCardResult.displayAlert)
        }

        fun checkSearchSuggestionSavedSearchCardTitle(expected: String) {
            assertEquals(expected, actualSearchSuggestionSavedSearchCardResult.title)
        }

        fun checkSearchSuggestionSavedSearchCardParams(expected: String) {
            assertEquals(expected, actualSearchSuggestionSavedSearchCardResult.params)
        }

        fun checkSearchSuggestionSavedSearchCardDestinationRoute(expected: String) {
            assertEquals(expected, actualSearchSuggestionSavedSearchCardResult.destination.route)
        }

        fun checkSearchSuggestionSavedSearchCardSubtitle(expected: String) {
            assertEquals(expected, actualSearchSuggestionSavedSearchCardResult.subtitle)
        }

        fun checkHomeFeedSavedSearchCardTitle(expected: String) {
            assertEquals(expected, actualHomeFeedSavedSearchCardResult?.title)
        }

        fun checkHomeFeedSavedSearchCardDestination(expected: DestinationDto) {
            assertEquals(expected, actualHomeFeedSavedSearchCardResult?.destination)
        }

        fun checkHomeFeedSavedSearchCardSubtitle(expected: String?) {
            assertEquals(expected, actualHomeFeedSavedSearchCardResult?.subtitle)
        }

        fun checkHomeFeedSavedSearchCardIsNull() {
            assertEquals(null, actualHomeFeedSavedSearchCardResult)
        }

        fun checkSavedSearchPriceText(expected: String?) {
            assertEquals(expected, actualSavedSearchPriceTextResult)
        }

        fun checkSavedSearchPriceTextIsNull() {
            assertEquals(null, actualSavedSearchPriceTextResult)
        }

        fun checkSavedSearchCardSubtitleText(expected: String?) {
            assertEquals(expected, actualSavedSearchSubtitleTextResult)
        }

        fun checkQueryParams(expectedParams: QueryParams) {
            assertEquals(expectedParams, actualQueryParamsResult)
        }

        fun checkLocationSuggestionsCalled(expectedKeyword: String) {
            coVerify { locationsApi.getLocationSuggestions(expectedKeyword) }
        }

        fun checkLocationSuggestionsNotCalled() {
            coVerify(exactly = 0) { locationsApi.getLocationSuggestions(any()) }
        }

        fun checkSavedSearchDto(expected: SavedSearchDto) {
            assertEquals(expected, actualSavedSearchDto)
        }
    }
}