package features.srp

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.RawCapiAddress
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.srp.ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME
import com.gumtree.mobile.features.srp.ANALYTICS_VIEW_SEARCH_RESULTS_EVENT_NAME
import com.gumtree.mobile.features.srp.SrpAnalyticsProvider
import com.gumtree.mobile.features.srp.SrpScreenUiConfiguration
import com.gumtree.mobile.features.srp.SrpSortHeaderFactory
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlin.test.assertNull
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawLocationFactory

class SrpAnalyticsProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return correct screen event on each page`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams() }
            Given { stubSearchResultCount() }
            Given { stubSearchTerm(DataFactory.SOME_SEARCH_TERM) }
            Given { stubOriginalSearchParameters() }
            Given { stubRawCapiAdList(RawCapiAdsFactory.createRawCapiAdList(1)) }

            When { getViewSearchResultsEvent("0") }
            Then { checkAnalyticsEventName(ANALYTICS_VIEW_SEARCH_RESULTS_EVENT_NAME) }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.PAGE_NUMBER, "0") }

            When { getViewSearchResultsEvent("1") }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.PAGE_NUMBER, "1") }

            When { getViewSearchResultsEvent("2") }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.PAGE_NUMBER, "2") }

            When { getViewSearchResultsEvent("3") }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.PAGE_NUMBER, "3") }

            When { getViewSearchResultsEvent("4") }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.PAGE_NUMBER, "4") }

            When { getViewSearchResultsEvent("5") }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.PAGE_NUMBER, "5") }
        }
    }

    @Test
    fun `should return correct screen params`() = runTest {
        runUnitTest(robot) {
            Given { stubRawLocation(DataFactory.SOME_LOCATION_NAME) }
            Given { stubCategoryIdName(DataFactory.SOME_AD_CATEGORY_NAME) }
            Given {
                stubQueryParams(
                    ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                    ApiQueryParams.LOCATION_ID to DataFactory.SOME_LOCATION_ID,
                    ApiQueryParams.DISTANCE to Distance.FIVE.toString(),
                    ApiQueryParams.MIN_PRICE to DataFactory.SOME_MIN_AD_PRICE,
                    ApiQueryParams.MAX_PRICE to DataFactory.SOME_MAX_AD_PRICE,
                    ApiQueryParams.PAGE to "0",
                )
            }
            Given { stubSearchResultCount(123) }
            When { getScreenParams() }
            Then { checkScreenParamValue(AnalyticsParams.Search.CATEGORY_ID, CategoryDefaults.CARS.id) }
            Then { checkScreenParamValue(AnalyticsParams.Search.LOCATION, DataFactory.SOME_LOCATION_NAME) }
            Then { checkScreenParamValue(AnalyticsParams.Search.LOCATION_DISTANCE, Distance.FIVE.toString()) }
            Then { checkScreenParamValue(AnalyticsParams.Search.MIN_PRICE, DataFactory.SOME_MIN_AD_PRICE) }
            Then { checkScreenParamValue(AnalyticsParams.Search.MAX_PRICE, DataFactory.SOME_MAX_AD_PRICE) }
            Then { checkScreenParamValue(AnalyticsParams.Search.CATEGORY_NAME, DataFactory.SOME_AD_CATEGORY_NAME) }
            Then { checkScreenParamValue(AnalyticsParams.Search.RESULTS, "123") }
            Then { checkScreenParamsSize(8) }
        }
    }

    @Test
    fun `should return correct sort search results event`() = runTest {
        runUnitTest(robot) {
            Given { stubCategoryIdName(DataFactory.SOME_AD_CATEGORY_NAME) }
            Given { stubRawLocation(DataFactory.SOME_LOCATION_NAME) }
            Given {
                stubQueryParams(
                    ApiQueryParams.LOCATION_ID to DataFactory.SOME_LOCATION_ID,
                    ApiQueryParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID,
                    ApiQueryParams.SORT_TYPE to SrpScreenUiConfiguration.PRICE_DESCENDING_KEY,
                )
            }
            When { getSortSearchResultsEvent() }
            Then { checkAnalyticsEventName(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.LOCATION_ID, DataFactory.SOME_LOCATION_ID) }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.LOCATION, DataFactory.SOME_LOCATION_NAME) }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.CATEGORY_ID, DataFactory.SOME_AD_CATEGORY_ID) }
            Then {
                checkAnalyticsEventParamValue(
                    AnalyticsParams.Search.LINK_TEXT,
                    SrpScreenUiConfiguration.PRICE_DESCENDING_VALUE.lowercase(),
                )
            }
            Then {
                checkAnalyticsEventParamValue(
                    AnalyticsParams.Search.CATEGORY_NAME,
                    DataFactory.SOME_AD_CATEGORY_NAME,
                )
            }
        }
    }

    @Test
    fun `should return correct sort search results event when locationId and sortType are null`() = runTest {
        runUnitTest(robot) {
            Given { stubCategoryIdName(DataFactory.SOME_AD_CATEGORY_NAME) }
            Given {
                stubQueryParams(
                    ApiQueryParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID,
                )
            }
            When { getSortSearchResultsEvent() }
            Then { checkAnalyticsEventName(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            Then { checkAnalyticsEventParamValue(AnalyticsParams.Search.CATEGORY_ID, DataFactory.SOME_AD_CATEGORY_ID) }
            Then {
                checkAnalyticsEventParamValue(
                    AnalyticsParams.Search.CATEGORY_NAME,
                    DataFactory.SOME_AD_CATEGORY_NAME,
                )
            }
            Then {
                checkAnalyticsEventParamValue(
                    AnalyticsParams.Search.LINK_TEXT,
                    SrpScreenUiConfiguration.DATE_DESCENDING_KEY.lowercase(),
                )
            }
            Then { checkAnalyticsEventParamValueDoesNotExist(AnalyticsParams.Search.LOCATION_ID) }
            Then { checkAnalyticsEventParamValueDoesNotExist(AnalyticsParams.Search.LOCATION) }
        }
    }

    @Test
    fun `should return correct sort search results event when unknown sortType is passed`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    ApiQueryParams.SORT_TYPE to DataFactory.anyString(),
                )
            }
            When { getSortSearchResultsEvent() }
            Then {
                checkAnalyticsEventParamValue(
                    AnalyticsParams.Search.LINK_TEXT,
                    SrpScreenUiConfiguration.DATE_DESCENDING_KEY.lowercase(),
                )
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "true, ${SrpScreenUiConfiguration.RELEVANCE_DESCENDING_KEY}",
        "false, ${SrpScreenUiConfiguration.DATE_DESCENDING_KEY}",
    )
    fun `should return sort type key`(hasRelevantSortType: Boolean, expected: String) = runTest {
        runUnitTest(robot) {
            When { getSortType(hasRelevantSortType) }
            Then { checkSortType(expected.lowercase()) }
        }
    }

    @Test
    fun `should return enhanced common listing params with SRP specific parameters`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAd() }
            When { getEnhancedCommonListingParams() }
            Then { checkEnhancedParamsContainCommonParams() }
            Then { checkEnhancedParamsContainSrpSpecificParams() }
            Then { checkEnhancedParamsSize() }
        }
    }

    @Test
    fun `should return enhanced common listing params when common params are empty`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAdWithEmptyCommonParams() }
            When { getEnhancedCommonListingParams() }
            Then { checkEnhancedParamsContainSrpSpecificParams() }
            Then { checkEnhancedParamsSize(21) } // 19个基础参数 + 2个SRP特定参数
        }
    }

    private class Robot : BaseRobot {
        private var actualEventDataResult: AnalyticsEventData? = null
        private var actualScreenParamsResult: Map<String, String>? = null
        private var actualEnhancedParamsResult: Map<String, String>? = null

        private val commonAnalyticsProviderFactory = CommonAnalyticsProviderFactory.createInstance()
        private var rawLocationFetcher = RawLocationFactory.createRawLocationFetcher()
        private val srpSortHeaderFactory = SrpSortHeaderFactory()

        private lateinit var queryParams: QueryParams
        private var searchResultCount: Int? = null
        private var searchTerm: String? = null
        private var actualSortTypeKeyResult: String? = null
        private lateinit var originalSearchParameters: QueryParams
        private lateinit var rawCapiAdList: RawCapiAdList
        private lateinit var rawCapiAd: RawCapiAd

        private val testSubject = SrpAnalyticsProvider(
            commonAnalyticsProviderFactory,
            rawLocationFetcher,
            srpSortHeaderFactory,
        )

        override fun setup() {
            mockkObject(CategoriesTreeCache)
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubQueryParams(vararg params: Pair<String, String>) {
            queryParams = params.toMap()
        }

        fun stubSearchResultCount(resultsNumber: Int? = null) {
            searchResultCount = resultsNumber
        }

        fun stubSearchTerm(searchTerm: String) {
            this.searchTerm = searchTerm
        }

        fun stubOriginalSearchParameters(vararg params: Pair<String, String>) {
            originalSearchParameters = params.toMap()
        }

        fun stubRawCapiAdList(rawCapiAdList: RawCapiAdList) {
            this.rawCapiAdList = rawCapiAdList
        }

        fun stubRawLocation(locationName: String) {
            rawLocationFetcher = RawLocationFactory.createRawLocationFetcher(name = locationName)
        }

        fun stubCategoryIdName(categoryIdName: String) {
            every { CategoriesTreeCache.getCategoryIdName(any()) } returns categoryIdName
        }

        fun stubRawCapiAd() {
            rawCapiAd = RawCapiAdsFactory.createRawCapiAd()
        }

        fun stubRawCapiAdWithEmptyCommonParams() {
            rawCapiAd = RawCapiAdsFactory.createRawCapiAd(
                adId = "",
                title = "",
                description = "",
                location = "",
                price = null,
                age = "",
                address = RawCapiAddress(),
                visibleOnMap = "",
                attributes = emptyList(),
                featuresActive = emptyList(),
                links = emptyList(),
                contactMethods = emptyList(),
                adSlots = emptyList(),
                categoryId = "",
                categoryName = "",
                priceFrequency = null,
                isMotorAd = false,
                skills = null,
                userId = null,
                userPublicId = "",
                accountId = "",
                vat = "",
                otherLocation = null,
                imageUrl = null
            )
        }

        suspend fun getViewSearchResultsEvent(page: String) {
            actualEventDataResult = testSubject.getViewSearchResultsEvent(
                page,
                queryParams,
                searchResultCount,
                searchTerm,
                originalSearchParameters,
                rawCapiAdList
            )
        }

        suspend fun getScreenParams() {
            actualScreenParamsResult = testSubject.getScreenParams(queryParams, searchResultCount, "0")
        }

        suspend fun getSortSearchResultsEvent() {
            actualEventDataResult = testSubject.getSortSearchResultsEvent(
                searchOptions = queryParams,
            )
        }

        fun getSortType(hasRelevantSort: Boolean) {
            actualSortTypeKeyResult = testSubject.getSortKey(hasRelevantSort)
        }

        fun getEnhancedCommonListingParams() {
            actualEnhancedParamsResult = testSubject.getEnhancedCommonListingParams("ad", rawCapiAd, "111")
        }

        fun checkSortType(expected: String) {
            assertEquals(expected, actualSortTypeKeyResult)
        }

        fun checkAnalyticsEventName(expected: String) {
            assertEquals(expected, actualEventDataResult?.eventName)
        }

        fun checkAnalyticsEventParamValue(key: String, expected: String) {
            assertEquals(expected, actualEventDataResult?.parameters?.get(key))
        }

        fun checkScreenParamValue(
            key: String,
            expected: String?,
        ) {
            assertEquals(expected, actualScreenParamsResult?.get(key))
        }

        fun checkScreenParamsSize(expected: Int) {
            assertEquals(expected, actualScreenParamsResult?.size)
        }

        fun checkAnalyticsEventParamValueDoesNotExist(key: String) {
            assertNull(actualEventDataResult?.parameters?.get(key))
        }

        fun checkEnhancedParamsContainCommonParams() {
            assertNotNull(actualEnhancedParamsResult)
            // 检查是否包含一些常见的参数
            assertTrue(actualEnhancedParamsResult!!.containsKey("ad_id"))
            assertTrue(actualEnhancedParamsResult!!.containsKey("ad_name"))
        }

        fun checkEnhancedParamsContainSrpSpecificParams() {
            assertNotNull(actualEnhancedParamsResult)
            assertEquals("srp", actualEnhancedParamsResult!!["page_referrer"])
        }

        fun checkEnhancedParamsSize() {
            assertNotNull(actualEnhancedParamsResult)
            assertTrue(actualEnhancedParamsResult!!.size > 3)
        }

        fun checkEnhancedParamsSize(expected: Int) {
            assertEquals(expected, actualEnhancedParamsResult?.size)
        }
    }
}
