package com.gumtree.mobile.di

internal const val CAPI_HEADERS_PROVIDER = "CapiHeadersProvider"
internal const val CAPI_API_CONFIGURATION = "CapiApiConfiguration"
internal const val CAPI_API_OK_HTTP_CLIENT = "CapiApiOkHttpClient"
const val CAPI_API_RETROFIT_CLIENT = "CapiApiRetrofitClient"

internal const val PAPI_HEADERS_PROVIDER = "PapiHeadersProvider"

internal const val LOCATIONS_API_BASE_URL = "LocationsApiBaseUrl"
internal const val LOCATIONS_API_OK_HTTP_CLIENT = "LocationsApiOkHttpClient"
const val LOCATIONS_API_RETROFIT_CLIENT = "LocationsApiRetrofitClient"

internal const val CATEGORIES_API_BASE_URL = "CategoriesApiBaseUrl"
internal const val CATEGORIES_API_OK_HTTP_CLIENT = "CategoriesApiOkHttpClient"
const val CATEGORIES_API_RETROFIT_CLIENT = "CategoriesApiRetrofitClient"

internal const val USER_SERVICE_API_BASE_URL = "UserServiceApiBaseUrl"
internal const val USER_SERVICE_API_OK_HTTP_CLIENT = "UserServiceApiOkHttpClient"
const val USER_SERVICE_API_RETROFIT_CLIENT = "UserServiceApiRetrofitClient"

internal const val CORE_CHAT_API_BASE_URL = "CoreChatApiBaseUrl"
internal const val CORE_CHAT_API_OK_HTTP_CLIENT = "CoreChatApiOkHttpClient"
const val CORE_CHAT_API_RETROFIT_CLIENT = "CoreChatApiRetrofitClient"

internal const val HOME_FEED_API_BASE_URL = "HomeFeedApiBaseUrl"
internal const val HOME_FEED_API_OK_HTTP_CLIENT = "HomeFeedApiOkHttpClient"
const val HOME_FEED_API_RETROFIT_CLIENT = "HomeFeedApiRetrofitClient"

internal const val CONVERSATIONS_API_BASE_URL = "ConversationsApiBaseUrl"
internal const val CONVERSATIONS_API_OK_HTTP_CLIENT = "ConversationsApiOkHttpClient"
const val CONVERSATIONS_API_RETROFIT_CLIENT = "ConversationsApiRetrofitClient"

internal const val USER_PROFILE_API_BASE_URL = "UserProfileApiBaseUrl"
internal const val USER_PROFILE_API_OK_HTTP_CLIENT = "UserProfileApiOkHttpClient"
const val USER_PROFILE_API_RETROFIT_CLIENT = "UserProfileApiRetrofitClient"

internal const val SIMILAR_ITEMS_API_BASE_URL = "SimilarItemsApiBaseUrl"
internal const val SIMILAR_ITEMS_API_OK_HTTP_CLIENT = "SimilarItemsApiOkHttpClient"
const val SIMILAR_ITEMS_RETROFIT_CLIENT = "SimilarItemsApiRetrofitClient"

internal const val BLOCK_USER_API_BASE_URL = "BlockUserApiBaseUrl"
internal const val BLOCK_USER_API_OK_HTTP_CLIENT = "BlockUserApiOkHttpClient"
const val BLOCK_USER_RETROFIT_CLIENT = "BlockUserApiRetrofitClient"

internal const val PHONE_NUMBER_API_BASE_URL = "PhoneNumberApiBaseUrl"
internal const val PHONE_NUMBER_API_OK_HTTP_CLIENT = "PhoneNumberApiOkHttpClient"
const val PHONE_NUMBER_RETROFIT_CLIENT = "PhoneNumberApiRetrofitClient"

internal const val CRM_API_BASE_URL = "CrmBaseApiUrl"
internal const val CRM_API_OK_HTTP_CLIENT = "CrmApiOkHttpClient"
const val CRM_RETROFIT_CLIENT = "CrmApiRetrofitClient"

internal const val PARTNERSHIP_ADS_API_BASE_URL = "PartnershipAdsApiBaseUrl"
internal const val PARTNERSHIP_ADS_API_OK_HTTP_CLIENT = "PartnershipAdsApiOkHttpClient"
const val PARTNERSHIP_ADS_RETROFIT_CLIENT = "PartnershipAdsApiRetrofitClient"

internal const val REVIEWS_API_BASE_URL = "ReviewsApiBaseUrl"
internal const val REVIEWS_API_OK_HTTP_CLIENT = "ReviewsApiOkHttpClient"
const val REVIEWS_API_RETROFIT_CLIENT = "ReviewsApiRetrofitClient"

internal const val FULL_ADS_SEARCH_API_BASE_URL = "FullAdsSearchApiBaseUrl"
internal const val FULL_ADS_SEARCH_API_OK_HTTP_CLIENT = "FullAdsSearchApiOkHttpClient"
const val FULL_ADS_SEARCH_API_RETROFIT_CLIENT = "FullAdsSearchApiRetrofitClient"

internal const val REPORT_CHAT_API_BASE_URL = "ReportChatApiBaseUrl"
internal const val REPORT_CHAT_API_OK_HTTP_CLIENT = "ReportChatApiOkHttpClient"
const val REPORT_CHAT_API_RETROFIT_CLIENT = "ReportChatApiRetrofitClient"

internal const val FAVOURITE_ADVERTS_API_BASE_URL = "FavouriteAdvertsApiBaseUrl"
internal const val FAVOURITE_ADVERTS_API_OK_HTTP_CLIENT = "FavouriteAdvertsApiOkHttpClient"
const val FAVOURITE_ADVERTS_API_RETROFIT_CLIENT = "FavouriteAdvertsApiRetrofitClient"

internal const val SAVED_SEARCH_API_BASE_URL = "SavedSearchApiBaseUrl"
internal const val SAVED_SEARCH_API_OK_HTTP_CLIENT = "SavedSearchApiOkHttpClient"
const val SAVED_SEARCH_API_RETROFIT_CLIENT = "SavedSearchApiRetrofitClient"

internal const val HTTP_SENSITIVE_BASIC_LOGGING = "HttpBasicSensitiveLogging"
internal const val XML_CONVERTER_FACTORY = "XMLConverterFactory"
internal const val GSON_CONVERTER_FACTORY = "GSONConverterFactory"
internal const val HTTP_STATUS_CONVERTER_FACTORY = "HttpStatusConverterFactory"
internal const val DEFAULT_GSON_SETUP = "DefaultGSONSetup"
internal const val DEFAULT_API_OK_HTTP_CONFIG = "DefaultApiOkHttpConfig"

internal const val HOME_FEED_PAGE_CALCULATOR = "HomeFeedPageCalculator"
internal const val FAVOURITES_PAGE_CALCULATOR = "FavouritesPageCalculator"
internal const val CONVERSATIONS_LIMITED_PAGE_CALCULATOR = "ConversationsLimitedPageCalculator"
internal const val CONVERSATIONS_INFINITE_PAGE_CALCULATOR = "ConversationsInfinitePageCalculator"
internal const val CHAT_PAGE_CALCULATOR = "ChatPageCalculator"
internal const val SAVED_SEARCHES_PAGE_CALCULATOR = "SavedSearchesPageCalculator"
internal const val SEARCH_RESULTS_PAGE_CALCULATOR = "SearchResultsPageCalculator"
internal const val SELLER_PROFILE_PAGE_CALCULATOR = "SellerProfilePageCalculator"
internal const val LIMITED_PAGE_CALCULATOR = "LimitedPageCalculator"

internal const val CONVERSATION_DATE_FORMATTER = "ConversationDateFormatter"
internal const val RAW_MESSAGE_DATE_FORMATTER = "RawMessageDateFormatter"
internal const val CHAT_DATE_FORMATTER = "ChatDateFormatter"
internal const val VIP_DATE_FORMATTER = "VipDateFormatter"
internal const val SELLER_PROFILE_DATE_FORMATTER = "SellerProfileDateFormatter"
internal const val SELLER_PROFILE_ACTIVE_STATUS_DATE_FORMATTER = "SellerProfileActiveStatusDateFormatter"
internal const val SELLER_PROFILE_REVIEW_DATE_FORMATTER = "SellerProfileReviewDateFormatter"

// todo - can be removed after migrating conversations from CAPI to core chat
internal const val CONVERSATIONS_REPOSITORY_CAPI = "ConversationsRepositoryCapi"
internal const val CONVERSATIONS_REPOSITORY_CORE_CHAT = "ConversationsRepositoryCoreChat"

internal const val PHONE_VERIFY_API_BASE_URL = "PhoneVerifyApiBaseUrl"
internal const val PHONE_VERIFY_API_OK_HTTP_CLIENT = "PhoneVerifyApiOkHttpClient"
const val PHONE_VERIFY_API_RETROFIT_CLIENT = "PhoneVerifyApiRetrofitClient"

internal const val SELLER_API_BASE_URL = "SellerApiBaseUrl"
internal const val SELLER_API_OK_HTTP_CLIENT = "SellerApiOkHttpClient"
const val SELLER_API_RETROFIT_CLIENT = "SellerApiRetrofitClient"
internal const val SELLER_HEADERS_PROVIDER = "SellerHeadersProvider"
