package features.screens.factories

import com.gumtree.mobile.features.screens.factories.IconFactory
import com.gumtree.mobile.features.screens.layoutsData.IconCardDto
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class IconFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return center icon card with expected data`() {
        runUnitTest(robot) {
            When { buildCenterTitleCard(IconCardDto.Resource.THUMBS_UP, IconCardDto.Size.MEDIUM) }
            Then { checkIconCardResource(IconCardDto.Resource.THUMBS_UP) }
            Then { checkIconCardSize(IconCardDto.Size.MEDIUM) }

            When { buildCenterTitleCard(IconCardDto.Resource.THUMBS_UP, IconCardDto.Size.LARGE) }
            Then { checkIconCardResource(IconCardDto.Resource.THUMBS_UP) }
            Then { checkIconCardSize(IconCardDto.Size.LARGE) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualIconCardResult: IconCardDto

        private val testSubject = IconFactory()

        fun buildCenterTitleCard(
            resource: IconCardDto.Resource,
            size: IconCardDto.Size,
        ) {
            actualIconCardResult = testSubject.buildCenterIconCard(resource, size) as IconCardDto
        }

        fun checkIconCardResource(expected: IconCardDto.Resource) {
            assertEquals(expected, actualIconCardResult.resource)
        }

        fun checkIconCardSize(expected: IconCardDto.Size) {
            assertEquals(expected, actualIconCardResult.size)
        }
    }
}