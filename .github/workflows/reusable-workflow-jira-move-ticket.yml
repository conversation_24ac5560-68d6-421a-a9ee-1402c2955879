# Re-usable workflow that:
#  1. Logs into Jira
#  2. Gets ticket id from input branch
#  3. Moves the corresponding ticket to column specified in input

on:
  workflow_call:
    inputs:
      branch_name: # Branch name must be in the format of <ticket id>/<description> e.g. GTNA-1234/adding-feature
        required: true
        type: string
      jira_column:
        type: string
    secrets:
      jira_base_url:
        required: true
      jira_user_email:
        required: true
      jira_api_toke:
        required: true

jobs:
  move-jira-ticket:
    runs-on: [gumtree-mobile]
    steps:
      - name: Log into Jira
        uses: atlassian/gajira-login@v3
        env:
          JIRA_BASE_URL: ${{ secrets.jira_base_url }}
          JIRA_USER_EMAIL: ${{ secrets.jira_user_email }}
          JIRA_API_TOKEN: ${{ secrets.jira_api_toke }}
      - name: Get Ticket Id from Branch
        env:
          BRANCH_NAME: ${{ inputs.branch_name }}
        run: echo "JIRA_TICKET_ID=$(echo "$BRANCH_NAME"| cut -d/ -f1)" >> $GITHUB_ENV
      - name: Move ticket to Code Review
        uses: atlassian/gajira-transition@v3
        with:
          issue: ${{ env.JIRA_TICKET_ID }}
          transition: ${{ inputs.jira_column }}
        continue-on-error: true
