package com.gumtree.mobile.api.crm

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val CRM_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "CRM_API_BASE_URL"
const val CRM_API_QA_PORT = 8088

class CrmApiBaseUrl : ApiBaseUrl() {

    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(CRM_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY)
            .toBaseUrl()
    } // see helm-chart/values.yaml
    override val localUrl: BaseUrl by lazy { LOCAL_HOST_BASE_URL.toBaseUrl() }
    override val localPort: Int = CRM_API_QA_PORT
}
