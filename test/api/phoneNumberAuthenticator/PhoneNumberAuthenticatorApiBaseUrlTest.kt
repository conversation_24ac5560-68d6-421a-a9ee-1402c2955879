package api.phoneNumberAuthenticator

import com.gumtree.mobile.LOCAL_ENV
import com.gumtree.mobile.api.common.HTTP_PROTOCOL
import com.gumtree.mobile.utils.ApplicationEnvironment
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import okhttp3.HttpUrl
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class PhoneNumberAuthenticatorApiBaseUrlTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return the expected scheme, host and port for QA environment`() {
        runUnitTest(robot) {
            Given { stubIsQaEnvironment(true) }
            Given { createApiBaseUrl() }
            When { getHttpUrl() }
            Then { checkUrlScheme(HTTP_PROTOCOL) }
            Then { checkUrlHost("localhost") }
            Then { checkUrlPort(PHONE_VERIFY_API_QA_PORT) }
        }
    }

    @Test
    fun `should return the expected scheme and host and default port for prod environment`() {
        runUnitTest(robot) {
            Given { stubProductionUrl("http://phone-verify-service.gumtree.com") }
            Given { stubIsQaEnvironment(false) }
            Given { createApiBaseUrl() }
            When { getHttpUrl() }
            Then { checkUrlScheme(HTTP_PROTOCOL) }
            Then { checkUrlHost("phone-verify-service.gumtree.com") }
            Then { checkUrlPort(80) } // 80 is the default port for HTTP scheme
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualHttpUrlResult: HttpUrl
        private lateinit var testSubject: PhoneNumberAuthenticatorApiBaseUrl

        override fun setup() {
            mockkObject(ApplicationEnvironment)
        }

        override fun tearsDown() {
            unmockkObject(ApplicationEnvironment)
        }

        fun stubProductionUrl(url: String) {
            every { ApplicationEnvironment.getProperty(PHONE_VERIFY_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY) } returns url
        }

        fun stubIsQaEnvironment(isQaEnvironment: Boolean) {
            val environmentString = if (isQaEnvironment) LOCAL_ENV else "production"
            every { ApplicationEnvironment.getProperty("ENVR") } returns environmentString
        }

        fun createApiBaseUrl() {
            testSubject = PhoneNumberAuthenticatorApiBaseUrl()
        }

        fun getHttpUrl() {
            actualHttpUrlResult = testSubject.get()
        }

        fun checkUrlScheme(expected: String) {
            Assertions.assertEquals(expected, actualHttpUrlResult.scheme)
        }

        fun checkUrlHost(expected: String) {
            Assertions.assertEquals(expected, actualHttpUrlResult.host)
        }

        fun checkUrlPort(expected: Int) {
            Assertions.assertEquals(expected, actualHttpUrlResult.port)
        }
    }
}