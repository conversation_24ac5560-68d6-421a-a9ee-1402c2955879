package com.gumtree.mobile.features.vip.v2

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.utils.TimeAgoFormatter
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.runOrNull
import java.time.format.DateTimeFormatter

/**
 * Helper class to map Vip Ad post timestamp into Vip posted since DTO
 * @param dateFormat - the date formatter (the pattern) of the String dates
 * @param vipScreenUiConfiguration - the Vip screen UI configurations
 */
class VipTimeAgoFormatter(
    override val dateFormat: DateTimeFormatter,
    private val vipScreenUiConfiguration: VipScreenUiConfiguration = VipScreenUiConfiguration,
) : TimeAgoFormatter {

    override fun getTimeAgoLabel(
        olderDate: String?,
        newerDate: String,
    ): String? {
        return runOrNull(olderDate.isNotNullOrEmpty()) {
            val (weeks, days, hours, minutes, seconds) = calculateWeeksDaysHoursMinutesSeconds(olderDate, newerDate)
            when {
                weeks > ZERO -> vipScreenUiConfiguration.getWeekText(weeks)
                days > ZERO -> vipScreenUiConfiguration.getDayText(days)
                hours > ZERO -> vipScreenUiConfiguration.getHourText(hours)
                minutes > ZERO -> vipScreenUiConfiguration.getMinuteText(minutes)
                seconds > ZERO -> vipScreenUiConfiguration.getSecondText()
                else -> null
            }
        }
    }
}
