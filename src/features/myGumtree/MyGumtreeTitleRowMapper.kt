package com.gumtree.mobile.features.myGumtree

import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeFilter
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeTitleRowCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem

class MyGumtreeTitleRowMapper {
    fun map(selectedFilterString: String = MyGumtreeFilter.ALL_ADS.text): RowLayout<UiItem> {
        val selectedFilter =
            MyGumtreeFilter.entries.find { it.name == selectedFilterString?.uppercase() } ?: MyGumtreeFilter.ALL_ADS
        return RowLayout(
            type = RowLayoutType.MY_GUMTREE_TITLE_ROW,
            data = listOf(
                MyGumtreeTitleRowCardDto(
                    title = MyGumtreeScreenUiConfiguration.TITLE,
                    selectedFilter,
                    MyGumtreeFilter.entries.associateWith { it.text },
                ),
            ),
            scrollingBehaviour = ScrollingCollapseBehaviour.COLLAPSE_ANYWHERE,
        )
    }
}
