package com.gumtree.mobile.features.homeFeed

import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.isZero

/**
 * Defines the HomeFeed screen UI components configuration (row positions etc)
 */
object HomeFeedScreenUiConfiguration {

    const val CATEGORIES = "Categories"
    const val FILTER_APPLIED = "filter applied"
    const val FILTERS_APPLIED = "filters applied"
    const val VIEW_MORE = "View more"

    /**
     * Saved Searches row position in the list with Home Feed RowLayouts
     */
    const val SAVED_SEARCH_ROW_POSITION = 1

    /**
     * The limit of Saved Searches items in the home feed saved searches carousel
     */
    const val SAVED_SEARCHES_LIMIT = 7

    /**
     * The ViewMoreCard button destination
     */
    val savedSearchesViewMoreCardDestination: DestinationDto = DestinationRoute.SAVED_SEARCHES.build()

    /**
     * Indicates whether the sticky bar should appear
     */
    fun shouldStickyBarAppearOnPage(page: String) = page.isZero()

    /**
     * Indicates the page where Saved Searches carousel should/could appear
     */
    fun shouldSavedSearchesRowAppearOnPage(page: String) = page.isZero()

    /**
     * Returns true if the given page should have a title row
     */
    fun shouldTitleRowAppearOnPage(page: String) = page.isZero()

    /**
     * Get the row position for the title row.
     * This should be after saved searches (if any) and before listings
     */
    fun getTitleRowPosition(
        hasSavedSearches: Boolean,
        page: String,
    ): Int {
        return when {
            hasSavedSearches && page.isZero() -> SAVED_SEARCH_ROW_POSITION + 1
            else -> SAVED_SEARCH_ROW_POSITION
        }
    }
}
