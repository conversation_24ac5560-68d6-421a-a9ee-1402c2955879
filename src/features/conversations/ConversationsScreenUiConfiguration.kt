package com.gumtree.mobile.features.conversations

/**
 * Defines the Conversations screen UI components configurations
 */

object ConversationsScreenUiConfiguration {
    const val CONVERSATION_LAST_MESSAGE_POST_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    const val CONVERSATION_NOW_TIME_AGO_TEXT = "Now"
    const val DEFAULT_PAGE_SIZE = "20"

    fun getWeekText(timeAgo: Long) = "${timeAgo}w"
    fun getDayText(timeAgo: Long) = "${timeAgo}d"
    fun getHourText(timeAgo: Long) = "${timeAgo}h"
    fun getMinuteText(timeAgo: Long) = "${timeAgo}m"
    fun getSecondText() = CONVERSATION_NOW_TIME_AGO_TEXT
}
