package features.myGumtree.v2

import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeFilter
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class MyGumtreeFilterTest {

    private val robot = Robot()

    @Test
    fun `should map MyGumtreeFilter of ALL_ADS to expected query params`() {
        runUnitTest(robot) {
            Given { stubFilter(MyGumtreeFilter.ALL_ADS.name) }
            When { map() }
            Then { checkMappedValue(MyGumtreeFilter.ALL_ADS.queryParams) }
        }
    }

    @Test
    fun `should map MyGumtreeFilter of ACTIVE to expected query params`() {
        runUnitTest(robot) {
            Given { stubFilter(MyGumtreeFilter.ACTIVE.name) }
            When { map() }
            Then { checkMappedValue(MyGumtreeFilter.ACTIVE.queryParams) }
        }
    }

    @Test
    fun `should map null to expected query params`() {
        runUnitTest(robot) {
            Given { stubFilter(null) }
            When { map() }
            Then { checkMappedValue(MyGumtreeFilter.ALL_ADS.queryParams) }
        }
    }

    private class Robot : BaseRobot {
        private var rawMyGumtreeFilter: String? = null
        private lateinit var actualFilterQueryParams: MyGumtreeFilter

        fun stubFilter(value: String?) {
            rawMyGumtreeFilter = value
        }

        fun map() {
            actualFilterQueryParams = MyGumtreeFilter.fromString(rawMyGumtreeFilter)
        }

        fun checkMappedValue(expected: String) {
            assertEquals(expected, actualFilterQueryParams.queryParams)
        }
    }
}
