@file:Suppress("Long<PERSON>eth<PERSON>", "LargeClass", "UnusedPrivateProperty")

package com.gumtree.mobile.features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.api.capi.models.RawSkillList
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.common.UserProfileService
import com.gumtree.mobile.api.conversations.models.RawUserInfo
import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import com.gumtree.mobile.api.similarItems.models.RawPapiSimilarItems
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.ImageType
import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.factories.SpaceRowFactory
import com.gumtree.mobile.features.screens.layoutsData.SellerLink
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFetcher
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.extensions.containsAuthorisationHeaders
import com.gumtree.mobile.utils.extensions.data.getAdSelfPublicWebsite
import com.gumtree.mobile.utils.extensions.data.getDescriptiveLocation
import com.gumtree.mobile.utils.extensions.data.getEncryptedVrnValue
import com.gumtree.mobile.utils.extensions.data.getFirstPictureImages
import com.gumtree.mobile.utils.extensions.data.getLocationId
import com.gumtree.mobile.utils.extensions.data.getVipCustomTabAdSlot
import com.gumtree.mobile.utils.extensions.data.toPostingSinceYearString
import com.gumtree.mobile.utils.extensions.getClientExperiments
import com.gumtree.mobile.utils.extensions.getClientSemantics
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.handleRequestOrNull
import common.AdjustTrackingData
import io.ktor.http.Headers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.slf4j.LoggerFactory

val vipLogger = LoggerFactory.getLogger("VIPLog")

/**
 * Handles all CRUD operations about the VIP feature
 */
interface VipRepository {
    /**
     * Read VIP screen
     * @param callHeaders - The request headers
     * @param adId - the Ad id
     * @return - the mapped data ready to be returned to the client
     */
    suspend fun readScreen(
        callHeaders: Headers,
        adId: String,
        userProfileData: UserProfileData?,
        requestId: String? = null,
    ): ScreenResponse
}

@Suppress("LongParameterList")
class DefaultVipRepository(
    private val vipService: VipService,
    private val userProfileService: UserProfileService,
    private val vipUIProvider: VipDefaultsUIProvider,
    private val vipBottomOverlayUIProvider: VipBottomOverlayUIProvider,
    private val vipVerticalsUIProvider: VipVerticalsUIProvider,
    private val vipAdvertsProvider: VipAdvertsProvider,
    private val rawLocationFetcher: RawLocationFetcher,
    private val sellerProfileActiveStatusFetcher: SellerProfileActiveStatusFetcher,
    private val papiHeadersProvider: ApiHeadersProvider,
    private val capiHeadersProvider: ApiHeadersProvider,
    private val vipAnalyticsProvider: VipAnalyticsProvider,
    private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider,
    private val dispatcherProvider: DispatcherProvider,
    private val spaceRowFactory: SpaceRowFactory,
    private val vipUiOptimizeProvider: VipUiOptimizeProvider,
) : VipRepository {

    override suspend fun readScreen(
        callHeaders: Headers,
        adId: String,
        userProfileData: UserProfileData?,
        requestId: String?,
    ): ScreenResponse {
        val capiUnAuthHeaders = capiHeadersProvider.createUnAuthorisedHeaders(callHeaders, addOptionalAuth = false)
        val papiAuthHeaders = papiHeadersProvider.createUnAuthorisedHeaders(callHeaders)
        val clientSemantics = callHeaders.getClientSemantics()
        val userEmail = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL]
        val rawAdDetailsData = withContext(dispatcherProvider.io) {
            vipService
                .getAdDetail(capiUnAuthHeaders, adId)
                .apply { VipScreenUiConfiguration.proceedWithAdPresentationOrThrowError(status) }
        }
        val isUserOwnAdFuture = withContext(dispatcherProvider.io) {
            async {
                userProfileService.isUserOwnAd(rawAdDetailsData.accountId, userEmail, userProfileData)
            }
        }
        val rawSellerProfileDataFuture = withContext(dispatcherProvider.io) {
            async {
                handleRequestOrNull {
                    vipService.getBaseUserProfile(papiAuthHeaders, rawAdDetailsData.userPublicId)
                }
            }
        }
        val rawSimilarItemsDataFuture = withContext(dispatcherProvider.io) {
            async {
                handleRequestOrNull {
                    vipService.getSimilarItems(
                        papiAuthHeaders,
                        VipScreenUiConfiguration.SIMILAR_ITEMS_DEFAULT_LIMIT,
                        adId,
                    )
                }
            }
        }
        val rawSellerUserInfoDataFuture = withContext(dispatcherProvider.io) {
            async {
                sellerProfileActiveStatusFetcher.fetchRawUserInfo(rawAdDetailsData.userId)
            }
        }
        val sellerSkillsFuture = withContext(dispatcherProvider.io) {
            async {
                if (CategoriesTreeCache.isServices(rawAdDetailsData.category.id)) {
                    vipService.getSelectedSkills(
                        capiUnAuthHeaders,
                        rawAdDetailsData.accountId,
                        rawAdDetailsData.category.id ?: "",
                    )
                } else {
                    null
                }
            }
        }
        val sellerSkills = sellerSkillsFuture.await()
        val partnershipDetailsData = getPartnershipDetails(rawAdDetailsData, clientSemantics)
        val category = rawAdDetailsData.category
        val locationId = rawAdDetailsData.getLocationId()
        val rawLocation: RawLocation =
            withContext(
                dispatcherProvider.io,
            ) { rawLocationFetcher.fetchByLocationIdAndType(locationId, LocationType.LOCATION) }
        val gamAdvertAttributes = vipAdvertsProvider.getVipGAMCommonAttributes(
            categoryId = category.id,
            rawAdDetailsData = rawAdDetailsData,
            rawLocation = rawLocation,
            isAuthenticated = callHeaders.containsAuthorisationHeaders(),
            clientSemantics = clientSemantics,
            experiments = callHeaders[ApiHeaderParams.EXPERIMENTS],
        )

        val isUserOwnAd = isUserOwnAdFuture.await()
        val rawSellerProfileData = rawSellerProfileDataFuture.await()
        val rawSimilarItemsData = rawSimilarItemsDataFuture.await()
        val rawSellerUserInfoData = rawSellerUserInfoDataFuture.await()

        val experiments = callHeaders.getClientExperiments()
        val vipToolbar = vipUIProvider.getVipToolbar(rawAdDetailsData, isUserOwnAd, experiments)
        val screenAdjustTrackingData = vipAdjustTrackingDataProvider.getScreenTrackingData(rawAdDetailsData)

        return when {
            CategoriesTreeCache.isVehicles(category.id) -> getMotorsVipScreen(
                callHeaders,
                rawAdDetailsData,
                rawSellerProfileData,
                rawSimilarItemsData,
                rawSellerUserInfoData,
                isUserOwnAd,
                gamAdvertAttributes,
                vipToolbar,
                screenAdjustTrackingData,
                partnershipDetailsData,
                spaceRowFactory,
                requestId,
            )

            CategoriesTreeCache.isPets(category.id) -> getPetsVipScreen(
                callHeaders,
                rawAdDetailsData,
                rawSellerProfileData,
                rawSimilarItemsData,
                rawSellerUserInfoData,
                isUserOwnAd,
                gamAdvertAttributes,
                vipToolbar,
                screenAdjustTrackingData,
                spaceRowFactory,
                requestId,
            )

            CategoriesTreeCache.isProperties(category.id) -> getPropertiesVipScreen(
                callHeaders,
                rawAdDetailsData,
                rawSellerProfileData,
                rawSimilarItemsData,
                rawSellerUserInfoData,
                isUserOwnAd,
                gamAdvertAttributes,
                vipToolbar,
                screenAdjustTrackingData,
                spaceRowFactory,
                requestId,
            )

            CategoriesTreeCache.isJobs(category.id) -> getJobsVipScreen(
                callHeaders,
                rawAdDetailsData,
                rawSellerProfileData,
                rawSimilarItemsData,
                rawSellerUserInfoData,
                isUserOwnAd,
                gamAdvertAttributes,
                vipToolbar,
                screenAdjustTrackingData,
                spaceRowFactory,
                requestId,
            )

            CategoriesTreeCache.isServices(category.id) -> {
                val experiments = callHeaders.getClientExperiments()
                when {
                    experiments.isB(Experiment.SERVICE_NEW_UI) -> getServiceVipScreen(
                        callHeaders,
                        rawAdDetailsData,
                        rawSellerProfileData,
                        rawSimilarItemsData,
                        rawSellerUserInfoData,
                        isUserOwnAd,
                        gamAdvertAttributes,
                        vipToolbar,
                        screenAdjustTrackingData,
                        partnershipDetailsData,
                        spaceRowFactory,
                        requestId,
                        sellerSkills,
                    )
                    else -> getDefaultVipScreen( // base (A) and other variants (C, D, etc.) use default screen
                        callHeaders,
                        rawAdDetailsData,
                        rawSellerProfileData,
                        rawSimilarItemsData,
                        rawSellerUserInfoData,
                        isUserOwnAd,
                        gamAdvertAttributes,
                        vipToolbar,
                        screenAdjustTrackingData,
                        partnershipDetailsData,
                        spaceRowFactory,
                        requestId,
                        sellerSkills,
                    )
                }
            }

            else -> getDefaultVipScreen(
                callHeaders,
                rawAdDetailsData,
                rawSellerProfileData,
                rawSimilarItemsData,
                rawSellerUserInfoData,
                isUserOwnAd,
                gamAdvertAttributes,
                vipToolbar,
                screenAdjustTrackingData,
                partnershipDetailsData,
                spaceRowFactory,
                requestId,
                sellerSkills,
            )
        }
    }

    private suspend fun getPartnershipDetails(
        rawAdDetailsData: RawCapiAd,
        clientSemantics: ClientSemantics,
    ): PartnershipDetailsResponse? {
        return handleRequestOrNull(enableLogging = false) {
            vipService.getPartnershipDetails(
                VipScreenUiConfiguration.VIP_PAGE_TYPE,
                clientSemantics.platform.name,
                VipScreenUiConfiguration.VIP_PARTNERSHIP_SLOT_ID,
                CategoriesTreeCache.getCategoryHierarchySeoName(rawAdDetailsData.category.id.orEmpty()),
                rawAdDetailsData.getDescriptiveLocation(),
                rawAdDetailsData.getFirstPictureImages(ImageType.THUMBNAIL_SQUARE)?.first()?.url.orEmpty(),
                rawAdDetailsData.title.orEmpty(),
                rawAdDetailsData.getVipCustomTabAdSlot()?.getEncryptedVrnValue(),
            )
        }
    }

    private suspend fun getDefaultVipScreen(
        callHeaders: Headers,
        rawAdDetailsData: RawCapiAd,
        rawSellerProfileData: RawPapiUserProfile?,
        rawSimilarItemsData: RawPapiSimilarItems?,
        rawUserInfoData: RawUserInfo?,
        isUserOwnAd: Boolean,
        gamAdvertAttributes: GAMAdvertAttributes,
        vipToolbar: ToolbarDto,
        screenAdjustTrackingData: AdjustTrackingData,
        partnershipDetailsData: PartnershipDetailsResponse?,
        spaceRowFactory: SpaceRowFactory,
        requestId: String? = null,
        skillData: RawSkillList?,
    ): ScreenResponse {
        val clientSemantics = callHeaders.getClientSemantics()
        val vipTopAdvert = vipAdvertsProvider.createVipTopAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        ).takeIf { partnershipDetailsData == null }
        val vipBottomAdvert = vipAdvertsProvider.createVipBottomAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipGalleryAdvert = vipAdvertsProvider.createVipGalleryAdvert(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val allVipGAMAdverts = vipAdvertsProvider.createAllVipGAMAdverts(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val analyticsScreenParams = vipAnalyticsProvider.getScreenParams(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            requestId,
        )
        val vipSellerWebSiteLink = SellerLink.createFromUrl(
            VipScreenUiConfiguration.VISIT_WEBSITE,
            rawAdDetailsData.websiteUrl,
            vipAnalyticsProvider.getSellerContactExternalEvent(
                analyticsScreenParams = analyticsScreenParams,
                linkUrl = rawAdDetailsData.websiteUrl,
                linkText = VipScreenUiConfiguration.VISIT_WEBSITE,
            ),
            clientSemantics,
            isMotorAd = rawAdDetailsData.isMotorsAdvert,
        )

        val vipImageRow = vipUIProvider.getVipImagesRow(rawAdDetailsData, vipGalleryAdvert)
        val vipTitleRow = vipUIProvider.getVipTitleRow(rawAdDetailsData)
        val vipPriceRow = vipUIProvider.getVipPriceRow(rawAdDetailsData)
        val vipLocationRow = vipUIProvider.getVipLocationRow(rawAdDetailsData)
        val vipDescriptionRow = vipUIProvider.getVipDescriptionRow(rawAdDetailsData)
        val vipPostedSinceRow = vipUIProvider.getVipPostedSinceRow(rawAdDetailsData)
        val vipPartnershipRow = vipUIProvider.getVipPartnershipAdvertRows(rawAdDetailsData, clientSemantics)
        val vipMobilePhonesKeyInfoRow = vipVerticalsUIProvider.getVipMobilePhonesKeyInfoRow(rawAdDetailsData)
        val vipSellerProfileRow = vipUIProvider.getVipSellerProfileRow(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            vipSellerWebSiteLink,
        )
        val vipMapRow = vipUIProvider.getVipMapRow(
            rawAdDetailsData = rawAdDetailsData,
            bottomMargin = Space.LARGE,
        )
        val isServices = CategoriesTreeCache.isServices(rawAdDetailsData.category.id)
        val vipSimilarItemsRows = vipUIProvider.getVipSimilarItemsRows(rawSimilarItemsData, includePrice = !isServices)
        val vipAdIdRow = vipUIProvider.getVipAdIdRow(rawAdDetailsData)
        val vatRow = vipUIProvider.getVipVATRow(rawAdDetailsData)
        val partnershipRow = partnershipDetailsData?.let { vipUIProvider.getPartnershipRow(it) }
        val contactMethod = vipBottomOverlayUIProvider.createContactMethod(rawAdDetailsData, callHeaders, isUserOwnAd)
        val bottomSpaceRow = spaceRowFactory.createSpaceRow(height = Space.MEDIUM)
        val sellerSkillsRow = skillData?.let { vipUIProvider.getVipSellerSkillsRow(it) }

        return ScreenResponse(
            portraitData = listOfNotNull(
                vipImageRow,
                vipTitleRow,
                vipPriceRow,
                vipLocationRow,
                partnershipRow,
                sellerSkillsRow,
                vipDescriptionRow,
                vipPostedSinceRow,
                vipTopAdvert,
                *vipPartnershipRow,
                vipMobilePhonesKeyInfoRow,
                vipSellerProfileRow,
                vipMapRow,
                vipBottomAdvert,
                *vipSimilarItemsRows,
                vipAdIdRow,
                vatRow,
                bottomSpaceRow,
            ),
            toolbar = vipToolbar,
            bottomOverlay = contactMethod,
            screenViewAnalyticsEvent = vipAnalyticsProvider.getScreenViewEvent(),
            analyticsParameters = analyticsScreenParams,
            gamAdvertsData = allVipGAMAdverts,
            adjustTrackingData = screenAdjustTrackingData,
        )
    }

    private suspend fun getMotorsVipScreen(
        callHeaders: Headers,
        rawAdDetailsData: RawCapiAd,
        rawSellerProfileData: RawPapiUserProfile?,
        rawSimilarItemsData: RawPapiSimilarItems?,
        rawUserInfoData: RawUserInfo?,
        isUserOwnAd: Boolean,
        gamAdvertAttributes: GAMAdvertAttributes,
        vipToolbar: ToolbarDto,
        screenAdjustTrackingData: AdjustTrackingData,
        partnershipDetailsData: PartnershipDetailsResponse?,
        spaceRowFactory: SpaceRowFactory,
        requestId: String? = null,
    ): ScreenResponse {
        val clientSemantics = callHeaders.getClientSemantics()
        val vipTopAdvert = vipAdvertsProvider.createVipTopAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        ).takeIf { partnershipDetailsData == null }
        val vipBottomAdvert = vipAdvertsProvider.createVipBottomAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipGalleryAdvert = vipAdvertsProvider.createVipGalleryAdvert(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val allVipGAMAdverts = vipAdvertsProvider.createAllVipGAMAdverts(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val analyticsScreenParams = vipAnalyticsProvider.getScreenParams(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            requestId,
        )
        val vipSellerWebSiteLink = SellerLink.createFromUrl(
            VipScreenUiConfiguration.VISIT_WEBSITE,
            rawAdDetailsData.websiteUrl,
            vipAnalyticsProvider.getSellerContactExternalEvent(
                analyticsScreenParams = analyticsScreenParams,
                linkUrl = rawAdDetailsData.websiteUrl,
                linkText = VipScreenUiConfiguration.VISIT_WEBSITE,
            ),
            clientSemantics,
            isMotorAd = rawAdDetailsData.isMotorsAdvert,
        )

        val vipImageRow = vipUIProvider.getVipImagesRow(rawAdDetailsData, vipGalleryAdvert)
        val vipTitleRow = vipUIProvider.getVipTitleRow(rawAdDetailsData)
        val vipPriceRow = vipUIProvider.getVipPriceRow(rawAdDetailsData)
        val vipLocationRow = vipUIProvider.getVipLocationRow(rawAdDetailsData)
        val vipLabelsRow = vipVerticalsUIProvider.getVipCarsLabelsRow(rawAdDetailsData)
        val vipDescriptionRow = vipUIProvider.getVipDescriptionRow(rawAdDetailsData)
        val vipPostedSinceRow = vipUIProvider.getVipPostedSinceRow(rawAdDetailsData)
        val vipPartnershipRow = vipUIProvider.getVipPartnershipAdvertRows(rawAdDetailsData, clientSemantics)
        val vipSpecificationTitleRow = vipVerticalsUIProvider.getVipCarsSpecificationTitleRow()
        val vipSpecificationRow = vipVerticalsUIProvider.getVipCarsSpecificationRow(rawAdDetailsData)
        val vipHpiCheckRow = vipVerticalsUIProvider.getVipCarsHpiCheckRow(rawAdDetailsData)
        val vipHpiTitleRow = vipVerticalsUIProvider.takeIf { vipHpiCheckRow.isNotNull() }?.getVipCarsHpiTitleRow()
        val vipHpiButtonRow = vipVerticalsUIProvider.takeIf { vipHpiCheckRow.isNotNull() }?.getVipCarsHpiButtonRow()
        val vipSellerProfileRow = vipUIProvider.getVipSellerProfileRow(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            vipSellerWebSiteLink,
        )
        val vipMapRow = vipUIProvider.getVipMapRow(rawAdDetailsData)
        val vipSimilarItemsRows = vipUIProvider.getVipSimilarItemsRows(rawSimilarItemsData)
        val vipAdIdRow = vipUIProvider.getVipAdIdRow(rawAdDetailsData)
        val vatRow = vipUIProvider.getVipVATRow(rawAdDetailsData)
        val contactMethod = vipBottomOverlayUIProvider.createContactMethod(rawAdDetailsData, callHeaders, isUserOwnAd)
        val partnershipRow = partnershipDetailsData?.let { vipUIProvider.getPartnershipRow(it) }
        val bottomSpaceRow = spaceRowFactory.createSpaceRow(height = Space.MEDIUM)

        return ScreenResponse(
            portraitData = listOfNotNull(
                vipImageRow,
                vipTitleRow,
                vipPriceRow,
                vipLocationRow,
                vipLabelsRow,
                *vipPartnershipRow,
                partnershipRow,
                vipDescriptionRow,
                vipPostedSinceRow,
                vipTopAdvert,
                vipSpecificationTitleRow,
                vipSpecificationRow,
                vipHpiTitleRow,
                vipHpiCheckRow,
                vipHpiButtonRow,
                vipSellerProfileRow,
                vipMapRow,
                vipBottomAdvert,
                *vipSimilarItemsRows,
                vipAdIdRow,
                vatRow,
                bottomSpaceRow,
            ),
            toolbar = vipToolbar,
            bottomOverlay = contactMethod,
            screenViewAnalyticsEvent = vipAnalyticsProvider.getScreenViewEvent(),
            analyticsParameters = analyticsScreenParams,
            gamAdvertsData = allVipGAMAdverts,
            adjustTrackingData = screenAdjustTrackingData,
        )
    }

    private suspend fun getPetsVipScreen(
        callHeaders: Headers,
        rawAdDetailsData: RawCapiAd,
        rawSellerProfileData: RawPapiUserProfile?,
        rawSimilarItemsData: RawPapiSimilarItems?,
        rawUserInfoData: RawUserInfo?,
        isUserOwnAd: Boolean,
        gamAdvertAttributes: GAMAdvertAttributes,
        vipToolbar: ToolbarDto,
        screenAdjustTrackingData: AdjustTrackingData,
        spaceRowFactory: SpaceRowFactory,
        requestId: String? = null,
    ): ScreenResponse {
        val clientSemantics = callHeaders.getClientSemantics()
        val vipTopAdvert = vipAdvertsProvider.createVipTopAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipBottomAdvert = vipAdvertsProvider.createVipBottomAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipGalleryAdvert = vipAdvertsProvider.createVipGalleryAdvert(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val allVipGAMAdverts = vipAdvertsProvider.createAllVipGAMAdverts(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val analyticsScreenParams = vipAnalyticsProvider.getScreenParams(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            requestId,
        )
        val vipSellerWebSiteLink = SellerLink.createFromUrl(
            VipScreenUiConfiguration.VISIT_WEBSITE,
            rawAdDetailsData.websiteUrl,
            vipAnalyticsProvider.getSellerContactExternalEvent(
                analyticsScreenParams = analyticsScreenParams,
                linkUrl = rawAdDetailsData.websiteUrl,
                linkText = VipScreenUiConfiguration.VISIT_WEBSITE,
            ),
            clientSemantics,
            isMotorAd = rawAdDetailsData.isMotorsAdvert,
        )

        val vipImageRow = vipUIProvider.getVipImagesRow(rawAdDetailsData, vipGalleryAdvert)
        val vipTitleRow = vipUIProvider.getVipTitleRow(rawAdDetailsData)
        val vipPriceRow = vipUIProvider.getVipPriceRow(rawAdDetailsData)
        val vipLocationRow = vipUIProvider.getVipLocationRow(rawAdDetailsData)
        val vipDescriptionRow = vipUIProvider.getVipDescriptionRow(rawAdDetailsData)
        val vipPostedSinceRow = vipUIProvider.getVipPostedSinceRow(rawAdDetailsData)
        val vipPartnershipRow = vipUIProvider.getVipPartnershipAdvertRows(rawAdDetailsData, clientSemantics)
        val vipPetsKeyInfoRow = vipVerticalsUIProvider.getVipPetsKeyInfoRow(rawAdDetailsData)
        val vipSellerProfileRow = vipUIProvider.getVipSellerProfileRow(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            vipSellerWebSiteLink,
        )
        val vipMapRow = vipUIProvider.getVipMapRow(rawAdDetailsData)
        val vipSimilarItemsRows = vipUIProvider.getVipSimilarItemsRows(rawSimilarItemsData)
        val vipAdIdRow = vipUIProvider.getVipAdIdRow(rawAdDetailsData)
        val vatRow = vipUIProvider.getVipVATRow(rawAdDetailsData)
        val contactMethod = vipBottomOverlayUIProvider.createContactMethod(rawAdDetailsData, callHeaders, isUserOwnAd)
        val bottomSpaceRow = spaceRowFactory.createSpaceRow(height = Space.MEDIUM)

        return ScreenResponse(
            portraitData = listOfNotNull(
                vipImageRow,
                vipTitleRow,
                vipPriceRow,
                vipLocationRow,
                vipDescriptionRow,
                vipPostedSinceRow,
                vipTopAdvert,
                *vipPartnershipRow,
                vipPetsKeyInfoRow,
                vipSellerProfileRow,
                vipMapRow,
                vipBottomAdvert,
                *vipSimilarItemsRows,
                vipAdIdRow,
                vatRow,
                bottomSpaceRow,
            ),
            toolbar = vipToolbar,
            bottomOverlay = contactMethod,
            screenViewAnalyticsEvent = vipAnalyticsProvider.getScreenViewEvent(),
            analyticsParameters = analyticsScreenParams,
            gamAdvertsData = allVipGAMAdverts,
            adjustTrackingData = screenAdjustTrackingData,
        )
    }

    private suspend fun getJobsVipScreen(
        callHeaders: Headers,
        rawAdDetailsData: RawCapiAd,
        rawSellerProfileData: RawPapiUserProfile?,
        rawSimilarItemsData: RawPapiSimilarItems?,
        rawUserInfoData: RawUserInfo?,
        isUserOwnAd: Boolean,
        gamAdvertAttributes: GAMAdvertAttributes,
        vipToolbar: ToolbarDto,
        screenAdjustTrackingData: AdjustTrackingData,
        spaceRowFactory: SpaceRowFactory,
        requestId: String? = null,
    ): ScreenResponse {
        val clientSemantics = callHeaders.getClientSemantics()
        val vipTopAdvert = vipAdvertsProvider.createVipTopAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipBottomAdvert = vipAdvertsProvider.createVipBottomAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipGalleryAdvert = vipAdvertsProvider.createVipGalleryAdvert(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val allVipGAMAdverts = vipAdvertsProvider.createAllVipGAMAdverts(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val analyticsScreenParams = vipAnalyticsProvider.getScreenParams(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            requestId,
        )
        val vipSellerWebSiteLink = SellerLink.createFromUrl(
            VipScreenUiConfiguration.VISIT_WEBSITE,
            rawAdDetailsData.websiteUrl,
            vipAnalyticsProvider.getSellerContactExternalEvent(
                analyticsScreenParams = analyticsScreenParams,
                linkUrl = rawAdDetailsData.websiteUrl,
                linkText = VipScreenUiConfiguration.VISIT_WEBSITE,
            ),
            clientSemantics,
            isMotorAd = rawAdDetailsData.isMotorsAdvert,
        )

        val vipImageRow = vipUIProvider.getVipImagesRow(rawAdDetailsData, vipGalleryAdvert)
        val vipTitleRow = vipUIProvider.getVipTitleRow(rawAdDetailsData)
        val vipLocationRow = vipUIProvider.getVipLocationRow(rawAdDetailsData)
        val vipMapRow = vipUIProvider.getVipMapRow(rawAdDetailsData)
        val vipJobsKeyInfoRow = vipVerticalsUIProvider.getVipJobsKeyInfoRow(rawAdDetailsData)
        val vipPostedSinceRow = vipUIProvider.getVipPostedSinceRow(rawAdDetailsData)
        val vipPartnershipRow = vipUIProvider.getVipPartnershipAdvertRows(rawAdDetailsData, clientSemantics)
        val vipDescriptionRow =
            vipUIProvider.getVipDescriptionRow(rawAdDetailsData, VipScreenUiConfiguration.JOBS_DESCRIPTION_LABEL_TEXT)
        val vipAdIdRow = vipUIProvider.getVipAdIdRow(rawAdDetailsData)
        val vatRow = vipUIProvider.getVipVATRow(rawAdDetailsData)
        val vipSellerProfileRow = vipUIProvider.getVipSellerProfileRow(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            vipSellerWebSiteLink,
        )
        val vipSimilarItemsRows =
            vipUIProvider.getVipSimilarItemsRows(rawSimilarItemsData, VipScreenUiConfiguration.SIMILAR_JOBS_TEXT, false)
        val bottomSpaceRow = spaceRowFactory.createSpaceRow(height = Space.MEDIUM)

        val contactMethod = vipBottomOverlayUIProvider.createContactMethod(rawAdDetailsData, callHeaders, isUserOwnAd)
        return ScreenResponse(
            portraitData = listOfNotNull(
                vipImageRow,
                vipTitleRow,
                vipLocationRow,
                vipMapRow,
                vipJobsKeyInfoRow,
                vipPostedSinceRow,
                vipTopAdvert,
                *vipPartnershipRow,
                vipDescriptionRow,
                vipSellerProfileRow,
                vipBottomAdvert,
                *vipSimilarItemsRows,
                vipAdIdRow,
                vatRow,
                bottomSpaceRow,
            ),
            toolbar = vipToolbar,
            bottomOverlay = contactMethod,
            screenViewAnalyticsEvent = vipAnalyticsProvider.getScreenViewEvent(),
            analyticsParameters = analyticsScreenParams,
            gamAdvertsData = allVipGAMAdverts,
            adjustTrackingData = screenAdjustTrackingData,
        )
    }

    private suspend fun getPropertiesVipScreen(
        callHeaders: Headers,
        rawAdDetailsData: RawCapiAd,
        rawSellerProfileData: RawPapiUserProfile?,
        rawSimilarItemsData: RawPapiSimilarItems?,
        rawUserInfoData: RawUserInfo?,
        isUserOwnAd: Boolean,
        gamAdvertAttributes: GAMAdvertAttributes,
        vipToolbar: ToolbarDto,
        screenAdjustTrackingData: AdjustTrackingData,
        spaceRowFactory: SpaceRowFactory,
        requestId: String? = null,
    ): ScreenResponse {
        val clientSemantics = callHeaders.getClientSemantics()
        val vipTopAdvert = vipAdvertsProvider.createVipTopAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipBottomAdvert = vipAdvertsProvider.createVipBottomAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipGalleryAdvert = vipAdvertsProvider.createVipGalleryAdvert(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val allVipGAMAdverts = vipAdvertsProvider.createAllVipGAMAdverts(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val analyticsScreenParams = vipAnalyticsProvider.getScreenParams(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            requestId,
        )
        val vipSellerWebSiteLink = SellerLink.createFromUrl(
            VipScreenUiConfiguration.VISIT_WEBSITE,
            rawAdDetailsData.websiteUrl,
            vipAnalyticsProvider.getSellerContactExternalEvent(
                analyticsScreenParams = analyticsScreenParams,
                linkUrl = rawAdDetailsData.websiteUrl,
                linkText = VipScreenUiConfiguration.VISIT_WEBSITE,
            ),
            clientSemantics,
            isMotorAd = rawAdDetailsData.isMotorsAdvert,
        )

        val vipImageRow = vipUIProvider.getVipImagesRow(rawAdDetailsData, vipGalleryAdvert)
        val vipTitleRow = vipUIProvider.getVipTitleRow(rawAdDetailsData)
        val vipPriceRow = vipUIProvider.getVipPriceRow(rawAdDetailsData)
        val vipLocationRow = vipUIProvider.getVipLocationRow(rawAdDetailsData)
        val vipDescriptionRow = vipUIProvider.getVipDescriptionRow(rawAdDetailsData)
        val vipPostedSinceRow = vipUIProvider.getVipPostedSinceRow(rawAdDetailsData)
        val vipPartnershipRow = vipUIProvider.getVipPartnershipAdvertRows(rawAdDetailsData, clientSemantics)
        val vipPropertiesKeyInfoRow = vipVerticalsUIProvider.getVipPropertiesKeyInfoRow(rawAdDetailsData)
        val vipMapRow = vipUIProvider.getVipMapRow(
            rawAdDetailsData = rawAdDetailsData,
            bottomMargin = Space.X_LARGE,
        )
        val vipSellerProfileRow = vipUIProvider.getVipSellerProfileRow(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            vipSellerWebSiteLink,
        )
        val vipLocationTitleRow = vipUIProvider.getLocationTitleRow(rawAdDetailsData)
        val vipSimilarItemsRows = vipUIProvider.getVipSimilarItemsRows(rawSimilarItemsData)
        val vipAdIdRow = vipUIProvider.getVipAdIdRow(rawAdDetailsData)
        val vatRow = vipUIProvider.getVipVATRow(rawAdDetailsData)
        val contactMethod = vipBottomOverlayUIProvider.createContactMethod(rawAdDetailsData, callHeaders, isUserOwnAd)
        val bottomSpaceRow = spaceRowFactory.createSpaceRow(height = Space.MEDIUM)

        return ScreenResponse(
            portraitData = listOfNotNull(
                vipImageRow,
                vipTitleRow,
                vipPriceRow,
                vipLocationRow,
                vipDescriptionRow,
                vipPostedSinceRow,
                vipTopAdvert,
                *vipPartnershipRow,
                vipPropertiesKeyInfoRow,
                vipLocationTitleRow,
                vipMapRow,
                vipSellerProfileRow,
                vipBottomAdvert,
                *vipSimilarItemsRows,
                vipAdIdRow,
                vatRow,
                bottomSpaceRow,
            ),
            toolbar = vipToolbar,
            bottomOverlay = contactMethod,
            screenViewAnalyticsEvent = vipAnalyticsProvider.getScreenViewEvent(),
            analyticsParameters = analyticsScreenParams,
            gamAdvertsData = allVipGAMAdverts,
            adjustTrackingData = screenAdjustTrackingData,
        )
    }

    private suspend fun getServiceVipScreen(
        callHeaders: Headers,
        rawAdDetailsData: RawCapiAd,
        rawSellerProfileData: RawPapiUserProfile?,
        rawSimilarItemsData: RawPapiSimilarItems?,
        rawUserInfoData: RawUserInfo?,
        isUserOwnAd: Boolean,
        gamAdvertAttributes: GAMAdvertAttributes,
        vipToolbar: ToolbarDto,
        screenAdjustTrackingData: AdjustTrackingData,
        partnershipDetailsData: PartnershipDetailsResponse?,
        spaceRowFactory: SpaceRowFactory,
        requestId: String? = null,
        skillData: RawSkillList?,
    ): ScreenResponse {
        val clientSemantics = callHeaders.getClientSemantics()
        val vipTopAdvert = vipAdvertsProvider.createVipTopAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        ).takeIf { partnershipDetailsData == null }
        val vipBottomAdvert = vipAdvertsProvider.createVipBottomAdvertRow(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val vipGalleryAdvert = vipAdvertsProvider.createVipGalleryAdvert(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val allVipGAMAdverts = vipAdvertsProvider.createAllVipGAMAdverts(
            rawAdDetailsData.category.id,
            gamAdvertAttributes,
            rawAdDetailsData.getAdSelfPublicWebsite(),
        )
        val analyticsScreenParams = vipAnalyticsProvider.getScreenParams(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            requestId,
        )
        val vipSellerWebSiteLink = SellerLink.createFromUrl(
            VipScreenUiConfiguration.VISIT_WEBSITE,
            rawAdDetailsData.websiteUrl,
            vipAnalyticsProvider.getSellerContactExternalEvent(
                analyticsScreenParams = analyticsScreenParams,
                linkUrl = rawAdDetailsData.websiteUrl,
                linkText = VipScreenUiConfiguration.VISIT_WEBSITE,
            ),
            clientSemantics,
            isMotorAd = rawAdDetailsData.isMotorsAdvert,
        )

        val vipTitleRow = vipUIProvider.getVipTitleRow(rawAdDetailsData)
        val vipDescriptionRow = vipUIProvider.getVipDescriptionRow(rawAdDetailsData)
        val vipPostedSinceRow = vipUIProvider.getVipPostedSinceRow(rawAdDetailsData)
        val vipPartnershipRow = vipUIProvider.getVipPartnershipAdvertRows(rawAdDetailsData, clientSemantics)
        val vipMobilePhonesKeyInfoRow = vipVerticalsUIProvider.getVipMobilePhonesKeyInfoRow(rawAdDetailsData)
        val vipSellerProfileRow = vipUIProvider.getVipSellerProfileRow(
            rawAdDetailsData,
            rawSellerProfileData,
            rawUserInfoData,
            vipSellerWebSiteLink,
        )
        val vipMapRow = vipUIProvider.getVipMapRow(
            rawAdDetailsData = rawAdDetailsData,
            bottomMargin = Space.LARGE,
        )
        val isServices = CategoriesTreeCache.isServices(rawAdDetailsData.category.id)
        val vipSimilarItemsRows = vipUIProvider.getVipSimilarItemsRows(rawSimilarItemsData, includePrice = !isServices)
        val vipAdIdRow = vipUIProvider.getVipAdIdRow(rawAdDetailsData)
        val vatRow = vipUIProvider.getVipVATRow(rawAdDetailsData)
        val partnershipRow = partnershipDetailsData?.let { vipUIProvider.getPartnershipRow(it) }
        val contactMethod = vipBottomOverlayUIProvider.createContactMethod(rawAdDetailsData, callHeaders, isUserOwnAd)
        val bottomSpaceRow = spaceRowFactory.createSpaceRow(height = Space.MEDIUM)
        val sellerSkillsRow = skillData?.let { vipUIProvider.getVipSellerSkillsRow(it) }
        val vipPublisherInfo = vipUiOptimizeProvider.getVipPublisherInfo(rawAdDetailsData)
        // overview
        val vipOverview = vipUiOptimizeProvider.getVipOverviewRow(
            rawAdDetailsData,
            vipSellerWebSiteLink,
            rawSellerProfileData?.userData?.registrationDate?.toPostingSinceYearString(),
        )
        // 图片栏
        val vipPortfolio = vipUiOptimizeProvider.getVipPortfolioImageRow(rawAdDetailsData, vipGalleryAdvert)
        // 分割线
        val dividerRow = vipUiOptimizeProvider.createDividerRow(bottomMargin = Space.MEDIUM)

        // 先构建所有非空布局列表
        val nonNullLayouts = listOfNotNull(
            vipTitleRow,
            vipPublisherInfo,
            vipOverview.takeIf { it.data.isNotEmpty() },
            dividerRow,
            vipPortfolio.takeIf { it.data.isNotEmpty() },
            vipUiOptimizeProvider.createDividerRow(
                bottomMargin = Space.MEDIUM,
            ).takeIf { vipPortfolio.data.isNotEmpty() },
            partnershipRow,
            sellerSkillsRow,
            vipUiOptimizeProvider.createDividerRow(
                bottomMargin = Space.MEDIUM,
            ).takeIf { sellerSkillsRow != null },
            vipDescriptionRow,
            dividerRow,
            vipPostedSinceRow,
            vipTopAdvert,
            *vipPartnershipRow,
            vipMobilePhonesKeyInfoRow,
            vipMapRow,
            vipBottomAdvert,
            *vipSimilarItemsRows,
            vipAdIdRow,
            vatRow,
            bottomSpaceRow,
        )

        // 构建 tabIndexMap，key 是 tab 名称，value 是布局在 nonNullLayouts 列表中的索引
        val tabIndexMap = mutableMapOf<String, Int>()
        if (vipOverview.data.isNotEmpty()) {
            tabIndexMap["Overview"] = nonNullLayouts.indexOf(vipOverview) + 1
        }
        if (vipPortfolio.data.isNotEmpty()) {
            tabIndexMap["Portfolio"] = nonNullLayouts.indexOf(vipPortfolio) + 1
        }
        if (sellerSkillsRow != null) {
            tabIndexMap["Services"] = nonNullLayouts.indexOf(sellerSkillsRow) + 1
        }
        tabIndexMap["Description"] = nonNullLayouts.indexOf(vipDescriptionRow) + 1

        // 导航栏
        val vipSectionTabs = vipUiOptimizeProvider.getVipSectionTabsRow(tabIndexMap)

        // 在 vipPublisherInfo 后面插入 vipSectionTabs
        val finalLayouts = nonNullLayouts.toMutableList()
        finalLayouts.add(2, vipSectionTabs)

        return ScreenResponse(
            portraitData = finalLayouts,
            toolbar = vipToolbar,
            bottomOverlay = contactMethod,
            screenViewAnalyticsEvent = vipAnalyticsProvider.getScreenViewEvent(),
            analyticsParameters = analyticsScreenParams,
            gamAdvertsData = allVipGAMAdverts,
            adjustTrackingData = screenAdjustTrackingData,
        )
    }
}
