package features.reviews

import com.gumtree.mobile.features.reviews.ReviewsToolbarProvider
import com.gumtree.mobile.features.screens.layoutsData.CloseToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When

class ReviewsToolbarProviderTest {

    private val robot = Robot()

    @Test
    fun `should return Close toolbar action`() {
        tools.runUnitTest(robot) {
            When { createReviewStepToolbar() }
            Then { checkChatToolbarActionsSize(1) }
            Then { checkChatToolbarActionTypeAtPosition(0, CloseToolbarActionDto::class.java) }
        }
    }

    private class Robot : BaseRobot {
        private var actualToolbarResult: ToolbarDto? = null

        private val testSubject = ReviewsToolbarProvider()

        fun createReviewStepToolbar() {
            actualToolbarResult = testSubject.createReviewStepToolbar()
        }

        fun checkChatToolbarActionsSize(expected: Int) {
            assertEquals(expected, actualToolbarResult?.actions?.size)
        }

        fun <T> checkChatToolbarActionTypeAtPosition(
            position: Int,
            expected: Class<T>,
        ) {
            assertInstanceOf(expected, actualToolbarResult?.actions?.get(position))
        }
    }
}