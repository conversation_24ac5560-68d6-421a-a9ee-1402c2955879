package com.gumtree.mobile.features.sellerProfile.v2

import com.gumtree.mobile.api.reviews.models.RawReview
import com.gumtree.mobile.api.reviews.models.RawReviews
import com.gumtree.mobile.features.screens.layoutsData.FIVE_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.FOUR_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.ONE_STAR_RATING
import com.gumtree.mobile.features.screens.layoutsData.ReviewsOverviewCardDto.RatingDistribution
import com.gumtree.mobile.features.screens.layoutsData.THREE_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.TWO_STARS_RATING
import com.gumtree.mobile.utils.extensions.percentOf
import java.math.RoundingMode

class SellerProfileReviewsOverviewCalculator {

    fun getReviewsOverviewData(rawReviewsData: RawReviews): Triple<Float, Int, List<RatingDistribution>> {
        val totalReviewsNumber = rawReviewsData.reviews.size
        return Triple(
            first = calculateAverageRating(rawReviewsData.reviews, totalReviewsNumber),
            second = totalReviewsNumber,
            third = calculateRatingDistribution(rawReviewsData.reviews, totalReviewsNumber),
        )
    }

    private fun calculateAverageRating(
        rawReviews: List<RawReview>,
        totalReviewsNumber: Int,
    ): Float {
        var totalReviewsRatings = 0
        rawReviews.forEach {
            totalReviewsRatings += it.rating
        }
        return (totalReviewsRatings.toDouble() / totalReviewsNumber)
            .toBigDecimal()
            .setScale(1, RoundingMode.DOWN)
            .toFloat()
    }

    private fun calculateRatingDistribution(
        rawReviews: List<RawReview>,
        totalReviewsNumber: Int,
    ): List<RatingDistribution> {
        return listOf(
            RatingDistribution(
                rating = ONE_STAR_RATING,
                percentage = rawReviews.filter { it.rating == ONE_STAR_RATING }.size percentOf totalReviewsNumber,
            ),
            RatingDistribution(
                rating = TWO_STARS_RATING,
                percentage = rawReviews.filter { it.rating == TWO_STARS_RATING }.size percentOf totalReviewsNumber,
            ),
            RatingDistribution(
                rating = THREE_STARS_RATING,
                percentage = rawReviews.filter { it.rating == THREE_STARS_RATING }.size percentOf totalReviewsNumber,
            ),
            RatingDistribution(
                rating = FOUR_STARS_RATING,
                percentage = rawReviews.filter { it.rating == FOUR_STARS_RATING }.size percentOf totalReviewsNumber,
            ),
            RatingDistribution(
                rating = FIVE_STARS_RATING,
                percentage = rawReviews.filter { it.rating == FIVE_STARS_RATING }.size percentOf totalReviewsNumber,
            ),
        )
    }
}
