package common

import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.common.AdDetailsProvider
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.AdDetailsDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.routes.DestinationRoute
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import kotlin.test.assertNull
import tools.rawDataFactory.RawFlatAdsFactory

class AdDetailsProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should NOT return ad details row if raw ad is null`() {
        runUnitTest(robot) {
            When { createAdDetailsRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should return ad details row with destination if clickable`() {
        runUnitTest(robot) {
            Given { stubRawAd() }
            Given { stubClickable(true) }
            When { createAdDetailsRow() }
            Then { checkRowLayoutType(RowLayoutType.AD_DETAILS_ROW) }
            Then { checkAdDetailsTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkAdDetailsPrice("£${DataFactory.SOME_AD_PRICE}") }
            Then { checkAdDetailsDestination(DestinationRoute.VIP.build(ApiQueryParams.AD_ID to DataFactory.SOME_AD_ID)) }
            Then { checkAdDetailsLocation(DataFactory.SOME_AD_LOCATION) }
            Then { checkAdDetailsImageUrlAtPosition(0, DataFactory.SOME_AD_IMAGE_URL) }
            Then { checkAdDetailsImageWidthAtPosition(0, 80) }
            Then { checkAdDetailsImageHeightAtPosition(0, 80) }
        }
    }

    @Test
    fun `should return ad details row without destination if NOT clickable`() {
        runUnitTest(robot) {
            Given { stubRawAd() }
            Given { stubClickable(false) }
            When { createAdDetailsRow() }
            Then { checkRowLayoutType(RowLayoutType.AD_DETAILS_ROW) }
            Then { checkAdDetailsPrice("£${DataFactory.SOME_AD_PRICE}") }
            Then { checkAdDetailsDestination(null) }
            Then { checkAdDetailsLocation(DataFactory.SOME_AD_LOCATION) }
            Then { checkAdDetailsImageUrlAtPosition(0, DataFactory.SOME_AD_IMAGE_URL) }
            Then { checkAdDetailsImageWidthAtPosition(0, 80) }
            Then { checkAdDetailsImageHeightAtPosition(0, 80) }
        }
    }

    private inner class Robot : BaseRobot {
        private var actualRowLayoutResult: RowLayout<UiItem>? = null

        private var rawAd: RawFlatAd? = null
        private var isClickable: Boolean = true

        private val testSubject = AdDetailsProvider()

        override fun setup() {
            rawAd = null
        }

        fun stubRawAd() {
            rawAd = RawFlatAdsFactory.createRawFlatAd(
                title = DataFactory.SOME_AD_TITLE,
                adId = DataFactory.SOME_AD_ID,
                price = DataFactory.SOME_AD_PRICE_IN_PENCE,
                locationId = DataFactory.SOME_AD_LOCATION_ID,
                location = DataFactory.SOME_AD_LOCATION,
                imageUrl = DataFactory.SOME_AD_IMAGE_URL,
            )
        }

        fun stubClickable(clickable: Boolean) {
            isClickable = clickable
        }

        fun createAdDetailsRow() {
            actualRowLayoutResult = testSubject.createAdDetailsRow(rawAd, isClickable)
        }

        fun checkRowLayoutIsNull() {
            assertNull(actualRowLayoutResult)
        }

        fun checkRowLayoutType(expected: RowLayoutType) {
            assertEquals(expected, actualRowLayoutResult?.type)
        }

        fun checkAdDetailsTitle(expected: String) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.title)
        }

        fun checkAdDetailsPrice(expected: String) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.price)
        }

        fun checkAdDetailsDestination(expected: DestinationDto?) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.destination)
        }

        fun checkAdDetailsLocation(expected: String) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.location)
        }

        fun checkAdDetailsImageUrlAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.images?.get(position)?.url)
        }

        fun checkAdDetailsImageWidthAtPosition(
            position: Int,
            expected: Int,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.images?.get(position)?.width)
        }

        fun checkAdDetailsImageHeightAtPosition(
            position: Int,
            expected: Int,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.first() as AdDetailsDto?)?.images?.get(position)?.height)
        }
    }
}
