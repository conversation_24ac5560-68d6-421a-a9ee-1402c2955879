package com.gumtree.mobile.features.screens

/**
 * Use to read the different Grid sizes for the different device orientations.
 * Also the different mobile BFF features could have different Grid sizes for the supported orientations
 */
interface GridSizes {
    /**
     * Get the Portrait orientation Grid size
     * @return - the number of th Grid span for Portrait display
     */
    fun getPortraitGridSize(): Int

    /**
     * Get the Landscape orientation Grid size
     * @return - the number of th Grid span for Landscape display
     */
    fun getLandscapeGridSize(): Int
}

fun <T : GridSizes> createGridSizes(
    factory: () -> T,
): T {
    return factory()
}
