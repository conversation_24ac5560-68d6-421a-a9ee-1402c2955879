package com.gumtree.mobile.features.login

import com.gumtree.mobile.plugins.AUTH_USER_PROFILE
import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiBodyParams
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.extensions.getHeaderOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.getUserEmailOrThrowUnauthorised
import com.gumtree.mobile.utils.extensions.getUserTokenOrThrowUnauthorised
import com.gumtree.mobile.utils.extensions.readBodyParamOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.readFormUrlEncodedParamOrThrow
import com.gumtree.mobile.utils.extensions.readUserProfileData
import com.gumtree.mobile.utils.extensions.respondNoContent
import io.ktor.http.Headers
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import org.koin.ktor.ext.inject

const val LOGIN_PATH = "$API_V1_MAIN_PATH/login"
const val LOGOUT_PATH = "$API_V1_MAIN_PATH/logout"
const val LOGIN_EXCHANGE_PATH = "$LOGIN_PATH/exchange"

fun Route.loginRoute() {
    val repository by inject<LoginRepository>()

    post(LOGIN_PATH) {
        val loginFormParams = call.receiveParameters()
        val authenticationType = AuthenticationType.fromString(loginFormParams[ApiBodyParams.AUTHENTICATION_TYPE])
        val socialRegistration: Boolean = loginFormParams[ApiBodyParams.SOCIAL_REGISTRATION].toBoolean()
        val optInMarketing: Boolean = loginFormParams[ApiBodyParams.OPT_IN_MARKETING].toBoolean()
        val username = loginFormParams.readFormUrlEncodedParamOrThrow(ApiBodyParams.USER_NAME_BODY)
        val userSecret = loginFormParams.readFormUrlEncodedParamOrThrow(ApiBodyParams.USER_SECRET_BODY)
        val threatMetrixSessionId = call.getHeaderOrThrowBadRequest(ApiHeaderParams.THREATMETRIX_SESSION)

        val userLoginData = repository.create(
            username,
            userSecret,
            threatMetrixSessionId,
            authenticationType,
            socialRegistration,
            optInMarketing,
        )

        LoginLogger.logLoginOutputData(userLoginData)

        call.respond(
            HttpStatusCode.OK,
            userLoginData,
        )
    }

    authenticate(AUTH_USER_PROFILE, optional = true) {
        post(LOGOUT_PATH) {
            val callHeaders: Headers = call.request.headers
            val userProfile = call.readUserProfileData()
            val userToken: String = userProfile.getUserTokenOrThrowUnauthorised(callHeaders)
            val logoutRequest = call.readBodyParamOrThrowBadRequest<LogoutRequest>()

            repository.delete(call.request.headers, userToken, logoutRequest)

            call.respondNoContent()
        }
    }

    authenticate(AUTH_USER_PROFILE, optional = true) {
        get(LOGIN_EXCHANGE_PATH) {
            val callHeaders: Headers = call.request.headers
            val userProfile = call.readUserProfileData()
            val userName = userProfile.getUserEmailOrThrowUnauthorised(callHeaders)
            val userToken = userProfile.getUserTokenOrThrowUnauthorised(callHeaders)
            LoginLogger.logLoginExchangeInputData(callHeaders, userProfile)
            val userLoginData = repository.exchange(callHeaders, userName, userToken)
            LoginLogger.logLoginExchangeOutputData(userLoginData)

            call.respond(
                HttpStatusCode.OK,
                userLoginData,
            )
        }
    }
}
