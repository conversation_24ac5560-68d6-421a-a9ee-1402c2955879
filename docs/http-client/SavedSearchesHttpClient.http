# @name GET User's all SavedSearches data

GET {{hostV1}}/saved-searches
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET SavedSearches Screen

GET {{hostV1}}/saved-searches/screen
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET SavedSearches Screen with page and size

GET {{hostV1}}/saved-searches/screen?page={{paging_page}}&size={{paging_size}}
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET SavedSearches Screen for Search Suggestions Screen

GET {{hostV1}}/saved-searches/screen?searchSuggestions=true
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name POST SavedSearches Screen with page, size and client saved searches cache

POST {{hostV1}}/saved-searches/screen?page={{paging_page}}&size={{paging_size}}
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}
Content-Type: application/json

{
  "668e4e70ef0ad97b3631d5a4":"2024-07-10T13:57:10.394Z",
  "668e5859ef0ae131762cadd8":"2024-07-10T13:57:09.877Z",
  "668e4e74ef0a5236790c585e":"2024-07-10T13:57:09.877Z",
  "668e5845ef0ad97b3631d5c8":"2024-07-10T13:57:09.877Z",
  "668e5848ef0ad189c2d9f88c":"2024-07-10T13:57:09.877Z"
}

###
# @name POST new saved search

POST {{hostV1}}/saved-searches
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}
Content-Type: application/json

{
  "params":
  {
    "locationId":{{location_id}},
    "distance":{{distance}},
    "categoryId": {{category_id}}
  }
}

###
# @name DELETE existing saved search with saved search ID in the path

DELETE {{hostV1}}/saved-searches/{{saved_search_id}}
Authorisation-User-Email: {{user_email}}
Authorisation-User-Token: {{user_token}}
Authorization: {{user_authorization}}
Experiments: {{experiments}}
UDID: {{udid}}