package tools.rawDataFactory

import com.gumtree.mobile.api.conversations.models.RawAllConversationsResponse
import com.gumtree.mobile.api.conversations.models.RawAttachment
import com.gumtree.mobile.api.conversations.models.RawConversation
import com.gumtree.mobile.api.conversations.models.RawMessage
import com.gumtree.mobile.api.conversations.models.SendMessageUserRole
import tools.DataFactory

object RawCoreChatFactory {

    fun createRawAllConversationsResponse(
        count: Int = 2,
        conversationId: String = DataFactory.SOME_CONVERSATION_ID,
        adId: String = DataFactory.SOME_AD_ID,
        sellerId: String = DataFactory.SOME_SELLER_ID,
        buyerId: String = DataFactory.SOME_BUYER_ID,
        createdAt: String = DataFactory.SOME_CONVERSATION_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_DATE,
        unreadMessagesCount: Int = DataFactory.SOME_UNREAD_MESSAGE_COUNT,
        messages: List<RawMessage> = createRawMessages()
    ): RawAllConversationsResponse = RawAllConversationsResponse(
        conversations = buildList {
            repeat(count) { index ->
                add(
                    createRawConversation(
                        id = "$conversationId$index",
                        adId = "$adId$index",
                        sellerId = sellerId,
                        buyerId = buyerId,
                        createdAt = createdAt,
                        updatedAt = updatedAt,
                        unreadMessagesCount = unreadMessagesCount,
                        messages = messages
                    )
                )
            }
        }
    )

    fun createRawConversation(
        id: String = DataFactory.SOME_CONVERSATION_ID,
        adId: String = DataFactory.SOME_AD_ID,
        sellerId: String = DataFactory.SOME_SELLER_ID,
        buyerId: String = DataFactory.SOME_BUYER_ID,
        createdAt: String = DataFactory.SOME_CONVERSATION_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_DATE,
        unreadMessagesCount: Int = DataFactory.SOME_UNREAD_MESSAGE_COUNT,
        messages: List<RawMessage> = createRawMessages(),
    ) = RawConversation(
        id = id,
        adId = adId,
        sellerId = sellerId,
        buyerId = buyerId,
        createdAt = createdAt,
        updatedAt = updatedAt,
        unreadMessagesCount = unreadMessagesCount,
        messages = messages
    )

    fun createRawMessage(
        id: String = DataFactory.SOME_MESSAGE_ID,
        text: String = DataFactory.SOME_MESSAGE_TEXT,
        createdAt: String = DataFactory.SOME_CONVERSATION_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_DATE,
        userRole: SendMessageUserRole = SendMessageUserRole.BUYER,
        userId: Long = DataFactory.SOME_USER_ID.toLong(),
        attachments: List<RawAttachment>? = null
    ) = RawMessage(
        id = id,
        text = text,
        createdAt = createdAt,
        updatedAt = updatedAt,
        userRole = userRole,
        userId = userId,
        attachments = attachments
    )

    fun createRawMessages(
        count: Int = 2,
        messageId: String = DataFactory.SOME_MESSAGE_ID,
        text: String = DataFactory.SOME_MESSAGE_TEXT,
        createdAt: String = DataFactory.SOME_CONVERSATION_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_DATE,
        userRole: SendMessageUserRole = SendMessageUserRole.BUYER,
        userId: String = DataFactory.SOME_USER_ID,
    ): List<RawMessage> = buildList {
        repeat(count) { index ->
            add(
                createRawMessage(
                    id = "$messageId$index",
                    text = "$text$index",
                    createdAt = createdAt,
                    updatedAt = updatedAt,
                    userRole = userRole,
                    userId = userId.toLong()
                )
            )
        }
    }

    fun createRawConversationByOtherDate(
        id: String = DataFactory.SOME_CONVERSATION_ID,
        adId: String = DataFactory.SOME_AD_ID,
        sellerId: String = DataFactory.SOME_SELLER_ID,
        buyerId: String = DataFactory.SOME_BUYER_ID,
        createdAt: String = DataFactory.SOME_CONVERSATION_FORMAT_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_FORMAT_DATE,
        unreadMessagesCount: Int = DataFactory.SOME_UNREAD_MESSAGE_COUNT,
        messages: List<RawMessage> = createRawMessagesByOtherDate(),
    ) = RawConversation(
        id = id,
        adId = adId,
        sellerId = sellerId,
        buyerId = buyerId,
        createdAt = createdAt,
        updatedAt = updatedAt,
        unreadMessagesCount = unreadMessagesCount,
        messages = messages
    )

    fun createRawMessagesByOtherDate(
        count: Int = 2,
        messageId: String = DataFactory.SOME_MESSAGE_ID,
        text: String = DataFactory.SOME_MESSAGE_TEXT,
        createdAt: String = DataFactory.SOME_CONVERSATION_FORMAT_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_FORMAT_DATE,
        userRole: SendMessageUserRole = SendMessageUserRole.BUYER,
        userId: String = DataFactory.SOME_USER_ID,
    ): List<RawMessage> = buildList {
        repeat(count) { index ->
            add(
                createRawMessage(
                    id = "$messageId$index",
                    text = "$text$index",
                    createdAt = createdAt,
                    updatedAt = updatedAt,
                    userRole = userRole,
                    userId = userId.toLong()
                )
            )
        }
    }

    fun createRawAllConversationsResponseBySameAd(
        count: Int = 2,
        conversationId: String = DataFactory.SOME_CONVERSATION_ID,
        adId: String = DataFactory.SOME_AD_ID,
        sellerId: String = DataFactory.SOME_SELLER_ID,
        buyerId: String = DataFactory.SOME_BUYER_ID,
        createdAt: String = DataFactory.SOME_CONVERSATION_DATE,
        updatedAt: String = DataFactory.SOME_CONVERSATION_DATE,
        unreadMessagesCount: Int = DataFactory.SOME_UNREAD_MESSAGE_COUNT,
        messages: List<RawMessage> = createRawMessages()
    ): RawAllConversationsResponse = RawAllConversationsResponse(
        conversations = buildList {
            repeat(count) { index ->
                add(
                    createRawConversation(
                        id = "$conversationId$index",
                        adId = adId,
                        sellerId = sellerId,
                        buyerId = buyerId,
                        createdAt = createdAt,
                        updatedAt = updatedAt,
                        unreadMessagesCount = unreadMessagesCount,
                        messages = messages
                    )
                )
            }
        }
    )
}
