openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-search-suggestions
  license:
    name: MIT
tags:
  - name: BFF-Search-suggestions
    description: Mobile Apps BFF Search Suggestions API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    QueryString:
      in: query
      name: q
      description: The user's query string
      required: true
      schema:
        type: string
  schemas:
    SearchSuggestionsResults:
      type: array
      maxItems: 7
      items:
        $ref: '#/components/schemas/SearchSuggestion'
    SearchSuggestion:
      type: object
      properties:
        searchTerm:
          type: string
        prefix:
          type: string
        boldText:
          type: string
        suffix:
          type: string
        category:
          $ref: '#/components/schemas/SearchCategory'
        destination:
          $ref: '#/components/schemas/DestinationDto'
        params:
          type: string
      required:
        - searchTerm
        - prefix
        - boldText
        - suffix
        - category
        - destination
        - params
    SearchCategory:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
        analyticsEventData:
          $ref: '#/components/schemas/AnalyticsEventData'
      required:
        - route
        - type
    AnalyticsEventData:
      type: object
      properties:
        eventName:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
      required:
        - eventName
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
paths:
  /search-suggestions:
    get:
      summary: Get the Mobile apps search suggestion results
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/QueryString"
      responses:
        '200':
          description: Get the Search suggestions results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SearchSuggestionsResults"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"