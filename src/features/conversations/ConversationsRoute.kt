package com.gumtree.mobile.features.conversations

import com.gumtree.mobile.abTests.ClientExperiments.Companion.shouldUseCoreChatRepo
import com.gumtree.mobile.di.CONVERSATIONS_REPOSITORY_CAPI
import com.gumtree.mobile.di.CONVERSATIONS_REPOSITORY_CORE_CHAT
import com.gumtree.mobile.routes.AD_ID_PATH
import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ID_PATH
import com.gumtree.mobile.routes.USER_ID_PATH
import com.gumtree.mobile.utils.extensions.readBodyParamOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.readPagingParams
import com.gumtree.mobile.utils.extensions.readPathParam
import com.gumtree.mobile.utils.extensions.respondNoContent
import com.gumtree.mobile.utils.extensions.respondSuccess
import com.gumtree.mobile.utils.regexes.RegEx
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import org.koin.core.qualifier.named
import org.koin.ktor.ext.inject

const val CONVERSATIONS_PATH = "$API_V1_MAIN_PATH/conversations"
const val CONVERSATIONS_SCREEN_PATH = "$CONVERSATIONS_PATH/screen"
const val CONVERSATIONS_DELETE_PATH = "$CONVERSATIONS_PATH/{$ID_PATH}"
const val CONVERSATIONS_CREATE_PATH = "$CONVERSATIONS_PATH/{$AD_ID_PATH}"
const val CONVERSATIONS_UNREAD_MESSAGES_PATH = "$CONVERSATIONS_PATH/unread-messages/{$USER_ID_PATH}"

fun Route.conversationsRoute() {
    val capiRepository by inject<ConversationsRepository>(named(CONVERSATIONS_REPOSITORY_CAPI))
    val coreChatRepo by inject<ConversationsRepository>(named(CONVERSATIONS_REPOSITORY_CORE_CHAT))

    fun getRepository(headers: Headers): ConversationsRepository =
        if (shouldUseCoreChatRepo(headers)) coreChatRepo else capiRepository

    get(CONVERSATIONS_SCREEN_PATH) {
        val callHeaders: Headers = call.request.headers
        val userEmail: String = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL].toString()
        val (pageNumber, pageSize) = call.readPagingParams(
            defaultSize = ConversationsScreenUiConfiguration.DEFAULT_PAGE_SIZE,
        )

        val response = getRepository(callHeaders).readScreen(
            callHeaders,
            userEmail,
            pageNumber,
            pageSize,
        )

        call.respondSuccess(response)
    }

    get(CONVERSATIONS_UNREAD_MESSAGES_PATH) {
        val callHeaders: Headers = call.request.headers
        val userEmail: String = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL].toString()
        val userId = call.readPathParam(USER_ID_PATH, RegEx.NUMERICAL_ID)

        val response = getRepository(callHeaders).getUnreadMessageCount(userId, userEmail)
        call.respondSuccess(response)
    }

    delete(CONVERSATIONS_DELETE_PATH) {
        val callHeaders: Headers = call.request.headers
        val userEmail: String = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL].toString()
        val conversationId = call.readPathParam(ID_PATH, RegEx.UDID)

        getRepository(callHeaders).delete(callHeaders, userEmail, conversationId)
        call.respondNoContent()
    }

    post(CONVERSATIONS_CREATE_PATH) {
        val callHeaders: Headers = call.request.headers
        val userEmail: String = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL].toString()
        val adId = call.readPathParam(AD_ID_PATH, RegEx.NUMERICAL_ID)
        val request = call.readBodyParamOrThrowBadRequest<CreateConversationRequest>()

        val response = getRepository(callHeaders).create(
            callHeaders,
            userEmail,
            adId,
            request,
        )

        call.respondSuccess(response)
    }
}
