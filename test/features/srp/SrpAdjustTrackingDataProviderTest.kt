package features.srp

import api.capi.models.RawCapiAd
import common.AdjustTrackingData
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory

class SrpAdjustTrackingDataProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return correct tracking data for 3 or more ads when page is zero`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAds(5) }
            Given { stubPage("0") }
            When { getScreenTrackingData() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_SCREEN_EVENT) }
            Then { checkProductsParameter("[0, 1, 2]") }
        }
    }

    @Test
    fun `should return correct tracking data for less than 3 ads when page is zero`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAds(2) }
            Given { stubPage("0") }
            When { getScreenTrackingData() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_SCREEN_EVENT) }
            Then { checkProductsParameter("[0, 1]") }
        }
    }

    @Test
    fun `should return correct tracking data for empty ad list when page is zero`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAds(0) }
            Given { stubPage("0") }
            When { getScreenTrackingData() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_SCREEN_EVENT) }
            Then { checkProductsParameter(emptyList<String>().toString()) }
        }
    }

    @Test
    fun `should return correct tracking data for null ad list when page is zero`() = runTest {
        runUnitTest(robot) {
            Given { stubNullRawAds() }
            Given { stubPage("0") }
            When { getScreenTrackingData() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_SCREEN_EVENT) }
            Then { checkProductsParameter(emptyList<String>().toString()) }
        }
    }

    @Test
    fun `should return null when page is not zero`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAds(5) }
            Given { stubPage("1") }
            When { getScreenTrackingData() }
            Then { checkNullTrackingData() }
        }
    }

    @Test
    fun `should return correct call seller event tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAd() }
            When { getCallSellerEvent() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_PHONE_EVENT) }
            Then { checkListingIdParameter("test-ad-id") }
        }
    }

    @Test
    fun `should return correct contact link event tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAd() }
            When { getContactLinkEvent() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_CONTACT_LINK_EVENT) }
            Then { checkListingIdParameter("test-ad-id") }
        }
    }

    @Test
    fun `should return correct start chat event tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAd() }
            When { getStartChatEvent() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_START_CHAT) }
            Then { checkListingIdParameter("test-ad-id") }
        }
    }

    @Test
    fun `should handle empty ad id in tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithEmptyId() }
            When { getCallSellerEvent() }
            Then { checkTrackingDataToken(SrpAdjustTrackingDataProvider.TOKEN_PHONE_EVENT) }
            Then { checkListingIdParameter("") }
        }
    }

    private class Robot : BaseRobot {
        private var actualTrackingData: AdjustTrackingData? = null
        private var rawAds: List<RawCapiAd>? = null
        private var rawAd: RawCapiAd? = null
        private var page: String = "0"

        private val testSubject = SrpAdjustTrackingDataProvider()

        fun stubRawAds(amount: Int) {
            rawAds = List(amount) { RawCapiAdsFactory.createRawCapiAd(adId = it.toString()) }
        }

        fun stubNullRawAds() {
            rawAds = null
        }

        fun stubPage(pageNumber: String) {
            page = pageNumber
        }

        fun stubRawAd() {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adId = "test-ad-id")
        }

        fun stubRawAdWithEmptyId() {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adId = "")
        }

        fun getScreenTrackingData() {
            actualTrackingData = testSubject.getScreenTrackingData(rawAds, page)
        }

        fun getCallSellerEvent() {
            actualTrackingData = testSubject.getCallSellerEvent(rawAd!!)
        }

        fun getContactLinkEvent() {
            actualTrackingData = testSubject.getContactLinkEvent(rawAd!!)
        }

        fun getStartChatEvent() {
            actualTrackingData = testSubject.getStartChatEvent(rawAd!!)
        }

        fun checkTrackingDataToken(expected: String) {
            assertEquals(expected, actualTrackingData?.eventToken)
        }

        fun checkProductsParameter(expected: String) {
            assertEquals(
                expected,
                actualTrackingData?.parameters?.get(SrpAdjustTrackingDataProvider.LISTING_IDS_KEY)
            )
        }

        fun checkListingIdParameter(expected: String) {
            assertEquals(
                expected,
                actualTrackingData?.parameters?.get(SrpAdjustTrackingDataProvider.LISTING_ID_KEY)
            )
        }

        fun checkNullTrackingData() {
            assertNull(actualTrackingData)
        }
    }
}
