package features.reportChat

import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenRequestBody
import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenResponse
import com.gumtree.mobile.api.reportChat.api.ReportChatApi
import com.gumtree.mobile.api.reportChat.bodies.RawReportChatReason
import com.gumtree.mobile.api.reportChat.bodies.ReportChatRequestBody
import com.gumtree.mobile.api.reportChat.ReportChatHeadersProvider
import com.gumtree.mobile.api.reportChat.models.RawAlreadyReported
import com.gumtree.mobile.api.reportChat.models.RawConversationReportId
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import com.gumtree.mobile.features.reportChat.ReportChatService
import io.ktor.server.plugins.BadRequestException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.UserServiceDataFactory
import tools.runUnitTest
import tools.runUnitTestForException

class ReportChatServiceTest {
    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should delegate reportChat call to the ReportChatApi`() {
        runUnitTest(robot) {
            Given { stubReportChatRequestBody() }
            Given { stubAuthHeader(DataFactory.SOME_TOKEN) }
            Given { stubReportChatToSucceed() }
            When { reportChat() }
            Then { verifyReportChatCalled() }
        }
    }

    @Test
    fun `should delegate is chat reported already call to the ReportChatApi`() {
        runUnitTest(robot) {
            Given { stubIsChatAlreadyReportedApiToSucceed() }
            Given { stubAuthHeader(DataFactory.SOME_TOKEN) }
            When { callIsChatAlreadyReported() }
            Then { verifyIsChatAlreadyReportedIsCalled() }
        }
    }

    @Test
    fun `should delegate generateCoreChatJwt call to the CoreChatAuthApi`() {
        runUnitTest(robot) {
            Given { stubTokenRequestBody() }
            Given { stubGenerateCoreChatJwtToSucceed() }
            When { generateCoreChatJwt() }
            Then { verifyGenerateCoreChatJwtCalled() }
            Then { checkTokenResponse() }
        }
    }

    @Test
    fun `should delegate getUserDetails call to the UserServiceApi`() {
        runUnitTest(robot) {
            Given { stubGetUserDetailsToSucceed() }
            When { getUserDetails() }
            Then { verifyGetUserDetailsCalled() }
            Then { checkUserDetailsResponse() }
        }
    }

    @Test
    fun `should propagate exception from reportChat`() {
        runUnitTestForException(robot, RuntimeException::class) {
            Given { stubReportChatRequestBody() }
            Given { stubAuthHeader(DataFactory.SOME_TOKEN) }
            Given { stubReportChatToFail() }
            When { reportChat() }
            Then { verifyReportChatCalled() }
        }
    }

    @Test
    fun `should propagate exception from isChatAlreadyReported`() {
        runUnitTestForException(robot, BadRequestException::class) {
            Given { stubIsChatAlreadyReportedApiToFail() }
            Given { stubAuthHeader(DataFactory.SOME_TOKEN) }
            When { callIsChatAlreadyReported() }
            Then { verifyIsChatAlreadyReportedIsCalled() }
        }
    }

    @Test
    fun `should propagate exception from generateCoreChatJwt`() {
        runUnitTestForException(robot, RuntimeException::class) {
            Given { stubTokenRequestBody() }
            Given { stubGenerateCoreChatJwtToFail() }
            When { generateCoreChatJwt() }
            Then { verifyGenerateCoreChatJwtCalled() }
        }
    }

    @Test
    fun `should propagate exception from getUserDetails`() {
        runUnitTestForException(robot, RuntimeException::class) {
            Given { stubGetUserDetailsToFail() }
            When { getUserDetails() }
            Then { verifyGetUserDetailsCalled() }
        }
    }

    private class Robot : BaseRobot {
        private val mockReportChatApi: ReportChatApi = mockk(relaxed = true)
        private val mockCoreChatAuthApi: CoreChatAuthApi = mockk(relaxed = true)
        private val mockUserServiceApi: UserServiceApi = mockk(relaxed = true)
        private lateinit var testSubject: ReportChatService
        private lateinit var reportChatRequestBody: ReportChatRequestBody
        private lateinit var tokenRequestBody: CoreChatStreamTokenRequestBody
        private lateinit var authHeader: Map<String, String>
        private var tokenResponse: CoreChatStreamTokenResponse? = null
        private var userDetailsResponse: RawUserServiceUserDetails? = null

        override fun setup() {
            testSubject = ReportChatService(
                reportChatApi = mockReportChatApi,
                coreChatAuthApi = mockCoreChatAuthApi,
                userServiceApi = mockUserServiceApi
            )
        }

        fun stubReportChatRequestBody() {
            reportChatRequestBody = ReportChatRequestBody(
                converseeUserId = DataFactory.SOME_USER_ID.toInt(),
                conversationId = DataFactory.SOME_CONVERSATION_ID,
                advertId = DataFactory.SOME_AD_ID.toInt(),
                reasons = listOf(RawReportChatReason.FRAUD_SCAM),
                comment = DataFactory.SOME_COMMENT
            )
        }

        fun stubAuthHeader(token: String) {
            authHeader = ReportChatHeadersProvider.createAuthHeader(token)
        }

        fun stubTokenRequestBody() {
            tokenRequestBody = CoreChatStreamTokenRequestBody(
                email = DataFactory.SOME_USER_EMAIL,
                userId = DataFactory.SOME_USER_ID
            )
        }

        fun stubReportChatToSucceed() {
            coEvery {
                mockReportChatApi.reportChat(any(), any())
            } returns RawConversationReportId(DataFactory.SOME_CONVERSATION_ID)
        }

        fun stubReportChatToFail() {
            coEvery {
                mockReportChatApi.reportChat(any(), any())
            } throws RuntimeException("Report chat failed")
        }

        fun stubIsChatAlreadyReportedApiToSucceed() {
            coEvery {
                mockReportChatApi.isChatAlreadyReported(any(), any())
            } returns RawAlreadyReported(true)
        }

        fun stubIsChatAlreadyReportedApiToFail() {
            coEvery {
                mockReportChatApi.isChatAlreadyReported(any(), any())
            } throws BadRequestException("Chat already reported check failed")
        }

        fun stubGenerateCoreChatJwtToSucceed() {
            coEvery {
                mockCoreChatAuthApi.generateCoreChatJwt(any())
            } returns CoreChatStreamTokenResponse(DataFactory.SOME_TOKEN)
        }

        fun stubGenerateCoreChatJwtToFail() {
            coEvery {
                mockCoreChatAuthApi.generateCoreChatJwt(any())
            } throws RuntimeException("JWT generation failed")
        }

        fun stubGetUserDetailsToSucceed() {
            coEvery {
                mockUserServiceApi.getUserDetails(DataFactory.SOME_USER_EMAIL)
            } returns UserServiceDataFactory.createRawUserDetails()
        }

        fun stubGetUserDetailsToFail() {
            coEvery {
                mockUserServiceApi.getUserDetails(any())
            } throws RuntimeException("Get user details failed")
        }

        fun reportChat() = runTest {
            testSubject.reportChat(reportChatRequestBody, authHeader)
        }

        fun callIsChatAlreadyReported() = runTest {
            testSubject.isChatAlreadyReported(DataFactory.SOME_CONVERSATION_ID, authHeader)
        }

        fun generateCoreChatJwt() = runTest {
            tokenResponse = testSubject.generateCoreChatJwt(tokenRequestBody)
        }

        fun getUserDetails() = runTest {
            userDetailsResponse = testSubject.getUserDetails(DataFactory.SOME_USER_EMAIL)
        }

        fun verifyReportChatCalled() {
            coVerify { mockReportChatApi.reportChat(reportChatRequestBody, authHeader) }
        }

        fun verifyIsChatAlreadyReportedIsCalled() {
            coVerify { mockReportChatApi.isChatAlreadyReported(DataFactory.SOME_CONVERSATION_ID, authHeader) }
        }

        fun verifyGenerateCoreChatJwtCalled() {
            coVerify { mockCoreChatAuthApi.generateCoreChatJwt(tokenRequestBody) }
        }

        fun verifyGetUserDetailsCalled() {
            coVerify { mockUserServiceApi.getUserDetails(DataFactory.SOME_USER_EMAIL) }
        }

        fun checkTokenResponse() {
            assertEquals(DataFactory.SOME_TOKEN, tokenResponse?.token)
        }

        fun checkUserDetailsResponse() {
            assertEquals(DataFactory.SOME_USER_ID, userDetailsResponse?.userId)
            assertEquals(DataFactory.SOME_USER_EMAIL, userDetailsResponse?.userEmail)
        }
    }
}
