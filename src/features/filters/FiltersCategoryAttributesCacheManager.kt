package com.gumtree.mobile.features.filters

import api.capi.models.RawCapiAd
import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.utils.extensions.data.toFilterCategoryAttributeDto
import com.gumtree.mobile.utils.handleRequestOrNull

/**
 * The class manages the fetching and caching of the Categories API Attributes related to the SRP and Filters screen
 */
class FiltersCategoryAttributesCacheManager(
    private val filtersService: FiltersService,
    private val filtersCategoryAttributesCache: FiltersCategoryAttributesCache,
) {
    // NOTE: We remove some of the Categories API attributes by excluding them manually on Mobile BFF level.
    // Ideally the Categories API should be updated with these changes, but unfortunately at the moment it's not possible.
    // Once day we should remove forcedExcludedAttributes Set and the usage of it
    private val forcedExcludedAttributes: Set<String> by lazy {
        setOf(
            RawCapiAd.ATTRIBUTE_JOBS_RECRUITER_TYPE,
            RawCapiAd.ATTRIBUTE_JOBS_HOURS,
            RawCapiAd.ATTRIBUTE_JOBS_LEVEL,
            RawCapiAd.ATTRIBUTE_JOBS_LANGUAGE,
            RawCapiAd.ATTRIBUTE_JOBS_COURSE,
            RawCapiAd.ATTRIBUTE_PROPERTY_PETS_ALLOWED,
            RawCapiAd.ATTRIBUTE_PROPERTY_ALL_BILLS_INCLUDED,
            RawCapiAd.ATTRIBUTE_PROPERTY_COUNCIL_TAX_INCLUDED,
        )
    }

    suspend fun updateFiltersCategoryAttributesCacheIfRequired() {
        if (!filtersCategoryAttributesCache.hasValidCache() || !filtersCategoryAttributesCache.isNotEmpty()) {
            requestCategoriesAttributes()
        }
    }

    suspend fun requestCategoriesAttributes(): List<CategoryAttributesCache.Data>? {
        return handleRequestOrNull {
            filtersService.getAllCategoriesAttributes()
                .filter {
                    it.srp?.filters?.isNotEmpty() ?: false &&
                        it.categoryIds.isNotEmpty() &&
                        !forcedExcludedAttributes.contains(it.name)
                } // filter only the SRP attributes, which are also Filters screen attributes and do have some filter values list
                .map { it.toFilterCategoryAttributeDto() }
                .apply { filtersCategoryAttributesCache.data = this }
        }
    }
}
