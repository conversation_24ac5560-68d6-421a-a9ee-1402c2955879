package features.forgotPassword

import com.gumtree.mobile.features.forgotPassword.FORGOT_PASSWORD_PATH
import com.gumtree.mobile.features.forgotPassword.ForgotPasswordRepository
import com.gumtree.mobile.features.forgotPassword.ForgotPasswordRequest
import com.gumtree.mobile.features.forgotPassword.RESET_PASSWORD_PATH
import com.gumtree.mobile.features.forgotPassword.ResetPasswordRequest
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.plugins.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.jsonHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runUnitTest

class ForgotPasswordRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete POST forgot password request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateForgotPasswordResponse() }
            When { postForgotPassword(DataFactory.SOME_USER_EMAIL) }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete POST forgot password request with BadRequest exception if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateForgotPasswordError(BadRequestException("Invalid username")) }
            When { postForgotPassword("test123aa.com") }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST reset password request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryResetPasswordResponse() }
            When {
                postResetPassword(
                    DataFactory.SOME_USER_SECRET,
                    DataFactory.SOME_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete POST reset password request with BadRequestException exception if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateResetPasswordError(BadRequestException("Invalid user secret")) }
            When {
                postResetPassword(
                    "invalid",
                    DataFactory.SOME_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: ForgotPasswordRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryCreateForgotPasswordResponse() {
            coEvery {
                repository.createForgotPassword(any(), any())
            } returns Unit
        }

        fun stubRepositoryResetPasswordResponse() {
            coEvery {
                repository.createResetPassword(any(), any())
            } returns Unit
        }

        fun stubRepositoryCreateForgotPasswordError(error: Throwable) {
            coEvery {
                repository.createForgotPassword(any(), any())
            } throws error
        }

        fun stubRepositoryCreateResetPasswordError(error: Throwable) {
            coEvery {
                repository.createResetPassword(any(), any())
            } throws error
        }

        suspend fun postForgotPassword(userName: String) {
            actualResponse = client.post(FORGOT_PASSWORD_PATH) {
                setBody(
                    ForgotPasswordRequest(userName = userName)
                )
                contentType(ContentType.Application.Json)
            }
        }

        suspend fun postResetPassword(
            userSecret: String,
            resetToken: String
        ) {
            actualResponse = client.post(RESET_PASSWORD_PATH) {
                setBody(
                    ResetPasswordRequest(
                        userSecret = userSecret,
                        resetToken = resetToken
                    )
                )
                contentType(ContentType.Application.Json)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }

}