package api.capi.models;

import com.google.appengine.repackaged.org.apache.http.util.TextUtils;
import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Root(name = "billing-address", strict = false)
public class RawBillingAddress {
    @Element(name = "full-address", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mFullAddress;

    @Element(name = "street", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mStreet;

    @Element(name = "additional-delivery-info", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mAdditionalDeliveryInfo;

    @Element(name = "city", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mCity;

    @Element(name = "state", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mState;

    @Element(name = "zip-code", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mZipCode;

    @Element(name = "country", required = false)
    @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
    public String mCountry;

    public RawBillingAddress() {}

    public RawBillingAddress(String street, String additionalDeliveryInfo, String city, String state, String zipCode, String country) {
        mStreet = street;
        mAdditionalDeliveryInfo = additionalDeliveryInfo;
        mCity = city;
        mState = state;
        mZipCode = zipCode;
        mCountry = country;

        StringBuilder sb = new StringBuilder();
        if (!TextUtils.isEmpty(mStreet)) {
            sb.append(mStreet).append(" ");
        }
        if (!TextUtils.isEmpty(mAdditionalDeliveryInfo)) {
            sb.append(mAdditionalDeliveryInfo).append(" ");
        }
        if (!TextUtils.isEmpty(mCity)) {
            sb.append(mCity).append(" ");
        }
        if (!TextUtils.isEmpty(mState)) {
            sb.append(mState).append(" ");
        }
        if (!TextUtils.isEmpty(mZipCode)) {
            sb.append(mZipCode).append(" ");
        }
        if (!TextUtils.isEmpty(mCountry)) {
            sb.append(mCountry);
        }
        mFullAddress = sb.toString();
    }
}