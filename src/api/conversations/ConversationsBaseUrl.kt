package com.gumtree.mobile.api.conversations

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val CONVERSATIONS_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "CONVERSATIONS_API_BASE_URL"
const val CONVERSATIONS_API_QA_PORT = 8089

class ConversationsBaseUrl : ApiBaseUrl() {

    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(
            CONVERSATIONS_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY,
        ).toBaseUrl()
    } // see helm-chart/values.yaml
    override val localUrl: BaseUrl by lazy { LOCAL_HOST_BASE_URL.toBaseUrl() }
    override val localPort: Int = CONVERSATIONS_API_QA_PORT
}
