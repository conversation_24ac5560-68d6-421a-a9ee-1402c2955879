@file:Suppress("MaximumLineLength")

package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.capi.models.RawCapiAdStatus
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.responses.ContentNotAvailableException
import com.gumtree.mobile.utils.extensions.optionalPluralSuffix

/**
 * Defines the Vip screen UI components configurations
 */

object VipScreenUiConfiguration {

    const val VIP_POSTED_SINCE_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    const val VIP_POSTED_SINCE_NOW_TEXT = "Now"
    const val VIP_HPI_GET_FULL_HISTORY_TEXT = "Get full history check"
    const val POSTED = "Posted"
    const val AGO = "ago"
    const val PRIVATE_SELLER = "Private seller"
    const val POSTING_SINCE = "Posting since"
    const val MEMBER_SINCE = "Member since"
    const val TRADE_SELLER = "Trade seller"
    const val PRICE = "Price:"
    const val PLEASE_VIEW_THIS_AD = "Please view this ad:"
    const val TOOLBAR_SHARE_ACTION_TITLE = "Share"
    const val TOOLBAR_REPORT_AD_ACTION_TITLE = "Report Ad"
    const val HPI_GET_FULL_HISTORY_URL = "https://gumtree.hpicheck.com/product-selection"
    const val VIP_PAGE_TYPE = "vip"
    const val VIP_PARTNERSHIP_SLOT_ID = "clickout1"
    const val VISIT_WEBSITE = "Visit website"

    /**
     * The minimum Vip gallery images count before we attach the "gallery" GAM Advert item (if the image gallery has less images than that number the GAM advert will not be attached)
     */
    const val IMAGE_GALLERY_GAM_ADVERT_ATTACH_LIMIT = 2

    /**
     * The Vip similar items limit
     */
    const val SIMILAR_ITEMS_DEFAULT_LIMIT = 6

    /**
     * The text displayed on the Vip description title/label
     */
    const val DESCRIPTION_LABEL_TEXT = "Description"

    /**
     * The text to be displayed on the VIP description title for the jobs category
     */
    const val JOBS_DESCRIPTION_LABEL_TEXT = "About the job"

    /**
     * The text displayed on the Vip Map location type
     */
    const val LOCATION_TYPE_LABEL_TEXT = "Location is approximate"

    /**
     * The aspect ratio for the map preview, this is only used for static maps on Android
     */
    const val MAP_ASPECT_RATIO: Double = (327.00 / 140.00)

    /**
     * The zoom for the map preview, this is only used for static maps on Android (between 0 - 21)
     */
    const val STATIC_MAP_ZOOM: Int = 14

    /**
     * The text displayed on the Vip similar items title
     */
    const val SIMILAR_ITEMS_TEXT = "Similar items"

    /**
     * The text displayed on the Vip similar jobs title
     */
    const val SIMILAR_JOBS_TEXT = "Similar jobs"

    /**
     * The text displayed as prefix on the Vip Ad ID info
     */
    const val AD_ID_PREXIF_TEXT = "Ad id:"

    /**
     * The text displayed as prefix on the VAT info
     */
    const val VAT_PREXIF_TEXT = "VAT Reg No:"

    /**
     * The default text for vip start conversation
     */
    const val START_CONVERSATION_CANNED_MESSAGE = "Is this still available?"

    /**
     * The default text for vip start conversation within Community or Services categories
     */
    const val I_AM_INTERESTED_CANNED_MESSAGE = "Hey, I'm interested"

    /**
     * The default text for vip continue conversation
     */
    const val CONTINUE_CONVERSATION_MESSAGE = "Continue conversation"

    /**
     * The default text for vip continue conversation
     */
    const val CTA_REQUEST_QUOTE = "Get a quote"

    /**
     * The title for vip start conversation button
     */
    const val START_CONVERSATION_BUTTON_TITLE = "Send"

    /**
     * The default text of vip email for motor ads
     */
    const val CTA_MESSAGE = "Message"

    /**
     * Control the visibility of the Toolbar share action
     */
    const val IS_TOOLBAR_SHARE_ACTION_HIDDEN = false

    /**
     * Control the visibility of the Toolbar report Ad action
     */
    const val IS_TOOLBAR_REPORT_AD_HIDDEN = true

    const val SPECIFICATIONS_ROW_TITLE = "Specifications"
    const val SPECIFICATIONS_OVERVIEW_TITLE = "Overview"
    const val SPECIFICATIONS_PERFORMANCE_TITLE = "Performance"
    const val SPECIFICATIONS_RUNNING_COST_TITLE = "Running Cost"
    const val SPECIFICATIONS_SAFETY_SECURITY_TITLE = "Safety & Security"
    const val SPECIFICATIONS_DRIVING_CONVENIENCE_TITLE = "Driving Convenience"
    const val SPECIFICATIONS_INTERIOR_TITLE = "Interior"
    const val SPECIFICATIONS_EXTERIOR_TITLE = "Exterior"

    const val HPI_ROW_TITLE_TEXT = "Vehicle history"
    const val HPI_ROW_COLLAPSABLE_ITEM_COUNT = 4
    const val HPI_DESCRIPTION_SUMMARY_TEXT = "Our vehicle history check is provided for guidance only. Please check all details with the seller before purchasing."
    const val HPI_DESCRIPTION_QUESTION_TEXT = "What is an accurate  HPI Report"
    const val HPI_DESCRIPTION_ANSWER_TEXT = "Buy the full report for guaranteed data accuracy, outstanding finance, valuation, mileage check & VIN match for £9.99 (normally £19.99)"

    /**
     * The title text displayed above the Vip property location map
     */
    const val PROPERTY_LOCATION_LABEL_TEXT = "Property location"

    /**
     * Some of the specifications require units which are not coming from the BE
     */
    val SPECIFICATION_UNITS = mapOf(
        RawCapiAd.ATTRIBUTE_VEHICLE_MILEAGE to "miles",
        RawCapiAd.ATTRIBUTE_VEHICLE_LUGGAGE_CAPACITY to "litres",
        RawCapiAd.ATTRIBUTE_VEHICLE_ENGINE_POWER to "bhp",
        RawCapiAd.ATTRIBUTE_VEHICLE_ENGINE_SIZE to "cc",
        RawCapiAd.ATTRIBUTE_VEHICLE_BROCHURE_ENGINE_SIZE to "L",
        RawCapiAd.ATTRIBUTE_VEHICLE_TOP_SPEED to "mph",
        RawCapiAd.ATTRIBUTE_VEHICLE_ACCELERATION_0_62 to "seconds",
        RawCapiAd.ATTRIBUTE_VEHICLE_AVERAGE_MPG to "mpg",
        RawCapiAd.ATTRIBUTE_VEHICLE_FUEL_CAPACITY to "litres",
        RawCapiAd.ATTRIBUTE_VEHICLE_URBAN_MPG to "mpg",
        RawCapiAd.ATTRIBUTE_VEHICLE_EXTRA_URBAN_MPG to "mpg",
        RawCapiAd.ATTRIBUTE_VEHICLE_EMISSION to "g/km",
    )

    fun getWeekText(timeAgo: Long) = "$POSTED $timeAgo ${"week".optionalPluralSuffix(timeAgo)} $AGO"

    fun getDayText(timeAgo: Long) = "$POSTED $timeAgo ${"day".optionalPluralSuffix(timeAgo)} $AGO"

    fun getHourText(timeAgo: Long) = "$POSTED $timeAgo ${"hour".optionalPluralSuffix(timeAgo)} $AGO"

    fun getMinuteText(timeAgo: Long) = "$POSTED $timeAgo ${"minute".optionalPluralSuffix(timeAgo)} $AGO"

    fun getSecondText() = VIP_POSTED_SINCE_NOW_TEXT

    @Suppress("unused")
    fun getSellerTypeText(isPrivate: Boolean) = EMPTY_STRING

    fun proceedWithAdPresentationOrThrowError(rawStatus: RawCapiAdStatus) {
        val adStatus = AdStatus.fromString(rawStatus.value)
        if (adStatus == AdStatus.DELETED || adStatus == AdStatus.DELETED_CS) {
            throw ContentNotAvailableException()
        }
    }
}
