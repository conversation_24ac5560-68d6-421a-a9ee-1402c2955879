package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.routes.DestinationDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed interface LinkCardDto : UiItem {
    @SerialName("text")
    val text: String

    @SerialName("destination")
    val destination: DestinationDto

    /**
     * Data model about the Settings screen link Views
     */
    @Serializable
    @SerialName("SETTING_LINK_CARD")
    data class Setting(
        override val text: String,
        override val destination: DestinationDto,
    ) : LinkCardDto

    @Serializable
    @SerialName("TEXT_LINK_CARD")
    data class Text(
        override val text: String,
        override val destination: DestinationDto,
    ) : LinkCardDto

    /**
     * Data model about Filters screen LinkCard
     */
    @Serializable
    @SerialName("FILTER_LINK_CARD")
    data class Filter(
        override val text: String,
        override val destination: DestinationDto,
        @SerialName("params")
        val params: List<String>,
        @SerialName("selectedValue")
        val selectedValue: List<Map<String, String>>? = null,
        @SerialName("subTitle")
        val subTitle: String?,
        @SerialName("changeAction")
        val changeAction: Action?,
    ) : LinkCardDto {
        enum class Action {
            REFRESH,
            RESET,
        }
    }

    @Serializable
    @SerialName("FILTER_ATTRIBUTE_LINK_CARD")
    data class FilterAttribute(
        override val text: String,
        override val destination: DestinationDto,
        @SerialName("isSelected")
        val isSelected: Boolean,
        @SerialName("selectedValue")
        val selectedValue: String? = null,
    ) : LinkCardDto
}
