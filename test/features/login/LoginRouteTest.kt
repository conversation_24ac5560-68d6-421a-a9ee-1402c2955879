package features.login

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.features.login.AuthenticationType
import com.gumtree.mobile.features.login.LOGIN_EXCHANGE_PATH
import com.gumtree.mobile.features.login.LOGIN_PATH
import com.gumtree.mobile.features.login.LOGOUT_PATH
import com.gumtree.mobile.features.login.LoginRepository
import com.gumtree.mobile.features.login.LogoutRequest
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiBodyParams
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.plugins.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.jsonHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runUnitTest
import kotlin.test.assertEquals
import tools.layoutsDataFactory.UserLoginDataFactory
import tools.routes.runRouteTestForException

class LoginRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete POST new user login request with success`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateLoginResponse() }
            When {
                createUserLogin(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    null,
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete POST new user social login request with success`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateLoginResponse() }
            When {
                createUserLogin(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    AuthenticationType.FACEBOOK.toString(),
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete POST new user login request with BadRequest exception if user email NOT provided`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                createUserLogin(
                    null,
                    DataFactory.SOME_USER_SECRET,
                    null,
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST new user login request with BadRequest exception if user secret NOT provided`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                createUserLogin(
                    DataFactory.SOME_USER_EMAIL,
                    null,
                    null,
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST new user login request with BadRequest status code if threat metrix header is empty`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                createUserLogin(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    AuthenticationType.GOOGLEID.toString(),
                    EMPTY_STRING,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST new user login request with BadRequest error if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateResponseError(BadRequestException("Wrong credentials")) }
            When {
                createUserLogin(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    null,
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST user logout request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                deleteUserLogin(
                    DataFactory.SOME_TOKEN,
                    DataFactory.SOME_UDID,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_PUSH_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete POST user logout request with status code UnAuthorized if user token NOT provided`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                deleteUserLogin(
                    null,
                    DataFactory.SOME_UDID,
                    EMPTY_STRING,
                    DataFactory.SOME_PUSH_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Unauthorized) }
        }
    }

    @Test
    fun `should complete POST user logout request with status code BadRequest if user ID NOT provided`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                deleteUserLogin(
                    DataFactory.SOME_TOKEN,
                    DataFactory.SOME_UDID,
                    EMPTY_STRING,
                    DataFactory.SOME_PUSH_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST user logout request with success and status code NoContent if user push token NOT provided`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                deleteUserLogin(
                    DataFactory.SOME_TOKEN,
                    DataFactory.SOME_UDID,
                    DataFactory.SOME_USER_ID,
                    EMPTY_STRING
                )
            }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete POST user logout request with BadRequest error if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryDeleteResponseError(BadRequestException("Invalid token")) }
            When {
                deleteUserLogin(
                    DataFactory.SOME_TOKEN,
                    DataFactory.SOME_UDID,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_PUSH_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete GET login exchange request with success`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryExchangeResponse() }
            When {
                loginExchange(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_TOKEN,
                    DataFactory.SOME_USER_AUTHORIZATION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET login exchange request with status code UnAuthorized if user email NOT provided`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                loginExchange(
                    null,
                    DataFactory.SOME_TOKEN,
                    null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Unauthorized) }
        }
    }

    @Test
    fun `should complete GET login exchange request with status code UnAuthorized if user token NOT provided`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                loginExchange(
                    DataFactory.SOME_USER_EMAIL,
                    null,
                    null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Unauthorized) }
        }
    }

    @Test
    fun `should complete GET login exchange request with BadRequest error if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryExchangeResponseError(BadRequestException("Wrong user")) }
            When {
                loginExchange(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_TOKEN,
                    DataFactory.SOME_USER_AUTHORIZATION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: LoginRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryCreateLoginResponse() {
            coEvery { repository.create(any(), any(), any(), any(), any(), any()) } returns UserLoginDataFactory.create()
        }

        fun stubRepositoryCreateResponseError(error: Throwable) {
            coEvery { repository.create(any(), any(), any(), any(), any(), any()) } throws error
        }

        fun stubRepositoryDeleteResponseError(error: Throwable) {
            coEvery { repository.delete(any(), any(), any()) } throws error
        }

        fun stubRepositoryExchangeResponse() {
            coEvery { repository.exchange(any(), any(), any()) } returns UserLoginDataFactory.create()
        }

        fun stubRepositoryExchangeResponseError(error: Throwable) {
            coEvery { repository.exchange(any(), any(), any()) } throws error
        }

        suspend fun createUserLogin(
            userName: String?,
            userSecret: String?,
            authenticationType: String?,
            threatMetrixSessionId: String,
        ) {
            actualResponse = client.post(LOGIN_PATH) {
                headers.append(ApiHeaderParams.THREATMETRIX_SESSION, threatMetrixSessionId)
                setBody(
                    listOf(
                        ApiBodyParams.USER_NAME_BODY to userName,
                        ApiBodyParams.USER_SECRET_BODY to userSecret,
                        ApiBodyParams.AUTHENTICATION_TYPE to authenticationType,
                    ).formUrlEncode()
                )
                contentType(ContentType.Application.FormUrlEncoded)
            }
        }

        suspend fun deleteUserLogin(
            userToken: String?,
            udid: String,
            userId: String,
            pushToken: String,
        ) {
            actualResponse = client.post(LOGOUT_PATH) {
                userToken?.let { headers { append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, it) } }
                headers { append(ApiHeaderParams.UDID, udid) }
                setBody(
                    LogoutRequest(
                        userId = userId,
                        pushToken = pushToken,
                    )
                )
                contentType(ContentType.Application.Json)
            }
        }

        suspend fun loginExchange(
            userEmail: String?,
            userToken: String?,
            userAuthorization: String?,
        ) {
            actualResponse = client.get(LOGIN_EXCHANGE_PATH) {
                userEmail?.let { headers { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, it) } }
                userToken?.let { headers { append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, it) } }
                userAuthorization?.let { headers { append(ApiHeaderParams.AUTHORISATION, it) } }
                contentType(ContentType.Application.Json)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }
    }
}
