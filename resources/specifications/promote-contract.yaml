openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-promote
  license:
    name: MIT
tags:
  - name: BFF-PROMOTE
    description: Mobile Apps BFF Promote API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  securitySchemes:
    ApiKeyAuthUserEmail:
      type: apiKey
      description: Security API key to authorize requests with an user email
      in: header
      name: Authorisation-User-Email
    ApiKeyAuthUserToken:
      type: apiKey
      description: Security API key to authorize requests with an user token
      in: header
      name: Authorisation-User-Token
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    AdId:
      in: query
      name: adId
      description: Id of the ad that is being promoted
      required: true
      schema:
        type: string
  schemas:
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
    ScreenResponse:
      type: object
      properties:
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        stickyBar:
          $ref: '#/components/schemas/StickyBar'
      required:
        - portraitData
    StickyBar:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/AdDetailsRow'
    AdDetailsRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - AD_DETAILS_ROW
        data:
          type: array
          items:
            $ref: '#/components/schemas/AdDetailsDto'
      required:
        - type
        - data
    AdDetailsDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - AD_DETAILS_CARD
        title:
          type: string
        price:
          type: string
        location:
          type: string
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - type
        - title
    RowLayoutItems:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/WebviewRow'
    WebviewRow:
      type: object
      properties:
        type:
          type: string
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/WebviewDto'
      required:
        - type
        - data
    WebviewDto:
      type: object
      properties:
        type:
          type: string
        url:
          type: string
      required:
        - type
        - url
    Image:
      type: object
      properties:
        url:
          type: string
        width:
          type: integer
        height:
          type: integer
      required:
        - url
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
security:
  - ApiKeyAuthUserEmail: []
    ApiKeyAuthUserToken: []
paths:
  /promote/screen:
    get:
      summary: Get the Mobile apps Promote screen
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/AdId"
        - $ref: "#/components/parameters/ExperimentsHeader"
      responses:
        '200':
          description: Response with all data to draw the promote Screen
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"