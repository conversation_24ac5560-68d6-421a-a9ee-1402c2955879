package features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipTitleCardDto
import com.gumtree.mobile.features.vip.VipTitleMapper
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class VipTitleMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return Vip title row`() {
        runUnitTest(robot) {
            Given { stubRawAd(DataFactory.SOME_AD_TITLE) }
            When { map() }
            Then { checkVipTitleRowType(RowLayoutType.VIP_TITLE_ROW) }
        }
    }

    @Test
    fun `should return Vip title row data`() {
        runUnitTest(robot) {
            Given { stubRawAd(DataFactory.SOME_AD_TITLE) }
            When { map() }
            Then { checkVipTitleRowDataCount(1) }
            Then { checkVipTitleRowDataType(VipTitleCardDto::class.java) }
            Then { checkVipTitleCardText(DataFactory.SOME_AD_TITLE) }
            Then { checkVipTitleCardHideFavorite(false) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualVipTitleCardDtoResult: RowLayout<UiItem>
        private lateinit var rawAd: RawCapiAd

        private lateinit var tested: VipTitleMapper

        override fun setup() {
            tested = VipTitleMapper()
        }

        fun stubRawAd(title: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(title = title)
        }

        fun map() {
            actualVipTitleCardDtoResult = tested.map(rawAd)
        }

        fun checkVipTitleRowType(expected: RowLayoutType) {
            assertEquals(expected, actualVipTitleCardDtoResult.type)
        }

        fun checkVipTitleRowDataCount(expected: Int) {
            assertEquals(expected, actualVipTitleCardDtoResult.data.size)
        }

        fun checkVipTitleRowDataType(expected: Class<VipTitleCardDto>) {
            assertInstanceOf(expected, actualVipTitleCardDtoResult.data.first())
        }

        fun checkVipTitleCardText(expected: String) {
            assertEquals(expected, (actualVipTitleCardDtoResult.data.first() as VipTitleCardDto).text)
        }

        fun checkVipTitleCardHideFavorite(expected: Boolean) {
            assertEquals(expected, (actualVipTitleCardDtoResult.data.first() as VipTitleCardDto).hideFavorite)
        }
    }
}
