package com.gumtree.mobile.features.filters

import com.google.common.annotations.VisibleForTesting
import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.common.ANY_KEY
import com.gumtree.mobile.common.FilterAttributeDefault
import com.gumtree.mobile.common.allCarsPriceFilterAttributeOptions
import com.gumtree.mobile.common.buildFilterAttributeOptions
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.DropdownFactory
import com.gumtree.mobile.features.screens.factories.InputFactory
import com.gumtree.mobile.features.screens.factories.LinkFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.InputCardDto
import com.gumtree.mobile.features.screens.layoutsData.ResetToggle
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.extensions.getAttributeOrDefault

/**
 * Provider of the Filters Attribute Views
 */
const val MIN_PREFIX = "min_"
const val MAX_PREFIX = "max_"

class FiltersAttributesProvider(
    private val filtersCategoryAttributesCache: FiltersCategoryAttributesCache,
    private val filtersAttributesPriorityOrder: FiltersAttributesPriorityOrder,
    private val titleFactory: TitleFactory,
    private val inputFactory: InputFactory,
    private val dropdownFactory: DropdownFactory,
    private val linkFactory: LinkFactory,
) {

    /**
     * Determines whether to create a link row for a given attribute based on experiment configuration
     *
     * For mobile attributes (color, storage capacity, condition), the link row is only created
     * if the SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION experiment is enabled (variant B).
     *
     * For mobile model attribute, the link row is only created if the SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL experiment
     * is enabled (variant B) and the category is Apple iPhone (10205) or Samsung (4663).
     *
     * For all other attributes, link rows are always created.
     *
     * @param attributeName - The name of the attribute to check
     * @param experiments - The client experiments configuration
     * @param categoryId - The category ID
     * @return true if a link row should be created, false otherwise
     */
    private fun shouldCreateLinkRow(
        attributeName: String,
        experiments: ClientExperiments,
        categoryId: String,
    ): Boolean {
        return when {
            (attributeName == ApiQueryParams.MOBILE_MODEL_APPLE && categoryId == CategoryDefaults.IPHONE.id) ||
                (attributeName == ApiQueryParams.MOBILE_MODEL_SAMSUNG && categoryId == CategoryDefaults.SAMSUNG.id) -> {
                experiments.isB(Experiment.SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL)
            }

            (attributeName == ApiQueryParams.MOBILE_MODEL_GOOGLE && categoryId == CategoryDefaults.GOOGLE.id) ||
                (attributeName == ApiQueryParams.MOBILE_MODEL_XIAOMI && categoryId == CategoryDefaults.XIAOMI.id) ||
                (attributeName == ApiQueryParams.MOBILE_MODEL_HUAWEI && categoryId == CategoryDefaults.HUAWEI.id) -> {
                experiments.isB(Experiment.SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI)
            }

            attributeName == ApiQueryParams.DIY_TOOLS_MATERIALS_CONDITION -> {
                experiments.isB(Experiment.SRP_FILTER_DIY_TOOLS_MATERIALS_CONDITION)
            }

            else -> true
        }
    }

    /**
     * Get the Filters attribute RowLayouts
     * @param categoryId - the categoryId
     * @param filtersParams - the request query params
     * @return - The List with all attribute RowLayout for the given categoryId
     */
    fun createAttributeRows(
        categoryId: String,
        filtersParams: QueryParams,
        experiments: ClientExperiments,
    ): Array<RowLayout<UiItem>> {
        val filtersAttributes = filtersAttributesPriorityOrder.prioritise(
            categoryId,
            filtersCategoryAttributesCache.findCategoryAttributesOrEmpty(categoryId),
        )
        var filtersAttributeRowLayouts = arrayOf<RowLayout<UiItem>>()

        filtersAttributes.forEach { attribute ->
            val newRowLayouts = processAttributeByType(
                attribute,
                filtersAttributes,
                categoryId,
                filtersParams,
                experiments,
            )
            filtersAttributeRowLayouts += newRowLayouts
        }

        return filtersAttributeRowLayouts
    }

    /**
     * Process an attribute based on its presentation type and return the appropriate row layouts
     */
    private fun processAttributeByType(
        attribute: FiltersCategoryAttributeDto,
        filtersAttributes: List<FiltersCategoryAttributeDto>,
        categoryId: String,
        filtersParams: QueryParams,
        experiments: ClientExperiments,
    ): Array<RowLayout<UiItem>> {
        val shouldHaveFreeToggle = experiments.isB(Experiment.GTB_499) || experiments.isB(Experiment.GTB_500)
        return when (attribute.presentationType) {
            CategoryAttributesCache.Data.PresentationType.DOUBLE_INPUT -> createDoubleInputRow(
                categoryId,
                attribute,
                filtersParams,
                shouldHaveFreeToggle,
            )

            CategoryAttributesCache.Data.PresentationType.DOUBLE_DROPDOWN -> createDoubleDropdownRow(
                attribute,
                filtersParams,
            )

            CategoryAttributesCache.Data.PresentationType.LINK -> processLinkAttribute(
                attribute,
                filtersAttributes,
                categoryId,
                filtersParams,
                experiments,
            )
        }
    }

    /**
     * Process a link attribute and return the appropriate row layouts
     */
    private fun processLinkAttribute(
        attribute: FiltersCategoryAttributeDto,
        filtersAttributes: List<FiltersCategoryAttributeDto>,
        categoryId: String,
        filtersParams: QueryParams,
        experiments: ClientExperiments,
    ): Array<RowLayout<UiItem>> {
        return when (attribute.name) {
            ApiQueryParams.VEHICLE_MAKE -> createDoubleLinkRow(
                attribute,
                FiltersCategoryAttributeDto.findAttributeByName(
                    filtersAttributes,
                    ApiQueryParams.VEHICLE_MODEL,
                ),
                filtersParams,
            )

            ApiQueryParams.VEHICLE_MODEL -> emptyArray() // we skip the vehicle_model attribute, shall be attached to vehicle_make attribute
            else -> if (shouldCreateLinkRow(attribute.name, experiments, categoryId)) {
                createLinkRow(
                    categoryId,
                    attribute,
                    filtersParams,
                )
            } else {
                emptyArray()
            }
        }
    }

    /**
     * Create a Filter attribute double input RowLayout
     * @param attributeDto - the attribute data
     * @param filtersParams - the request query params
     * @return - The Filter attribute double input RowLayout
     */
    private fun createDoubleInputRow(
        categoryId: String,
        attributeDto: FiltersCategoryAttributeDto,
        filtersParams: QueryParams,
        shouldHavePriceResetToggle: Boolean,
    ): Array<RowLayout<UiItem>> {
        val attributeTitleRow = createAttributeTitleRow(attributeDto)
        val attributeRow = when (attributeDto.name) {
            "price" -> createPriceAttributeRow(categoryId, filtersParams, shouldHavePriceResetToggle)
            else -> createGenericDoubleInputRow(attributeDto, filtersParams)
        }
        return arrayOf(attributeTitleRow, attributeRow)
    }

    /**
     * Create a Filter attribute double dropdown RowLayout
     * @param attributeDto - the attribute data
     * @param filtersParams - the request query params
     * @return - The Filter attribute double dropdown RowLayout
     */
    private fun createDoubleDropdownRow(
        attributeDto: FiltersCategoryAttributeDto,
        filtersParams: QueryParams,
    ): Array<RowLayout<UiItem>> {
        val attributeTitleRow = createAttributeTitleRow(attributeDto)
        val attributeRow = when (attributeDto.name) {
            "salary" -> createSalaryDoubleDropdownRow(attributeDto, filtersParams)
            else -> createGenericDoubleDropdownRow(attributeDto, filtersParams)
        }
        return arrayOf(attributeTitleRow, attributeRow)
    }

    /**
     * Create a Filter attribute link RowLayout
     * @param attributeDto - the attribute data
     * @param filtersParams - the request query params
     * @return - The Filter attribute link RowLayout
     */
    private fun createLinkRow(
        categoryId: String,
        attributeDto: FiltersCategoryAttributeDto,
        filtersParams: QueryParams,
    ): Array<RowLayout<UiItem>> {
        val selectedValue = attributeDto.toSelectedValue(filtersParams)
        return arrayOf(
            RowLayout(
                type = RowLayoutType.LINK_ROW,
                data = listOf(
                    linkFactory.buildFilterLinkCard(
                        linkDisplayText = attributeDto.label,
                        linkParams = listOf(attributeDto.name),
                        linkSelectedValue = selectedValue?.let { listOf(it) },
                        destination = DestinationRoute.SET_FILTER.build(
                            ApiQueryParams.NAME to attributeDto.name,
                            ApiQueryParams.VALUE to filtersParams[attributeDto.name],
                            ApiQueryParams.CATEGORY_ID to categoryId,
                        ),
                    ),
                ),
                bottomDivider = true,
            ),
        )
    }

    /**
     * Create a Filter attribute double link RowLayout (vehicle_make + vehicle_model)
     * @param firstAttributeDto - the first attribute data
     * @param secondAttributeDto - the second attribute data
     * @param filtersParams - the request query params
     * @return - The Filter attribute double link RowLayout
     */
    private fun createDoubleLinkRow(
        firstAttributeDto: FiltersCategoryAttributeDto,
        secondAttributeDto: FiltersCategoryAttributeDto?,
        filtersParams: QueryParams,
    ): Array<RowLayout<UiItem>> {
        val firstSelectedValue = firstAttributeDto.toSelectedValue(filtersParams)
        val secondSelectedValue = secondAttributeDto?.toSelectedValue(filtersParams)
        return arrayOf(
            RowLayout(
                type = RowLayoutType.LINK_ROW,
                data = listOf(
                    linkFactory.buildFilterLinkCard(
                        linkDisplayText = "${firstAttributeDto.label} & ${secondAttributeDto?.label}",
                        linkParams = listOfNotNull(firstAttributeDto.name, secondAttributeDto?.name),
                        linkSelectedValue = firstSelectedValue?.let { listOfNotNull(it, secondSelectedValue) },
                        destination = DestinationRoute.SET_FILTER.build(
                            mapOf(
                                ApiQueryParams.NAME to firstAttributeDto.name,
                                ApiQueryParams.VALUE to filtersParams[firstAttributeDto.name],
                                ApiQueryParams.DEPENDENT_NAME to secondAttributeDto?.name,
                                ApiQueryParams.DEPENDENT_VALUE to filtersParams[secondAttributeDto?.name],
                            ),
                        ),
                    ),
                ),
                bottomDivider = true,
            ),
        )
    }

    /**
     * Create a Filter attribute title RowLayout
     * @param attributeDto - the attribute data
     * @return - The Filter attribute title RowLayout
     */
    private fun createAttributeTitleRow(attributeDto: FiltersCategoryAttributeDto): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.TITLE_ROW,
            data = listOf(
                titleFactory.buildLeftTitleCard(
                    titleText = attributeDto.label,
                    titleSize = TitleCardDto.Size.SMALL,
                ),
            ),
        )
    }

    /**
     * Create the Filters Price RowLayout - it will depend on the categoryId
     * @param categoryId - the categoryId
     * @param filtersParams - the request query params
     * @return - The Price attribute RowLayout
     */
    private fun createPriceAttributeRow(
        categoryId: String,
        filtersParams: QueryParams,
        shouldHavePriceResetToggle: Boolean,
    ): RowLayout<UiItem> {
        return when (categoryId) {
            CategoryDefaults.CARS.id -> createPriceDoubleDropdownRow(filtersParams)
            else -> createPriceDoubleInputRow(filtersParams, shouldHavePriceResetToggle)
        }
    }

    /**
     * Create the Filters Price double input RowLayout
     * @param filtersParams - the request query params
     * @return - The Price double input RowLayout
     */
    @VisibleForTesting
    fun createPriceDoubleInputRow(
        filtersParams: QueryParams,
        shouldHavePriceResetToggle: Boolean,
    ): RowLayout<UiItem> {
        val minPrice = filtersParams[ApiQueryParams.MIN_PRICE]
        val maxPrice = filtersParams[ApiQueryParams.MAX_PRICE]
        return RowLayout(
            type = RowLayoutType.DOUBLE_INPUT_ROW,
            data = listOf(
                inputFactory.buildDoubleInputCard(
                    input1Param = ApiQueryParams.MIN_PRICE,
                    input1Text = minPrice,
                    input1Title = FiltersScreenUiConfiguration.MIN_TEXT,
                    input1Hint = FiltersScreenUiConfiguration.MIN_PRICE_HINT_TEXT,
                    input1InputType = InputCardDto.InputType.CURRENCY,
                    input2Param = ApiQueryParams.MAX_PRICE,
                    input2Text = maxPrice,
                    input2Title = FiltersScreenUiConfiguration.MAX_TEXT,
                    input2Hint = FiltersScreenUiConfiguration.MAX_PRICE_HINT_TEXT,
                    input2InputType = InputCardDto.InputType.CURRENCY,
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                    resetToggle = if (shouldHavePriceResetToggle) {
                        ResetToggle(FiltersScreenUiConfiguration.PRICE_RESET_TEXT)
                    } else {
                        null
                    },
                ),
            ),
            bottomDivider = true,
        )
    }

    /**
     * Create the Filters generic double input RowLayout. These are all double input RowLayout cases which except from the special cases
     * @param attributeDto - the attribute data
     * @param filtersParams - the request query params
     * @return - The generic double input RowLayout
     */
    private fun createGenericDoubleInputRow(
        attributeDto: FiltersCategoryAttributeDto,
        filtersParams: QueryParams,
    ): RowLayout<UiItem> {
        val minParamKey = MIN_PREFIX + attributeDto.name
        val maxParamKey = MAX_PREFIX + attributeDto.name
        val minValue = filtersParams[minParamKey]
        val maxValue = filtersParams[maxParamKey]
        return RowLayout(
            type = RowLayoutType.DOUBLE_INPUT_ROW,
            data = listOf(
                inputFactory.buildDoubleInputCard(
                    input1Param = minParamKey,
                    input1Text = minValue,
                    input1Title = FiltersScreenUiConfiguration.MIN_TEXT,
                    input1Hint = FiltersScreenUiConfiguration.MIN_TEXT + attributeDto.label,
                    input1InputType = attributeDto.toInputType(),
                    input2Param = maxParamKey,
                    input2Text = maxValue,
                    input2Title = FiltersScreenUiConfiguration.MAX_TEXT,
                    input2Hint = FiltersScreenUiConfiguration.MAX_TEXT + attributeDto.label,
                    input2InputType = attributeDto.toInputType(),
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                    resetToggle = null,
                ),
            ),
            bottomDivider = true,
        )
    }

    /**
     * Create the Filters Price double dropdown RowLayout
     * @param filtersParams - the request query params
     * @return - The Price double dropdown RowLayout
     */
    // TODO: Try to remove this method when Categories API has the cars price attribute dropdown options
    private fun createPriceDoubleDropdownRow(filtersParams: QueryParams): RowLayout<UiItem> {
        val minPrice = filtersParams.getAttributeOrDefault(ApiQueryParams.MIN_PRICE, ANY_KEY)
        val maxPrice = filtersParams.getAttributeOrDefault(ApiQueryParams.MAX_PRICE, ANY_KEY)
        return RowLayout(
            type = RowLayoutType.DOUBLE_DROPDOWN_ROW,
            data = listOf(
                dropdownFactory.buildDoubleDropdownCard(
                    title1 = FiltersScreenUiConfiguration.MIN_TEXT,
                    paramName1 = ApiQueryParams.MIN_PRICE,
                    selectedOption1 = minPrice,
                    dropdownOptions1 = buildFilterAttributeOptions(
                        allCarsPriceFilterAttributeOptions,
                        FilterAttributeDefault.NO_MIN,
                    ),
                    title2 = FiltersScreenUiConfiguration.MAX_TEXT,
                    paramName2 = ApiQueryParams.MAX_PRICE,
                    selectedOption2 = maxPrice,
                    dropdownOptions2 = buildFilterAttributeOptions(
                        allCarsPriceFilterAttributeOptions,
                        FilterAttributeDefault.NO_MAX,
                    ),
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                ),
            ),
            bottomDivider = true,
        )
    }

    /**
     * Create the Filters salary double dropdown RowLayout
     * @param attributeDto - the attribute data
     * @param filtersParams - the request query params
     * @return - The Salary double dropdown attribute RowLayout
     */
    private fun createSalaryDoubleDropdownRow(
        attributeDto: FiltersCategoryAttributeDto,
        filtersParams: QueryParams,
    ): RowLayout<UiItem> {
        val minSalary = filtersParams.getAttributeOrDefault(ApiQueryParams.MIN_SALARY, ANY_KEY)
        val maxSalary = filtersParams.getAttributeOrDefault(ApiQueryParams.MAX_SALARY, ANY_KEY)
        return RowLayout(
            type = RowLayoutType.DOUBLE_DROPDOWN_ROW,
            data = listOf(
                dropdownFactory.buildDoubleDropdownCard(
                    title1 = FiltersScreenUiConfiguration.MIN_TEXT,
                    paramName1 = ApiQueryParams.MIN_SALARY,
                    selectedOption1 = minSalary,
                    dropdownOptions1 = buildFilterAttributeOptions(attributeDto.values, FilterAttributeDefault.NO_MIN),
                    title2 = FiltersScreenUiConfiguration.MAX_TEXT,
                    paramName2 = ApiQueryParams.MAX_SALARY,
                    selectedOption2 = maxSalary,
                    dropdownOptions2 = buildFilterAttributeOptions(attributeDto.values, FilterAttributeDefault.NO_MAX),
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                ),
            ),
            bottomDivider = true,
        )
    }

    /**
     * Create the Filters generic double dropdown RowLayout. These are all double dropdown RowLayout cases which except from the special cases
     * @param attributeDto - the attribute data
     * @param filtersParams - the request query params
     * @return - The generic double dropdown attribute RowLayout
     */
    private fun createGenericDoubleDropdownRow(
        attributeDto: FiltersCategoryAttributeDto,
        filtersParams: QueryParams,
    ): RowLayout<UiItem> {
        val minParamKey = MIN_PREFIX + attributeDto.name
        val maxParamKey = MAX_PREFIX + attributeDto.name
        val minValue = filtersParams[minParamKey] ?: FilterAttributeDefault.NO_MIN.key
        val maxValue = filtersParams[maxParamKey] ?: FilterAttributeDefault.NO_MAX.key
        return RowLayout(
            type = RowLayoutType.DOUBLE_DROPDOWN_ROW,
            data = listOf(
                dropdownFactory.buildDoubleDropdownCard(
                    title1 = FiltersScreenUiConfiguration.MIN_TEXT,
                    paramName1 = minParamKey,
                    selectedOption1 = minValue,
                    dropdownOptions1 = buildFilterAttributeOptions(attributeDto.values, FilterAttributeDefault.NO_MIN),
                    title2 = FiltersScreenUiConfiguration.MAX_TEXT,
                    paramName2 = maxParamKey,
                    selectedOption2 = maxValue,
                    dropdownOptions2 = buildFilterAttributeOptions(attributeDto.values, FilterAttributeDefault.NO_MAX),
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                ),
            ),
            bottomDivider = true,
        )
    }
}
