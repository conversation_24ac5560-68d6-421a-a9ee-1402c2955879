package com.gumtree.mobile.api.capi.bodies

import com.gumtree.mobile.api.capi.Namespaces
import org.simpleframework.xml.Attribute
import org.simpleframework.xml.Element
import org.simpleframework.xml.Namespace
import org.simpleframework.xml.NamespaceList
import org.simpleframework.xml.Root

@Root(name = "user-activation")
@Namespace(reference = Namespaces.USER, prefix = Namespaces.Prefix.USER)
@NamespaceList(Namespace(reference = Namespaces.TYPES))
data class RawAccountActivationBody(

    @field:Attribute(name = "version", required = true)
    @param:Attribute(name = "version", required = true)
    val version: String,

    @Namespace(reference = Namespaces.USER, prefix = Namespaces.Prefix.USER)
    @field:Element(name = "user:token", required = true)
    @param:Element(name = "user:token")
    val token: String,

    @Namespace(reference = Namespaces.USER, prefix = Namespaces.Prefix.USER)
    @field:Element(name = "user:signature", required = true)
    @param:Element(name = "user:signature")
    val signature: String,

)
