package com.gumtree.mobile.utils.extensions.data

import com.gumtree.mobile.api.categories.models.RawCategoriesAttribute
import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto

/**
 * Mapping between the RawCategoriesAttribute data and the BFF FiltersCategoryAttributeDto
 */
fun RawCategoriesAttribute.toFilterCategoryAttributeDto(): FiltersCategoryAttributeDto {
    return FiltersCategoryAttributeDto(
        name = this.name,
        label = this.label,
        dataType = this.type.toCategoryAttributeCacheDataType(),
        presentationType = this.toCategoryAttributeCacheDataPresentationType(),
        values = this.srp.toCategoryAttributeCacheDataValues(),
        categoryIds = this.categoryIds.sorted(),
    )
}

/**
 * Mapping between the RawCategoriesAttribute.Type data and the BFF CategoryAttributesCache.Data.Type
 */
private fun RawCategoriesAttribute.Type.toCategoryAttributeCacheDataType(): CategoryAttributesCache.Data.Type {
    return when (this) {
        RawCategoriesAttribute.Type.bool -> CategoryAttributesCache.Data.Type.BOOL
        RawCategoriesAttribute.Type.integer -> CategoryAttributesCache.Data.Type.INTEGER
        RawCategoriesAttribute.Type.number -> CategoryAttributesCache.Data.Type.NUMBER
        RawCategoriesAttribute.Type.string -> CategoryAttributesCache.Data.Type.STRING
        RawCategoriesAttribute.Type.date -> CategoryAttributesCache.Data.Type.DATE
        RawCategoriesAttribute.Type.enum -> CategoryAttributesCache.Data.Type.ENUM
        RawCategoriesAttribute.Type.currency -> CategoryAttributesCache.Data.Type.CURRENCY
        RawCategoriesAttribute.Type.year -> CategoryAttributesCache.Data.Type.YEAR
    }
}

/**
 * Mapping between the RawCategoriesAttribute data and the BFF CategoryAttributesCache.Data.PresentationType.
 * The PresentationType shall identify of how the attribute is presented on the Filters screen
 */
private fun RawCategoriesAttribute.toCategoryAttributeCacheDataPresentationType(): CategoryAttributesCache.Data.PresentationType {
    val filterType = srp?.filters?.firstOrNull()?.type
    return when {
        filterType == RawCategoriesAttribute.Srp.Filter.Type.range -> CategoryAttributesCache.Data.PresentationType.DOUBLE_INPUT
        filterType == RawCategoriesAttribute.Srp.Filter.Type.multi_enum_range -> CategoryAttributesCache.Data.PresentationType.DOUBLE_DROPDOWN
        searchStyle == RawCategoriesAttribute.SearchStyle.RANGE -> CategoryAttributesCache.Data.PresentationType.DOUBLE_DROPDOWN
        else -> CategoryAttributesCache.Data.PresentationType.LINK
    }
}

/**
 * Mapping between the RawCategoriesAttribute.Srp data and the BFF List with CategoryAttributesCache.Data.Value, these are the values for single attribute
 */
private fun RawCategoriesAttribute.Srp?.toCategoryAttributeCacheDataValues(): List<CategoryAttributesCache.Data.Value> {
    return when (this) {
        null -> emptyList()
        else -> {
            filters?.firstOrNull()
                ?.values
                ?.filter { it.value.isNotEmpty() }
                ?.map {
                    CategoryAttributesCache.Data.Value(
                        label = it.label,
                        value = it.value,
                        dependentValue = it.dependentAttributeValue,
                    )
                } ?: emptyList()
        }
    }
}
