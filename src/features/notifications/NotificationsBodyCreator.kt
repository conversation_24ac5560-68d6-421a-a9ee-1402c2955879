package com.gumtree.mobile.features.notifications

import com.gumtree.mobile.api.conversations.bodies.RawNotificationsSubscriptionRequest

class NotificationsBodyCreator {

    fun create(
        pushToken: String,
        userId: String,
    ): RawNotificationsSubscriptionRequest {
        return RawNotificationsSubscriptionRequest(
            id = pushToken,
            userId = userId.toLong(),
        )
    }
}
