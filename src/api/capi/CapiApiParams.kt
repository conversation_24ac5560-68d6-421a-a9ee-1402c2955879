package com.gumtree.mobile.api.capi

/**
 * All CAPI API parameter names
 */
object CapiApiParams {

    internal const val TEMPLATE = "template"
    internal const val ID = "id"
    internal const val LATITUDE = "latitude"
    internal const val LONGITUDE = "longitude"
    internal const val USER_NAME = "username"
    internal const val AD_ID = "adId"
    internal const val AD_IDS = "adIds"
    internal const val ADVERT_ID = "advertId"
    internal const val CONVERSATION_ID = "conversationId"
    internal const val USER_ID = "userId"
    internal const val USER_IDS = "userIds"
    internal const val STATUS = "status"
    internal const val STATUSES = "statuses"
    internal const val Q = "q"
    internal const val TITLE = "title"
    internal const val CATEGORY_ID = "categoryId"
    internal const val LOCATION_ID = "locationId"
    internal const val ZIPCODE = "zipcode"
    internal const val DISTANCE = "distance"
    internal const val ALERT_ID = "alertId"
    internal const val COUNTER_TYPE = "counterType"
    internal const val SOURCE = "source"
    internal const val COPY_ME = "copyMe"
    internal const val VIN = "vin"
    internal const val SIZE = "size"
    internal const val PAGE = "page"
    internal const val OFFSET = "offSet"
    internal const val PASSWORD = "password"
    internal const val REL = "rel"
    internal const val TAIL = "tail"
    internal const val TOKEN = "token"
    internal const val SIGNATURE = "signature"
    internal const val ATTRIBUTE = "attribute"
    internal const val SOCIAL_AUTO_REGISTRATION = "socialAutoRegistration"
    internal const val PROVIDE_CATEGORIES = "provideCategories"
    internal const val IS_AUTOMATIC_RE_POST = "isAutomaticRepost"
    internal const val INCLUDE_TOP_ADS = "includeTopAds"
    internal const val AD_STATUS = "ad-status"
    internal const val SORT_TYPE = "sortType"
    internal const val PICTURE_REQUIRED = "pictureRequired"
    internal const val SEARCH_OPTIONS_EXACT_MATCH = "searchOptionsExactMatch"
    internal const val MIN_PRICE = "minPrice"
    internal const val MAX_PRICE = "maxPrice"
    internal const val PLATFORM = "platform"
    internal const val USE_FIVE_EIGHT_SUGGESTION_FLAG = "use58SuggestionFlag"
    const val IS_HIT_RANK_RELEVANT = "hitRankRelevantFlag"
    const val IS_HIT_RECALL_ALL_CATE = "hitRecallAllCateFlag"
    const val IS_HIT_SHOW_MOST_RELEVANCE = "hitMostRelevanceFlag"
}
