package features.srp

import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.features.srp.SrpCapiQueryParamsFactory
import com.gumtree.mobile.features.srp.SrpService
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import io.mockk.coEvery
import io.mockk.coVerifyOrder
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class SrpServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use Capi search API to get search result page`() = runTest {
        runUnitTest(robot) {
            Given { stubPage(DataFactory.SOME_PAGE_NUMBER) }
            Given { stubSize(DataFactory.SOME_SIZE) }
            Given {
                stubSearchOptions(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            When { searchAds() }
            Then { checkSearchAdsActionsInOrder((DataFactory.SOME_SIZE.toInt() - 2).toString()) }
        }
    }

    @Test
    fun `should use Capi search API to get search result page with default page size`() = runTest {
        runUnitTest(robot) {
            Given { stubPage(DataFactory.SOME_PAGE_NUMBER) }
            Given { stubSize("dwed232") }
            Given {
                stubSearchOptions(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            When { searchAds() }
            Then { checkSearchAdsActionsInOrder((DEFAULT_PAGE_SIZE.toInt() - 2).toString()) }

            Given { stubPage("1") }
            Given { stubSize(EMPTY_STRING) }
            Given {
                stubSearchOptions(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            When { searchAds() }
            Then { checkSearchAdsActionsInOrder((DEFAULT_PAGE_SIZE.toInt() - 2).toString()) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var searchOptions: QueryParams
        private val capiSearchApi: CapiSearchApi = mockk(relaxed = true)
        private val apiHeaders: ApiHeaders = mockk(relaxed = true)
        private val srpCapiQueryParamsFactory: SrpCapiQueryParamsFactory = mockk(relaxed = true)
        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private lateinit var page: String
        private lateinit var size: String

        private lateinit var tested: SrpService

        override fun setup() {
            tested = SrpService(capiSearchApi, srpCapiQueryParamsFactory, locationsApi)
        }

        fun stubPage(page: String) {
            this.page = page
        }

        fun stubSize(size: String) {
            this.size = size
        }

        fun stubSearchOptions(queryParams: QueryParams) {
            searchOptions = queryParams
            coEvery { srpCapiQueryParamsFactory.appendRequiredCapiSearchOptions(any()) } returns searchOptions
        }

        suspend fun searchAds() {
            tested.searchAds(apiHeaders, searchOptions, page, size)
        }

        fun checkSearchAdsActionsInOrder(expectedSize: String) {
            coVerifyOrder {
                srpCapiQueryParamsFactory.appendRequiredCapiSearchOptions(any())
                capiSearchApi.searchAds(apiHeaders, searchOptions, page, expectedSize)
            }
        }
    }
}