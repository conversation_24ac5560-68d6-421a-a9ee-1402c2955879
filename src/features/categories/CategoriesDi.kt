package com.gumtree.mobile.features.categories

import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Provides all dependencies about the Categories feature
 */
val categoriesModule = module {
    single { CategoriesTreeMapper() }
    single<CategoriesRepository> {
        DefaultCategoriesRepository(
            categoriesService = getFromKoin(),
            categoriesTreeMapper = getFromKoin(),
            dispatcherProvider = getFromKoin(),
        )
    }
    single { CategoriesService(categoryApi = getFromKoin()) }
}
