package features.syncPhoneVerifyResult


import com.gumtree.mobile.features.syncPhoneVerifyResult.SYNC_PHONE_VERIFY_SCREEN_PATH
import com.gumtree.mobile.features.syncPhoneVerifyResult.SyncPhoneVerifyStateRepository
import com.gumtree.mobile.features.syncPhoneVerifyResult.SyncResponseDto
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import io.ktor.client.request.get
import io.ktor.client.request.headers
import io.ktor.client.request.parameter
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpStatusCode
import io.ktor.server.plugins.BadRequestException
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class SyncPhoneVerifyStateRouteTest {
    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET sync phone verify screen with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponse() }
            When {
                getSyncPhoneVerifyScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userId = DataFactory.SOME_USER_ID,
                    adId = DataFactory.SOME_AD_ID,
                    editorId = DataFactory.SOME_EDITOR_ID,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET sync phone verify screen with error when userId is missing`() = runRouteTestForException(module, NullPointerException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            When {
                getSyncPhoneVerifyScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userId = null,
                    adId = DataFactory.SOME_AD_ID,
                    editorId = DataFactory.SOME_EDITOR_ID,
                )
            }
        }
    }

    @Test
    fun `should complete GET sync phone verify screen with error when adId is missing`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            When {
                getSyncPhoneVerifyScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userId = DataFactory.SOME_USER_ID,
                    adId = null,
                    editorId = DataFactory.SOME_EDITOR_ID,
                )
            }
        }
    }

    @Test
    fun `should complete GET sync phone verify screen with error when repository throws exception`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryCreateConversationReviewError(BadRequestException("Invalid data")) }
            When {
                getSyncPhoneVerifyScreen(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userId = DataFactory.SOME_USER_ID,
                    adId = DataFactory.SOME_AD_ID,
                    editorId = DataFactory.SOME_EDITOR_ID,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    private class Robot : BaseRouteRobot() {
        val repository: SyncPhoneVerifyStateRepository = mockk(relaxed = true)
        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any(), any(), any(), any(), any())
            } returns SyncResponseDto(true,"","","")
        }

        fun stubRepositoryCreateConversationReviewError(error: Throwable) {
            coEvery {
                repository.readScreen(any(), any(), any(), any(), any())
            } throws error
        }

        suspend fun getSyncPhoneVerifyScreen(
            userEmail: String?,
            userId: String?,
            adId: String?,
            editorId: String?  // 新增editorId参数，默认为null
        ) {
            actualResponse = client.get(SYNC_PHONE_VERIFY_SCREEN_PATH) {
                userEmail?.let { headers { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, it) } }
                userId?.let { parameter(ApiQueryParams.USER_ID, it) }
                adId?.let { parameter(ApiQueryParams.AD_ID, it) }
                editorId?.let { parameter(ApiQueryParams.EDITOR_ID, it) } // 新增editorId参数处理
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }
    }
}