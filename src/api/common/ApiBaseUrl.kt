package com.gumtree.mobile.api.common

import com.gumtree.mobile.isLocalHostEnvironment
import okhttp3.HttpUrl

/**
 * Typealias about the different Gumtree API BaseUrls (CAPI, PAPI, Locations API etc).
 * first - will be the url protocol (http, https)
 * second - will be the url host
 */
typealias BaseUrl = Pair<String, String>

/**
 * Define different Gumtree APIs base urls (schema, host, path, port etc.)
 * The Api base URL will depend on the environment where the BFF service is deployed
 */
const val LOCAL_HOST_BASE_URL = "http://localhost"
const val INITIAL_PROD_PORT = -1
abstract class ApiBaseUrl {
    /**
     * The API production base url
     */
    abstract val url: BaseUrl

    /**
     * The API QA base url
     */
    abstract val localUrl: BaseUrl

    /**
     * The API QA port
     */
    abstract val localPort: Int

    /**
     * The API prod port
     */
    open val prodPort: Int = INITIAL_PROD_PORT

    /**
     * Get the ready-made base url of the API (will depend on the environment where the BFF is running)
     */
    fun get(): HttpUrl {
        return when {
            isLocalHostEnvironment() -> createLocalBaseUrl()
            else -> createBaseUrl()
        }
    }

    private fun createBaseUrl(): HttpUrl {
        return HttpUrl.Builder()
            .scheme(url.first)
            .host(url.second)
            .apply {
                if (prodPort != INITIAL_PROD_PORT) {
                    port(prodPort)
                }
            }
            .build()
    }

    private fun createLocalBaseUrl(): HttpUrl {
        return HttpUrl.Builder()
            .scheme(localUrl.first)
            .host(localUrl.second)
            .port(localPort)
            .build()
    }
}
