package features.reviews

import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.api.reviews.models.RawReview
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider
import com.gumtree.mobile.common.analytics.FORM_NAME_KEY
import com.gumtree.mobile.common.analytics.FORM_VALIDATION_KEY
import com.gumtree.mobile.common.analytics.SUCCESS
import com.gumtree.mobile.features.reviews.ANALYTICS_RATE_USER_EVENT_NAME
import com.gumtree.mobile.features.reviews.ANALYTICS_RATING_PARAM_KEY
import com.gumtree.mobile.features.reviews.ANALYTICS_REVIEW_DIRECTION_PARAM_KEY
import com.gumtree.mobile.features.reviews.ANALYTICS_REVIEW_FORM_NAME_VALUE
import com.gumtree.mobile.features.reviews.ANALYTICS_START_RATE_USER_EVENT_NAME
import com.gumtree.mobile.features.reviews.ReviewsAnalyticsProvider
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawFlatAdsFactory
import tools.runUnitTest

class ReviewsAnalyticsProviderTest {

    private val robot = Robot()

    @Test
    fun `should return correct start review event for seller to buyer`() {
        runUnitTest(robot) {
            When { getStartReviewEvent(isConversationAboutMyPosting = true) }
            Then {
                checkEventData(
                    AnalyticsEventData(
                        eventName = ANALYTICS_START_RATE_USER_EVENT_NAME,
                        parameters = mapOf(
                            FORM_NAME_KEY to ANALYTICS_REVIEW_FORM_NAME_VALUE,
                            ANALYTICS_REVIEW_DIRECTION_PARAM_KEY to RawReview.Direction.S2B.toString(),
                        ),
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct start review event for buyer to seller`() {
        runUnitTest(robot) {
            When { getStartReviewEvent(isConversationAboutMyPosting = false) }
            Then {
                checkEventData(
                    AnalyticsEventData(
                        eventName = ANALYTICS_START_RATE_USER_EVENT_NAME,
                        parameters = mapOf(
                            FORM_NAME_KEY to ANALYTICS_REVIEW_FORM_NAME_VALUE,
                            ANALYTICS_REVIEW_DIRECTION_PARAM_KEY to RawReview.Direction.B2S.toString(),
                        ),
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct post review event for seller to buyer`() {
        runUnitTest(robot) {
            Given { stubRawAd() }
            When { getPostReviewEvent(reviewRating = 5, isConversationAboutMyPosting = true) }
            Then {
                checkEventData(
                    AnalyticsEventData(
                        eventName = ANALYTICS_RATE_USER_EVENT_NAME,
                        parameters = mapOf(
                            FORM_VALIDATION_KEY to SUCCESS,
                            FORM_NAME_KEY to ANALYTICS_REVIEW_FORM_NAME_VALUE,
                            ANALYTICS_RATING_PARAM_KEY to "5",
                            ANALYTICS_REVIEW_DIRECTION_PARAM_KEY to RawReview.Direction.S2B.toString(),
                        ),
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct post review event for buyer to seller`() {
        runUnitTest(robot) {
            Given { stubRawAd() }
            When { getPostReviewEvent(reviewRating = 3, isConversationAboutMyPosting = false) }
            Then {
                checkEventData(
                    AnalyticsEventData(
                        eventName = ANALYTICS_RATE_USER_EVENT_NAME,
                        parameters = mapOf(
                            FORM_VALIDATION_KEY to SUCCESS,
                            FORM_NAME_KEY to ANALYTICS_REVIEW_FORM_NAME_VALUE,
                            ANALYTICS_RATING_PARAM_KEY to "3",
                            ANALYTICS_REVIEW_DIRECTION_PARAM_KEY to RawReview.Direction.B2S.toString(),
                        ),
                    )
                )
            }
        }
    }

    private class Robot : BaseRobot {
        private var actualEventDataResult: AnalyticsEventData? = null
        private lateinit var rawAd: RawFlatAd
        private val commonAnalyticsParamsProvider: CommonAnalyticsProvider = mockk(relaxed = true)

        private val testSubject = ReviewsAnalyticsProvider(commonAnalyticsParamsProvider)

        fun stubRawAd(flatAd: RawFlatAd = RawFlatAdsFactory.createRawFlatAd()) {
            rawAd = flatAd
        }

        fun getStartReviewEvent(isConversationAboutMyPosting: Boolean) {
            actualEventDataResult = testSubject.getStartReviewEvent(isConversationAboutMyPosting)
        }

        fun getPostReviewEvent(reviewRating: Int, isConversationAboutMyPosting: Boolean) {
            actualEventDataResult = testSubject.getPostReviewEvent(reviewRating, isConversationAboutMyPosting, rawAd)
        }

        fun checkEventData(expected: AnalyticsEventData) {
            assertEquals(expected, actualEventDataResult)
        }
    }
}
