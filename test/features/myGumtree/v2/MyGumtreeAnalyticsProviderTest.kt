package features.myGumtree.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.CONTENT_GROUP_KEY
import com.gumtree.mobile.common.analytics.CONTENT_TYPE_KEY
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider
import com.gumtree.mobile.common.analytics.FORM_NAME_KEY
import com.gumtree.mobile.common.analytics.FORM_VALIDATION_KEY
import com.gumtree.mobile.common.analytics.SUCCESS
import com.gumtree.mobile.features.myGumtree.v2.AD_DELETED_EVENT_NAME
import com.gumtree.mobile.features.myGumtree.v2.AD_POSTED_EVENT_NAME
import com.gumtree.mobile.features.myGumtree.v2.AD_STATUS_KEY
import com.gumtree.mobile.features.myGumtree.v2.AD_TYPE_FREE
import com.gumtree.mobile.features.myGumtree.v2.AD_TYPE_KEY
import com.gumtree.mobile.features.myGumtree.v2.AD_TYPE_PAID
import com.gumtree.mobile.features.myGumtree.v2.FORM_NAME
import com.gumtree.mobile.features.myGumtree.v2.FORM_STEP_KEY
import com.gumtree.mobile.features.myGumtree.v2.FORM_STEP_VALUE
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeAnalyticsProvider
import com.gumtree.mobile.features.myGumtree.v2.PostType
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.annotations.ParallelTest
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawFlatAdsFactory
import tools.runUnitTest

@ParallelTest
class MyGumtreeAnalyticsProviderTest {

    private val robot = Robot()

    @Test
    fun `should return adPostedSuccess event for relist`() {
        runUnitTest(robot) {
            Given { stubClientSemantics(ClientSemantics(platform = ClientPlatform.ANDROID)) }
            Given { stubPostType(PostType.RELIST) }
            Given { stubRawAd() }
            Given { stubIsPaidAd(false) }
            Given { stubCommonParams(mapOf(DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE)) }
            When { getAdPostedAnalyticsEvent() }
            Then {
                checkEvent(
                    AnalyticsEventData(
                        AD_POSTED_EVENT_NAME,
                        mapOf(
                            DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE,
                            FORM_STEP_KEY to FORM_STEP_VALUE,
                            CONTENT_GROUP_KEY to "relist ad page",
                            CONTENT_TYPE_KEY to "manage ads page app android",
                            FORM_NAME_KEY to FORM_NAME,
                            FORM_VALIDATION_KEY to SUCCESS,
                            AD_TYPE_KEY to AD_TYPE_FREE,
                            AD_STATUS_KEY to PostType.analyticsEventValue(PostType.RELIST),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return adPostedSuccess event for edit`() {
        runUnitTest(robot) {
            Given { stubClientSemantics(ClientSemantics(platform = ClientPlatform.ANDROID)) }
            Given { stubPostType(PostType.EDIT) }
            Given { stubRawAd() }
            Given { stubIsPaidAd(false) }
            Given { stubCommonParams(mapOf(DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE)) }
            When { getAdPostedAnalyticsEvent() }
            Then {
                checkEvent(
                    AnalyticsEventData(
                        AD_POSTED_EVENT_NAME,
                        mapOf(
                            DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE,
                            FORM_STEP_KEY to FORM_STEP_VALUE,
                            CONTENT_GROUP_KEY to "edit ad page",
                            CONTENT_TYPE_KEY to "manage ads page app android",
                            FORM_NAME_KEY to FORM_NAME,
                            FORM_VALIDATION_KEY to SUCCESS,
                            AD_TYPE_KEY to AD_TYPE_FREE,
                            AD_STATUS_KEY to PostType.analyticsEventValue(PostType.EDIT),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return adPostedSuccess event for new`() {
        runUnitTest(robot) {
            Given { stubClientSemantics(ClientSemantics(platform = ClientPlatform.IOS)) }
            Given { stubPostType(PostType.NEW) }
            Given { stubRawAd() }
            Given { stubIsPaidAd(false) }
            Given { stubCommonParams(mapOf(DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE)) }
            When { getAdPostedAnalyticsEvent() }
            Then {
                checkEvent(
                    AnalyticsEventData(
                        AD_POSTED_EVENT_NAME,
                        mapOf(
                            DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE,
                            FORM_STEP_KEY to FORM_STEP_VALUE,
                            CONTENT_GROUP_KEY to "post ad page",
                            CONTENT_TYPE_KEY to "manage ads page app ios",
                            FORM_NAME_KEY to FORM_NAME,
                            FORM_VALIDATION_KEY to SUCCESS,
                            AD_TYPE_KEY to AD_TYPE_FREE,
                            AD_STATUS_KEY to PostType.analyticsEventValue(PostType.NEW),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return adPostedSuccess event for free`() {
        runUnitTest(robot) {
            Given { stubClientSemantics(ClientSemantics(platform = ClientPlatform.IOS)) }
            Given { stubPostType(PostType.NEW) }
            Given { stubRawAd() }
            Given { stubIsPaidAd(false) }
            Given { stubCommonParams(mapOf(DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE)) }
            When { getAdPostedAnalyticsEvent() }
            Then {
                checkEvent(
                    AnalyticsEventData(
                        AD_POSTED_EVENT_NAME,
                        mapOf(
                            DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE,
                            FORM_STEP_KEY to FORM_STEP_VALUE,
                            CONTENT_GROUP_KEY to "post ad page",
                            CONTENT_TYPE_KEY to "manage ads page app ios",
                            FORM_NAME_KEY to FORM_NAME,
                            FORM_VALIDATION_KEY to SUCCESS,
                            AD_TYPE_KEY to AD_TYPE_FREE,
                            AD_STATUS_KEY to PostType.analyticsEventValue(PostType.NEW),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return adPostedSuccess event for paid`() {
        runUnitTest(robot) {
            Given { stubClientSemantics(ClientSemantics(platform = ClientPlatform.IOS)) }
            Given { stubPostType(PostType.NEW) }
            Given { stubRawAd() }
            Given { stubIsPaidAd(true) }
            Given { stubCommonParams(mapOf(DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE)) }
            When { getAdPostedAnalyticsEvent() }
            Then {
                checkEvent(
                    AnalyticsEventData(
                        AD_POSTED_EVENT_NAME,
                        mapOf(
                            DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE,
                            FORM_STEP_KEY to FORM_STEP_VALUE,
                            CONTENT_GROUP_KEY to "post ad page",
                            CONTENT_TYPE_KEY to "manage ads page app ios",
                            FORM_NAME_KEY to FORM_NAME,
                            FORM_VALIDATION_KEY to SUCCESS,
                            AD_TYPE_KEY to AD_TYPE_PAID,
                            AD_STATUS_KEY to PostType.analyticsEventValue(PostType.NEW),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return deleted ad event`() {
        runUnitTest(robot) {
            Given { stubRawCapiAd() }
            Given { stubCommonParams(mapOf(DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE)) }
            When { getDeleteAdAnalyticsEvent() }
            Then {
                AnalyticsEventData(
                    AD_DELETED_EVENT_NAME,
                    mapOf(
                        DataFactory.SOME_COMMON_ANALYTICS_KEY to DataFactory.SOME_COMMON_ANALYTICS_VALUE,
                    )
                )
            }
        }
    }

    inner class Robot : BaseRobot {
        private lateinit var actualAnalyticsEventResult: AnalyticsEventData
        private lateinit var clientSemantics: ClientSemantics
        private lateinit var rawAd: RawFlatAd
        private lateinit var rawCapiAd: RawCapiAd
        private lateinit var postType: PostType
        private var isPaidAd: Boolean? = null
        private val commonAnalyticsParamsProvider: CommonAnalyticsProvider = mockk(relaxed = true)

        private val testSubject = MyGumtreeAnalyticsProvider(commonAnalyticsParamsProvider)

        fun stubClientSemantics(clientSemantics: ClientSemantics) {
            this.clientSemantics = clientSemantics
        }

        fun stubRawCapiAd(capiAd: RawCapiAd = RawCapiAdsFactory.createRawCapiAd()) {
            rawCapiAd = capiAd
        }

        fun stubRawAd(flatAd: RawFlatAd = RawFlatAdsFactory.createRawFlatAd()) {
            rawAd = flatAd
        }

        fun stubCommonParams(params: Map<String, String>) {
            every { commonAnalyticsParamsProvider.getCommonListingParams(prefix = any(), capiAd = any()) } returns params
            every { commonAnalyticsParamsProvider.getCommonListingParams(prefix = any(), flatAd = any()) } returns params
        }

        fun stubIsPaidAd(isPaidAd: Boolean) {
            this.isPaidAd = isPaidAd
        }

        fun stubPostType(postType: PostType) {
            this.postType = postType
        }

        fun getAdPostedAnalyticsEvent() {
            actualAnalyticsEventResult = testSubject.getAdPostedAnalyticsEvent(rawAd, clientSemantics, postType, isPaidAd!!)
        }

        fun getDeleteAdAnalyticsEvent() {
            actualAnalyticsEventResult = testSubject.getDeleteAdAnalyticsEvent(rawCapiAd)
        }

        fun checkEvent(expected: AnalyticsEventData) {
            assertEquals(expected, actualAnalyticsEventResult)
        }
    }
}
