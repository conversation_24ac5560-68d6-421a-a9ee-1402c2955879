package com.gumtree.mobile.features.login

import com.gumtree.mobile.common.PushNotificationsService
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Provides all dependencies for User Login feature
 */
val loginModule = module {
    single { LoginIssuedDateFormatter() }
    single {
        LoginMapper(
            userProfileJWTService = getFromKoin(),
            issuedDateFormatter = getFromKoin(),
        )
    }
    single {
        LoginService(userServiceApi = getFromKoin())
    }
    single {
        PushNotificationsService(
            coreChatAuthApi = getFromKoin(),
            conversationsApi = getFromKoin(),
            userServiceApi = getFromKoin(),
        )
    }
    single<LoginRepository> {
        DefaultLoginRepository(
            loginService = getFromKoin(),
            pushNotificationsService = getFromKoin(),
            loginMapper = getFromKoin(),
            dispatcherProvider = getFromKoin(),
        )
    }
}
