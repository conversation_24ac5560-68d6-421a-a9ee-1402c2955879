package com.gumtree.mobile.responses

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import common.AdjustTrackingData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Typealias about the screen responses portrait data
 */
typealias PortraitData = List<RowLayout<UiItem>>

/**
 * Typealias about the screen responses landscape data
 */
typealias LandscapeData = List<RowLayout<UiItem>>

/**
 * Typealias about the screen responses stickyBar data
 */
typealias StickyBar = List<RowLayout<UiItem>>

/**
 * Typealias about the screen responses bottomOverlay data
 */
typealias BottomOverlay = List<RowLayout<UiItem>>

/**
 * Typealias about the BFF QueryParams
 */
typealias QueryParams = Map<String, String?>

/**
 * Generic Screen response data. Each screen response from this API should use it to reply
 * @param portraitData - the portrait data of the screen (required)
 * @param landscapeData - the landscape data of the screen (optional)
 * @param nextPage - the nextPage data of the screen, usually used in screens with listings (optional)
 * @param toolbar - the toolbar data of the screen, usually used in screens with toolbar like Chat, VIP (optional)
 * @param stickyBar - the stickyBar data of the screen, usually used in screens with stickyBar like Chat, SRP, HomeFeed (optional)
 * @param bottomOverlay - the bottomOverlay data of the screen, usually used in screens with a sticky View at the bottom like Chat and VIP, (optional)
 * @param title - the title of the screen (optional)
 * @param searchParam - the search params hashmap in case of the screens like Filters and eventually SRP. On the other screens this property shall be NULL
 */
@Serializable
data class ScreenResponse(
    @SerialName("portraitData")
    val portraitData: PortraitData,
    @SerialName("landscapeData")
    val landscapeData: LandscapeData? = null,
    @SerialName("nextPage")
    val nextPage: String? = null,
    @SerialName("toolbar")
    var toolbar: ToolbarDto? = null,
    @Deprecated("This should be slowly phased out in favour of topbar")
    @SerialName("stickyBar")
    val stickyBar: StickyBar? = null,
    @SerialName("topBar")
    val topBar: TopBar? = null,
    @SerialName("bottomOverlay")
    val bottomOverlay: BottomOverlay? = null,
    @SerialName("title")
    val title: String? = null,
    @SerialName("searchParam")
    val searchParam: QueryParams? = null,
    @SerialName("screenViewAnalyticsEvent")
    val screenViewAnalyticsEvent: AnalyticsEventData? = null,
    @SerialName("analyticsParameters")
    val analyticsParameters: Map<String, String>? = null,
    @Deprecated("This is currently kept for backwards compatibility. Android 10.1.6 and above no longer need this")
    @SerialName("screenViewAnalyticsEvents")
    val screenViewAnalyticsEvents: List<AnalyticsEventData>? = null,
    @SerialName("gamAdvertsData")
    val gamAdvertsData: List<GAMAdvertDto>? = null,
    @SerialName("adjustTrackingData")
    val adjustTrackingData: AdjustTrackingData? = null,
)

/**
 * Generic UI Flow (sequence of screens) response data
 * @param flow - the flow of the screens in the UI flow
 */
@Serializable
data class ScreensFlowResponse(
    @SerialName("flow")
    val flow: List<ScreenResponse>,
)

@Serializable
data class TopBar(
    val scrollingCollapseBehaviour: ScrollingCollapseBehaviour? = null,
    val rows: List<RowLayout<UiItem>>,
)
