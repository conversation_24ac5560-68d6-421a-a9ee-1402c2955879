package features.filters

import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import com.gumtree.mobile.features.filters.FiltersCategoryAttributesCache
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.CurrentDateProvider
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.FiltersCategoryAttributesFactory
import tools.runUnitTest

class FiltersCategoryAttributesCacheTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should NOT have valid cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given { stubFiltersCategoryAttributesCache(null) }
            When { hasValidCache() }
            Then { checkHasValidCache(false) }
        }
    }

    @Test
    fun `should have valid cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            When { hasValidCache() }
            Then { checkHasValidCache(true) }
        }
    }

    @Test
    fun `should NOT have cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given { stubFiltersCategoryAttributesCache(null)}
            When { getCacheData() }
            Then { checkCacheDataIsNull() }
        }
    }

    @Test
    fun `should have cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            When { getCacheData() }
            Then { checkCacheDataIsNotNull() }
        }
    }

    @Test
    fun `should return a category attributes list if find category attributes by categoryId is successful`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    listOf(
                        FiltersCategoryAttributesFactory.createFilterAttribute(
                            attributeLabel = "attribute_1",
                            attributeCategoryIds = listOf(CategoryDefaults.PETS.id.toInt(), CategoryDefaults.JOBS.id.toInt(), CategoryDefaults.CARS.id.toInt())
                        ),
                        FiltersCategoryAttributesFactory.createFilterAttribute(
                            attributeLabel = "attribute_2",
                            attributeCategoryIds = listOf(CategoryDefaults.PETS.id.toInt(), CategoryDefaults.JOBS.id.toInt(), CategoryDefaults.CARS.id.toInt())
                        ),
                        FiltersCategoryAttributesFactory.createFilterAttribute(
                            attributeLabel = "attribute_3",
                            attributeCategoryIds = listOf(CategoryDefaults.PETS.id.toInt(), CategoryDefaults.JOBS.id.toInt(), CategoryDefaults.CARS.id.toInt())
                        )
                    )
                )
            }

            When { findCategoryAttributesOrEmpty(CategoryDefaults.PETS.id) }
            Then { checkFoundCategoryAttributeLabelAtPosition(0, "attribute_1") }
            Then { checkCategoryAttributesListSize(3) }

            When { findCategoryAttributesOrEmpty(CategoryDefaults.JOBS.id) }
            Then { checkFoundCategoryAttributeLabelAtPosition(1, "attribute_2") }
            Then { checkCategoryAttributesListSize(3) }

            When { findCategoryAttributesOrEmpty(CategoryDefaults.CARS.id) }
            Then { checkFoundCategoryAttributeLabelAtPosition(2, "attribute_3") }
            Then { checkCategoryAttributesListSize(3) }
        }
    }

    @Test
    fun `should return empty category attributes list if find category attributes by categoryId is NOT successful`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            When { findCategoryAttributesOrEmpty("6882188338192") }
            Then { checkCategoryAttributesListSize(0) }
        }
    }

    @Test
    fun `should return empty category attributes list if find category attributes cache is NULL`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(null) }
            When { findCategoryAttributesOrEmpty("6882188338192") }
            Then { checkCategoryAttributesListSize(0) }
        }
    }

    @Test
    fun `should find category attribute if find category attribute exist`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            When { findAttributeByName("price") }
            Then { checkCategoryAttributeName("price") }
            Then { checkCategoryAttributeLabel("Price") }

            When { findAttributeByName("seller_type") }
            Then { checkCategoryAttributeName("seller_type") }
            Then { checkCategoryAttributeLabel("Seller type") }

            When { findAttributeByName("job_lang") }
            Then { checkCategoryAttributeName("job_lang") }
            Then { checkCategoryAttributeLabel("Language") }
        }
    }

    @Test
    fun `should find category attribute by name and category id if find category attribute exist`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }

            When { findAttributeByName("seller_type", "214") }
            Then { checkCategoryAttributeName("seller_type") }
            Then { checkCategoryAttributeLabel("Seller type") }
        }
    }

    @Test
    fun `should NOT find category attribute if find category attribute by name is with NULL name`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            When { findAttributeByName(null) }
            Then { checkCategoryAttributeName(null) }
            Then { checkCategoryAttributeLabel(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualHasValidCacheResult: Boolean = false
        private var actualCacheDataResult: List<FiltersCategoryAttributeDto>? = null
        private lateinit var actualFoundCategoryAttributesListResult: List<FiltersCategoryAttributeDto>
        private var actualFoundCategoryAttributeResult: FiltersCategoryAttributeDto? = null

        private val testSubject = FiltersCategoryAttributesCache

        override fun setup() {
            mockkObject(CurrentDateProvider)
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubCurrentDateTimestamp(timestamp: Long) {
            every { CurrentDateProvider.getCurrentTimestamp() } returns timestamp
        }

        fun stubFiltersCategoryAttributesCache(filtersCategoryAttributesList: List<FiltersCategoryAttributeDto>?) {
            testSubject.data = filtersCategoryAttributesList
        }

        fun hasValidCache() {
            actualHasValidCacheResult = testSubject.hasValidCache()
        }

        fun getCacheData() {
            actualCacheDataResult = testSubject.data
        }

        fun findCategoryAttributesOrEmpty(id: String) {
            actualFoundCategoryAttributesListResult = testSubject.findCategoryAttributesOrEmpty(id)
        }

        fun findAttributeByName(name: String?, categoryId: String? = null) {
            actualFoundCategoryAttributeResult = testSubject.findAttributeByName(name, categoryId)
        }

        fun checkHasValidCache(expected: Boolean) {
            assertEquals(actualHasValidCacheResult, expected)
        }

        fun checkCacheDataIsNull() {
            assertNull(actualCacheDataResult)
        }

        fun checkCacheDataIsNotNull() {
            assertNotNull(actualCacheDataResult)
        }

        fun checkFoundCategoryAttributeLabelAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, actualFoundCategoryAttributesListResult[position].label)
        }

        fun checkCategoryAttributesListSize(expected: Int) {
            assertEquals(expected, actualFoundCategoryAttributesListResult.size)
        }

        fun checkCategoryAttributeName(expected: String?) {
            assertEquals(expected, actualFoundCategoryAttributeResult?.name)
        }

        fun checkCategoryAttributeLabel(expected: String?) {
            assertEquals(expected, actualFoundCategoryAttributeResult?.label)
        }

    }
}