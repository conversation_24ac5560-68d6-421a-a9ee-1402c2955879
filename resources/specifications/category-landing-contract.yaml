openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-category-landing
  license:
    name: MIT
tags:
  - name: BFF-Category-Landing
    description: Mobile Apps BFF Category Landing API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    CategoryIdQuery:
      in: query
      name: categoryId
      description: Category ID
      required: true
      schema:
        type: integer
        format: int64
    LocationIdQuery:
      in: query
      name: locationId
      description: Location ID
      required: false
      schema:
        type: integer
        format: int64
    LatitudeQuery:
      in: query
      name: latitude
      description: Latitude of the location
      required: false
      schema:
        type: number
        format: double
    LongitudeQuery:
      in: query
      name: longitude
      description: Longitude of the location
      required: false
      schema:
        type: number
        format: double
    VehicleMakeQuery:
      in: query
      name: vehicle_make
      description: The cars category vehicle make
      required: false
      schema:
        type: string
    VehicleModelQuery:
      in: query
      name: vehicle_model
      description: The cars category vehicle model
      required: false
      schema:
        type: string
  schemas:
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
    ScreenResponse:
      type: object
      properties:
        title:
          type: string
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        landscapeData:
          $ref: '#/components/schemas/RowLayoutItems'
        analyticsParameters:
          type: object
          additionalProperties:
            type: string
      required:
        - portraitData
    RowLayoutItems:
      type: array
      items:
        $ref: '#/components/schemas/RowLayout'
    RowLayout:
      type: object
      properties:
        type:
          type: string
          enum:
            - IMAGE_ROW
            - TITLE_ROW
            - DROPDOWN_ROW
            - BUTTON_ROW
            - VEHICLE_BODY_TYPE_ROW
            - TODAY_PICKS_ROW
            - LISTING_ROW
        data:
          $ref: '#/components/schemas/CategoryLandingItems'
        scrollingBehaviour:
          type: string
          enum:
            - COLLAPSE_AT_TOP
            - COLLAPSE_ANYWHERE
        topDivider:
          type: boolean
        bottomDivider:
          type: boolean
      required:
        - type
        - data
    CategoryLandingItems:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/ImageCardDto'
          - $ref: '#/components/schemas/TitleCenterCardDto'
          - $ref: '#/components/schemas/DropdownCardDto'
          - $ref: '#/components/schemas/PrimaryButtonCardDto'
          - $ref: '#/components/schemas/VehicleBodyTypeCardDto'
          - $ref: '#/components/schemas/TodayPicksCardDto'
          - $ref: '#/components/schemas/ListingCardDto'
    ImageCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - IMAGE_CARD
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
      required:
        - type
        - image
    TitleCenterCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - TITLE_CENTER_CARD
        text:
          type: string
        size:
          type: string
          enum:
            - LARGE
            - MEDIUM
            - SMALL
        colour:
          type: string
          enum:
            - FOREGROUND_SUBDUED
            - FOREGROUND_DEFAULT
      required:
        - type
        - text
        - size
    DropdownCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - DROPDOWN_CARD
        title:
          type: string
        param:
          type: string
        selectedOption:
          type: string
        options:
          type: object
          additionalProperties:
            type: string
        changeAction:
          type: string
          enum:
            - REFRESH
      required:
        - type
        - title
        - param
        - selectedOption
        - options
    PrimaryButtonCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - PRIMARY_BUTTON_CARD
        text:
          type: string
        size:
          type: string
          enum:
            - LARGE
            - MEDIUM
            - SMALL
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - type
        - text
        - size
        - destination
    VehicleBodyTypeCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - VEHICLE_BODY_TYPE_CARD
        title:
          type: string
        bodyTypes:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/BodyType'
      required:
        - type
        - title
        - bodyTypes
    BodyType:
      type: object
      properties:
        text:
          type: string
        iconType:
          type: string
          enum:
            - HATCHBACK
            - SALOON
            - COUPE
            - SUV
            - PICKUP
            - CONVERTIBLE
            - VAN
            - ESTATE
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - text
        - iconType
        - destination
    TodayPicksCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - TODAY_PICKS_CARD
        text:
          type: string
        location:
          type: object
          properties:
            text:
              type: string
            destination:
              $ref: '#/components/schemas/DestinationDto'
      required:
        - type
        - text
        - location
    ListingCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_CARD
        adId:
          type: integer
          format: int64
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        location:
          type: string
        price:
          type: string
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
        isUrgent:
          type: boolean
        isFeatured:
          type: boolean
      required:
        - type
        - adId
        - title
        - destination
    Image:
      type: object
      properties:
        url:
          type: string
        width:
          type: integer
        height:
          type: integer
      required:
        - url
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
        analyticsEventData:
          $ref: '#/components/schemas/AnalyticsEventData'
      required:
        - route
        - type
    AnalyticsEventData:
      type: object
      properties:
        eventName:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
      required:
        - eventName
paths:
  /category-landing/screen:
    get:
      summary: Get the category landing screen
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/CategoryIdQuery"
        - $ref: "#/components/parameters/LocationIdQuery"
        - $ref: "#/components/parameters/LatitudeQuery"
        - $ref: "#/components/parameters/LongitudeQuery"
        - $ref: "#/components/parameters/VehicleMakeQuery"
        - $ref: "#/components/parameters/VehicleModelQuery"
      responses:
        '200':
          description: The mobile apps category landing screen UI ready to be rendered on the users device
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"