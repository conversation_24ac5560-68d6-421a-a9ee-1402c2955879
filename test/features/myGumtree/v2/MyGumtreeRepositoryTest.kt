package features.myGumtree.v2

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.RawCapiAdStatus
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.api.reviews.models.RawCanPostReview
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import com.gumtree.mobile.api.userService.models.RawUserServiceUserType
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.chat.ChatService
import com.gumtree.mobile.features.myGumtree.v2.DefaultMyGumtreeRepository
import com.gumtree.mobile.features.myGumtree.v2.Mappers
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeAnalyticsProvider
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeRepository
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeService
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeTitleRowMapper
import com.gumtree.mobile.features.myGumtree.v2.PostType
import com.gumtree.mobile.features.reviews.ReviewsAccountsFetcher
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.ConversationData
import com.gumtree.mobile.features.screens.layoutsData.PotentialBuyerDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import com.gumtree.mobile.utils.extensions.getOrEmpty
import tools.layoutsDataFactory.MyGumtreeDataFactory
import tools.layoutsDataFactory.ProfileDataFactory
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import retrofit2.Response
import tools.BaseRobot
import tools.DataFactory
import tools.DataFactory.SOME_ANALYTICS_EVENT_NAME
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import utils.TestDispatcherProvider
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import tools.DataFactory.SOME_AD_FOR_SALE_ID
import tools.rawDataFactory.RawCapiConversationsFactory
import tools.rawDataFactory.RawCoreChatFactory
import tools.rawDataFactory.RawFlatAdsFactory
import tools.rawDataFactory.UserServiceDataFactory

class MyGumtreeRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should map all input data to correct screen response`() = runTest {
        val listings = MyGumtreeDataFactory.createListingsRows()
        val profileRow = ProfileDataFactory.createProfileRow(DataFactory.SOME_USER_DISPLAY_NAME)
        val titleRow = MyGumtreeTitleRowMapper().map()
        val nextPage = "nextPage"

        runUnitTest(robot) {
            Given {
                stubMyGumtreeAdsMapper(listings)
                stubProfileRowMapper(profileRow)
                stubTitleRowMapper(titleRow)
                stubNextPage(nextPage)
            }
            When { readScreen() }
            Then { checkScreenResponse(listings, profileRow, titleRow, nextPage) }
        }
    }

    @Test
    fun `should map all input data to correct potential buyer response with capi chat`() = runTest {
        runUnitTest(robot) {
            Given {
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCapi(0)
                stubReviewAccount()
                stubPostReview(true)
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyerNotEmpty(listOf()) }
        }
    }

    @Test
    fun `should map all input data to correct potential buyer response with capi chat No Post`() = runTest {
        runUnitTest(robot) {
            Given {
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCapi(0)
                stubReviewAccount()
                stubPostReview(false)
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should map all input data to correct potential buyer response with core chat`() = runTest {
        runUnitTest(robot) {
            Given {
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCoreChat(10)
                stubReviewAccount()
                stubPostReview(true)
            }
            When { getPotentialBuyerByCoreChat(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyerNotEmpty(listOf()) }
        }
    }

    @Test
    fun `should map all input data to correct potential buyer response with core chat No Post`() = runTest {
        runUnitTest(robot) {
            Given {
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCoreChat(10)
                stubReviewAccount()
                stubPostReview(false)
            }
            When { getPotentialBuyerByCoreChat(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should map all input data to correct potential buyer response with core chat filter equals adId`() = runTest {
        runUnitTest(robot) {
            Given {
                stubAuthHeaders()
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCoreChat(10)
                stubConversationDetailFromCapi(10)
                stubReviewAccount()
                stubPostReview(true)
            }
            When { getPotentialBuyerByCoreChat(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_EDITOR_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should use service to delete user ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulDeleteUserAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkServiceDeleteUserAd(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should create authorisation headers when delete user ad`() = runTest {
        runUnitTest(robot) {
            Given { stubSuccessfulDeleteUserAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkProviderCreateAuthorisationHeaders() }
        }
    }

    @Test
    fun `should create authorised headers, call service in order when deleting user ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulDeleteUserAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkDeleteUserAdActionsInOrder(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should authenticate when reposting`() = runTest {
        runUnitTest(robot) {
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            Given { stubAuthHeaders() }
            When { repostFreeAd() }
            Then { checkRepostCalled() }
        }
    }

    @Test
    fun `should return adPostedSuccess event`() = runTest {
        val analyticsEventData = AnalyticsEventData(SOME_ANALYTICS_EVENT_NAME)
        runUnitTest(robot) {
            Given { stubRawAdResponse() }
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            Given { stubPostType(PostType.EDIT) }
            Given { stubIsPaidAd(true) }
            Given { stubClientSemantics(ClientSemantics(platform = ClientPlatform.ANDROID)) }
            Given { stubGetAdPostedAnalyticsEventResponse(analyticsEventData) }
            When { getAdPostedSuccessAnalyticsEvent() }
            Then { checkAdPostedAnalyticsEvent(analyticsEventData) }
            Then { checkAdPostedAnalyticsEventParams() }
        }
    }

    @Test
    fun `should not modify rawCapiAds for supported app version`() = runTest {
        val listings = MyGumtreeDataFactory.createListingsRows()
        val profileRow = ProfileDataFactory.createProfileRow(DataFactory.SOME_USER_DISPLAY_NAME)
        val titleRow = MyGumtreeTitleRowMapper().map()
        val nextPage = "nextPage"
        val rawCapiAds = RawCapiAdList().apply {
            rawAds = listOf(
                RawCapiAd().apply { id = "ad1"; status = RawCapiAdStatus("PENDING_VERIFY") },
                RawCapiAd().apply { id = "ad2"; status = RawCapiAdStatus("ACTIVE") }
            )
        }

        runUnitTest(robot) {
            Given {
                stubMyGumtreeAdsMapper(listings)
                stubProfileRowMapper(profileRow)
                stubTitleRowMapper(titleRow)
                stubNextPage(nextPage,2)
                stubPlatformHeader("ANDROID")
                stubAppVersionHeader("10.1.26")
                stubUserAdsResponse(rawCapiAds)
            }
            When { readScreen() }
            Then {
                checkScreenResponse(listings, profileRow, titleRow, nextPage)
                checkMyGumtreeAdsMapperCalled(rawCapiAds)
                checkRawCapiAdsNotFiltered(rawCapiAds)
            }
        }
    }

    @Test
    fun `should filter PHONE_VERIFY ads in rawCapiAds for unsupported app version`() = runTest {
        val listings = MyGumtreeDataFactory.createListingsRows()
        val profileRow = ProfileDataFactory.createProfileRow(DataFactory.SOME_USER_DISPLAY_NAME)
        val titleRow = MyGumtreeTitleRowMapper().map()
        val nextPage = "nextPage"
        val rawCapiAds = RawCapiAdList().apply {
            rawAds = listOf(
                RawCapiAd().apply { id = "ad1"; status = RawCapiAdStatus("PENDING_VERIFY") },
                RawCapiAd().apply { id = "ad2"; status = RawCapiAdStatus("ACTIVE") }
            )
        }

        runUnitTest(robot) {
            Given {
                stubMyGumtreeAdsMapper(listings)
                stubProfileRowMapper(profileRow)
                stubTitleRowMapper(titleRow)
                stubNextPage(nextPage,1)
                stubPlatformHeader("ANDROID")
                stubAppVersionHeader("1.0.0")
                stubUserAdsResponse(rawCapiAds)
            }
            When { readScreen() }
            Then {
                checkScreenResponse(listings, profileRow, titleRow, nextPage)
                checkMyGumtreeAdsMapperCalled(rawCapiAds)
                checkRawCapiAdsFiltered(rawCapiAds)
            }
        }
    }

    @Test
    fun `should handle null rawCapiAds or empty rawAds`() = runTest {
        val listings = MyGumtreeDataFactory.createListingsRows()
        val profileRow = ProfileDataFactory.createProfileRow(DataFactory.SOME_USER_DISPLAY_NAME)
        val titleRow = MyGumtreeTitleRowMapper().map()
        val nextPage = "nextPage"
        val rawCapiAds = RawCapiAdList().apply { rawAds = emptyList() }

        runUnitTest(robot) {
            Given {
                stubMyGumtreeAdsMapper(listings)
                stubProfileRowMapper(profileRow)
                stubTitleRowMapper(titleRow)
                stubNextPage(nextPage)
                stubPlatformHeader("ANDROID")
                stubAppVersionHeader("1.0.0")
                stubUserAdsResponse(rawCapiAds)
            }
            When { readScreen() }
            Then {
                checkScreenResponse(listings, profileRow, titleRow, nextPage)
                checkMyGumtreeAdsMapperCalled(rawCapiAds)
                checkRawCapiAdsEmpty(rawCapiAds)
            }
        }
    }

    @Test
    fun `should map capi conversation with actual conversation data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubAuthHeaders()
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCapi(1)
                stubConversationDetailFromCapi(1)
                stubReviewAccount()
                stubPostReview(true)
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyerNotEmpty(listOf(MyGumtreeDataFactory.createConversationData())) }
        }
    }

    @Test
    fun `should filter out capi conversation when adId does not match`() = runTest {
        runUnitTest(robot) {
            Given {
                stubAuthHeaders()
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCapi(1)
                stubConversationDetailFromCapi(1)
                stubReviewAccount()
                stubPostReview(true)
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should filter out capi conversation when user is PRO`() = runTest {
        runUnitTest(robot) {
            Given {
                stubAuthHeaders()
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetailsWithProUser()
                stubAllConversationFromCapi(1)
                stubConversationDetailFromCapi(1)
                stubReviewAccount()
                stubPostReview(true)
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should filter out capi conversation when cannot post review`() = runTest {
        runUnitTest(robot) {
            Given {
                stubAuthHeaders()
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCapi(1)
                stubConversationDetailFromCapi(1)
                stubReviewAccount()
                stubPostReview(false)
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should filter out capi conversation when canPostReview returns null`() = runTest {
        runUnitTest(robot) {
            Given {
                stubAuthHeaders()
                stubPotentialBuyerMapperFromCapi(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCapi(1)
                stubConversationDetailFromCapi(1)
                stubReviewAccount()
                stubPostReviewReturnsNull()
            }
            When { getPotentialBuyer(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should filter out core chat conversation when canPostReview returns null`() = runTest {
        runUnitTest(robot) {
            Given {
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubUserDetailsByUserId(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCoreChat(1)
                stubReviewAccount()
                stubPostReviewReturnsNull()
            }
            When { getPotentialBuyerByCoreChat(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    @Test
    fun `should filter out core chat conversation when cannot post review`() = runTest {
        runUnitTest(robot) {
            Given {
                stubPotentialBuyerMapperFromCorechat(MyGumtreeDataFactory.createConversationData())
                stubChatServiceAd(RawFlatAdsFactory.createRawFlatAd(categoryId = SOME_AD_FOR_SALE_ID))
                stubUserDetails(UserServiceDataFactory.createRawUserDetails())
                stubUserDetailsByUserId(UserServiceDataFactory.createRawUserDetails())
                stubAllConversationFromCoreChat(1)
                stubReviewAccount()
                stubPostReview(false)
            }
            When { getPotentialBuyerByCoreChat(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkPotentialBuyer(listOf()) }
        }
    }

    private class Robot : BaseRobot {

        private lateinit var testSubject: MyGumtreeRepository
        private var service: MyGumtreeService = mockk(relaxed = true)

        private val authHeadersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val analyticsProvider: MyGumtreeAnalyticsProvider = mockk(relaxed = true)
        private val chatService: ChatService = mockk(relaxed = true)
        private val reviewsAccountsFetcher: ReviewsAccountsFetcher = mockk(relaxed = true)
        private val mappers: Mappers = mockk(relaxed = true) {
            every { myGumtreeTitleRowMapper } returns mockk(relaxed = true)
            every { myGumtreeAdsMapper } returns mockk(relaxed = true)
            every { profileMapper } returns mockk(relaxed = true)
            every { potentialBuyerMapper } returns mockk(relaxed = true)
        }
        private var experimentVariant: Variant = Variant.B
        private val authHeaders: ApiHeaders = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true) {
            every { getOrEmpty(ApiHeaderParams.AUTHORISATION_USER_EMAIL) } returns DataFactory.SOME_USER_EMAIL
            every { getOrEmpty(ApiHeaderParams.PLATFORM) } returns ClientPlatform.ANDROID.name
        }
        private val callHeadersWithCoreChat: Headers = mockk(relaxed = true) {
            every { getOrEmpty(ApiHeaderParams.AUTHORISATION_USER_EMAIL) } returns DataFactory.SOME_USER_EMAIL
            every { getOrEmpty(ApiHeaderParams.PLATFORM) } returns ClientPlatform.ANDROID.name
            every { getOrEmpty(ApiHeaderParams.EXPERIMENTS) } returns createExperimentsHeader()
        }
        private val pageCalculator: PageCalculator = mockk {
            every { calculateNextPage(any(), any()) } returns EMPTY_STRING
            every { calculateNextPage(any(), any(), any() ,any()) } returns EMPTY_STRING
        }
        private lateinit var clientSemantics: ClientSemantics
        private lateinit var postType: PostType

        private lateinit var adId: String
        private lateinit var capiAd: RawCapiAd
        private lateinit var rawAd: RawFlatAd
        private var isPaidAd: Boolean = false

        private var actualResponse: ScreenResponse? = null
        private var actualAdPostedEvent: AnalyticsEventData? = null
        private var actualPotentialBuyerResponse: PotentialBuyerDto? = null

        override fun setup() {
            testSubject = DefaultMyGumtreeRepository(
                authHeadersProvider,
                authHeadersProvider,
                service,
                pageCalculator,
                TestDispatcherProvider(),
                analyticsProvider,
                mappers,
                chatService,
                reviewsAccountsFetcher
            )
            experimentVariant = Variant.A
        }

        fun stubAuthHeaders() {
            every { authHeadersProvider.createAuthorisedHeaders(callHeaders) } returns authHeaders
        }

        fun stubAdId(adId: String) {
            this.adId = adId
        }

        fun stubRawAdResponse(rawAd: RawFlatAd = RawFlatAdsFactory.createRawFlatAd()) {
            coEvery { service.getAdDetailsById(any()) } returns rawAd
            this.rawAd = rawAd
        }

        fun stubNextPage(nextPage: String) {
            every { pageCalculator.calculateNextPage(any(), any()) } returns nextPage
        }

        fun stubNextPage(nextPage: String, dataSize: Int = 0) {
            every { pageCalculator.calculateNextPage(
                page = any()
                , size = any()
                , nonPagingQueryParams = any()
                , dataSize = dataSize )
            } returns nextPage
        }

        fun stubMyGumtreeAdsMapper(adRows: List<RowLayout<UiItem>>) {
            every { mappers.myGumtreeAdsMapper.map(any(), any(), any()) } returns adRows
        }

        fun stubPotentialBuyerMapperFromCapi(conversationData: ConversationData) {
            every { mappers.potentialBuyerMapper.map(any()) } returns conversationData
        }

        fun stubPotentialBuyerMapperFromCorechat(conversationData: ConversationData) {
            every { mappers.potentialBuyerMapper.mapWithoutBuyerName(any()) } returns conversationData
        }

        fun stubChatServiceAd(rawCapiAd: RawFlatAd) {
            coEvery { chatService.getAdDetailsById(any()) } returns rawCapiAd
        }

        fun stubUserDetails(rawUser: RawUserServiceUserDetails) {
            coEvery { service.getUserDetails(any()) } returns rawUser
        }

        fun stubUserDetailsWithProUser() {
            coEvery { service.getUserDetails(any()) } returns UserServiceDataFactory.createRawUserDetails(
                userType = RawUserServiceUserType.PRO
            )
        }

        fun stubAllConversationFromCapi(number: Int) {
            if (number == 0) {
                val emptyConversationList = RawCapiConversationsFactory.createRawConversationList(0, 0)
                coEvery { 
                    service.getAllConversations(any<ApiHeaders>(), any<String>(), any<String>()) 
                } returns emptyConversationList
            } else {
                val conversationList = RawCapiConversationsFactory.createRawConversationList(
                    number, 
                    6, 
                    conversationAdId = DataFactory.SOME_AD_ID
                )
                val emptyConversationList = RawCapiConversationsFactory.createRawConversationList(0, 0)
                coEvery { 
                    service.getAllConversations(any<ApiHeaders>(), any<String>(), any<String>()) 
                } returnsMany listOf(conversationList, emptyConversationList)
            }
        }

        fun stubAllConversationFromCoreChat(number: Int) {
            coEvery { service.getAllConversations(any<String>(), any(), any()) } returns
                RawCoreChatFactory.createRawAllConversationsResponseBySameAd(count = number)
        }

        fun stubReviewAccount() {
            coEvery { reviewsAccountsFetcher.fetchAccountIdByUserId(any(), any()) } returns
                MyGumtreeDataFactory.createReviewAccountData()
        }

        fun stubPostReview(canReview: Boolean) {
            coEvery { chatService.canPostReview(any()) } returns RawCanPostReview(value = canReview)
        }

        fun stubPostReviewReturnsNull() {
            coEvery { chatService.canPostReview(any()) } throws Exception("Network error")
        }

        fun stubUserDetailsByUserId(rawUser: RawUserServiceUserDetails) {
            coEvery { service.getUserDetailsByUserId(any()) } returns rawUser
        }

        fun stubConversationDetailFromCapi(number: Int) {
            val conversation = RawCapiConversationsFactory.createRawConversationWithRawMessages(number)
            coEvery { 
                service.getConversationDetails(any<ApiHeaders>(), any<String>(), any<String>()) 
            } returns conversation
        }

        fun stubTitleRowMapper(titleRow: RowLayout<UiItem>) {
            every { mappers.myGumtreeTitleRowMapper.map(any()) } returns titleRow
        }

        fun stubProfileRowMapper(profileRow: RowLayout<UiItem>) {
            every { mappers.profileMapper.map(any()) } returns profileRow
        }

        fun stubSuccessfulDeleteUserAdHttpResult() {
            coEvery { service.deleteUserAd(any(), any(), any()) } returns Response.success(Unit)
        }

        fun stubClientSemantics(clientSemantics: ClientSemantics) {
            this.clientSemantics = clientSemantics
        }

        fun stubPostType(postType: PostType) {
            this.postType = postType
        }

        fun stubIsPaidAd(isPaidAd: Boolean) {
            this.isPaidAd = isPaidAd
        }

        fun stubGetAdPostedAnalyticsEventResponse(analyticsEventData: AnalyticsEventData) {
            every { analyticsProvider.getAdPostedAnalyticsEvent(any(), any(), any(), any()) } returns analyticsEventData
        }

        fun stubPlatformHeader(platform: String) {
            every { callHeaders[ApiHeaderParams.PLATFORM] } returns platform
        }

        fun stubAppVersionHeader(appVersion: String) {
            every { callHeaders[ApiHeaderParams.APP_VERSION] } returns appVersion
        }

        fun stubUserAdsResponse(rawCapiAds: RawCapiAdList) {
            coEvery { service.getUserAds(any(), any(), any(), any(), any()) } returns rawCapiAds
        }

        suspend fun getAdPostedSuccessAnalyticsEvent() {
            actualAdPostedEvent = testSubject.getAdPostedSuccessAnalyticsEvent(callHeaders, adId, postType, isPaidAd)
        }

        suspend fun readScreen() {
            actualResponse = testSubject.readScreen(callHeaders, "ALL_ADS", "", "20", DEFAULT_PAGE_SIZE)
        }

        suspend fun delete(userName: String, adId: String) {
            testSubject.delete(callHeaders, userName, adId)
        }

        suspend fun getPotentialBuyer(
            userName: String,
            adId: String,
        ) {
            actualPotentialBuyerResponse = testSubject.getPotentialBuyer(callHeaders, userName, adId)
        }

        suspend fun getPotentialBuyerByCoreChat(
            userName: String,
            adId: String,
        ) {
            actualPotentialBuyerResponse = testSubject.getPotentialBuyer(callHeadersWithCoreChat, userName, adId)
        }

        suspend fun repostFreeAd() {
            testSubject.repostFreeAd(callHeaders, adId)
        }

        fun checkRepostCalled() {
            coVerify { service.repostForFree(any(), adId) }
        }

        fun checkScreenResponse(
            portraitData: PortraitData,
            profileRow: RowLayout<UiItem>,
            titleRow: RowLayout<UiItem>,
            nextPage: String,
        ) {
            assertEquals(portraitData, actualResponse?.portraitData)
            assertEquals(listOf(profileRow, titleRow), actualResponse?.stickyBar)
            assertEquals(nextPage, actualResponse?.nextPage)
        }

        fun checkPotentialBuyer(
            conversationDatas: List<ConversationData>,
        ) {
            assertEquals(conversationDatas, actualPotentialBuyerResponse?.conversationData)
        }

        fun checkPotentialBuyerNotEmpty(
            conversationDatas: List<ConversationData>,
        ) {
            assertNotNull(conversationDatas)
        }

        fun checkServiceDeleteUserAd(expectedUserName: String, expectedAdId: String) {
            coVerify { service.deleteUserAd(authHeaders, expectedUserName, expectedAdId) }
        }

        fun checkProviderCreateAuthorisationHeaders() {
            verify { authHeadersProvider.createAuthorisedHeaders(callHeaders) }
        }

        fun checkDeleteUserAdActionsInOrder(expectedUserName: String, expectedAdId: String) {
            coVerifyOrder {
                authHeadersProvider.createAuthorisedHeaders(callHeaders)
                service.deleteUserAd(authHeaders, expectedUserName, expectedAdId)
            }
        }

        fun checkAdPostedAnalyticsEventParams() {
            verify { analyticsProvider.getAdPostedAnalyticsEvent(rawAd, clientSemantics, postType, isPaidAd) }
        }

        fun checkAdPostedAnalyticsEvent(expected: AnalyticsEventData) {
            assertEquals(expected, actualAdPostedEvent)
        }

        fun checkMyGumtreeAdsMapperCalled(rawCapiAds: RawCapiAdList) {
            verify { mappers.myGumtreeAdsMapper.map(rawCapiAds, any(), any()) }
        }

        fun checkRawCapiAdsNotFiltered(rawCapiAds: RawCapiAdList) {
            assertEquals(2, rawCapiAds.rawAds?.size)
            assertEquals(rawCapiAds.rawAds?.any { it.status?.value == "PENDING_VERIFY" } , true)
        }

        fun checkRawCapiAdsFiltered(rawCapiAds: RawCapiAdList) {
            assertEquals(1, rawCapiAds.rawAds?.size)
            assertEquals(rawCapiAds.rawAds?.any { it.status?.value == "PENDING_VERIFY" }, false)
        }

        fun checkRawCapiAdsEmpty(rawCapiAds: RawCapiAdList) {
            assertEquals(0, rawCapiAds.rawAds?.size)
        }

        private fun createExperimentsHeader(): String {
            return "${Experiment.GTNA_4300.key}.${experimentVariant.name}"
        }
    }
}
