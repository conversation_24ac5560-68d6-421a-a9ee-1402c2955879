package com.gumtree.mobile.api.homeFeed

import com.gumtree.mobile.api.common.OkHttpConnectionPoolConfiguration
import java.util.concurrent.TimeUnit

// https://github.com/Netflix/Hystrix/wiki/Configuration#ThreadPool 12 RPS ×0.5 seconds + 4 = 10
const val HOME_FEED_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS = 10
const val HOME_FEED_API_CONNECTION_POOL_KEEP_ALIVE_DURATION = 300L
class HomeFeedApiOkHttpConnectionPoolConfiguration : OkHttpConnectionPoolConfiguration {

    override val maxIdleConnections: Int = HOME_FEED_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS

    override val keepAliveDuration: Long = HOME_FEED_API_CONNECTION_POOL_KEEP_ALIVE_DURATION

    override val timeUnit: TimeUnit = TimeUnit.SECONDS
}
