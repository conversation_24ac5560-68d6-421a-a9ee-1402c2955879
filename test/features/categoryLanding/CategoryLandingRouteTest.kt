package features.categoryLanding

import com.gumtree.mobile.features.categoryLanding.CATEGORY_LANDING_SCREEN_PATH
import com.gumtree.mobile.features.categoryLanding.CategoryLandingRepository
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.defaultHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class CategoryLandingRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET category landing screen request with success for Cars category`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getCategoryLandingScreen(CategoryDefaults.CARS.id) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET category landing screen request with IllegalArgumentException error if categoryId NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getCategoryLandingScreen(null) }
        }
    }

    @Test
    fun `should complete GET category landing screen request with error when repository error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(UnauthorisedException()) }
            When { getCategoryLandingScreen(CategoryDefaults.CARS.id) }
        }
    }

    private class Robot: BaseRouteRobot() {

        val repository: CategoryLandingRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse
        private lateinit var screenPortraitData: List<RowLayout<UiItem>>
        private var screenLandscapeData: List<RowLayout<UiItem>>? = null

        fun stubScreenPortraitData() {
            screenPortraitData = emptyList()
        }

        fun stubScreenLandscapeData() {
            screenLandscapeData = null
        }

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery {
                repository.readScreen(any(), any(), any())
            } throws error
        }

        suspend fun getCategoryLandingScreen(categoryId: String?) {
            actualResponse = client.get(CATEGORY_LANDING_SCREEN_PATH) {
                parameter(ApiQueryParams.CATEGORY_ID, categoryId)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }
}