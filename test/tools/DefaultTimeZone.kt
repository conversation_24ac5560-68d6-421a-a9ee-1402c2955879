package tools

import org.junit.jupiter.api.extension.ExtendWith

/**
 * `@DefaultTimeZone` is a JUnit Jupiter extension to change the value
 * returned by [java.util.TimeZone.getDefault] for a test execution.
 *
 *
 * The [java.util.TimeZone] to set as the default `TimeZone` is
 * configured bei specifying the `TimeZone` ID as defined by
 * [java.util.TimeZone.getTimeZone]. After the annotated element
 * has been executed, the default `TimeZone` will be restored to its
 * original value.
 *
 *
 * `@DefaultTimeZone` can be used on the method and on the class
 * level. If a class is annotated, the configured `TimeZone` will be the
 * default `TimeZone` for all tests inside that class. Any method level
 * configurations will override the class level default `TimeZone`.
 *
 * @see java.util.TimeZone.getDefault
 */
@Target(
    AnnotationTarget.FUNCTION,
    AnnotationTarget.PROPERTY_GETTER,
    AnnotationTarget.PROPERTY_SETTER,
    AnnotationTarget.CLASS
)
@Retention(AnnotationRetention.RUNTIME)
@ExtendWith(DefaultTimeZoneExtension::class)
annotation class DefaultTimeZone(
    /**
     * The ID for a `TimeZone`, either an abbreviation such as "PST", a
     * full name such as "America/Los_Angeles", or a custom ID such as
     * "GMT-8:00". Note that the support of abbreviations is for JDK 1.1.x
     * compatibility only and full names should be used.
     */
    val value: String
)