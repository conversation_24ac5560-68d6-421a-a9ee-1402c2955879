package com.gumtree.mobile.api.partnerships.api

import com.gumtree.mobile.api.partnerships.PartnershipAdsApiParams
import com.gumtree.mobile.api.partnerships.PartnershipAdsUrls
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import retrofit2.http.GET
import retrofit2.http.Query
import utils.ConvertHttpStatus

fun interface PartnershipAdsApi {
    @ConvertHttpStatus(from = 204, to = 404)
    @GET(PartnershipAdsUrls.CLICK_OUT_ENDPOINT)
    suspend fun getPartnershipDetails(
        @Query(PartnershipAdsApiParams.PAGE_TYPE) pageType: String,
        @Query(PartnershipAdsApiParams.PLATFORM) platform: String,
        @Query(PartnershipAdsApiParams.SLOT_ID) slotId: String,
        @Query(PartnershipAdsApiParams.CATEGORY_HIERARCHY_SEO_NAME) categoryHierarchy: String,
        @Query(PartnershipAdsApiParams.LISTING_LOCATION) location: String?,
        @Query(PartnershipAdsApiParams.LISTING_IMAGE) imageUrl: String?,
        @Query(PartnershipAdsApiParams.LISTING_TITLE) title: String?,
        @Query(PartnershipAdsApiParams.ENCRYPTED_VRN_ZUTO, encoded = true) encryptedVrn: String?,
    ): PartnershipDetailsResponse
}
