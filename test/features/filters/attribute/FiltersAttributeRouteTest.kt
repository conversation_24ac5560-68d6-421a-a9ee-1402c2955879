package features.filters.attribute

import com.gumtree.mobile.features.filters.attribute.FILTERS_ATTRIBUTE_SCREEN_PATH
import com.gumtree.mobile.features.filters.attribute.FiltersAttributeRepository
import com.gumtree.mobile.features.filters.FiltersCategoryAttributesCacheManager
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiQueryParams
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class FiltersAttributeRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
        single { robot.filtersCategoryAttributesCacheManager }
    }

    @Test
    fun `should complete GET filters attribute values screen request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getFiltersAttributeValuesScreen("seller_type", "Seller type") }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET filters attribute values screen request with error`() = runRouteTestForException(module, InternalError::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(InternalError()) }
            When { getFiltersAttributeValuesScreen(DataFactory.anyString(), DataFactory.anyString()) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: FiltersAttributeRepository = mockk(relaxed = true)
        val filtersCategoryAttributesCacheManager: FiltersCategoryAttributesCacheManager = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse
        private lateinit var screenPortraitData: List<RowLayout<UiItem>>
        private var screenLandscapeData: List<RowLayout<UiItem>>? = null

        fun stubScreenPortraitData() {
            screenPortraitData = emptyList()
        }

        fun stubScreenLandscapeData() {
            screenLandscapeData = null
        }

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery {
                repository.readScreen(any())
            } throws error
        }

        suspend fun getFiltersAttributeValuesScreen(
            name: String? = null,
            value: String? = null
        ) {
            actualResponse = client.get(FILTERS_ATTRIBUTE_SCREEN_PATH) {
                parameter(ApiQueryParams.NAME, name)
                parameter(ApiQueryParams.VALUE, value)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }
}