package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("VIP_BUTTON_CARD")
data class VipButtonCardDto( // TODO: Rename this class and make the name more generic (the serial name potentially could stay as it is)
    val text: String,
    val size: Size,
    val buttonType: Type,
    val iconType: IconType?,
    val action: ActionDto,
    val ratio: Int = 1,
) : UiItem {

    /**
     * The different icons to be used on button where required
     */
    enum class IconType { PHONE, EMAIL, MESSAGE, REQUEST }

    /**
     * The UI type of button (matching design system)
     */
    enum class Type { PRIMARY, SECONDARY, TEXT }

    /**
     * The different sizes of the button (matching the design system)
     */
    enum class Size { LARGE, MEDIUM, SMALL }
}
