package com.gumtree.mobile.api.common

import java.util.concurrent.TimeUnit

internal const val CONNECTION_TIMEOUT: Long = 2
internal const val READ_TIMEOUT: Long = 10
internal const val WRITE_TIMEOUT: Long = 10
internal const val CALL_TIMEOUT: Long = 11

/**
 * Interface for the OkHttp clients configurations
 */
interface OkHttpConfiguration {
    /**
     * The maximum time for the OkHttp client connection before throws timeout
     */
    val connectTimeout: Long

    /**
     * The maximum time for the OkHttp client connection when read data before throws timeout
     */
    val readTimeout: Long

    /**
     * The maximum time for the OkHttp client connection when write data before throws timeout
     */
    val writeTimeout: Long

    val callTimout: Long

    /**
     * The time unit for the timeouts
     */
    val timeunit: TimeUnit
}

class DefaultApiOkHttpConfiguration : OkHttpConfiguration {

    override val connectTimeout: Long = CONNECTION_TIMEOUT

    override val readTimeout: Long = READ_TIMEOUT

    override val writeTimeout: Long = WRITE_TIMEOUT

    override val callTimout: Long = CALL_TIMEOUT

    override val timeunit: TimeUnit = TimeUnit.SECONDS
}
