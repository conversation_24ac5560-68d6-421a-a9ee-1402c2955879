package com.gumtree.mobile.utils.extensions

import io.ktor.http.Parameters
import io.ktor.server.plugins.MissingRequestParameterException

/**
 * Read the request FormUrlEncoded parameters
 * @param paramKey - the key of the FormUrlEncoded parameter
 * @return - the value of the FormUrlEncoded parameter
 * If the FormUrlEncoded parameter is not found or empty MissingRequestParameterException shall be thrown
 */
fun Parameters.readFormUrlEncodedParamOrThrow(paramKey: String): String {
    val param = this[paramKey]
    return when {
        param.isNullOrEmpty() -> throw MissingRequestParameterException(paramKey)
        else -> param
    }
}

/**
 * Read the request FormUrlEncoded parameter or return default value
 * @param paramKey - the key of the FormUrlEncoded parameter
 * @param default - the default value
 * @return - the value of the FormUrlEncoded parameter or the default value if the parameter value is NULL
 */
fun Parameters.readFormUrlEncodedParamOrDefault(
    paramKey: String,
    default: String,
): String {
    val param = this[paramKey]
    return when {
        param.isNotNull() -> param
        else -> default
    }
}
