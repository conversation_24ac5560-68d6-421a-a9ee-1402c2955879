package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("VIP_SECTION_TABS_CARD")
data class VipSectionTabsCardDto(
    @SerialName("isSticky")
    val isSticky: <PERSON><PERSON><PERSON>,
    @SerialName("tabs")
    val tabs: List<TabItem>,
) : UiItem

@Serializable
data class TabItem(
    @SerialName("label")
    val label: String,
    @SerialName("navigatorIndex")
    val navigatorIndex: Int,
)
