package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("VIP_MAP_CARD")
data class VipMapCardDto(
    @SerialName("isInteractive")
    val isInteractive: Boolean,
    @SerialName("locationLabel")
    val locationLabel: String,
    @SerialName("locationTypeLabel")
    val locationTypeLabel: String,
    @SerialName("latitude")
    val latitude: String,
    @SerialName("longitude")
    val longitude: String,
    @SerialName("radius")
    val radius: String,
    @SerialName("staticMap")
    val staticMap: StaticMapDto?,
) : UiItem
