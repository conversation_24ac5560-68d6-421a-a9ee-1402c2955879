package com.gumtree.mobile.features.blockUser

import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ID_PATH
import com.gumtree.mobile.utils.extensions.readPathParam
import com.gumtree.mobile.utils.extensions.respondCreated
import com.gumtree.mobile.utils.extensions.respondNoContent
import com.gumtree.mobile.utils.regexes.RegEx
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.delete
import io.ktor.server.routing.post
import org.koin.ktor.ext.inject

const val BLOCK_USER_PATH = "$API_V1_MAIN_PATH/block-user/{$ID_PATH}"

fun Route.blockUserRoute() {
    val repository by inject<BlockUserRepository>()

    post(BLOCK_USER_PATH) {
        val callHeaders: Headers = call.request.headers
        val userId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        val response = repository.create(callHeaders, userId)

        call.respondCreated(response)
    }

    delete(BLOCK_USER_PATH) {
        val callHeaders: Headers = call.request.headers
        val userId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        repository.delete(callHeaders, userId)
        call.respondNoContent()
    }
}
