package features.vip

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdSlot
import api.capi.models.RawCapiContactMethod
import api.phoneNumber.api.PhoneNumberApi
import api.similarItems.api.SimilarItemsApi
import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.capi.apis.CapiAdsApi
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.capi.apis.CapiReplyApi
import com.gumtree.mobile.api.capi.models.RawCapiAdLink
import com.gumtree.mobile.api.capi.models.RawCapiAddress
import com.gumtree.mobile.api.common.UserProfileService
import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.locations.models.RawLocationSuggestions
import com.gumtree.mobile.api.papi.PapiHeadersProvider
import com.gumtree.mobile.api.partnerships.api.PartnershipAdsApi
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import com.gumtree.mobile.api.userProfile.api.UserProfileApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.ExternalPartnershipAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.OtherInfoCardDto
import com.gumtree.mobile.features.screens.layoutsData.ReportAdToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ShareToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipDescriptionCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipLocationCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipMapCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipPriceCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipTitleCardDto
import com.gumtree.mobile.features.sellerProfile.v2.DefaultSellerProfileActiveStatusFetcher
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.features.vip.DefaultVipRepository
import com.gumtree.mobile.features.vip.OtherInfoMapper
import com.gumtree.mobile.features.vip.StaticMapFactory
import com.gumtree.mobile.features.vip.VipAdjustTrackingDataProvider
import com.gumtree.mobile.features.vip.VipAdvertsFactory
import com.gumtree.mobile.features.vip.VipAdvertsProvider
import com.gumtree.mobile.features.vip.VipAnalyticsProvider
import com.gumtree.mobile.features.vip.VipBottomOverlayUIProvider
import com.gumtree.mobile.features.vip.VipDefaultsUIProvider
import com.gumtree.mobile.features.vip.VipDescriptionMapper
import com.gumtree.mobile.features.vip.VipHpiFactory
import com.gumtree.mobile.features.vip.VipImageGalleryMapper
import com.gumtree.mobile.features.vip.VipLocationMapper
import com.gumtree.mobile.features.vip.VipMapMapper
import com.gumtree.mobile.features.vip.VipPartnershipAdvertsFactory
import com.gumtree.mobile.features.vip.VipPartnershipAnalyticsProvider
import com.gumtree.mobile.features.vip.VipPostedSinceMapper
import com.gumtree.mobile.features.vip.VipPriceMapper
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration.VIP_POSTED_SINCE_DATE_FORMAT
import com.gumtree.mobile.features.vip.VipSellerProfileMapper
import com.gumtree.mobile.features.vip.VipService
import com.gumtree.mobile.features.vip.VipSimilarItemsMapper
import com.gumtree.mobile.features.vip.VipSpecificationFactory
import com.gumtree.mobile.features.vip.VipTimeAgoFormatter
import com.gumtree.mobile.features.vip.VipTitleMapper
import com.gumtree.mobile.features.vip.VipToolbarActionsProvider
import com.gumtree.mobile.features.vip.VipVerticalsUIProvider
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.responses.BottomOverlay
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import com.gumtree.mobile.utils.extensions.data.MAP_RADIUS_IN_METERS
import io.ktor.http.Headers
import io.ktor.http.HeadersBuilder
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawCapiContactMethodFactory
import tools.rawDataFactory.RawLocationFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.runUnitTest
import utils.TestDispatcherProvider

class DefaultVipRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return Ad image gallery row`() {
        runUnitTest(robot) {
            Given { stubRawAdImage(DataFactory.SOME_AD_IMAGE_URL) }
            When { getVipImagesRow() }
            Then { checkVipImagesRowLayoutType() }
            Then { checkVipImagesRowDataImage(DataFactory.largeSquareImages(DataFactory.SOME_BASE_IMAGE_URL)) }
        }
    }

    @Test
    fun `should return Ad image gallery row with empty data`() {
        runUnitTest(robot) {
            Given { stubRawAdImage(null) }
            When { getVipImagesRow() }
            Then { checkVipImagesRowIsEmpty() }
        }
    }

    @Test
    fun `should return Ad title row`() {
        runUnitTest(robot) {
            Given { stubRawAdTitle(DataFactory.SOME_AD_TITLE) }
            When { getVipTitleRow() }
            Then { checkVipTitleRowLayoutType() }
            Then { checkVipTitleRowDataText(DataFactory.SOME_AD_TITLE) }
        }
    }

    @Test
    fun `should return Ad price row`() {
        runUnitTest(robot) {
            Given { stubRawAdPrice(DataFactory.SOME_AD_PRICE) }
            When { getVipPriceRow() }
            Then { checkVipPriceRowLayoutType() }
            Then { checkVipPriceRowDataText("£${DataFactory.SOME_AD_PRICE}") }
        }
    }

    @Test
    fun `should NOT return Ad price row`() {
        runUnitTest(robot) {
            Given { stubRawAdPrice("09sds") }
            When { getVipPriceRow() }
            Then { checkVipPriceRowIsNull() }
        }
    }

    @Test
    fun `should return Ad location row`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowLayoutType() }
            Then { checkVipLocationRowDataLocationText("Manchester") }
            Then { checkVipLocationRowDataLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkVipLocationRowDataLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkVipLocationRowDataRadius(MAP_RADIUS_IN_METERS) }
        }
    }

    @Test
    fun `should NOT return Ad location row if city is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = null,
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Ad location row if latitude or longitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = null,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowIsNull() }

            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = null,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowIsNull() }
        }
    }

    @Test
    fun `should return Ad description row`() {
        runUnitTest(robot) {
            Given { stubRawAdDescription(DataFactory.SOME_AD_DESCRIPTION) }
            When { getVipDescriptionRow() }
            Then { checkVipDescriptionRowLayoutType() }
            Then { checkVipDescriptionRowDataLabel(VipScreenUiConfiguration.DESCRIPTION_LABEL_TEXT) }
            Then { checkVipDescriptionRowDataText(DataFactory.SOME_AD_DESCRIPTION) }
        }
    }

    @Test
    fun `should return Ad posted since row`() {
        runUnitTest(robot) {
            Given { stubRawAdAge(DataFactory.SOME_AD_START_DATE_TIME) }
            When { getVipPostedSinceRow() }
            Then { checkVipPostedSinceRowLayoutType() }
        }
    }

    @Test
    fun `should NOT return Ad posted since row`() {
        runUnitTest(robot) {
            Given { stubRawAdAge("2e2ee2e23") }
            When { getVipPostedSinceRow() }
            Then { checkVipPostedSinceRowIsNull() }
        }
    }

    @Test
    fun `should return Ad partnership row`() {
        runUnitTest(robot) {
            Given { stubRawPartnershipData(DataFactory.SOME_AD_TITLE, DataFactory.SOME_EXTERNAL_URL) }
            When { getPartnershipRow() }
            Then { checkVipPartnershipRowLayoutType() }
            Then { checkVipPartnershipRowText(DataFactory.SOME_AD_TITLE) }
            Then { checkVipPartnershipRowWebUrl(DataFactory.SOME_EXTERNAL_URL) }
        }
    }

    @Test
    fun `should return Ad Map row`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowLayoutType() }
            Then { checkVipMapRowDataIsInteractive(false) }
            Then { checkVipMapRowDataLocationLabel("London") }
            Then { checkVipMapRowDataLocationTypeLabel(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapRowDataLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkVipMapRowDataLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkVipMapRowDataRadius(MAP_RADIUS_IN_METERS) }
        }
    }

    @Test
    fun `should NOT return Ad Map row if visibleOnMap is false`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = false.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Ad Map row if city is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = null,
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Ad Map row if latitude or longitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Bristol",
                        latitude = null,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }

            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Bristol",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = null,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should return 1 Ad partnership advert row if there is 1 partnership advert slot`() {
        runUnitTest(robot) {
            Given {
                stubRawPartnershipAdvertSlots(
                    listOf(
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        }
                    )
                )
            }
            When { getVipPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkVipPartnershipAdvertRowsSize(1) }
        }
    }

    @Test
    fun `should return 2 Ad partnership advert rows if there are 2 partnership advert slots`() {
        runUnitTest(robot) {
            Given {
                stubRawPartnershipAdvertSlots(
                    listOf(
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        },
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        }
                    )
                )
            }
            When { getVipPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkVipPartnershipAdvertRowsSize(2) }
        }
    }

    @Test
    fun `should NOT return Ad partnership advert rows if slots are empty`() {
        runUnitTest(robot) {
            Given { stubRawPartnershipAdvertSlots(emptyList()) }
            When { getVipPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkVipPartnershipAdvertRowsSize(0) }
        }
    }

    @Test
    fun `should return Ad ID row`() {
        runUnitTest(robot) {
            Given { stubRawAdId(DataFactory.SOME_AD_ID) }
            When { getVipAdIdRow() }
            Then { checkVipOtherInfoRowLayoutType() }
            Then { checkVipOtherInfoRowDataText("Ad id: ${DataFactory.SOME_AD_ID}") }
        }
    }

    @Test
    fun `should return VAT row`() {
        runUnitTest(robot) {
            Given { stubRawAdVAT(DataFactory.SOME_VAT) }
            When { getVipVATRow() }
            Then { checkVipOtherInfoRowLayoutType() }
            Then { checkVipOtherInfoRowDataText("VAT Reg No: ${DataFactory.SOME_VAT}") }
        }
    }

    @Test
    fun `should return toolbar with ShareAction and ReportAdAction`() {
        runUnitTest(robot) {
            Given {
                stubRawAdIdAndShareLink(
                    DataFactory.SOME_AD_ID,
                    "https://www.gumtree.com/p/for-sale/native-indian-girl-costume/1444698872"
                )
            }
            When { getVipToolbar() }
            Then { checkVipToolbarActionsSize(2) }
            Then { checkVipToolbarActionTypeAtPosition(0, ShareToolbarActionDto::class.java) }
            Then { checkVipToolbarActionTypeAtPosition(1, ReportAdToolbarActionDto::class.java) }
        }
    }

    @Test
    fun `should return toolbar with ReportAdAction if share url is NOT found`() {
        runUnitTest(robot) {
            Given { stubRawAdIdAndShareLink(DataFactory.SOME_AD_ID, null) }
            When { getVipToolbar() }
            Then { checkVipToolbarActionsSize(1) }
            Then { checkVipToolbarActionTypeAtPosition(0, ReportAdToolbarActionDto::class.java) }
        }
    }

    @Test
    fun `should return BottomOverlay`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithContactMethods() }
            When { getBottomOverlay() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
        }
    }

    @Test
    fun `should not return BottomOverlay`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdId(adId = DataFactory.SOME_AD_ID) }
            When { getBottomOverlay() }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should not return BottomOverlay for user own ads`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdId(adId = DataFactory.SOME_AD_ID) }
            When { getBottomOverlay(isUserOwnAd = true) }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should not include price in similar items when ad is from services category`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            Given { stubCapiAdsApiResponse() }
            When { readScreen() }
            Then { checkSimilarAdsCalledWithIncludesPrice(false) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualToolbarResult: ToolbarDto
        private var actualBottomOverlayResult: BottomOverlay? = null
        private lateinit var actualScreenResponse: ScreenResponse
        private var actualVipImagesRowResult: RowLayout<UiItem>? = null
        private lateinit var actualVipTitleRowResult: RowLayout<UiItem>
        private var actualVipPriceRowResult: RowLayout<UiItem>? = null
        private var actualVipLocationRowResult: RowLayout<UiItem>? = null
        private lateinit var actualVipDescriptionRowResult: RowLayout<UiItem>
        private var actualVipPostedSinceRowResult: RowLayout<UiItem>? = null
        private var actualVipMapRowResult: RowLayout<UiItem>? = null
        private lateinit var actualVipPartnershipRowResults: RowLayout<UiItem>
        private lateinit var actualVipPartnershipAdvertRowsResult: Array<RowLayout<UiItem>?>
        private lateinit var actualAdIdRowResult: RowLayout<UiItem>

        private lateinit var headersBuilder: HeadersBuilder
        private lateinit var rawAd: RawCapiAd
        private lateinit var rawPartnershipData: PartnershipDetailsResponse
        private var userProfileData: UserProfileData? = null

        private val capiAdsApi: CapiAdsApi = mockk(relaxed = true)
        private val capiReplyApi: CapiReplyApi = mockk(relaxed = true)
        private val conversationApi: CapiConversationApi = mockk(relaxed = true)
        private val papiSimilarItemsApi: SimilarItemsApi = mockk(relaxed = true)
        private val userProfileApi: UserProfileApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val staticMapFactory: StaticMapFactory = mockk(relaxed = true)
        private val phoneNumberApi: PhoneNumberApi = mockk(relaxed = true)
        private val partnershipAdsApi: PartnershipAdsApi = mockk(relaxed = true)
        private val conversationsApi: ConversationsApi = mockk(relaxed = true)
        private val coreChatAuthApi: CoreChatAuthApi = mockk(relaxed = true)
        private val vipAnalyticsProvider: VipAnalyticsProvider = mockk(relaxed = true)
        private val vipTimeAgoFormatter =
            spyk(VipTimeAgoFormatter(createUKDateTimeFormatter(VIP_POSTED_SINCE_DATE_FORMAT)))
        private val sellerProfileActiveStatusFormatter = SellerProfileActiveStatusFormatter(
            createUKDateTimeFormatter(
                SellerProfileScreenUiConfiguration.SELLER_PROFILE_ACTIVE_STATUS_DATE_FORMAT
            )
        )
        private val vipService =
            VipService(
                capiAdsApi,
                capiReplyApi,
                papiSimilarItemsApi,
                userProfileApi,
                phoneNumberApi,
                partnershipAdsApi,
            )
        private val userProfileService = UserProfileService(userServiceApi = mockk(relaxed = true))
        private val vipSpecificationFactory = VipSpecificationFactory()
        private val vipHpiFactory = VipHpiFactory()
        private val titleFactory = TitleFactory()
        private val vipPartnershipAnalyticsProvider = VipPartnershipAnalyticsProvider()
        private val vipVerticalsUIProvider = VipVerticalsUIProvider(
            vipSpecificationFactory,
            vipHpiFactory,
            titleFactory,
            vipPartnershipAnalyticsProvider
        )
        private val vipPartnershipAdvertsFactory = VipPartnershipAdvertsFactory(VipPartnershipAnalyticsProvider())
        private val vipImageGalleryMapper = VipImageGalleryMapper()
        private val vipTitleMapper = VipTitleMapper()
        private val vipPriceMapper = VipPriceMapper()
        private val vipLocationMapper = VipLocationMapper(locationsApi)
        private val vipDescriptionMapper = VipDescriptionMapper()
        private val vipPostedSinceMapper = VipPostedSinceMapper(vipTimeAgoFormatter)
        private val vipSellerProfileMapper = VipSellerProfileMapper(sellerProfileActiveStatusFormatter)
        private val vipMapMapper = VipMapMapper(locationsApi = locationsApi, staticMapFactory = staticMapFactory)
        private val vipSimilarItemsMapper = spyk(VipSimilarItemsMapper(vipAnalyticsProvider = vipAnalyticsProvider))
        private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider = mockk(relaxed = true)
        private val otherInfoMapper = OtherInfoMapper()
        private val vipToolbarActionsProvider = VipToolbarActionsProvider(vipAdjustTrackingDataProvider)
        private val capiHeadersProvider = CapiHeadersProvider()
        private val papiHeadersProvider = PapiHeadersProvider()
        private val callHeaders: Headers = mockk(relaxed = true)
        private val vipAdvertsProvider = VipAdvertsProvider(VipAdvertsFactory(), GAMAdvertUtils(CategoriesTreeCache))
        private val rawLocationFetcher = RawLocationFactory.createRawLocationFetcher()
        private val sellerProfileActiveStatusFetcher =
            DefaultSellerProfileActiveStatusFetcher(userServiceApi, coreChatAuthApi, conversationsApi)
        private val vipDefaultsUIProvider = VipDefaultsUIProvider(
            vipImageGalleryMapper,
            vipTitleMapper,
            vipPriceMapper,
            vipLocationMapper,
            vipDescriptionMapper,
            vipPostedSinceMapper,
            vipSellerProfileMapper,
            vipMapMapper,
            vipPartnershipAdvertsFactory,
            vipSimilarItemsMapper,
            otherInfoMapper,
            vipToolbarActionsProvider,
            vipAnalyticsProvider,
            titleFactory,
            TestDispatcherProvider(),
        )

        private val bottomOverlayUIProvider = VipBottomOverlayUIProvider(
            conversationApi,
            capiHeadersProvider,
            vipAnalyticsProvider,
            vipAdjustTrackingDataProvider,
            TestDispatcherProvider(),
        )

        private lateinit var testSubject: DefaultVipRepository

        override fun setup() {
            testSubject = DefaultVipRepository(
                vipService,
                userProfileService,
                vipDefaultsUIProvider,
                bottomOverlayUIProvider,
                vipVerticalsUIProvider,
                vipAdvertsProvider,
                rawLocationFetcher,
                sellerProfileActiveStatusFetcher,
                papiHeadersProvider,
                capiHeadersProvider,
                vipAnalyticsProvider,
                vipAdjustTrackingDataProvider,
                TestDispatcherProvider(),
            )
            headersBuilder = HeadersBuilder()
            mockkObject(CategoriesTreeCache)
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubCapiAdsApiResponse() {
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawBaseUserProfile() {
            coEvery { userProfileApi.getBaseUserProfile(any(), any()) } returns RawPapiUserProfileFactory.createRawUserProfile()
        }

        fun stubUserProfileData(userProfile: UserProfileData?) {
            userProfileData = userProfile
        }

        fun stubLocationsResponse(locationSuggestions: List<RawLocation>) {
            coEvery { locationsApi.getLocationSuggestions(any()) } returns RawLocationSuggestions(locationSuggestions)
        }

        fun stubRawAdId(adId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adId = adId)
        }

        fun stubIsServices(isServices: Boolean) {
            every { CategoriesTreeCache.isServices(any()) } returns isServices
        }

        fun stubIsProperties(isProperties: Boolean) {
            every { CategoriesTreeCache.isProperties(any()) } returns isProperties
        }

        fun stubRawAdVAT(vat: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(vat = vat)
        }

        fun stubRawAdImage(imageUrl: String?) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(imageUrl = imageUrl)
        }

        fun stubRawAdTitle(title: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(title = title)
        }

        fun stubRawAdPrice(price: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(price = price)
        }

        fun stubRawAdAddress(address: RawCapiAddress, visibleOnMap: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(address = address, visibleOnMap = visibleOnMap)
        }

        fun stubRawPartnershipAdvertSlots(adSlots: List<RawCapiAdSlot>) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adSlots = adSlots)
        }

        fun stubRawAdDescription(description: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(description = description)
        }

        fun stubRawAdAge(adAge: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(age = adAge)
        }

        fun stubRawAdIdAndShareLink(
            adId: String,
            shareLink: String?
        ) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(
                adId = adId,
                links = listOf(RawCapiAdLink(href = shareLink, rel = "self-public-website"))
            )
        }

        fun stubRawAdWithContactMethods() {
            val contactMethod = RawCapiContactMethodFactory.contactMethod("chat", RawCapiContactMethod.CHAT)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(contactMethod))
        }

        fun stubRawPartnershipData(
            title: String,
            url: String
        ) {
            rawPartnershipData = PartnershipDetailsResponse(
                id = DataFactory.SOME_AD_ID,
                icon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL,
                ctaText = title,
                clickOutUrl = url
            )
        }

        fun getVipImagesRow() {
            actualVipImagesRowResult = vipDefaultsUIProvider.getVipImagesRow(rawAd)
        }

        fun getVipTitleRow() {
            actualVipTitleRowResult = vipDefaultsUIProvider.getVipTitleRow(rawAd)
        }

        fun getVipPriceRow() {
            actualVipPriceRowResult = vipDefaultsUIProvider.getVipPriceRow(rawAd)
        }

        suspend fun getVipLocationRow() {
            actualVipLocationRowResult = vipDefaultsUIProvider.getVipLocationRow(rawAd)
        }

        fun getVipDescriptionRow() {
            actualVipDescriptionRowResult = vipDefaultsUIProvider.getVipDescriptionRow(rawAd)
        }

        fun getVipPostedSinceRow() {
            actualVipPostedSinceRowResult = vipDefaultsUIProvider.getVipPostedSinceRow(rawAd)
        }

        fun getPartnershipRow() {
            actualVipPartnershipRowResults = vipDefaultsUIProvider.getPartnershipRow(rawPartnershipData)
        }

        suspend fun getVipMapRow() {
            actualVipMapRowResult = vipDefaultsUIProvider.getVipMapRow(rawAd)
        }

        fun getVipPartnershipAdvertRows(clientSemantics: ClientSemantics) {
            actualVipPartnershipAdvertRowsResult = vipDefaultsUIProvider.getVipPartnershipAdvertRows(
                rawAd,
                clientSemantics,
            )
        }

        fun getVipAdIdRow() {
            actualAdIdRowResult = vipDefaultsUIProvider.getVipAdIdRow(rawAd)
        }

        fun getVipVATRow() {
            actualAdIdRowResult = vipDefaultsUIProvider.getVipVATRow(rawAd)!!
        }

        fun getVipToolbar(isUserOwnAd: Boolean = false) {
            actualToolbarResult = vipDefaultsUIProvider.getVipToolbar(rawAd, isUserOwnAd)
        }

        suspend fun getBottomOverlay(isUserOwnAd: Boolean = false) {
            actualBottomOverlayResult = bottomOverlayUIProvider.createContactMethod(rawAd, callHeaders, isUserOwnAd)
        }

        suspend fun readScreen() {
            actualScreenResponse = testSubject.readScreen(callHeaders, rawAd.id, userProfileData)
        }

        fun checkVipImagesRowLayoutType() {
            assertEquals(RowLayoutType.VIP_IMAGE_GALLERY_ROW, actualVipImagesRowResult?.type)
        }

        fun checkVipImagesRowDataImage(expected: List<Image>) {
            assertEquals(expected, (actualVipImagesRowResult?.data?.first() as? VipImageCardDto)?.images)
        }

        fun checkVipImagesRowIsEmpty() {
            assertEquals(0, actualVipImagesRowResult?.data?.size)
        }

        fun checkVipTitleRowLayoutType() {
            assertEquals(RowLayoutType.VIP_TITLE_ROW, actualVipTitleRowResult.type)
        }

        fun checkVipTitleRowDataText(expected: String) {
            assertEquals(expected, (actualVipTitleRowResult.data.first() as? VipTitleCardDto)?.text)
        }

        fun checkVipPriceRowLayoutType() {
            assertEquals(RowLayoutType.VIP_PRICE_ROW, actualVipPriceRowResult?.type)
        }

        fun checkVipPriceRowDataText(expected: String) {
            assertEquals(expected, (actualVipPriceRowResult?.data?.first() as? VipPriceCardDto)?.text)
        }

        fun checkVipPriceRowIsNull() {
            assertNull(actualVipPriceRowResult)
        }

        fun checkVipLocationRowLayoutType() {
            assertEquals(RowLayoutType.VIP_LOCATION_ROW, actualVipLocationRowResult?.type)
        }

        fun checkVipLocationRowDataLocationText(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.text)
        }

        fun checkVipLocationRowDataLatitude(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.latitude)
        }

        fun checkVipLocationRowDataLongitude(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.longitude)
        }

        fun checkVipLocationRowDataRadius(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.radius)
        }

        fun checkVipLocationRowIsNull() {
            assertNull(actualVipLocationRowResult)
        }

        fun checkVipDescriptionRowLayoutType() {
            assertEquals(RowLayoutType.VIP_DESCRIPTION_ROW, actualVipDescriptionRowResult.type)
        }

        fun checkVipPartnershipRowText(expected: String) {
            assertEquals(expected, (actualVipPartnershipRowResults.data.first() as? ExternalPartnershipAdvertDto)?.text)
        }

        fun checkVipPartnershipRowWebUrl(expected: String) {
            assertEquals(
                expected,
                (actualVipPartnershipRowResults.data.first() as? ExternalPartnershipAdvertDto)?.webUrl
            )
        }

        fun checkVipDescriptionRowDataLabel(expected: String) {
            assertEquals(expected, (actualVipDescriptionRowResult.data.first() as? VipDescriptionCardDto)?.label)
        }

        fun checkVipDescriptionRowDataText(expected: String) {
            assertEquals(expected, (actualVipDescriptionRowResult.data.first() as? VipDescriptionCardDto)?.text)
        }

        fun checkVipPostedSinceRowLayoutType() {
            assertEquals(RowLayoutType.VIP_POSTED_SINCE_ROW, actualVipPostedSinceRowResult?.type)
        }

        fun checkVipPartnershipRowLayoutType() {
            assertEquals(RowLayoutType.EXTERNAL_PARTNERSHIP_ADVERT_ROW, actualVipPartnershipRowResults?.type)
        }

        fun checkVipPostedSinceRowIsNull() {
            assertNull(actualVipPostedSinceRowResult)
        }

        fun checkVipMapRowLayoutType() {
            assertEquals(RowLayoutType.VIP_MAP_ROW, actualVipMapRowResult?.type)
        }

        fun checkVipMapRowDataIsInteractive(expected: Boolean) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.isInteractive)
        }

        fun checkVipMapRowDataLocationLabel(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.locationLabel)
        }

        fun checkVipMapRowDataLocationTypeLabel(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.locationTypeLabel)
        }

        fun checkVipMapRowDataLatitude(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.latitude)
        }

        fun checkVipMapRowDataLongitude(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.longitude)
        }

        fun checkVipMapRowDataRadius(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.radius)
        }

        fun checkVipMapRowIsNull() {
            assertNull(actualVipMapRowResult)
        }

        fun checkVipOtherInfoRowLayoutType() {
            assertEquals(RowLayoutType.OTHER_INFO_ROW, actualAdIdRowResult.type)
        }

        fun checkVipOtherInfoRowDataText(expected: String) {
            assertEquals(expected, (actualAdIdRowResult.data.first() as? OtherInfoCardDto)?.text)
        }

        fun checkVipToolbarActionsSize(expected: Int) {
            assertEquals(expected, actualToolbarResult.actions.size)
        }

        fun <T> checkVipToolbarActionTypeAtPosition(
            position: Int,
            expected: T
        ) {
            assertEquals(expected, actualToolbarResult.actions[position].javaClass)
        }

        fun checkBottomOverlayRowIsNotNull() {
            assertNotNull(actualBottomOverlayResult)
        }

        fun checkBottomOverlayRowIsNull() {
            assertNull(actualBottomOverlayResult)
        }

        fun checkBottomOverlayRowCount(expected: Int) {
            assertEquals(expected, actualBottomOverlayResult?.first()?.data?.size)
        }

        fun checkVipPartnershipAdvertRowsSize(expected: Int) {
            assertEquals(expected, actualVipPartnershipAdvertRowsResult.size)
        }

        fun checkSimilarAdsCalledWithIncludesPrice(includePrice: Boolean) {
            verify { vipSimilarItemsMapper.map(any(), any(), any(), includePrice) }
        }
    }
}