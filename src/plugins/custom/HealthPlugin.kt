package com.gumtree.mobile.plugins.custom

import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCallPipeline
import io.ktor.server.application.BaseApplicationPlugin
import io.ktor.server.application.call
import io.ktor.server.request.path
import io.ktor.server.response.respondText
import io.ktor.util.AttributeKey

/**
 * The original Plugin could be found ---> https://github.com/zensum/ktor-health-check
 */

/**
 * Health and readiness are established by looking at a number of checks.
 * A check can be something like: "Are we connected to the database?" or "Is this component in a live state?"
 */

/**
 * A check is a nullary function returning a boolean indicating the success of the check.
 */
typealias Check = suspend () -> Boolean

/**
 * A check map is simply a map of names to Check functions.
 */
private typealias CheckMap = Map<String, Check>

/**
 * Recommendations are to use "healthz" and "readyz" for the endpoint names
 */
const val HEALTH_CHECK_ENDPOINT_URL = "healthz"
const val READY_CHECK_ENDPOINT_URL = "readyz"

/**
 * The Mobile BFF Health plugin instance
 */
val MobileBFFHealth = Health.Feature

class Health private constructor(val config: Configuration) {

    fun addInterceptor(pipeline: ApplicationCallPipeline) {
        val checks = config.getChecksWithFunctions()
        if (checks.isEmpty()) return
        val lengths = checks.keys.map { it.length }
        val maxL = lengths.maxOrNull()!!
        val minL = lengths.minOrNull()!!
        pipeline.intercept(ApplicationCallPipeline.Call) {
            val path = call.request.path().trim('/')
            if (path.length > maxL || path.length < minL) {
                return@intercept
            }
            val check = checks[path] ?: return@intercept
            val (status, json) = healthCheck(check)
            call.respondText(json, ContentType.Application.Json, status)
            finish()
        }
    }

    class Configuration internal constructor() {
        private var checks: Map<String, CheckMapBuilder> = emptyMap()
        private var noHealth = false
        private var noReady = false

        internal fun getChecksWithFunctions() =
            checks.mapValues { (_, v) -> v.toFunction() }

        private fun ensureDisableUnambiguous(url: String) {
            checks[url]?.let {
                if (it.notEmpty()) {
                    throw AssertionError(
                        "Cannot disable a check which has been assigned functions",
                    )
                }
            }
        }

        /**
         * Calling this disables the default health check on /healthz
         */
        fun disableHealthCheck() {
            noHealth = true
            ensureDisableUnambiguous("healthz")
        }

        /**
         * Calling this disabled the default ready check on /readyz
         */
        fun disableReadyCheck() {
            noReady = true
            ensureDisableUnambiguous("readyz")
        }

        private fun getCheck(url: String) = checks.getOrElse(url) {
            CheckMapBuilder().also {
                checks = checks + (url to it)
            }
        }

        /**
         * Adds a check function to a custom check living at the specified URL
         */
        private fun customCheck(url: String, name: String, check: Check) {
            getCheck(normalizeURL(url)).add(name, check)
        }

        /**
         * Add a health check giving it a name
         * Example: healthCheck("something") { something.isInGoodHealth() }
         */
        fun healthCheck(name: String, check: Check) {
            customCheck(HEALTH_CHECK_ENDPOINT_URL, name, check)
        }

        /**
         * Add a ready check giving it a name
         * Example: readyCheck("PAPI") { papi.ping() }
         */
        fun readyCheck(name: String, check: Check) {
            customCheck(READY_CHECK_ENDPOINT_URL, name, check)
        }

        internal fun ensureWellKnown() {
            if (!noHealth) {
                getCheck(READY_CHECK_ENDPOINT_URL)
            }
            if (!noReady) {
                getCheck(HEALTH_CHECK_ENDPOINT_URL)
            }
        }
    }

    companion object Feature : BaseApplicationPlugin<ApplicationCallPipeline, Configuration, Health> {
        override val key = AttributeKey<Health>("Health")
        override fun install(
            pipeline: ApplicationCallPipeline,
            configure: Configuration.() -> Unit,
        ) = Health(
            Configuration()
                .apply(configure)
                .apply { ensureWellKnown() },
        ).apply { addInterceptor(pipeline) }
    }
}

internal class CheckMapBuilder {
    private var inner: CheckMap = emptyMap()
    fun add(name: String, fn: Check) {
        inner = inner + (name to fn)
    }
    fun notEmpty() = inner.isNotEmpty()
    internal fun toFunction() =
        inner.toFunction()
}

internal suspend fun healthCheck(fn: suspend () -> Map<String, Boolean>) = fn().let {
    val success = it.values.all { it }
    val json = convertChecksResultsToJSON(it)
    val status = if (success) {
        HttpStatusCode.OK
    } else {
        HttpStatusCode.InternalServerError
    }
    status to json
}

@Suppress("MagicNumber")
private fun convertChecksResultsToJSON(res: Map<String, Boolean>): String {
    val estimatedJsonSize = 20
    return StringBuilder(res.size * estimatedJsonSize)
        .apply {
            append('{')
            // We use a prefix to add a comma before all but the first element
            var prefix = "\""
            res.forEach { (k, v) ->
                append(prefix)
                prefix = ",\""
                append(k)
                append("\":")
                append(v)
            }
            append("}")
        }.toString()
}

/**
 * A CheckMap can be converted to a function returning results for each of the checks.
 */
private fun CheckMap.toFunction(): suspend () -> Map<String, Boolean> = {
    mapValues { it.value() }
}

private fun normalizeURL(url: String) = url.trim('/').also {
    require(url.trim('/').isNotBlank()) {
        "The passed in URL must be more than one character not counting a leading slash"
    }
}
