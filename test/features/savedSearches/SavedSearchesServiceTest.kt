package features.savedSearches

import api.capi.models.RawCapiSavedSearchList
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.apis.CapiSavedSearchesApi
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.capi.bodies.RawCapiSavedSearchBody
import com.gumtree.mobile.api.capi.models.SavedSearchStatus
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.savedSearch.api.SavedSearchApi
import com.gumtree.mobile.api.savedSearch.bodies.RawAddSavedSearchBody
import com.gumtree.mobile.api.savedSearch.models.RawSavedSearches
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.features.savedSearches.SavedSearchesService
import com.gumtree.mobile.responses.QueryParams
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.rawDataFactory.RawCapiSavedSearchesFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawSavedSearchFactory

class SavedSearchesServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use Capi search API to get search result page`() = runTest {
        runUnitTest(robot) {
            When {
                searchAds(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    ),
                    DataFactory.SOME_PAGE_NUMBER,
                    DataFactory.SOME_SIZE,
                )
            }
            Then {
                checkSearchAdsActionsInOrder(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    ),
                    DataFactory.SOME_PAGE_NUMBER,
                    DataFactory.SOME_SIZE,
                )
            }
        }
    }

    @Test
    fun `should use service to add new saved search`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawCapiSavedSearchBody(
                    SavedSearchStatus.ACTIVE,
                    DataFactory.SOME_AD_TITLE
                )
            }
            When { addSavedSearchToUserSavedSearches() }
            Then { checkApiAddSavedSearch() }
        }
    }

    @Test
    fun `should use saved search API to add new saved search`() = runTest {
        runUnitTest(robot) {
            Given { stubRawSavedSearchBody() }
            When { addSavedSearch(DataFactory.SOME_USER_ID) }
            Then { checkSavedSearchApiAddSavedSearch(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should use CAPI service to get all user's saved searches`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawCapiSavedSearches(24, listOf(SavedSearchStatus.ACTIVE, SavedSearchStatus.INACTIVE)) }
            When { getAllUsersSavedSearches() }
            Then { checkApiGetAllUserSavedSearches() }
        }
    }

    @Test
    fun `should use saved search service to get all user's saved searches`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawSavedSearches(24) }
            When { getAllSavedSearches(DataFactory.SOME_USER_ID) }
            Then { checkApiGetAllSavedSearches(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should use service to get user's saved searches`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawCapiSavedSearches(24, listOf(SavedSearchStatus.ACTIVE, SavedSearchStatus.INACTIVE)) }
            When { getUsersSavedSearches(DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkApiGetUserSavedSearches(DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should slice all pages of the user's saved searches`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawCapiSavedSearches(55, listOf(SavedSearchStatus.ACTIVE, SavedSearchStatus.INACTIVE)) }
            When { getUsersSavedSearches("0", DataFactory.SOME_SIZE) }
            Then { checkUsersSavedSearchesPageSize(DataFactory.SOME_SIZE.toInt()) }

            Given { stubNumberOfRandomRawCapiSavedSearches(55, listOf(SavedSearchStatus.ACTIVE, SavedSearchStatus.INACTIVE)) }
            When { getUsersSavedSearches("1", DataFactory.SOME_SIZE) }
            Then { checkUsersSavedSearchesPageSize(DataFactory.SOME_SIZE.toInt()) }

            Given { stubNumberOfRandomRawCapiSavedSearches(55, listOf(SavedSearchStatus.ACTIVE, SavedSearchStatus.INACTIVE)) }
            When { getUsersSavedSearches("2", DataFactory.SOME_SIZE) }
            Then { checkUsersSavedSearchesPageSize(15) }

            Given { stubNumberOfRandomRawCapiSavedSearches(55, listOf(SavedSearchStatus.ACTIVE, SavedSearchStatus.INACTIVE)) }
            When { getUsersSavedSearches("3", DataFactory.SOME_SIZE) }
            Then { checkUsersSavedSearchesPageSize(0) }
        }
    }

    @Test
    fun `should use service to delete saved search`() = runTest {
        runUnitTest(robot) {
            When { deleteSavedSearchFromUserSavedSearches(DataFactory.SOME_AD_ID) }
            Then { checkApiDeleteSavedSearchWithId(DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should use saved search API to delete saved search`() = runTest {
        runUnitTest(robot) {
            When { deleteSavedSearch(DataFactory.SOME_USER_ID, DataFactory.SOME_SAVED_SEARCH_ID) }
            Then { checkSavedSearchApiDeleteSavedSearch(DataFactory.SOME_USER_ID, DataFactory.SOME_SAVED_SEARCH_ID) }
        }
    }

    private class Robot: BaseRobot {
        private val capiSavedSearchesApi: CapiSavedSearchesApi = mockk(relaxed = true)
        private val capiSearchApi: CapiSearchApi = mockk(relaxed = true)
        private val savedSearchApi: SavedSearchApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val authHeaders: ApiHeaders = mockk(relaxed = true)
        private lateinit var rawCapiSavedSearchBody: RawCapiSavedSearchBody
        private lateinit var rawAddSavedSearchBody: RawAddSavedSearchBody

        private lateinit var actualRawCapiSavedSearchList: RawCapiSavedSearchList
        private lateinit var actualRawSavedSearches: RawSavedSearches

        private lateinit var testSubject: SavedSearchesService

        override fun setup() {
            testSubject = SavedSearchesService(capiSavedSearchesApi, capiSearchApi, savedSearchApi, userServiceApi)
        }

        fun stubNumberOfRandomRawCapiSavedSearches(
            number: Int,
            savedSearchStatuses: List<SavedSearchStatus> = emptyList()
        ) {
            coEvery { capiSavedSearchesApi.getUserSavedSearches(any(), any(), any()) } returns
                RawCapiSavedSearchesFactory.createRawSavedSearchList(number, savedSearchStatuses)
        }

        fun stubNumberOfRandomRawSavedSearches(number: Int) {
            coEvery { savedSearchApi.getAllSavedSearches(any()) } returns RawSavedSearchFactory.createRawSavedSearches(number)
        }

        fun stubRawCapiSavedSearchBody(
            status: SavedSearchStatus,
            searchDescription: String
        ) {
            rawCapiSavedSearchBody = RawCapiSavedSearchesFactory.createRawCapiSavedSearchBody(
                status,
                searchDescription,
            )
        }

        fun stubRawSavedSearchBody() {
            rawAddSavedSearchBody = RawSavedSearchFactory.createRawSavedSearchBody()
        }

        suspend fun addSavedSearchToUserSavedSearches() {
            testSubject.addSavedSearchToUserSavedSearches(authHeaders, rawCapiSavedSearchBody)
        }

        suspend fun addSavedSearch(userId: String) {
            testSubject.addSavedSearch(userId, rawAddSavedSearchBody)
        }

        suspend fun getAllUsersSavedSearches() {
            actualRawCapiSavedSearchList = testSubject.getAllUserSavedSearches(authHeaders)
        }

        suspend fun getAllSavedSearches(userId: String) {
            actualRawSavedSearches = testSubject.getAllSavedSearches(userId)
        }

        suspend fun getUsersSavedSearches(
            page: String,
            size: String
        ) {
            actualRawCapiSavedSearchList = testSubject.getUserSavedSearches(authHeaders, page, size)
        }

        suspend fun searchAds(
            searchOptions: QueryParams,
            page: String,
            size: String,
        ) {
            testSubject.searchAds(authHeaders, searchOptions, page, size)
        }

        suspend fun deleteSavedSearchFromUserSavedSearches(savedSearchId: String) {
            testSubject.deleteSavedSearchFromUserSavedSearches(authHeaders, savedSearchId)
        }

        suspend fun deleteSavedSearch(userId: String, savedSearchId: String) {
            testSubject.deleteSavedSearch(userId, savedSearchId)
        }

        fun checkApiAddSavedSearch() {
            coVerify { capiSavedSearchesApi.addSavedSearchToUserSavedSearches(authHeaders, rawCapiSavedSearchBody) }
        }

        fun checkSavedSearchApiAddSavedSearch(userId: String) {
            coVerify { savedSearchApi.addSavedSearch(userId, rawAddSavedSearchBody) }
        }

        fun checkApiGetAllUserSavedSearches() {
            coVerify { capiSavedSearchesApi.getAllUserSavedSearches(authHeaders) }
        }

        fun checkApiGetAllSavedSearches(userId: String) {
            coVerify { savedSearchApi.getAllSavedSearches(userId) }
        }

        fun checkApiGetUserSavedSearches(
            expectedPaged: String,
            expectedSize: String,
        ) {
            coVerify { capiSavedSearchesApi.getUserSavedSearches(authHeaders, expectedPaged, expectedSize) }
        }

        fun checkApiDeleteSavedSearchWithId(
            expected: String
        ) {
            coVerify { capiSavedSearchesApi.deleteSavedSearchFromUserSavedSearches(authHeaders, expected) }
        }

        fun checkSavedSearchApiDeleteSavedSearch(userId: String, savedSearchId: String) {
            coVerify { savedSearchApi.deleteSavedSearch(userId, savedSearchId) }
        }

        fun checkUsersSavedSearchesPageSize(expected: Int) {
            Assertions.assertEquals(expected, actualRawCapiSavedSearchList.rawCapiSavedSearches.size)
        }

        fun checkSearchAdsActionsInOrder(
            expectedSearchOptions: QueryParams,
            expectedPage: String,
            expectedSize: String,
        ) {
            coVerifyOrder {
                capiSearchApi.searchAds(authHeaders, expectedSearchOptions, expectedPage, expectedSize)
            }
        }
    }
}
