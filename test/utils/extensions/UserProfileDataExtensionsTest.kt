package utils.extensions

import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.utils.extensions.getUserEmailOrThrowUnauthorised
import com.gumtree.mobile.utils.extensions.getUserTokenOrThrowUnauthorised
import io.ktor.http.Headers
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CallHeadersFactory
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.dataFactory.UserProfileDataFactory
import tools.runUnitTest
import tools.runUnitTestForException

@ParallelTest
class UserProfileDataExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should return user email from user profile data`() {
        runUnitTest(robot) {
            Given { stubUserProfile(UserProfileDataFactory.create(userEmail = DataFactory.ANOTHER_USER_EMAIL, userToken = DataFactory.ANOTHER_TOKEN)) }
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            When { getUserEmailOrThrowUnauthorised() }
            Then { checkUserEmail(DataFactory.ANOTHER_USER_EMAIL) }
        }
    }

    @Test
    fun `should return user email from call headers`() {
        runUnitTest(robot) {
            Given { stubUserProfile(null) }
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            When { getUserEmailOrThrowUnauthorised() }
            Then { checkUserEmail(DataFactory.SOME_USER_EMAIL) }
        }
    }

    @Test
    fun `should return user token from user profile data`() {
        runUnitTest(robot) {
            Given { stubUserProfile(UserProfileDataFactory.create(userEmail = DataFactory.ANOTHER_USER_EMAIL, userToken = DataFactory.ANOTHER_TOKEN)) }
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            When { getUserTokenOrThrowUnauthorised() }
            Then { checkUserToken(DataFactory.ANOTHER_TOKEN) }
        }
    }

    @Test
    fun `should return user token from call headers`() {
        runUnitTest(robot) {
            Given { stubUserProfile(null) }
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            When { getUserTokenOrThrowUnauthorised() }
            Then { checkUserToken(DataFactory.SOME_TOKEN) }
        }
    }

    @Test
    fun `should throw UnAuthorised exception when user email is NOT in the user profile and NOT in the call headers`() {
        runUnitTestForException(robot, UnauthorisedException::class) {
            Given { stubUserProfile(null) }
            Given { stubCallHeaders(CallHeadersFactory.createUnAuthHeaders()) }
            When { getUserEmailOrThrowUnauthorised() }
        }
    }

    @Test
    fun `should throw UnAuthorised exception when user token is NOT in the user profile and NOT in the call headers`() {
        runUnitTestForException(robot, UnauthorisedException::class) {
            Given { stubUserProfile(null) }
            Given { stubCallHeaders(CallHeadersFactory.createUnAuthHeaders()) }
            When { getUserTokenOrThrowUnauthorised() }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualUserEmailResult: String
        private lateinit var actualUserTokenResult: String

        private lateinit var callHeaders: Headers
        private var userProfileData: UserProfileData? = null

        fun stubUserProfile(userProfile: UserProfileData?) {
            userProfileData = userProfile
        }

        fun stubCallHeaders(headers: Headers) {
            callHeaders = headers
        }

        fun getUserEmailOrThrowUnauthorised() {
            actualUserEmailResult = userProfileData.getUserEmailOrThrowUnauthorised(callHeaders)
        }

        fun getUserTokenOrThrowUnauthorised() {
            actualUserTokenResult = userProfileData.getUserTokenOrThrowUnauthorised(callHeaders)
        }

        fun checkUserEmail(expected: String) {
            assertEquals(expected, actualUserEmailResult)
        }

        fun checkUserToken(expected: String) {
            assertEquals(expected, actualUserTokenResult)
        }
    }
}
