package tools.dataFactory

import com.gumtree.mobile.features.screens.TextSegment
import com.gumtree.mobile.features.screens.Typography
import com.gumtree.mobile.features.screens.layoutsData.IconType
import com.gumtree.mobile.features.screens.layoutsData.LabelDto

object LabelDtoFactory {

    fun createRegistrationLabel(year: String): LabelDto = LabelDto(TextSegment(year, Typography.BODY_LARGE_REGULAR), IconType.CALENDAR)

    fun createMileageLabel(mileage: String): LabelDto = LabelDto(TextSegment(mileage, Typography.BODY_LARGE_REGULAR), IconType.SPEEDO)

    fun createTransmissionLabel(transmission: String): LabelDto = LabelDto(TextSegment(transmission, Typography.BODY_LARGE_REGULAR), IconType.GEARBOX)

    fun createSellerTypeLabel(sellerType: String): LabelDto = LabelDto(TextSegment(sellerType, Typography.BODY_LARGE_REGULAR), IconType.GARAGE)

    fun createFuelTypeLabel(fuelType: String): LabelDto = LabelDto(TextSegment(fuelType, Typography.BODY_LARGE_REGULAR), IconType.FUELPUMP)
}
