package com.gumtree.mobile.common.analytics

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.features.myGumtree.pluraliseString
import com.gumtree.mobile.utils.TimeAgoFormatter
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.runOrNull
import java.time.format.DateTimeFormatter

private const val MONTH_SUFFIX = " month"
private const val MONTHS_SUFFIX = " months"
private const val YEAR_SUFFIX = " year"
private const val YEARS_SUFFIX = " years"
const val UNDER_MONTH = "Under a month"

class AnalyticsPostingDurationFormatter(override val dateFormat: DateTimeFormatter) : TimeAgoFormatter {

    override fun getTimeAgoLabel(
        olderDate: String?,
        newerDate: String,
    ): String? {
        return runOrNull(olderDate.isNotNullOrEmpty()) {
            val (years, months, _, _, minutes) = calculateYearsMonthsWeeksDaysHours(olderDate, newerDate)
            when {
                years > ZERO -> pluraliseString(years.toInt(), YEAR_SUFFIX, YEARS_SUFFIX)
                months > ZERO -> pluraliseString(months.toInt(), MONTH_SUFFIX, MONTHS_SUFFIX)
                minutes > ZERO -> UNDER_MONTH
                else -> null
            }
        }
    }

    override fun getTimeAgoLabel(
        olderTimestamp: Long?,
        newerTimestamp: Long,
    ): String? {
        return runOrNull(olderTimestamp.isNotNull()) {
            val (years, months, _, _, minutes) = calculateYearsMonthsWeeksDaysHours(olderTimestamp, newerTimestamp)
            when {
                years > ZERO -> pluraliseString(years.toInt(), YEAR_SUFFIX, YEARS_SUFFIX)
                months > ZERO -> pluraliseString(months.toInt(), MONTH_SUFFIX, MONTHS_SUFFIX)
                minutes > ZERO -> UNDER_MONTH
                else -> null
            }
        }
    }
}
