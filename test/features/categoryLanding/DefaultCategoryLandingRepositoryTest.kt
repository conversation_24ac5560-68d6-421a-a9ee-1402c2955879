package features.categoryLanding

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.features.categoryLanding.CarsCategoryLandingAnalyticsProvider
import com.gumtree.mobile.features.categoryLanding.CarsCategoryLandingUIProvider
import com.gumtree.mobile.features.categoryLanding.CategoryLandingScreenUiConfiguration
import com.gumtree.mobile.features.categoryLanding.CategoryLandingService
import com.gumtree.mobile.features.categoryLanding.CategoryLandingSimilarItemsMapper
import com.gumtree.mobile.features.categoryLanding.DefaultCategoryLandingRepository
import com.gumtree.mobile.features.filters.FiltersCategoryAttributesCache
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.ButtonsFactory
import com.gumtree.mobile.features.screens.factories.DropdownFactory
import com.gumtree.mobile.features.screens.factories.ImageFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.providers.TodayPicksProvider
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.layoutsDataFactory.FiltersCategoryAttributesFactory
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawLocationFactory
import utils.TestDispatcherProvider

class DefaultCategoryLandingRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return category landing screen expected portrait rows when vehicle_make and vehicle_model provided for CARS category`() = runTest {
        runUnitTest(robot) {
            Given { stubUnAuthHeaders() }
            Given { stubRawAds(10) }
            Given { stubQueryParam(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id) }
            Given { stubQueryParam(ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubQueryParam(ApiQueryParams.VEHICLE_MAKE to "audi") }
            Given { stubQueryParam(ApiQueryParams.VEHICLE_MODEL to "A5") }
            When { readScreen(CategoryDefaults.CARS.id) }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.IMAGE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.TITLE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.DROPDOWN_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.DROPDOWN_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.TITLE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.BUTTON_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.VEHICLE_BODY_TYPE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.TODAY_PICKS_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }

            Then { checkScreenResponseTitle(CategoryLandingScreenUiConfiguration.CARS_SCREEN_TITLE) }
            Then { checkPortraitScreenResponseListingsDataSize(12) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }
        }
    }

    @Test
    fun `should return category landing screen expected portrait rows when vehicle_make, vehicle_model, minPrice and maxPrice provided for CARS category`() = runTest {
        runUnitTest(robot) {
            Given { stubUnAuthHeaders() }
            Given { stubRawAds(10) }
            Given { stubQueryParam(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id) }
            Given { stubQueryParam(ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubQueryParam(ApiQueryParams.VEHICLE_MAKE to "audi") }
            Given { stubQueryParam(ApiQueryParams.VEHICLE_MODEL to "A5") }
            Given { stubQueryParam(ApiQueryParams.MIN_PRICE to "5000") }
            Given { stubQueryParam(ApiQueryParams.MAX_PRICE to "23000") }
            When { readScreen(CategoryDefaults.CARS.id) }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.IMAGE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.TITLE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.DROPDOWN_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.DROPDOWN_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.TITLE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.BUTTON_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.VEHICLE_BODY_TYPE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.TODAY_PICKS_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }

            Then { checkScreenResponseTitle(CategoryLandingScreenUiConfiguration.CARS_SCREEN_TITLE) }
            Then { checkPortraitScreenResponseListingsDataSize(12) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualScreenResponseResult: ScreenResponse

        private val callHeaders: Headers = mockk(relaxed = true)
        private val unAuthHeaders: ApiHeaders = mockk(relaxed = true)
        private val headersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private var service: CategoryLandingService = mockk(relaxed = true)
        private var rawLocationFetcher: RawLocationFetcher = mockk(relaxed = true)
        private val filtersCategoryAttributesCache = FiltersCategoryAttributesCache
        private val carsCategoryLandingUIProvider = CarsCategoryLandingUIProvider(
            TitleFactory(),
            ImageFactory(),
            DropdownFactory(),
            ButtonsFactory(),
            CategoryLandingSimilarItemsMapper(
                CarsCategoryLandingAnalyticsProvider(
                    CommonAnalyticsProviderFactory.createInstance(),
                    RawLocationFactory.createRawLocationFetcher(),
                )
            ),
        )
        private val analyticsProvider: CarsCategoryLandingAnalyticsProvider = mockk(relaxed = true)

        private val todayPicksProvider = TodayPicksProvider(rawLocationFetcher)
        private var filtersParams: LinkedHashMap<String, String?> = linkedMapOf()
        private lateinit var rawAds: RawCapiAdList

        private lateinit var testSubject: DefaultCategoryLandingRepository

        override fun setup() {
            filtersCategoryAttributesCache.data = FiltersCategoryAttributesFactory.createFiltersAttributes()
            testSubject = DefaultCategoryLandingRepository(
                service,
                carsCategoryLandingUIProvider,
                analyticsProvider,
                todayPicksProvider,
                filtersCategoryAttributesCache,
                headersProvider,
                TestDispatcherProvider(),
            )
            filtersParams.clear()
        }

        fun stubUnAuthHeaders() {
            every { headersProvider.createUnAuthorisedHeaders(callHeaders) } returns unAuthHeaders
        }

        fun stubQueryParam(pair: Pair<String,String?>) {
            filtersParams[pair.first] = pair.second
        }

        fun stubRawAds(number: Int) {
            rawAds = RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
            coEvery { service.searchAds(any(), any(), any(), any()) } returns rawAds
        }

        suspend fun readScreen(categoryId: String) {
            actualScreenResponseResult = testSubject.readScreen(callHeaders, categoryId, filtersParams)
        }

        fun checkPortraitScreenResponseListingsDataSize(expected: Int) {
            assertEquals(expected, actualScreenResponseResult.portraitData.size)
        }

        fun checkLandscapeScreenResponseListingsDataSize(expected: Int?) {
            assertEquals(expected, actualScreenResponseResult.landscapeData?.size)
        }

        fun checkPortraitScreenResponseRowAtPosition(
            position: Int,
            expectedType: RowLayoutType
        ) {
            assertEquals(expectedType, actualScreenResponseResult.portraitData[position].type)
        }

        fun checkScreenResponseTitle(expected: String) {
            assertEquals(expected, actualScreenResponseResult.title)
        }
    }

}