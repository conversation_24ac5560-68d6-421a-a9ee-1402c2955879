package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.capi.models.RawCapiAdLink
import com.gumtree.mobile.features.screens.layoutsData.ReportAdToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ShareToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarActionDto
import com.gumtree.mobile.features.vip.v2.VipAdjustTrackingDataProvider
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.v2.VipToolbarActionsProvider
import common.AdjustTrackingData
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertInstanceOf
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class VipToolbarActionsProviderTest {

    private val robot = Robot()

    @Test
    fun `should return Report Ad toolbar action`() {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    adId = DataFactory.SOME_AD_ID,
                    title = DataFactory.SOME_AD_TITLE,
                    price = "20000",
                    shareLink = DataFactory.SOME_AD_PUBLIC_LINK
                )
            }
            When { createReportAdAction() }
            Then { checkReportAdToolbarActionType(ReportAdToolbarActionDto::class.java) }
            Then { checkReportAdToolbarActionTitle(VipScreenUiConfiguration.TOOLBAR_REPORT_AD_ACTION_TITLE) }
            Then { checkReportAdToolbarActionIsHidden(true) }
            Then { checkReportAdToolbarActionAdId(DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should NOT return Report Ad toolbar action for user own ad`() {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    adId = DataFactory.SOME_AD_ID,
                    title = DataFactory.SOME_AD_TITLE,
                    price = "10000",
                    shareLink = DataFactory.SOME_AD_PUBLIC_LINK
                )
            }
            When { createReportAdAction(isUserOwnAd = true) }
            Then { checkReportAdToolbarActionIsNull() }
        }
    }

    @Test
    fun `should return Share Ad toolbar action`() {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    adId = DataFactory.SOME_AD_ID,
                    title = DataFactory.SOME_AD_TITLE,
                    price = "10000",
                    shareLink = DataFactory.SOME_AD_PUBLIC_LINK
                )
            }
            When { createShareAdAction() }
            Then { checkShareAdToolbarActionType(ShareToolbarActionDto::class.java) }
            Then { checkShareAdToolbarActionTitle(VipScreenUiConfiguration.TOOLBAR_SHARE_ACTION_TITLE) }
            Then { checkShareAdToolbarActionIsHidden(false) }
            Then {
                checkShareAdToolbarActionMessage(
                    with(StringBuilder()) {
                        append(VipScreenUiConfiguration.PLEASE_VIEW_THIS_AD)
                        append("\n\n")
                        append(DataFactory.SOME_AD_TITLE).append("\n")
                        append(VipScreenUiConfiguration.PRICE + "£10,000")
                        append("\n")
                        append(DataFactory.SOME_AD_PUBLIC_LINK)
                        append("?")
                    }.toString()
                )
            }
        }
    }

    @Test
    fun `should return Share Ad toolbar action for Ad without price`() {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    adId = DataFactory.SOME_AD_ID,
                    title = DataFactory.SOME_AD_TITLE,
                    price = null,
                    shareLink = DataFactory.SOME_AD_PUBLIC_LINK
                )
            }
            When { createShareAdAction() }
            Then { checkShareAdToolbarActionType(ShareToolbarActionDto::class.java) }
            Then { checkShareAdToolbarActionTitle(VipScreenUiConfiguration.TOOLBAR_SHARE_ACTION_TITLE) }
            Then { checkShareAdToolbarActionIsHidden(false) }
            Then {
                checkShareAdToolbarActionMessage(
                    with(StringBuilder()) {
                        append(VipScreenUiConfiguration.PLEASE_VIEW_THIS_AD)
                        append("\n\n")
                        append(DataFactory.SOME_AD_TITLE).append("\n")
                        append("\n")
                        append(DataFactory.SOME_AD_PUBLIC_LINK)
                        append("?")
                    }.toString()
                )
            }
        }
    }

    @Test
    fun `should NOT return Share Ad toolbar action if Ad self public website is NOT found`() {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    adId = DataFactory.SOME_AD_ID,
                    title = DataFactory.SOME_AD_TITLE,
                    price = "10000",
                    shareLink = null
                )
            }
            When { createShareAdAction() }
            Then { checkShareAdToolbarActionIsNull() }
        }
    }

    @Test
    fun `should include vipAdjustTrackingData in Share Ad toolbar action`() {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    adId = DataFactory.SOME_AD_ID,
                    title = DataFactory.SOME_AD_TITLE,
                    price = "10000",
                    shareLink = DataFactory.SOME_AD_PUBLIC_LINK
                )
            }
            Given { stubVipAdjustTrackingData() }
            When { createShareAdAction() }
            Then { checkShareAdToolbarActionType(ShareToolbarActionDto::class.java) }
            Then { checkShareAdToolbarActionAdjustTrackingData() }
        }
    }

    private class Robot: BaseRobot {

        private var actualReportAdToolbarActionResult: ToolbarActionDto? = null
        private var actualShareAdToolbarActionResult: ToolbarActionDto? = null
        private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider = mockk(relaxed = true)
        private lateinit var rawAd: RawCapiAd

        private val testSubject = VipToolbarActionsProvider(vipAdjustTrackingDataProvider)

        fun stubRawAd(
            adId: String,
            title: String,
            price: String?,
            shareLink: String?
        ) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(
                adId = adId,
                title = title,
                price = price,
                links = listOf(RawCapiAdLink(href = shareLink, rel = "self-public-website"))
            )
        }

        fun stubVipAdjustTrackingData() {
            every { vipAdjustTrackingDataProvider.getShareTrackingData(any()) } returns AdjustTrackingData(
                DataFactory.SOME_TOKEN,
                mapOf(VipAdjustTrackingDataProvider.LISTING_ID_KEY to DataFactory.SOME_AD_ID)
            )
        }

        fun createReportAdAction(isUserOwnAd: Boolean = false) {
            actualReportAdToolbarActionResult = testSubject.createReportAdAction(rawAd, isUserOwnAd)
        }

        fun createShareAdAction() {
            actualShareAdToolbarActionResult = testSubject.createShareAdAction(rawAd)
        }

        fun checkReportAdToolbarActionType(expected: Class<ReportAdToolbarActionDto>) {
            assertInstanceOf(expected, actualReportAdToolbarActionResult)
        }

        fun checkReportAdToolbarActionTitle(expected: String) {
            assertEquals(expected, (actualReportAdToolbarActionResult as ReportAdToolbarActionDto).title)
        }

        fun checkReportAdToolbarActionIsHidden(expected: Boolean) {
            assertEquals(expected, (actualReportAdToolbarActionResult as ReportAdToolbarActionDto).isHidden)
        }

        fun checkReportAdToolbarActionAdId(expected: String) {
            assertEquals(expected, (actualReportAdToolbarActionResult as ReportAdToolbarActionDto).adId)
        }

        fun checkShareAdToolbarActionType(expected: Class<ShareToolbarActionDto>) {
            assertInstanceOf(expected, actualShareAdToolbarActionResult)
        }

        fun checkShareAdToolbarActionTitle(expected: String) {
            assertEquals(expected, (actualShareAdToolbarActionResult as ShareToolbarActionDto).title)
        }

        fun checkShareAdToolbarActionIsHidden(expected: Boolean) {
            assertEquals(expected, (actualShareAdToolbarActionResult as ShareToolbarActionDto).isHidden)
        }

        fun checkShareAdToolbarActionMessage(expected: String) {
            assertEquals(expected, (actualShareAdToolbarActionResult as ShareToolbarActionDto).message)
        }

        fun checkShareAdToolbarActionIsNull() {
            assertNull(actualShareAdToolbarActionResult)
        }

        fun checkReportAdToolbarActionIsNull() {
            assertNull(actualReportAdToolbarActionResult)
        }

        fun checkShareAdToolbarActionAdjustTrackingData() {
            val shareAction = actualShareAdToolbarActionResult as ShareToolbarActionDto
            assertNotNull(shareAction.adjustTrackingData)
            assertEquals(DataFactory.SOME_TOKEN, shareAction.adjustTrackingData?.eventToken)
            assertEquals(
                mapOf(VipAdjustTrackingDataProvider.LISTING_ID_KEY to DataFactory.SOME_AD_ID),
                shareAction.adjustTrackingData?.parameters
            )
        }
    }
}
