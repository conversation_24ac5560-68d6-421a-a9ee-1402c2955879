package com.gumtree.mobile.cache

import com.gumtree.mobile.common.FilterAttributeDefault
import com.gumtree.mobile.utils.CurrentDateProvider
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.toIntOrDefault
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * CategoryAttributesCache interface
 * <T> is the data type which the CategoryAttributesCache holds
 */
interface CategoryAttributesCache<T : CategoryAttributesCache.Data> : Cache {
    /**
     * The category attributes BFF cache data
     */
    var data: List<T>?

    /**
     * Find all attributes for category by categoryId
     * @param id - the categoryId
     * @return - the list with attributes for the looking categoryId or empty list in the case of not attributes found for that categoryId
     */
    fun findCategoryAttributesOrEmpty(id: String): List<T>

    /**
     * Find single attribute by attribute key/name
     * @param name - the name of the attribute
     * @param categoryId - the category id of the attribute
     * @return - the attribute for the given name and the given category id or NULL in the case of no attribute found
     */
    fun findAttributeByName(name: String?, categoryId: String? = null): T?

    /**
     * Each individual data type (like FiltersCategoryAttributeDto and SrpCategoryAttributeDto) should implement this Data interface.
     * It will allow the IndexedAttribute interface to index the attributes data by category ID,
     * @property name - the name(this is the key) of the attribute
     * @property label - the label (this is the text) of the attribute
     * @property dataType - the data type of the attribute
     * @property presentationType - the presentation type of the attribute (how should look on the screen)
     * @property values - the list with all possible values of the attribute
     * @property categoryIds - the list with all categories where the attribute should be presented
     */
    interface Data {
        val name: String
        val label: String
        val dataType: Type
        val presentationType: PresentationType
        val values: List<Value>
        val categoryIds: List<Int>

        @Serializable
        enum class Type {
            BOOL,
            INTEGER,
            NUMBER,
            STRING,
            DATE,
            ENUM,
            CURRENCY,
            YEAR,
        }

        @Serializable
        enum class PresentationType {
            DOUBLE_INPUT, // price min and max as two text inputs UI
            DOUBLE_DROPDOWN, // salary min and max as two dropdowns UI
            LINK, // vehicle_make, vehicle_model, vehicle_doors etc. (any other attribute) as link UI
        }

        @Serializable
        data class Value(
            @SerialName("label")
            val label: String,
            @SerialName("value")
            val value: String,
            @SerialName("dependentValue")
            val dependentValue: String? = null,
        ) {
            fun isAny(): Boolean {
                return value == FilterAttributeDefault.ANY.key
            }
        }
    }
}

/**
 * It's the default implementation of the AttributesCache.IndexedAttribute interface
 * @param expireTime - the time in mills when the category attributes cache data is considered as expired
 */
class DefaultCategoryAttributesCache<T : CategoryAttributesCache.Data>(
    override val expireTime: Int,
) : CategoryAttributesCache<T> {
    /**
     * The last timestamp when the data cache was updated
     */
    private var lastUpdateTimestamp: Long = 0L

    override var data: List<T>? = null
        set(value) {
            field = value
            lastUpdateTimestamp = when (value) {
                null -> 0L
                else -> CurrentDateProvider.getCurrentTimestamp() // everytime when we store new NonNull data in the cache we update the cache timestamp
            }
        }

    override fun hasValidCache(): Boolean {
        return CurrentDateProvider.getCurrentTimestamp() - lastUpdateTimestamp < expireTime
    }

    override fun isNotEmpty(): Boolean {
        return data?.isNotEmpty() ?: false
    }

    override fun findCategoryAttributesOrEmpty(id: String): List<T> {
        return data?.mapNotNull {
            when {
                it.categoryIds.binarySearch(id.toIntOrDefault(-1)) >= 0 -> it
                else -> null
            }
        } ?: emptyList()
    }

    override fun findAttributeByName(name: String?, categoryId: String?): T? {
        return name?.let {
            when {
                categoryId.isNotNull() -> data?.firstOrNull {
                    it.name == name && it.categoryIds.binarySearch(categoryId.toIntOrDefault(-1)) >= 0
                }
                else -> data?.firstOrNull { it.name == name }
            }
        }
    }
}
