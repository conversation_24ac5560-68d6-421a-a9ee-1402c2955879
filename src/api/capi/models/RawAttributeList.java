package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.List;

@Root(strict = false)
@Namespace(reference = Namespaces.ATTRIBUTE)
public class RawAttributeList {
    @ElementList(inline = true, entry = "attribute", required = false)
    public List<RawCapiAttribute> mAttributes;

}
