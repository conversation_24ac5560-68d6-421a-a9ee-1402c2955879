package com.gumtree.mobile.utils.extensions.data

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.TextSegment
import com.gumtree.mobile.features.screens.Typography
import com.gumtree.mobile.features.screens.layoutsData.IconType
import com.gumtree.mobile.features.screens.layoutsData.LabelDto
import com.gumtree.mobile.features.screens.layoutsData.LabelDto.LabelStyle
import com.gumtree.mobile.features.screens.layoutsData.LabelDto.LabelType
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.toFormattedNumberWithSuffix

fun RawCapiAd.getLabelForRegistrationYear(): LabelDto? {
    val registration = getAttributeValue(RawCapiAd.ATTRIBUTE_VEHICLE_REGISTRATION)
    return when {
        registration.isNotNull() ->
            LabelDto(
                text = getAttributeTextSegment(registration),
                iconStart = IconType.CALENDAR,
                labelType = LabelType.ATTRIBUTE,
                labelStyle = LabelStyle.BACKGROUND,
            )
        else -> null
    }
}

fun RawCapiAd.getLabelForMileage(): LabelDto? {
    val mileage = getAttributeValue(RawCapiAd.ATTRIBUTE_VEHICLE_MILEAGE)
    return when {
        mileage.isNotNull() ->
            LabelDto(
                text = getAttributeTextSegment(mileage.toFormattedNumberWithSuffix("miles")),
                iconStart = IconType.SPEEDO,
                labelType = LabelType.ATTRIBUTE,
                labelStyle = LabelStyle.BACKGROUND,
            )
        else -> null
    }
}

fun RawCapiAd.getLabelForTransmission(): LabelDto? {
    val transmission = getAttributeValue(RawCapiAd.ATTRIBUTE_VEHICLE_TRANSMISSION)
    return when {
        transmission.isNotNull() ->
            LabelDto(
                text = getAttributeTextSegment(transmission),
                iconStart = IconType.GEARBOX,
            )
        else -> null
    }
}

fun RawCapiAd.getLabelForSeller(): LabelDto? {
    val seller = getAttributeValue(RawCapiAd.ATTRIBUTE_SELLER_TYPE)
    return when {
        seller.isNotNull() ->
            LabelDto(
                text = getAttributeTextSegment(seller),
                iconStart = IconType.GARAGE,
            )
        else -> null
    }
}

fun RawCapiAd.getLabelForFuelType(): LabelDto? {
    val fuel = getAttributeValue(RawCapiAd.ATTRIBUTE_VEHICLE_FUEL)
    return when {
        fuel.isNotNull() ->
            LabelDto(
                text = getAttributeTextSegment(fuel),
                iconStart = IconType.FUELPUMP,
            )
        else -> null
    }
}

private fun getAttributeTextSegment(text: String) = TextSegment(text, Typography.BODY_LARGE_REGULAR)
