package com.gumtree.mobile.api.userService.models

import com.google.gson.annotations.SerializedName

data class RawUserServiceUserDetails(
    @SerializedName("userId")
    val userId: String,
    @SerializedName("firstName")
    val firstName: String,
    @SerializedName("lastName")
    val lastName: String,
    @SerializedName("userEmail")
    val userEmail: String,
    @SerializedName("userType")
    val userType: RawUserServiceUserType,
    @SerializedName("registrationDate")
    val registrationDate: String,
    @SerializedName("accountIds")
    val accountIds: List<String>,
    @SerializedName("postingSince")
    val postingSinceDate: String?,
    @SerializedName("phoneNumber")
    val phoneNumber: String?,
)
