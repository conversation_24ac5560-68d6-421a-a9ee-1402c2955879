package features.reportChat

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.features.reportChat.REPORT_CHAT_PATH
import com.gumtree.mobile.features.reportChat.ReportChatReason
import com.gumtree.mobile.features.reportChat.ReportChatRepository
import com.gumtree.mobile.features.reportChat.ReportChatRequest
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.plugins.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.jsonHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runUnitTest
import kotlin.test.assertEquals

class ReportChatRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete POST report chat request with success and status code Created`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateChatReportSuccess() }
            When {
                createReportChat(
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_USER_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT,
                    DataFactory.SOME_USER_AUTHORIZATION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Created) }
        }
    }

    @Test
    fun `should complete POST report chat request with BadRequest exception if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateChatReportError(BadRequestException("Invalid counterPartyId or adId")) }
            When {
                createReportChat(
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_USER_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT,
                    DataFactory.SOME_USER_AUTHORIZATION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST report chat request with BadRequest if invalid request body`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                createReportChat(
                    DataFactory.SOME_AD_ID,
                    EMPTY_STRING,
                    DataFactory.SOME_USER_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT,
                    DataFactory.SOME_USER_AUTHORIZATION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST report chat request with Unauthorized if JWT user profile value is missing`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                createReportChat(
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_USER_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT,
                    authorization = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Unauthorized) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: ReportChatRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryCreateChatReportSuccess() {
            coEvery {
                repository.createChatReport(any(), any())
            } returns Unit
        }

        fun stubRepositoryCreateChatReportError(error: Throwable) {
            coEvery {
                repository.createChatReport(any(), any())
            } throws error
        }

        suspend fun createReportChat(
            adId: String,
            conversationId: String,
            counterPartyUserId: String,
            reason: ReportChatReason,
            comment: String,
            authorization: String?,
        ) {
            actualResponse = client.post(REPORT_CHAT_PATH) {
                authorization?.let { headers { append(HttpHeaders.Authorization, it) } }
                setBody(
                    ReportChatRequest(
                        adId = adId,
                        conversationId = conversationId,
                        counterPartyUserId = counterPartyUserId,
                        reason = reason,
                        comment = comment
                    )
                )
                contentType(ContentType.Application.Json)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }
    }
}
