package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.ArrayList;
import java.util.List;

@Root(name="picture")
@Namespace(reference= Namespaces.PICTURE, prefix="pic")
public class RawUserPicture {

    public static final String VIP = "vip";
    public static final String SRP = "srp";

    @Attribute(name = "rel", required = false)
    public String rel;

    @ElementList(entry = "link", inline=true, required = false)
    @Namespace(reference=Namespaces.PICTURE)
    public List<RawUserPictureLink> mRawUserPictureLinks = new ArrayList<>();

    public RawUserPicture(){}

    public RawUserPicture(@ElementList(entry = "link", inline = true) List<RawUserPictureLink> rawUserPictureLinks) {
        mRawUserPictureLinks = rawUserPictureLinks;
    }

    public List<RawUserPictureLink> getRawUserPictureLinks() {
        return mRawUserPictureLinks;
    }

    public void setRawUserPictureLinks(List<RawUserPictureLink> rawUserPictureLinks) {
        mRawUserPictureLinks = rawUserPictureLinks;
    }

    public String getRel() {
        return rel;
    }

    public void setRel(String rel) {
        this.rel = rel;
    }
}
