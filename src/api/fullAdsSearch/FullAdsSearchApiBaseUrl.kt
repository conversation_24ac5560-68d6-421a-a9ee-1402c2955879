package com.gumtree.mobile.api.fullAdsSearch

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val FULL_ADS_SEARCH_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "FULL_ADS_SEARCH_API_BASE_URL"
const val FULL_ADS_SEARCH_API_QA_PORT = 8097

class FullAdsSearchApiBaseUrl : ApiBaseUrl() {

    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(
            FULL_ADS_SEARCH_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY,
        ).toBaseUrl()
    } // see helm-chart/values.yaml
    override val localUrl: BaseUrl by lazy { LOCAL_HOST_BASE_URL.toBaseUrl() }
    override val localPort: Int = FULL_ADS_SEARCH_API_QA_PORT
}
