package features.sellerProfile.v2

import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileReviewDateFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.utils.CurrentDateProvider
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class SellerProfileReviewDateFormatterTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @ParameterizedTest
    @CsvSource(
        "2022-07-01T08:20:59.816Z, 2023-07-08T08:20:59.816Z, 1 year ago",
        "2021-07-01T08:20:59.816Z, 2023-07-15T08:20:59.816Z, 2 years ago",
        "2020-04-22T10:30:19.816Z, 2022-05-07T05:15:14.816Z, 2 years ago",
        "2017-04-22T10:30:19.816Z, 2022-05-07T05:15:14.816Z, 5 years ago",
        "2013-01-03T08:20:59.816Z, 2023-03-15T08:20:59.816Z, 10 years ago",
    )
    fun `should return seller profile posting for text in years`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgo() }
            Then { checkSellerProfilePostingSince(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2023-06-01T08:20:59.816Z, 2023-07-08T08:20:59.816Z, 1 month ago",
        "2024-06-01T08:20:59.816Z, 2024-07-25T08:20:59.816Z, 1 month ago",
        "2024-06-01T08:20:59.816Z, 2024-08-15T08:20:59.816Z, 2 months ago",
        "2022-02-05T10:30:59.816Z, 2022-04-23T03:25:44.816Z, 2 months ago",
        "2023-03-22T10:30:19.816Z, 2023-09-07T05:15:14.816Z, 5 months ago",
        "2023-01-03T08:20:59.816Z, 2023-11-15T08:20:59.816Z, 10 months ago",
    )
    fun `should return seller profile posting for text in months`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgo() }
            Then { checkSellerProfilePostingSince(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2023-07-01T08:20:59.523Z, 2023-07-08T08:20:59.523Z, 1 week ago",
        "2023-07-01T05:45:59.523Z, 2023-07-08T08:20:59.523Z, 1 week ago",
        "2023-07-01T08:20:59.523Z, 2023-07-15T08:20:59.523Z, 2 weeks ago",
        "2022-02-05T10:30:59.523Z, 2022-02-23T03:25:44.523Z, 2 weeks ago",
        "2023-01-03T08:20:59.523Z, 2023-02-01T08:20:59.523Z, 4 weeks ago",
    )
    fun `should return seller profile posting for in weeks`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgo() }
            Then { checkSellerProfilePostingSince(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2024-07-01T08:20:59.523Z, 2024-07-02T08:20:59.523Z, 1 day ago",
        "2024-07-01T05:45:59.523Z, 2024-07-02T08:20:59.523Z, 1 day ago",
        "2024-07-11T08:20:59.523Z, 2024-07-13T08:20:59.523Z, 2 days ago",
        "2024-02-05T10:30:59.523Z, 2024-02-08T10:25:44.523Z, 2 days ago",
        "2024-01-13T08:20:59.523Z, 2024-01-20T05:20:59.523Z, 6 days ago",
        "2024-12-24T00:12:39.523Z, 2024-12-30T09:22:29.523Z, 6 days ago",
    )
    fun `should return seller profile posting for in days`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgo() }
            Then { checkSellerProfilePostingSince(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2020-08-01T08:20:59.523Z, 2020-08-01T09:21:09.523Z, 1 hour ago",
        "2023-07-11T10:20:59.523Z, 2023-07-11T11:22:38.523Z, 1 hour ago",
        "2022-02-05T10:30:49.523Z, 2022-02-05T12:40:59.523Z, 2 hours ago",
        "2023-01-13T08:20:29.523Z, 2023-01-13T13:40:40.523Z, 5 hours ago",
        "2023-12-24T12:12:39.523Z, 2023-12-25T01:17:50.523Z, 13 hours ago",
        "2024-12-24T10:10:10.523Z, 2024-12-25T06:15:00.523Z, 20 hours ago",
        "2024-11-30T03:10:10.523Z, 2024-12-01T02:15:00.523Z, 23 hours ago",
    )
    fun `should return seller profile posting for in hours`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgo() }
            Then { checkSellerProfilePostingSince(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2023-07-01T, 2023-07-01T05:46",
        "2023-07-01T05:46, dasdasdasdsa",
        " '', ''",
    )
    fun `should return NULL seller profile posting for if the date is NOT in the correct format`(oldDate: String, newDate: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgo() }
            Then { checkSellerProfilePostingSince(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualTimeAgoResultResult: String? = null

        private lateinit var olderDate: String
        private lateinit var newerDate: String

        private val testSubject = SellerProfileReviewDateFormatter(
            createUKDateTimeFormatter(SellerProfileScreenUiConfiguration.SELLER_PROFILE_REVIEW_DATE_FORMAT)
        )

        override fun setup() {
            mockkObject(CurrentDateProvider)
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubOlderDate(date: String) {
            olderDate = date
        }

        fun stubNewerDate(date: String) {
            newerDate = date
        }

        fun getTimeAgo() {
            actualTimeAgoResultResult = testSubject.getTimeAgoLabel(olderDate, newerDate)
        }

        fun checkSellerProfilePostingSince(expected: String?) {
            assertEquals(expected, actualTimeAgoResultResult)
        }
    }
}