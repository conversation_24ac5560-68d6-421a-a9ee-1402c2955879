package common

import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.common.toDistance
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class DistanceTest {

    private val robot = Robot()

    @Test
    fun `should return ZERO Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("ZERO") }
            When { stringToDistance() }
            Then { checkDistance(Distance.ZERO) }
        }
    }

    @Test
    fun `should return QUARTER Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("QUARTER") }
            When { stringToDistance() }
            Then { checkDistance(Distance.QUARTER) }
        }
    }

    @Test
    fun `should return HALF Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("HALF") }
            When { stringToDistance() }
            Then { checkDistance(Distance.HALF) }
        }
    }

    @Test
    fun `should return ONE Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("ONE") }
            When { stringToDistance() }
            Then { checkDistance(Distance.ONE) }
        }
    }

    @Test
    fun `should return THREE Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("THREE") }
            When { stringToDistance() }
            Then { checkDistance(Distance.THREE) }
        }
    }

    @Test
    fun `should return FIVE Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("FIVE") }
            When { stringToDistance() }
            Then { checkDistance(Distance.FIVE) }
        }
    }

    @Test
    fun `should return TEN Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("TEN") }
            When { stringToDistance() }
            Then { checkDistance(Distance.TEN) }
        }
    }

    @Test
    fun `should return FIFTEEN Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("FIFTEEN") }
            When { stringToDistance() }
            Then { checkDistance(Distance.FIFTEEN) }
        }
    }

    @Test
    fun `should return THIRTY Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("THIRTY") }
            When { stringToDistance() }
            Then { checkDistance(Distance.THIRTY) }
        }
    }

    @Test
    fun `should return FIFTY Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("FIFTY") }
            When { stringToDistance() }
            Then { checkDistance(Distance.FIFTY) }
        }
    }

    @Test
    fun `should return SEVENTY_FIVE Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("SEVENTY_FIVE") }
            When { stringToDistance() }
            Then { checkDistance(Distance.SEVENTY_FIVE) }
        }
    }

    @Test
    fun `should return HUNDRED Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("HUNDRED") }
            When { stringToDistance() }
            Then { checkDistance(Distance.HUNDRED) }
        }
    }

    @Test
    fun `should return HUNDRED_FIFTY Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("HUNDRED_FIFTY") }
            When { stringToDistance() }
            Then { checkDistance(Distance.HUNDRED_FIFTY) }
        }
    }

    @Test
    fun `should return TWO_HUNDRED_FIFTY Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("TWO_HUNDRED_FIFTY") }
            When { stringToDistance() }
            Then { checkDistance(Distance.TWO_HUNDRED_FIFTY) }
        }
    }

    @Test
    fun `should return FIVE_HUNDRED Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("FIVE_HUNDRED") }
            When { stringToDistance() }
            Then { checkDistance(Distance.FIVE_HUNDRED) }
        }
    }

    @Test
    fun `should return NATIONWIDE Distance`() {
        runUnitTest(robot) {
            Given { stubDistanceAsString("something") }
            When { stringToDistance() }
            Then { checkDistance(Distance.NATIONWIDE) }

            Given { stubDistanceAsString("TWO") }
            When { stringToDistance() }
            Then { checkDistance(Distance.NATIONWIDE) }

            Given { stubDistanceAsString(null) }
            When { stringToDistance() }
            Then { checkDistance(Distance.NATIONWIDE) }
        }
    }

    @Test
    fun `should return ZERO`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.ZERO) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.ZERO.toString()) }
        }
    }

    @Test
    fun `should return QUARTER`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.QUARTER) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.QUARTER.toString()) }
        }
    }

    @Test
    fun `should return HALF`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.HALF) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.HALF.toString()) }
        }
    }

    @Test
    fun `should return ONE`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.ONE) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.ONE.toString()) }
        }
    }

    @Test
    fun `should return THREE`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.THREE) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.THREE.toString()) }
        }
    }

    @Test
    fun `should return FIVE`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.FIVE) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.FIVE.toString()) }
        }
    }

    @Test
    fun `should return TEN`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.TEN) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.TEN.toString()) }
        }
    }

    @Test
    fun `should return FIFTEEN`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.FIFTEEN) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.FIFTEEN.toString()) }
        }
    }

    @Test
    fun `should return THIRTY`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.THIRTY) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.THIRTY.toString()) }
        }
    }

    @Test
    fun `should return FIFTY`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.FIFTY) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.FIFTY.toString()) }
        }
    }

    @Test
    fun `should return SEVENTY_FIVE`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.SEVENTY_FIVE) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.SEVENTY_FIVE.toString()) }
        }
    }

    @Test
    fun `should return HUNDRED`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.HUNDRED) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.HUNDRED.toString()) }
        }
    }

    @Test
    fun `should return HUNDRED_FIFTY`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.HUNDRED_FIFTY) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.HUNDRED_FIFTY.toString()) }
        }
    }

    @Test
    fun `should return TWO_HUNDRED_FIFTY`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.TWO_HUNDRED_FIFTY) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.TWO_HUNDRED_FIFTY.toString()) }
        }
    }

    @Test
    fun `should return FIVE_HUNDRED`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.FIVE_HUNDRED) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.FIVE_HUNDRED.toString()) }
        }
    }

    @Test
    fun `should return NATIONWIDE`() {
        runUnitTest(robot) {
            Given { stubDistance(Distance.NATIONWIDE) }
            When { distanceToString() }
            Then { checkDistanceAsString(Distance.NATIONWIDE.toString()) }
        }
    }

    @Test
    fun `should remove QUARTER and HALF distances from the list with all distances`() {
        runUnitTest(robot) {
            Then { checkAllDistanceOptionsSize(Distance.entries.size - 2) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualDistanceResult: Distance
        private lateinit var actualDistanceStringResult: String
        private var testSubjectAsString: String? = null
        private lateinit var testSubject: Distance

        fun stubDistanceAsString(value: String?) {
            testSubjectAsString = value
        }

        fun stubDistance(value: Distance) {
            testSubject = value
        }

        fun stringToDistance() {
            actualDistanceResult = testSubjectAsString.toDistance()
        }

        fun distanceToString() {
            actualDistanceStringResult = testSubject.toString()
        }

        fun checkDistance(expected: Distance) {
            assertEquals(expected, actualDistanceResult)
        }

        fun checkDistanceAsString(expected: String) {
            assertEquals(expected, actualDistanceStringResult)
        }

        fun checkAllDistanceOptionsSize(expected: Int) {
            assertEquals(expected, Distance.allOptions.size)
        }

    }
}