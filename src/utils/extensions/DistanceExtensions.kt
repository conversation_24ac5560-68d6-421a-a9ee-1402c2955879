package com.gumtree.mobile.utils.extensions

import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.locations.LocationType

/**
 * Map a Distance to a SRP chip text
 */
fun Distance.toSrpChipText(): String? {
    return when (this) {
        Distance.NATIONWIDE -> null
        Distance.QUARTER,
        Distance.HALF,
        -> " - $value m"
        else -> " - ${value.toInt()} m"
    }
}

/**
 * Map a Distance to Filters dropdown option text
 */
fun Distance.toFilterDropdownText(): String {
    return when (this) {
        Distance.NATIONWIDE -> Distance.NATIONWIDE.name
        Distance.QUARTER -> "Within 1/4 miles"
        Distance.HALF -> "Within 1/2 miles"
        Distance.ONE -> "Within 1 mile"
        else -> "Within ${value.toInt()} miles"
    }
}

fun Distance.migrateLegacyData(locationType: LocationType): Distance {
    return when (this) {
        Distance.ZERO, Distance.QUARTER, Distance.HALF -> when (locationType) {
            LocationType.POSTCODE -> Distance.ONE
            else -> Distance.ZERO
        }
        else -> this
    }
}
