package features.categories

import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.CurrentDateProvider
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class CategoriesTreeCacheTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should NOT have valid cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given { stubCategoriesTreeCache(null) }
            When { hasValidCache() }
            Then { checkHasValidCache(false) }
        }
    }

    @Test
    fun `should have valid cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given {
                stubCategoriesTreeCache(
                    CategoryDto(
                        id = DataFactory.anyString(),
                        text = DataFactory.anyString(),
                        idName = DataFactory.anyString(),
                        seoDisplayName = DataFactory.anyString(),
                        children = emptyList()
                    )
                )
            }
            When { hasValidCache() }
            Then { checkHasValidCache(true) }
        }
    }

    @Test
    fun `should NOT have cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given { stubCategoriesTreeCache(null)}
            When { getCacheData() }
            Then { checkCacheDataIsNull() }
        }
    }

    @Test
    fun `should have cache data`() {
        runUnitTest(robot) {
            Given { stubCurrentDateTimestamp(1694076222036) }
            Given {
                stubCategoriesTreeCache(
                    CategoryDto(
                        id = DataFactory.anyString(),
                        text = DataFactory.anyString(),
                        idName = DataFactory.anyString(),
                        seoDisplayName = DataFactory.anyString(),
                        children = emptyList()
                    )
                )
            }
            When { getCacheData() }
            Then { checkCacheDataIsNotNull() }
        }
    }

    @Test
    fun `should return a category item if find item by ID is successful`() {
        runUnitTest(robot) {
            Given {
                stubCategoriesTreeCache(
                    CategoryDto(
                        id = CategoryDefaults.ALL_CATEGORIES.id,
                        text = CategoryDefaults.ALL_CATEGORIES.text,
                        idName =  CategoryDefaults.ALL_CATEGORIES.idName,
                        seoDisplayName = DataFactory.anyString(),
                        children = listOf(
                            CategoryDto(
                                id = CategoryDefaults.PETS.id,
                                text = CategoryDefaults.PETS.text,
                                idName = CategoryDefaults.PETS.idName,
                                seoDisplayName = DataFactory.anyString(),
                            ),
                            CategoryDto(
                                id = CategoryDefaults.JOBS.id,
                                text = CategoryDefaults.JOBS.text,
                                idName = CategoryDefaults.JOBS.idName,
                                seoDisplayName = DataFactory.anyString(),
                            ),
                            CategoryDto(
                                id = CategoryDefaults.CARS.id,
                                text = CategoryDefaults.CARS.text,
                                idName = CategoryDefaults.CARS.idName,
                                seoDisplayName = DataFactory.anyString(),
                            )
                        )
                    )
                )
            }
            When { findItemByIdOrDefault(CategoryDefaults.CARS.id) }
            Then { checkFoundItemById(CategoryDefaults.CARS.text) }

            When { findItemByIdOrDefault(CategoryDefaults.PETS.id) }
            Then { checkFoundItemById(CategoryDefaults.PETS.text) }

            When { findItemByIdOrDefault(CategoryDefaults.JOBS.id) }
            Then { checkFoundItemById(CategoryDefaults.JOBS.text) }
        }
    }

    @Test
    fun `should return the default category item if find item by ID is NOT successful`() {
        runUnitTest(robot) {
            Given {
                stubCategoriesTreeCache(
                    CategoryDto(
                        id = CategoryDefaults.ALL_CATEGORIES.id,
                        text = CategoryDefaults.ALL_CATEGORIES.text,
                        idName = CategoryDefaults.ALL_CATEGORIES.idName,
                        seoDisplayName = CategoryDefaults.ALL_CATEGORIES.seoDisplayName,
                        children = listOf(
                            CategoryDto(
                                id = CategoryDefaults.PETS.id,
                                text = CategoryDefaults.PETS.text,
                                idName = CategoryDefaults.PETS.idName,
                                seoDisplayName = DataFactory.anyString(),
                            )
                        )
                    )
                )
            }
            When { findItemByIdOrDefault("6882188338192") }
            Then { checkFoundItemById(CategoryDefaults.ALL_CATEGORIES.text) }

            When { findItemByIdOrDefault(CategoryDefaults.CARS.id + "55") }
            Then { checkFoundItemById(CategoryDefaults.ALL_CATEGORIES.text) }
        }
    }

    @Test
    fun `should get nearby distance for motors category`() {
        runUnitTest(robot) {
            Given { stubCategoryId(CategoryDefaults.MOTORS.id) }
            When { getNearbyDistanceForCategory() }
            Then { checkDistance(Distance.SEVENTY_FIVE) }
        }
    }

    @Test
    fun `should get nearby distance for other category`() {
        runUnitTest(robot) {
            Given { stubCategoryId(CategoryDefaults.JOBS.id) }
            When { getNearbyDistanceForCategory() }
            Then { checkDistance(Distance.FIFTEEN) }
        }
    }

    @Test
    fun `should concatenate seo name`() {
        runUnitTest(robot) {
            Given {
                stubCategoriesTreeCache(
                    CategoryDto(
                        id = CategoryDefaults.ALL_CATEGORIES.id,
                        text = CategoryDefaults.ALL_CATEGORIES.text,
                        idName = CategoryDefaults.ALL_CATEGORIES.idName,
                        seoDisplayName = CategoryDefaults.ALL_CATEGORIES.seoDisplayName,
                        children = listOf(
                            CategoryDto(
                                id = CategoryDefaults.PETS.id,
                                text = CategoryDefaults.PETS.text,
                                idName = CategoryDefaults.PETS.idName,
                                seoDisplayName = DataFactory.anyString(),
                                parentId = CategoryDefaults.ALL_CATEGORIES.id,
                            )
                        )
                    )
                )
            }
            When { getCategoryHierarchySeoName(CategoryDefaults.PETS.id) }
            Then { checkCategoryHierarchySeoName("${CategoryDefaults.ALL_CATEGORIES.idName}>${CategoryDefaults.PETS.idName}") }
        }
    }

    @Test
    fun `should concatenate seo display name`() {
        runUnitTest(robot) {
            Given {
                stubCategoriesTreeCache(
                    CategoryDto(
                        id = CategoryDefaults.ALL_CATEGORIES.id,
                        text = CategoryDefaults.ALL_CATEGORIES.text,
                        idName = CategoryDefaults.ALL_CATEGORIES.idName,
                        seoDisplayName = CategoryDefaults.ALL_CATEGORIES.seoDisplayName,
                        children = listOf(
                            CategoryDto(
                                id = CategoryDefaults.PETS.id,
                                text = CategoryDefaults.PETS.text,
                                idName = CategoryDefaults.PETS.idName,
                                seoDisplayName = CategoryDefaults.PETS.seoDisplayName,
                                parentId = CategoryDefaults.ALL_CATEGORIES.id,
                            )
                        )
                    )
                )
            }
            When { getCategoryHierarchySeoDisplayName(CategoryDefaults.PETS.id) }
            Then { checkCategoryHierarchySeoDisplayName("${CategoryDefaults.ALL_CATEGORIES.seoDisplayName}>${CategoryDefaults.PETS.seoDisplayName}") }
        }
    }

    private class Robot: BaseRobot {
        private var actualHasValidCacheResult: Boolean = false
        private var actualCacheDataResult: CategoryDto? = null
        private var categoryId: String = ""
        private var actualDistance: Distance? = null
        private lateinit var actualFoundCategoryDtoResult: CategoryDto
        private lateinit var actualCategoryHierarchySeoName: String
        private lateinit var actualCategoryHierarchySeoDisplayName: String

        private val testSubject = CategoriesTreeCache

        override fun setup() {
            mockkObject(CurrentDateProvider)
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubCurrentDateTimestamp(timestamp: Long) {
            every { CurrentDateProvider.getCurrentTimestamp() } returns timestamp
        }

        fun stubCategoriesTreeCache(categoriesTree: CategoryDto?) {
            testSubject.data = categoriesTree
        }

        fun hasValidCache() {
            actualHasValidCacheResult = testSubject.hasValidCache()
        }

        fun getCacheData() {
            actualCacheDataResult = testSubject.data
        }

        fun findItemByIdOrDefault(id: String) {
            actualFoundCategoryDtoResult = testSubject.findItemByIdOrDefault(id)
        }

        fun getCategoryHierarchySeoName(id: String) {
            actualCategoryHierarchySeoName = testSubject.getCategoryHierarchySeoName(id)
        }

        fun getCategoryHierarchySeoDisplayName(id: String) {
            actualCategoryHierarchySeoDisplayName = testSubject.getCategoryHierarchySeoDisplayName(id)
        }


        fun checkHasValidCache(expected: Boolean) {
            assertEquals(actualHasValidCacheResult, expected)
        }

        fun checkCacheDataIsNull() {
            assertNull(actualCacheDataResult)
        }

        fun checkCacheDataIsNotNull() {
            assertNotNull(actualCacheDataResult)
        }

        fun checkFoundItemById(expected: String) {
            assertEquals(expected, actualFoundCategoryDtoResult.text)
        }

        fun stubCategoryId(categoryId: String) {
            this.categoryId = categoryId
        }

        fun getNearbyDistanceForCategory() {
            actualDistance = testSubject.getNearbyDistanceForCategory(categoryId)
        }

        fun checkDistance(expected: Distance) {
            assertEquals(expected, actualDistance)
        }

        fun checkCategoryHierarchySeoName(expected: String) {
            assertEquals(expected, actualCategoryHierarchySeoName)
        }

        fun checkCategoryHierarchySeoDisplayName(expected: String) {
            assertEquals(expected, actualCategoryHierarchySeoDisplayName)
        }
    }
}