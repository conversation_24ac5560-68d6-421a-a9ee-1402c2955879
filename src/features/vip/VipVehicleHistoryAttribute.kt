package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.layoutsData.HPICardDto

/**
 * Data class to define the VIP vehicle history attributes
 * @property title - the display title of the vehicle history attribute
 * @property required - indication if the history check is required or not
 * @property pass - indication if the history check pass or not
 * @property description - the display description of the vehicle history attribute when required (some attributes require description text)
 */
data class VipVehicleHistoryAttribute(
    val title: String,
    val required: <PERSON><PERSON>an,
    val pass: Boolean,
    val description: String?,
)

const val ATTRIBUTE_VEHICLE_NOT_STOLEN_TITLE = "Not stolen"
const val ATTRIBUTE_VEHICLE_NOT_SCRAPPED_TITLE = "Not scrapped"
const val ATTRIBUTE_VEHICLE_NOT_EXPORTED_TITLE = "Not exported"
const val ATTRIBUTE_VEHICLE_NOT_CAT_A_OR_B_TITLE = "Not Cat A/B"
const val ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D_TITLE = "Not Cat C/D"
const val ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N_TITLE = "Not Cat S/N"
const val ATTRIBUTE_VEHICLE_UK_MODEL_TITLE = "Not imported"
const val ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE_TITLE = "Original number plate"
const val ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR_TITLE = "Original colour"

@Suppress("MaxLineLength")
const val ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D_DESCRIPTION = "Category C\n\nThe vehicle is repairable but the costs exceeds the vehicle\'s value. Can re-appear on road.\n\nCategory D\n\nThe vehicle is repairable but repair costs are significant compared to the vehicle value – including time delays to source parts. Can re-appear on road."

@Suppress("MaxLineLength")
const val ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N_DESCRIPTION = "Category S\\n\\nThe vehicle is repairable but has been structurally damaged. Can re-appear on road.\\n\\nCategory N\\n\\nThe vehicle is repairable and has not been structurally damaged. Can re-appear on road."

@Suppress("MaxLineLength")
const val ATTRIBUTE_VEHICLE_UK_MODEL_DESCRIPTION = "Buying or selling an imported car is perfectly legal. Be sure you aware of the implications of owning an imported vehicle."

@Suppress("MaxLineLength")
const val ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE_DESCRIPTION = "Different number plates are commonplace. Ensure the number plates on the car match the V5C documentation. Be sure of what number plate comes with the car you are buying."

@Suppress("MaxLineLength")
const val ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR_DESCRIPTION = "A different colour from the original is perfectly legal and there may be good reasons. Look for other evidence of modifications that may cost more to insure."

/**
 * The list with all vehicle history required attributes for the HPI checks display
 * All required attributes will be displayed as soon as the listing has RawCapiAd.ATTRIBUTE_VEHICLE_HISTORY_CHECKED == true
 */
val vehicleHistoryRequiredAttributes: Map<String, VipVehicleHistoryAttribute> = linkedMapOf(
    RawCapiAd.ATTRIBUTE_VEHICLE_NOT_STOLEN to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_NOT_STOLEN_TITLE,
        required = true,
        pass = true,
        description = null,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_NOT_SCRAPPED to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_NOT_SCRAPPED_TITLE,
        required = true,
        pass = true,
        description = null,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_NOT_EXPORTED to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_NOT_EXPORTED_TITLE,
        required = true,
        pass = true,
        description = null,
    ),
)

/**
 * The list with all optional vehicle history attributes for the HPI checks display
 * The optional attribute are displayed only if they exist in the listing attributes list
 * Some of the optional attributes have description text, others don't
 */
val vehicleHistoryOptionalAttributes: Map<String, VipVehicleHistoryAttribute> = linkedMapOf(
    RawCapiAd.ATTRIBUTE_VEHICLE_NOT_CAT_A_OR_B to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_NOT_CAT_A_OR_B_TITLE,
        required = false,
        pass = false,
        description = null,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D_TITLE,
        required = false,
        pass = false,
        description = ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D_DESCRIPTION,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N_TITLE,
        required = false,
        pass = false,
        description = ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N_DESCRIPTION,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_UK_MODEL to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_UK_MODEL_TITLE,
        required = false,
        pass = false,
        description = ATTRIBUTE_VEHICLE_UK_MODEL_DESCRIPTION,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE_TITLE,
        required = false,
        pass = false,
        description = ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE_DESCRIPTION,
    ),
    RawCapiAd.ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR to VipVehicleHistoryAttribute(
        title = ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR_TITLE,
        required = false,
        pass = false,
        description = ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR_DESCRIPTION,
    ),
)

/**
 * Extension function to map VipVehicleHistoryAttribute to HPICardDto.Check
 */
fun VipVehicleHistoryAttribute.toHpiCardDtoCheck(status: HPICardDto.Check.Status): HPICardDto.Check {
    return HPICardDto.Check(
        label = title,
        value = status.toDisplayText(),
        status = status,
        description = status.toDisplayDescription(description),
    )
}
