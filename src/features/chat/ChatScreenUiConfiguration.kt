package com.gumtree.mobile.features.chat

import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd.AdCategory
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.utils.extensions.isNotNull

/**
 * Defines the Chat screen UI components configurations
 */

object ChatScreenUiConfiguration {
    const val BLOCK_USER_TEXT = "Block"
    const val REPORT_CONVERSATION_TEXT = "Report"
    const val RAW_MESSAGE_POST_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX" // the format of the RawMessage post date
    const val CHAT_MESSAGE_POST_DATE_FORMAT = "hh:mm a, MMM d, yyyy" // the format of the BFF chat message
    const val CONVERSATION_ID_PREFIX = "messaging:"
    const val VIEW_PROFILE_TEXT = "View Profile"
    const val CHAT_REVIEW_TITLE_TEXT = "Rate your experience with"

    /**
     * Control the visibility of the Toolbar Block user action
     */
    const val IS_TOOLBAR_BLOCK_USER_HIDDEN = true

    /**
     * Control the visibility of the Toolbar Report conversation action
     */
    const val IS_TOOLBAR_REPORT_CONVERSATION_HIDDEN = true

    /**
     * Conversation reviews are allowed only in "For Sale" category and all sub-categories
     */
    fun isPostingReviewAllowed(categoryId: String?): Boolean {
        return when {
            categoryId.isNotNull() -> CategoriesTreeCache.isForSale(categoryId)
            else -> false
        }
    }

    /**
     * Conversation reviews are allowed only in "For Sale" category and all sub-categories
     * expend for sale on motors
     */
    fun isPostingReviewAllowed(categoryId: List<AdCategory>?): Boolean {
        return when {
            categoryId.isNotNull() -> CategoriesTreeCache.isForSaleExpendMotors(categoryId.map { it.id })
            else -> false
        }
    }
}
