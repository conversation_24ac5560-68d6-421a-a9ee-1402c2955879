package com.gumtree.mobile.features.srp

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiContactMethod
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.features.screens.layoutsData.ContactButtonCardDto
import com.gumtree.mobile.features.screens.layoutsData.ContactButtonRowDto
import com.gumtree.mobile.features.screens.layoutsData.DestinationActionDto
import com.gumtree.mobile.features.screens.layoutsData.PhoneActionDto
import com.gumtree.mobile.features.screens.layoutsData.SrpButtonLabel
import com.gumtree.mobile.features.screens.layoutsData.SrpButtonType
import com.gumtree.mobile.features.screens.layoutsData.SrpIconType
import com.gumtree.mobile.features.screens.layoutsData.SrpSize
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.isNotNull
import features.srp.SrpAdjustTrackingDataProvider

class SrpButtonUIProvider(
    private val srpAnalyticsProvider: SrpAnalyticsProvider,
    private val srpAdjustTrackingDataProvider: SrpAdjustTrackingDataProvider,
) {

    fun createContactMethod(
        rawAdDetailsData: RawCapiAd,
        isUserOwnAd: Boolean,
    ): List<ContactButtonRowDto>? {
        val adStatus = rawAdDetailsData.status?.value?.let { AdStatus.fromString(it) } ?: AdStatus.UNKNOWN
        return when {
            isUserOwnAd -> null
            adStatus == AdStatus.EXPIRED || adStatus == AdStatus.DELETED || adStatus == AdStatus.UNKNOWN -> null
            else -> contactMethodOverlay(rawAdDetailsData)
        }
    }

    private fun contactMethodOverlay(
        rawAdDetailsData: RawCapiAd,
    ): List<ContactButtonRowDto>? {
        val selectedContactMethods = rawAdDetailsData.contactMethods?.filter { it.enabled.toBoolean() } ?: emptyList()
        // 只保留 PHONE 和 EMAIL 联系方式
        val phoneContactMethod = selectedContactMethods.firstOrNull { it.name == RawCapiContactMethod.PHONE }
        val emailContactMethod = selectedContactMethods.firstOrNull { it.name == RawCapiContactMethod.EMAIL }
        return when {
            phoneContactMethod.isNotNull() && emailContactMethod.isNotNull() -> {
                // 两种联系方式都有，返回两个按钮
                listOf(
                    createPhoneContactMethod(rawAdDetailsData, phoneContactMethod.label),
                    createEmailContactMethod(rawAdDetailsData),
                )
            }
            phoneContactMethod.isNotNull() -> {
                // 只有电话联系方式
                listOf(createPhoneContactMethod(rawAdDetailsData, phoneContactMethod.label))
            }
            emailContactMethod.isNotNull() -> {
                // 只有邮件联系方式
                listOf(createEmailContactMethod(rawAdDetailsData))
            }
            else -> null
        }
    }

    private fun createPhoneContactMethod(
        rawAdDetailsData: RawCapiAd,
        label: String,
    ): ContactButtonRowDto {
        return ContactButtonRowDto(
            data = listOf(
                ContactButtonCardDto(
                    text = label,
                    size = SrpSize.MEDIUM,
                    buttonType = SrpButtonType.SECONDARY,
                    iconType = SrpIconType.PHONE,
                    action = PhoneActionDto(
                        number = null,
                        analyticsEventData = srpAnalyticsProvider.getCallSellerEvent(),
                        adjustTrackingData = srpAdjustTrackingDataProvider.getCallSellerEvent(rawAdDetailsData),
                    ),
                ),
            ),
        )
    }

    private fun createEmailContactMethod(
        rawAdDetailsData: RawCapiAd,
    ): ContactButtonRowDto {
        return ContactButtonRowDto(
            data = listOf(
                ContactButtonCardDto(
                    text = SrpButtonLabel.REQUEST.displayText,
                    size = SrpSize.MEDIUM,
                    buttonType = SrpButtonType.PRIMARY,
                    iconType = SrpIconType.REQUEST,
                    action = DestinationActionDto(
                        destination = DestinationRoute.ENQUIRE.build(
                            ApiQueryParams.ID to rawAdDetailsData.id,
                        ),
                    ),
                ),
            ),
        )
    }
}
