package com.gumtree.mobile.features.homeFeed

import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.StickyBar

/**
 * Provider of HomeFeed StickyBar data
 */
@Deprecated("should use the HomeFeedTopBar provider from now on - this is used up to Android 10.1.23 and iOS 18.1.20")
class HomeFeedStickyBarProvider(
    private val homeFeedChipsFactory: HomeFeedChipsFactory,
) {

    /**
     * Get the list with HomeFeed sticky bar RowLayouts
     * @param locationId - the user's HomeFeed locationId
     * @param locationType - the user's HomeFeed location type
     * @param locationName - the user's HomeFeed location name
     * @param distance - the user's HomeFeed location distance
     * @return - a list with all RowLayouts in the HomeFeed StickyBar
     */
    fun createHomeFeedStickyBar(
        locationId: String,
        locationType: LocationType,
        locationName: String,
        distance: Distance,
    ): StickyBar {
        return listOf(
            generateHomeFeedCategoryChips(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            ),
        )
    }

    private fun generateHomeFeedCategoryChips(
        locationId: String,
        locationType: LocationType,
        locationName: String,
        distance: Distance,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.CHIPS_ROW,
            bottomDivider = true,
            data = homeFeedChipsFactory.buildHomeFeedChips(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            ),
        )
    }
}
