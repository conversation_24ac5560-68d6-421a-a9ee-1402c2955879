package api.locations

import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.locations.models.RawLocation.Type
import com.gumtree.mobile.routes.DEFAULT_UK_LATITUDE
import com.gumtree.mobile.routes.DEFAULT_UK_LONGITUDE
import com.gumtree.mobile.utils.LocationDefaults
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.runUnitTest
import tools.When

class RawLocationFetcherTest {

    private val robot = Robot()

    @Test
    fun `should return raw location data about ALL_UK`() {
        runUnitTest(robot) {
            When { createAllUKRawLocationData() }
            Then { checkRawLocationId(LocationDefaults.ALL_UK.id.toInt()) }
            Then { checkRawLocationName(LocationDefaults.ALL_UK.text) }
            Then { checkRawLocationType(Type.location) }
            Then { checkRawLocationLatitude(DEFAULT_UK_LATITUDE.toDouble()) }
            Then { checkRawLocationLongitude(DEFAULT_UK_LONGITUDE.toDouble()) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualRawLocationResult: RawLocation

        fun createAllUKRawLocationData() {
            actualRawLocationResult = RawLocationFetcher.createAllUKRawLocationData()
        }

        fun checkRawLocationId(expected: Int) {
            assertEquals(expected, actualRawLocationResult.id)
        }

        fun checkRawLocationName(expected: String) {
            assertEquals(expected, actualRawLocationResult.name)
        }

        fun checkRawLocationType(expected: Type) {
            assertEquals(expected, actualRawLocationResult.type)
        }

        fun checkRawLocationLatitude(expected: Double?) {
            assertEquals(expected, actualRawLocationResult.latitude)
        }

        fun checkRawLocationLongitude(expected: Double?) {
            assertEquals(expected, actualRawLocationResult.longitude)
        }
    }
}