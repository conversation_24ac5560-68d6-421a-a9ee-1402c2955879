kotlin.code.style=official
kotlin.daemon.jvmargs=-Xmx8G
org.gradle.caching=true

######################
# Project Plugins versions
# See https://plugins.gradle.org/ for new Plugin versions
#####################
kotlinSerialisationPluginVersion = 1.9.23
googleCloudPluginVersion = 3.4.0
sonarCloudPluginVersion = 6.0.1.5171

######################
# Project Dependencies versions
# See https://mvnrepository.com/ for new SDK versions
# See Ktor Official doc https://ktor.io/docs/gradle-application-plugin.html for updates
#####################
googleCloundVersion = 2.5.0
kotlinVersion = 1.9.23
kotlinCoroutinesVersion = 1.7.3
ktorVersion = 2.3.13
koverVersion = 0.9.1
detektVersion = 1.23.8

googleCloudAppengineVersion = 2.0.22
okhttpVersion = 4.12.0
retrofitVersion = 2.11.0
gsonVersion = "2.10.1"
logbackVersion = 1.5.5
swaggerUIVersion = 1.0.47
prometheusVersion = 1.10.3

koinVersion = 3.4.0
simpleXmlVersion = 2.7.1
tikXmlParserVersion = 0.8.13

mockkVersion = 1.13.14
junitVersion = 5.11.4

kotlinTestFixturesVersion = 1.0.5
######################
