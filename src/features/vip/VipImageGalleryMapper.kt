package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.utils.extensions.data.toVipImageCardDto
import com.gumtree.mobile.utils.extensions.isNotNull

/**
 * Mapping the RawCapiAd data into List with VipImageCardDto mobile application data
 */
class VipImageGalleryMapper {

    fun map(
        rawData: RawCapiAd,
        gamAdvertImageCard: VipImageCardDto?,
    ): RowLayout<UiItem> {
        val vipImageCardDtoList = rawData.pictures?.mapNotNull {
            it.toVipImageCardDto()
        } ?: emptyList()

        return RowLayout(
            type = RowLayoutType.VIP_IMAGE_GALLERY_ROW,
            data = appendGamAdvert(vipImageCardDtoList, gamAdvertImageCard),
        )
    }

    private fun appendGamAdvert(
        imageCardDtoList: List<VipImageCardDto>,
        gamAdvertImageCard: VipImageCardDto?,
    ): List<VipImageCardDto> {
        return when {
            gamAdvertImageCard.isNotNull() && imageCardDtoList.size >= VipScreenUiConfiguration.IMAGE_GALLERY_GAM_ADVERT_ATTACH_LIMIT -> {
                imageCardDtoList
                    .toMutableList()
                    .apply {
                        add(gamAdvertImageCard)
                    }
            }
            else -> imageCardDtoList
        }
    }
}
