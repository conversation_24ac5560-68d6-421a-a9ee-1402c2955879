@file:Suppress("LongMethod")

package com.gumtree.mobile.features.homeFeed

import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.utils.extensions.isGreaterThan
import com.gumtree.mobile.utils.extensions.isOne
import com.gumtree.mobile.utils.extensions.isTwo
import com.gumtree.mobile.utils.extensions.isZero

/**
 * HomeFeed Adverts UI configurations, defines the adverts positions and the adverts appearance on the HomeFeed pages.
 *
 * See the BFF Wiki https://github.com/gumtree-tech/mobile-apps-bff/wiki/HomeFeed-screen-adverts-UI-Configurations#landscape-orientation
 *
 * **** PORTRAIT ****
 * Page 0 - HPTO -> POS-0, TOP -> POS-5, MIDDLE_1 -> POS-10
 * Page 1 - MIDDLE_2 -> POS-5, MIDDLE_3_1 -> POS-10
 * Page 2 - MIDDLE_3_2 -> POS-5, MIDDLE_3_3 -> POS-10 (second MIDDLE_3 appearance in the same page)
 * Page 3 - MIDDLE_3_4 -> POS-5, MIDDLE_3_5 -> POS-10 (second MIDDLE_3 appearance in the same page)
 * Page 4 - MIDDLE_3_6 -> POS-5, MIDDLE_3_7 -> POS-10 (second MIDDLE_3 appearance in the same page)
 * Page 5 - MIDDLE_3_8 -> POS-5, MIDDLE_3_9 -> POS-10 (second MIDDLE_3 appearance in the same page)
 * etc. (infinite repeat MIDDLE_3 slot just generate unique "key" property value)
 *
 * **** LANDSCAPE ****
 * Page 0 - HPTO -> POS-0, TOP -> POS-5
 * Page 1 - MIDDLE_1 -> POS-5
 * Page 2 - MIDDLE_2 -> POS-5
 * Page 3 - MIDDLE_3_1 -> POS-5
 * Page 4 - MIDDLE_3_2 -> POS-5
 * Page 5 - MIDDLE_3_3 -> POS-5
 * Page 6 - MIDDLE_3_4 -> POS-5
 * Page 7 - MIDDLE_3_5 -> POS-5
 * etc. (infinite repeat MIDDLE_3 slot just generate unique "key" property value)
 */
object HomeFeedAdvertsUiConfiguration {
    /**
     * HPTO advert row position in the list with Home Feed RowLayouts
     */
    const val HPTO_ADVERT_ROW_POSITION = 0

    /**
     * TOP advert row position in the list with Home Feed RowLayouts
     */
    const val TOP_ADVERT_ROW_POSITION = 5

    /**
     * MIDDLE_1 advert row position in the list with Home Feed RowLayouts
     */
    const val MIDDLE_1_ADVERT_ROW_POSITION = 5

    /**
     * MIDDLE_2 advert row position in the list with Home Feed RowLayouts
     */
    const val MIDDLE_2_ADVERT_ROW_POSITION = 5

    /**
     * MIDDLE_3 advert row position in the list with Home Feed RowLayouts
     */
    const val MIDDLE_3_ADVERT_ROW_POSITION = 5

    /**
     * Indicates whether the HomeFeed HPTO advert should appear and on which position
     */
    fun hptoAdvertAppearance(page: String): Pair<Boolean, Int> {
        return Pair(page.isZero(), HPTO_ADVERT_ROW_POSITION)
    }

    /**
     * Indicates whether the HomeFeed TOP advert should appear and on which position
     */
    fun topAdvertAppearance(page: String): Pair<Boolean, Int> {
        return Pair(page.isZero(), TOP_ADVERT_ROW_POSITION)
    }

    /**
     * Indicates whether the HomeFeed MIDDLE_1 advert should appear and on which position
     */
    fun middle1AdvertAppearance(
        page: String,
        isLandscape: Boolean,
    ): Pair<Boolean, Int> {
        return when {
            isLandscape -> Pair(page.isOne(), MIDDLE_1_ADVERT_ROW_POSITION)
            else -> Pair(page.isZero(), MIDDLE_1_ADVERT_ROW_POSITION * 2)
        }
    }

    /**
     * Indicates whether the HomeFeed MIDDLE_2 advert should appear and on which position
     */
    fun middle2AdvertAppearance(
        page: String,
        isLandscape: Boolean,
    ): Pair<Boolean, Int> {
        return when {
            isLandscape -> Pair(page.isTwo(), MIDDLE_2_ADVERT_ROW_POSITION)
            else -> Pair(page.isOne(), MIDDLE_2_ADVERT_ROW_POSITION)
        }
    }

    /**
     * Indicates whether the HomeFeed MIDDLE_3 advert should appear and on which position
     */
    fun middle3AdvertAppearance(
        page: String,
        isLandscape: Boolean,
    ): Pair<Boolean, Int> {
        return when {
            isLandscape -> Pair(page.isGreaterThan(2), MIDDLE_3_ADVERT_ROW_POSITION)
            page.isOne() -> Pair(page.isOne(), MIDDLE_3_ADVERT_ROW_POSITION * 2)
            else -> Pair(page.isGreaterThan(1), MIDDLE_3_ADVERT_ROW_POSITION)
        }
    }

    /**
     * Indicates whether the HomeFeed MIDDLE_3 advert should appear for a second time on the page and on which position
     */
    fun middle3SecondAdvertAppearance(
        page: String,
        isLandscape: Boolean,
    ): Pair<Boolean, Int> {
        return when {
            isLandscape -> Pair(false, MIDDLE_3_ADVERT_ROW_POSITION) // no second middle3 slot for landscape
            else -> Pair(
                page.isGreaterThan(1),
                MIDDLE_3_ADVERT_ROW_POSITION * 2,
            ) // second middle3 slot should appear only if the page is 2 and onwards
        }
    }
}

/**
 * Append all advert RowLayouts into HomeFeed screen (portrait and landscape data) for specific page number.
 * @param page - the screen page
 * @param homeFeedGAMAttributes - the common HomeFeed GAM Advert attributes
 * @param advertsProvider - the HomeFeed advert RowLayouts provider
 * @return - A new Pair of portrait and landscape data lists with the appended advert RowLayouts for the specific HomeFeed screen page.
 * If a page shouldn't have adverts, the method returns the original Pair of portrait and landscape data
 */
fun Pair<List<RowLayout<UiItem>>, List<RowLayout<UiItem>>?>.appendHomeFeedAdvertRowLayouts(
    page: String,
    homeFeedGAMAttributes: GAMAdvertAttributes,
    advertsProvider: HomeFeedAdvertsProvider,
): Pair<List<RowLayout<UiItem>>, List<RowLayout<UiItem>>?> {
    val newPortraitData = first.toMutableList()
    val newLandscapeData = second?.toMutableList()
    val newPortraitDataSize = newPortraitData.size
    val newLandscapeDataSize = newLandscapeData?.size ?: ZERO

    val (shouldHptoAdvertAppear, hptoAdvertPosition) = HomeFeedAdvertsUiConfiguration.hptoAdvertAppearance(page)
    val (shouldTopAdvertAppear, topAdvertPosition) = HomeFeedAdvertsUiConfiguration.topAdvertAppearance(page)
    val (shouldMiddle1AdvertPortraitAppear, middle1AdvertPortraitPosition) = HomeFeedAdvertsUiConfiguration.middle1AdvertAppearance(
        page,
        false,
    )
    val (shouldMiddle2AdvertPortraitAppear, middle2AdvertPortraitPosition) = HomeFeedAdvertsUiConfiguration.middle2AdvertAppearance(
        page,
        false,
    )
    val (shouldMiddle3AdvertPortraitAppear, middle3AdvertPortraitPosition) = HomeFeedAdvertsUiConfiguration.middle3AdvertAppearance(
        page,
        false,
    )
    val (shouldMiddle3SecondAdvertPortraitAppear, middle3SecondAdvertPortraitPosition) = HomeFeedAdvertsUiConfiguration.middle3SecondAdvertAppearance(
        page,
        false,
    )

    val (shouldMiddle1AdvertLandscapeAppear, middle1AdvertLandscapePosition) = HomeFeedAdvertsUiConfiguration.middle1AdvertAppearance(
        page,
        true,
    )
    val (shouldMiddle2AdvertLandscapeAppear, middle2AdvertLandscapePosition) = HomeFeedAdvertsUiConfiguration.middle2AdvertAppearance(
        page,
        true,
    )
    val (shouldMiddle3AdvertLandscapeAppear, middle3AdvertLandscapePosition) = HomeFeedAdvertsUiConfiguration.middle3AdvertAppearance(
        page,
        true,
    )
    val (shouldMiddle3SecondAdvertLandscapeAppear, middle3SecondAdvertLandscapePosition) = HomeFeedAdvertsUiConfiguration.middle3SecondAdvertAppearance(
        page,
        true,
    )

    // !!IMPORTANT!!
    // The appending of the adverts should be done in reversed order (from the last towards the first).
    // This shall guarantee the required advert rows on the correct positions in the HomeFeed portrait and landscape lists
    if (shouldMiddle3SecondAdvertPortraitAppear && newPortraitDataSize >= middle3SecondAdvertPortraitPosition) {
        val middle3SecondAdvertRow = advertsProvider.createHomeFeedMiddle3AdvertRow(homeFeedGAMAttributes)
        newPortraitData.add(middle3SecondAdvertPortraitPosition, middle3SecondAdvertRow)
    }
    if (shouldMiddle3AdvertPortraitAppear && newPortraitDataSize >= middle3AdvertPortraitPosition) {
        val middle3AdvertRow = advertsProvider.createHomeFeedMiddle3AdvertRow(homeFeedGAMAttributes)
        newPortraitData.add(middle3AdvertPortraitPosition, middle3AdvertRow)
    }
    if (shouldMiddle2AdvertPortraitAppear && newPortraitDataSize >= middle2AdvertPortraitPosition) {
        val middle2AdvertRow = advertsProvider.createHomeFeedMiddle2AdvertRow(homeFeedGAMAttributes)
        newPortraitData.add(middle2AdvertPortraitPosition, middle2AdvertRow)
    }
    if (shouldMiddle1AdvertPortraitAppear && newPortraitDataSize >= middle1AdvertPortraitPosition) {
        val middle1AdvertRow = advertsProvider.createHomeFeedMiddle1AdvertRow(homeFeedGAMAttributes)
        newPortraitData.add(middle1AdvertPortraitPosition, middle1AdvertRow)
    }

    if (shouldMiddle3SecondAdvertLandscapeAppear && newLandscapeDataSize >= middle3SecondAdvertLandscapePosition) {
        val middle3SecondAdvertRow = advertsProvider.createHomeFeedMiddle3AdvertRow(homeFeedGAMAttributes)
        newLandscapeData?.add(middle3SecondAdvertLandscapePosition, middle3SecondAdvertRow)
    }
    if (shouldMiddle3AdvertLandscapeAppear && newLandscapeDataSize >= middle3AdvertLandscapePosition) {
        val middle3AdvertRow = advertsProvider.createHomeFeedMiddle3AdvertRow(homeFeedGAMAttributes)
        newLandscapeData?.add(middle3AdvertLandscapePosition, middle3AdvertRow)
    }
    if (shouldMiddle2AdvertLandscapeAppear && newLandscapeDataSize >= middle2AdvertLandscapePosition) {
        val middle2AdvertRow = advertsProvider.createHomeFeedMiddle2AdvertRow(homeFeedGAMAttributes)
        newLandscapeData?.add(middle2AdvertLandscapePosition, middle2AdvertRow)
    }
    if (shouldMiddle1AdvertLandscapeAppear && newLandscapeDataSize >= middle1AdvertLandscapePosition) {
        val middle1AdvertRow = advertsProvider.createHomeFeedMiddle1AdvertRow(homeFeedGAMAttributes)
        newLandscapeData?.add(middle1AdvertLandscapePosition, middle1AdvertRow)
    }

    if (shouldTopAdvertAppear) {
        val topAdvertRow = advertsProvider.createHomeFeedTopAdvertRow(homeFeedGAMAttributes)
        if (newPortraitDataSize >= topAdvertPosition) {
            newPortraitData.add(topAdvertPosition, topAdvertRow)
        }
        if (newLandscapeDataSize >= topAdvertPosition) {
            newLandscapeData?.add(topAdvertPosition, topAdvertRow)
        }
    }

    if (shouldHptoAdvertAppear) {
        val hptoAdvertRow = advertsProvider.createHomeFeedHptoAdvertRow(homeFeedGAMAttributes)
        newPortraitData.add(hptoAdvertPosition, hptoAdvertRow)
        newLandscapeData?.add(hptoAdvertPosition, hptoAdvertRow)
    }

    return Pair(
        first = newPortraitData,
        second = newLandscapeData,
    )
}
