package com.gumtree.mobile.features.savedSearches

import com.gumtree.mobile.api.capi.models.RawCapiSavedSearch
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.routes.DEFAULT_PAGE_NUMBER
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import com.gumtree.mobile.utils.extensions.data.toQueryParams

class SavedSearchIndicatorProvider(private val savedSearchesService: SavedSearchesService) {

    suspend fun hasNewResults(
        apiHeaders: ApiHeaders,
        rawSavedSearch: RawCapiSavedSearch,
        savedSearchClientCacheRequest: Map<String, String>,
    ): String? {
        val savedSearchModAfterDate = savedSearchClientCacheRequest[rawSavedSearch.id] ?: EMPTY_STRING // "2024-07-13T15:46:13.171Z"
        val savedSearchQueryParams = rawSavedSearch.searchLink.toQueryParams().toMutableMap().apply {
            put("modAfter", savedSearchModAfterDate)
        }
        val numFound = savedSearchesService.searchAds(
            apiHeaders,
            savedSearchQueryParams,
            DEFAULT_PAGE_NUMBER,
            DEFAULT_PAGE_SIZE,
        )
            .rawPaging
            .numFound
        return when {
            numFound > 0 -> rawSavedSearch.id
            else -> null
        }
    }
}
