package com.gumtree.mobile.features.screens.factories

import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.layoutsData.UiItem

class SpaceRowFactory {

    fun createSpaceRow(
        width: Space? = null,
        height: Space? = null,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.SPACE_ROW,
            data = emptyList(),
            style = Style(
                Style.Spacing(
                    margins = Style.Spacing.Dimension(right = width, bottom = height),
                ),
            ),
        )
    }
}
