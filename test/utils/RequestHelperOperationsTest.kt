package utils

import api.capi.models.RawCapiSavedSearchList
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.features.homeFeed.HomeFeedSavedSearchesService
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.handleAuthRequestOrNull
import com.gumtree.mobile.utils.handleAuthRequestOrThrow
import com.gumtree.mobile.utils.handleRequestOrNull
import io.ktor.http.*
import io.ktor.server.plugins.NotFoundException
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.coroutines.Given
import tools.coroutines.When
import tools.coroutines.runUnitTest
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.DataFactory
import tools.DataFactory.anyString
import tools.annotations.ParallelTest
import tools.coroutines.Then
import tools.coroutines.runUnitTestForException

@ParallelTest
class RequestHelperOperationsTest {

    private val robot = Robot()

    @Test
    fun `should return the request response`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestSuccessfulResponse() }
            When { handleRequestOrNullIfError(withLogging = true) }
            Then { checkRequestResponseIsNotNull() }
        }
    }

    @Test
    fun `should return the request response without logging`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestSuccessfulResponse() }
            When { handleRequestOrNullIfError(withLogging = false) }
            Then { checkRequestResponseIsNotNull() }
        }
    }

    @Test
    fun `should return NULL request response`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestErrorResponse() }
            When { handleRequestOrNullIfError(withLogging = true) }
            Then { checkRequestResponseIsNull() }
        }
    }

    @Test
    fun `should return NULL request response without logging`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestErrorResponse() }
            When { handleRequestOrNullIfError(withLogging = false) }
            Then { checkRequestResponseIsNull() }
        }
    }

    @Test
    fun `should return the request response for request with auth headers`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestSuccessfulResponse() }
            When { handleAuthRequestOrNull(userEmail = DataFactory.SOME_USER_EMAIL, userToken = DataFactory.SOME_USER_SECRET) }
            Then { checkRequestResponseIsNotNull() }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "${DataFactory.SOME_USER_EMAIL}, ",
        " , ${DataFactory.SOME_USER_SECRET}",
        " , ",
    )
    fun `should return NULL for request without auth headers`(userEmail: String?, userToken: String?) = runTest {
        runUnitTest(robot) {
            When { handleAuthRequestOrNull(userEmail = userEmail, userToken = userToken) }
            Then { checkRequestResponseIsNull() }
        }
    }

    @Test
    fun `should return NULL request response for failing request with auth headers`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestErrorResponse() }
            When { handleAuthRequestOrNull(userEmail = DataFactory.SOME_USER_EMAIL, userToken = DataFactory.SOME_USER_SECRET) }
            Then { checkRequestResponseIsNull() }
        }
    }

    @Test
    fun `should return the request response for request with auth headers and no errors`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestSuccessfulResponse() }
            When { handleAuthorizationRequestOrThrow(userEmail = DataFactory.SOME_USER_EMAIL, userToken = DataFactory.SOME_USER_SECRET) }
            Then { checkRequestResponseIsNotNull() }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "${DataFactory.SOME_USER_EMAIL}, ",
        " , ${DataFactory.SOME_USER_SECRET}",
        " , ",
    )
    fun `should throw UnAuthorized error for request without auth headers`(userEmail: String?, userToken: String?) = runTest {
        runUnitTestForException(robot, UnauthorisedException::class) {
            When { handleAuthorizationRequestOrThrow(userEmail = userEmail, userToken = userToken) }
        }
    }

    @Test
    fun `should throw error for failing request with auth headers`() = runTest {
        runUnitTestForException(robot, NotFoundException::class) {
            Given { stubRequestErrorResponse(NotFoundException()) }
            When { handleAuthorizationRequestOrThrow(userEmail = DataFactory.SOME_USER_EMAIL, userToken = DataFactory.SOME_USER_SECRET) }
        }
    }

    private class Robot: BaseRobot {

        private val homeFeedSavedSearchesService: HomeFeedSavedSearchesService = mockk(relaxed = true)
        private val rawCapiSavedSearchList: RawCapiSavedSearchList = mockk(relaxed = true)
        private val authorisationHeaders: ApiHeaders = mockk(relaxed = true)
        private var actualRequestResult: RawCapiSavedSearchList? = null

        fun stubRequestSuccessfulResponse() {
            coEvery { homeFeedSavedSearchesService.getUserSavedSearches(any(), any(), any()) } returns rawCapiSavedSearchList
        }

        fun stubRequestErrorResponse(error: Exception = UnauthorisedException()) {
            coEvery { homeFeedSavedSearchesService.getUserSavedSearches(any(), any(), any()) } throws error
        }

        suspend fun handleRequestOrNullIfError(withLogging: Boolean) {
            actualRequestResult = handleRequestOrNull(enableLogging = withLogging) {
                homeFeedSavedSearchesService.getUserSavedSearches(authorisationHeaders, anyString(), anyString())
            }
        }

        suspend fun handleAuthRequestOrNull(
            userEmail: String?,
            userToken: String?,
        ) {
            val callHeaders = HeadersBuilder().apply {
                userEmail?.let { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, it) }
                userToken?.let { append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, it) }
            }.build()
            actualRequestResult = handleAuthRequestOrNull(callHeaders) {
                homeFeedSavedSearchesService.getUserSavedSearches(authorisationHeaders, anyString(), anyString())
            }
        }

        suspend fun handleAuthorizationRequestOrThrow(
            userEmail: String?,
            userToken: String?,
        ) {
            val callHeaders = HeadersBuilder().apply {
                userEmail?.let { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, it) }
                userToken?.let { append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, it) }
            }.build()
            actualRequestResult = handleAuthRequestOrThrow(callHeaders) {
                homeFeedSavedSearchesService.getUserSavedSearches(authorisationHeaders, anyString(), anyString())
            }
        }

        fun checkRequestResponseIsNotNull() {
            assertNotNull(actualRequestResult)
        }

        fun checkRequestResponseIsNull() {
            assertNull(actualRequestResult)
        }
    }
}