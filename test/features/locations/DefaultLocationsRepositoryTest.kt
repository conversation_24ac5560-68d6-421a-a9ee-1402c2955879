package features.locations

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.locations.api.DEFAULT_LOCATION_SUGGESTIONS_TYPES
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.locations.models.RawLocationSuggestions
import com.gumtree.mobile.features.locations.DefaultLocationsRepository
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.locations.LocationsService
import com.gumtree.mobile.features.locations.LocationsSuggestionsMapper
import com.gumtree.mobile.features.locations.SimpleLocationDto
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.coroutines.runUnitTestForException
import tools.rawDataFactory.RawLocationFactory
import utils.TestDispatcherProvider

class DefaultLocationsRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should get nearest location by lat and long`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawLocation(
                    locationId = DataFactory.SOME_LOCATION_ID.toInt(),
                    name = DataFactory.SOME_LOCATION_NAME,
                    locationType = RawLocation.Type.location,
                    locationLat = DataFactory.SOME_LOCATION_LAT_DOUBLE,
                    locationLng = DataFactory.SOME_LOCATION_LON_DOUBLE
                )
            }
            When { getNearestLocation(DataFactory.SOME_LOCATION_LAT, DataFactory.SOME_LOCATION_LNG) }
            Then {
                checkServiceGetLocationDetailsByLatLng(
                    DataFactory.SOME_LOCATION_LAT,
                    DataFactory.SOME_LOCATION_LNG
                )
            }
        }
    }

    @Test
    fun `should get location suggestions by keyword`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    locationId = DataFactory.SOME_LOCATION_ID.toInt(),
                    name = DataFactory.SOME_LOCATION_NAME,
                    locationType = RawLocation.Type.location,
                    locationLat = DataFactory.SOME_LOCATION_LAT_DOUBLE,
                    locationLng = DataFactory.SOME_LOCATION_LON_DOUBLE,
                )
            }
            When { readLocationSuggestions(DataFactory.SOME_LOCATION_KEYWORD) }
            Then {
                checkGetLocationSuggestionsCalled(DataFactory.SOME_LOCATION_KEYWORD)
                checkLocationSuggestions(
                    listOf(
                        SimpleLocationDto(
                            DataFactory.SOME_LOCATION_ID,
                            DataFactory.SOME_LOCATION_NAME,
                            LocationType.LOCATION,
                            DataFactory.SOME_LOCATION_LAT_DOUBLE,
                            DataFactory.SOME_LOCATION_LON_DOUBLE,
                        ),
                    )
                )
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "LOCATION, location",
        "POSTCODE, postcode",
        "OUTCODE, outcode",
    )
    fun `should get location suggestions with expected location types`(
        locationType: String,
        expectedLocationType: String,
    ) = runTest {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    locationId = DataFactory.SOME_LOCATION_ID.toInt(),
                    name = DataFactory.SOME_LOCATION_NAME,
                    locationType = RawLocation.Type.location,
                    locationLat = DataFactory.SOME_LOCATION_LAT_DOUBLE,
                    locationLng = DataFactory.SOME_LOCATION_LON_DOUBLE,
                )
            }
            When { readLocationSuggestions(DataFactory.SOME_LOCATION_KEYWORD, LocationType.fromString(locationType)) }
            Then {
                checkGetLocationSuggestionsCalled(DataFactory.SOME_LOCATION_KEYWORD, expectedLocationType)
            }
        }
    }

    @Test
    fun `should get location suggestions with default location types`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    locationId = DataFactory.SOME_LOCATION_ID.toInt(),
                    name = DataFactory.SOME_LOCATION_NAME,
                    locationType = RawLocation.Type.location,
                    locationLat = DataFactory.SOME_LOCATION_LAT_DOUBLE,
                    locationLng = DataFactory.SOME_LOCATION_LON_DOUBLE,
                )
            }
            When { readLocationSuggestions(DataFactory.SOME_LOCATION_KEYWORD) }
            Then {
                checkGetLocationSuggestionsCalled(DataFactory.SOME_LOCATION_KEYWORD, DEFAULT_LOCATION_SUGGESTIONS_TYPES)
            }
        }
    }

    @Test
    fun `should throw IllegalArgumentException when getting location suggestions without keyword`() = runTest {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            When { readLocationSuggestions(EMPTY_STRING) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "${DataFactory.SOME_LOCATION_LAT}, ''",
        "'', ${DataFactory.SOME_LOCATION_LNG}",
    )
    fun `should throw IllegalArgumentException when reading nearest location with only latitude or only longitude`(
        lat: String,
        lng: String,
    ) = runTest {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            When { getNearestLocation(lat, lng) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualLocationSuggestionsResult: List<SimpleLocationDto>
        private lateinit var actualLocationResult: SimpleLocationDto

        private val locationSuggestionOptions = mutableListOf<RawLocation>()

        private val locationsService: LocationsService = mockk(relaxed = true)
        private val locationsSuggestionsMapper = spyk(LocationsSuggestionsMapper())

        private lateinit var testSubject: DefaultLocationsRepository

        override fun setup() {
            testSubject = DefaultLocationsRepository(
                locationsService,
                locationsSuggestionsMapper,
                TestDispatcherProvider(),
            )
            locationSuggestionOptions.clear()
        }

        fun stubRawLocationSuggestion(
            name: String,
            locationType: RawLocation.Type,
            locationId: Int,
            locationLat: Double?,
            locationLng: Double?,
        ) {
            val rawLocationSuggestion = RawLocationFactory.createRawLocation(
                locationId,
                name,
                locationType,
                locationId,
                locationLat,
                locationLng,
            )
            locationSuggestionOptions.add(rawLocationSuggestion)
            coEvery { locationsService.getLocationSuggestions(any()) } returns RawLocationSuggestions(options = locationSuggestionOptions)
        }

        fun stubRawLocation(
            name: String,
            locationType: RawLocation.Type,
            locationId: Int,
            locationLat: Double?,
            locationLng: Double?,
        ) {
            val rawLocation = RawLocationFactory.createRawLocation(
                locationId,
                name,
                locationType,
                locationId,
                locationLat,
                locationLng,
            )
            coEvery { locationsService.getLocationById(any()) } returns rawLocation
            coEvery { locationsService.getLocationByLatLng(any(), any()) } returns rawLocation
        }

        suspend fun readLocationSuggestions(query: String, locationType: LocationType? = null) {
            actualLocationSuggestionsResult = testSubject.readLocationSuggestions(query, locationType)
        }

        suspend fun getNearestLocation(latitude: String, longitude: String) {
            actualLocationResult = testSubject.getNearestLocation(latitude, longitude)
        }

        fun checkLocationSuggestions(expected: List<SimpleLocationDto>) {
            assertEquals(expected, actualLocationSuggestionsResult)
        }

        fun checkGetLocationSuggestionsCalled(
            keyword: String,
            locationTypes: String = DEFAULT_LOCATION_SUGGESTIONS_TYPES,
        ) {
            coVerify { locationsService.getLocationSuggestions(keyword, locationTypes) }
        }

        fun checkServiceGetLocationDetailsByLatLng(latitude: String, longitude: String) {
            coVerify { locationsService.getLocationByLatLng(latitude, longitude) }
        }
    }
}