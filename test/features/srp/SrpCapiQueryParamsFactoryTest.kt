package features.srp

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.utils.extensions.ATTR_PREFIX
import com.gumtree.mobile.features.srp.SORT_TYPE_DATE_DESCENDING
import com.gumtree.mobile.features.srp.SrpCapiQueryParamsFactory
import com.gumtree.mobile.features.srp.SrpScreenUiConfiguration.RELEVANCE_DESCENDING_KEY
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class SrpCapiQueryParamsFactoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should append all expected common CAPI SRP query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParamsSize(6) }
            Then { checkQueryParam(CapiApiParams.AD_STATUS, AdStatus.ACTIVE.toString()) }
            Then { checkQueryParam(CapiApiParams.SORT_TYPE, SORT_TYPE_DATE_DESCENDING) }
            Then { checkQueryParam(CapiApiParams.INCLUDE_TOP_ADS, "true") }
            Then { checkQueryParam(CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH, "true") }
            Then { checkQueryParam(CapiApiParams.PICTURE_REQUIRED, "false") }
        }
    }

    @Test
    fun `should keep the original SRP request query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ApiQueryParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParamsSize(7) }
            Then { checkQueryParam(ApiQueryParams.LOCATION_ID, DataFactory.SOME_AD_LOCATION_ID) }
            Then { checkQueryParam(ApiQueryParams.CATEGORY_ID, DataFactory.SOME_AD_CATEGORY_ID) }
        }
    }

    @Test
    fun `should append the default value of the sort type query parameter if it is NOT there`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParamsSize(6) }
            Then { checkQueryParam(ApiQueryParams.SORT_TYPE, SORT_TYPE_DATE_DESCENDING) }
        }
    }

    @Test
    fun `should NOT append the default value of the sort type query parameter if it is there`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ApiQueryParams.SORT_TYPE to "SOME_SORT_TYPE_VALUE"
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParamsSize(6) }
            Then { checkQueryParam(ApiQueryParams.SORT_TYPE, "SOME_SORT_TYPE_VALUE") }
        }
    }

    @Test
    fun `should modify specific category attribute query parameters`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                        ApiQueryParams.VEHICLE_MAKE to DataFactory.SOME_VEHICLE_MAKE,
                        ApiQueryParams.VEHICLE_MODEL to DataFactory.SOME_VEHICLE_MODEL,
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam("attr[${ApiQueryParams.VEHICLE_MAKE}]", DataFactory.SOME_VEHICLE_MAKE) }
            Then { checkQueryParam(ApiQueryParams.VEHICLE_MAKE, null) }
            Then { checkQueryParam("attr[${ApiQueryParams.VEHICLE_MODEL}]", DataFactory.SOME_VEHICLE_MODEL) }
            Then { checkQueryParam(ApiQueryParams.VEHICLE_MODEL, null) }
        }
    }

    @Test
    fun `should NOT modify min and max specific category attribute query parameters`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                        ApiQueryParams.MIN_PRICE to DataFactory.SOME_MIN_AD_PRICE,
                        ApiQueryParams.MAX_PRICE to DataFactory.SOME_MAX_AD_PRICE,
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam(ApiQueryParams.MIN_PRICE, DataFactory.SOME_MIN_AD_PRICE) }
            Then { checkQueryParam(ApiQueryParams.MAX_PRICE, DataFactory.SOME_MAX_AD_PRICE) }
        }
    }

    @Test
    fun `should modify CARS specific category attribute query parameters and NOT modify the rest query parameters`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ApiQueryParams.DISTANCE to Distance.THREE.name,
                        ApiQueryParams.SORT_TYPE to SORT_TYPE_DATE_DESCENDING,
                        CapiApiParams.INCLUDE_TOP_ADS to "true",
                        CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH to "true",
                        CapiApiParams.PICTURE_REQUIRED to "false",
                        ApiQueryParams.Q to DataFactory.SOME_KEYWORD,
                        ApiQueryParams.VEHICLE_MAKE to DataFactory.SOME_VEHICLE_MAKE,
                        ApiQueryParams.VEHICLE_MODEL to DataFactory.SOME_VEHICLE_MODEL,
                        ApiQueryParams.VEHICLE_BODY_TYPE to "coupe",
                        ApiQueryParams.MIN_PRICE to DataFactory.SOME_MIN_AD_PRICE,
                        ApiQueryParams.MAX_PRICE to DataFactory.SOME_MAX_AD_PRICE,
                        ApiQueryParams.PAGE to DataFactory.SOME_PAGE_NUMBER,
                        ApiQueryParams.SIZE to DataFactory.SOME_SIZE,
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam(ApiQueryParams.CATEGORY_ID, CategoryDefaults.CARS.id) }
            Then { checkQueryParam(ApiQueryParams.LOCATION_ID, DataFactory.SOME_AD_LOCATION_ID) }
            Then { checkQueryParam(ApiQueryParams.DISTANCE, Distance.THREE.name) }
            Then { checkQueryParam(ApiQueryParams.SORT_TYPE, SORT_TYPE_DATE_DESCENDING) }
            Then { checkQueryParam(CapiApiParams.INCLUDE_TOP_ADS, "true") }
            Then { checkQueryParam(CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH, "true") }
            Then { checkQueryParam(CapiApiParams.PICTURE_REQUIRED, "false") }
            Then { checkQueryParam(ApiQueryParams.Q, DataFactory.SOME_KEYWORD) }
            Then { checkQueryParam("attr[${ApiQueryParams.VEHICLE_MAKE}]", DataFactory.SOME_VEHICLE_MAKE) }
            Then { checkQueryParam("attr[${ApiQueryParams.VEHICLE_MODEL}]", DataFactory.SOME_VEHICLE_MODEL) }
            Then { checkQueryParam("attr[${ApiQueryParams.VEHICLE_BODY_TYPE}]", "coupe") }
            Then { checkQueryParam(ApiQueryParams.MIN_PRICE, DataFactory.SOME_MIN_AD_PRICE) }
            Then { checkQueryParam(ApiQueryParams.MAX_PRICE, DataFactory.SOME_MAX_AD_PRICE) }
            Then { checkQueryParam(ApiQueryParams.PAGE, DataFactory.SOME_PAGE_NUMBER) }
            Then { checkQueryParam(ApiQueryParams.SIZE, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should modify TO SHARE specific category attribute query parameters and NOT modify the rest query parameters`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to "2547",
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ApiQueryParams.DISTANCE to Distance.FIVE.name,
                        ApiQueryParams.SORT_TYPE to SORT_TYPE_DATE_DESCENDING,
                        CapiApiParams.INCLUDE_TOP_ADS to "true",
                        CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH to "true",
                        CapiApiParams.PICTURE_REQUIRED to "false",
                        RawCapiAd.ATTRIBUTE_PROPERTY_TYPE to "flat",
                        RawCapiAd.ATTRIBUTE_PROPERTY_ROOM_TYPE to "single",
                        RawCapiAd.ATTRIBUTE_PROPERTY_COUPLES to "yes",
                        ApiQueryParams.MIN_PRICE to DataFactory.SOME_MIN_AD_PRICE,
                        ApiQueryParams.MAX_PRICE to DataFactory.SOME_MAX_AD_PRICE,
                        ApiQueryParams.PAGE to DataFactory.SOME_PAGE_NUMBER,
                        ApiQueryParams.SIZE to DataFactory.SOME_SIZE,
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam(ApiQueryParams.CATEGORY_ID, "2547") }
            Then { checkQueryParam(ApiQueryParams.LOCATION_ID, DataFactory.SOME_AD_LOCATION_ID) }
            Then { checkQueryParam(ApiQueryParams.DISTANCE, Distance.FIVE.name) }
            Then { checkQueryParam(ApiQueryParams.SORT_TYPE, SORT_TYPE_DATE_DESCENDING) }
            Then { checkQueryParam(CapiApiParams.INCLUDE_TOP_ADS, "true") }
            Then { checkQueryParam(CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH, "true") }
            Then { checkQueryParam(CapiApiParams.PICTURE_REQUIRED, "false") }
            Then { checkQueryParam("attr[${RawCapiAd.ATTRIBUTE_PROPERTY_TYPE}]", "flat") }
            Then { checkQueryParam("attr[${RawCapiAd.ATTRIBUTE_PROPERTY_ROOM_TYPE}]", "single") }
            Then { checkQueryParam("attr[${RawCapiAd.ATTRIBUTE_PROPERTY_COUPLES}]", "yes") }
            Then { checkQueryParam(ApiQueryParams.MIN_PRICE, DataFactory.SOME_MIN_AD_PRICE) }
            Then { checkQueryParam(ApiQueryParams.MAX_PRICE, DataFactory.SOME_MAX_AD_PRICE) }
            Then { checkQueryParam(ApiQueryParams.PAGE, DataFactory.SOME_PAGE_NUMBER) }
            Then { checkQueryParam(ApiQueryParams.SIZE, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should modify SECURITY specific category attribute query parameters and NOT modify the rest query parameters`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to "12208",
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ApiQueryParams.DISTANCE to Distance.THREE.name,
                        ApiQueryParams.SORT_TYPE to SORT_TYPE_DATE_DESCENDING,
                        CapiApiParams.INCLUDE_TOP_ADS to "true",
                        CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH to "true",
                        CapiApiParams.PICTURE_REQUIRED to "false",
                        RawCapiAd.ATTRIBUTE_JOBS_CONTRACT_TYPE to "freelance",
                        RawCapiAd.ATTRIBUTE_JOBS_RECRUITER_TYPE to "agency",
                        RawCapiAd.ATTRIBUTE_JOBS_HOURS to "full_time",
                        "minSalary" to "20000",
                        "maxSalary" to "40000",
                        ApiQueryParams.PAGE to DataFactory.SOME_PAGE_NUMBER,
                        ApiQueryParams.SIZE to DataFactory.SOME_SIZE,
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam(ApiQueryParams.CATEGORY_ID, "12208") }
            Then { checkQueryParam(ApiQueryParams.LOCATION_ID, DataFactory.SOME_AD_LOCATION_ID) }
            Then { checkQueryParam(ApiQueryParams.DISTANCE, Distance.THREE.name) }
            Then { checkQueryParam(ApiQueryParams.SORT_TYPE, SORT_TYPE_DATE_DESCENDING) }
            Then { checkQueryParam(CapiApiParams.INCLUDE_TOP_ADS, "true") }
            Then { checkQueryParam(CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH, "true") }
            Then { checkQueryParam(CapiApiParams.PICTURE_REQUIRED, "false") }
            Then { checkQueryParam(RawCapiAd.ATTRIBUTE_JOBS_RECRUITER_TYPE, "agency") }
            Then { checkQueryParam(RawCapiAd.ATTRIBUTE_JOBS_HOURS, "full_time") }
            Then { checkQueryParam("attr[${RawCapiAd.ATTRIBUTE_JOBS_CONTRACT_TYPE}]", "freelance") }
            Then { checkQueryParam("minSalary", "20000") }
            Then { checkQueryParam("maxSalary", "40000") }
            Then { checkQueryParam(ApiQueryParams.PAGE, DataFactory.SOME_PAGE_NUMBER) }
            Then { checkQueryParam(ApiQueryParams.SIZE, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should keep the original SRP request query params with attr prefix`() = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        "$ATTR_PREFIX${ApiQueryParams.VEHICLE_MAKE}]" to DataFactory.SOME_VEHICLE_MAKE,
                        "$ATTR_PREFIX${ApiQueryParams.VEHICLE_MODEL}]" to DataFactory.SOME_VEHICLE_MODEL
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam("$ATTR_PREFIX${ApiQueryParams.VEHICLE_MAKE}]", DataFactory.SOME_VEHICLE_MAKE) }
            Then { checkQueryParam("$ATTR_PREFIX${ApiQueryParams.VEHICLE_MODEL}]", DataFactory.SOME_VEHICLE_MODEL) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "false, $SORT_TYPE_DATE_DESCENDING",
        "true, $RELEVANCE_DESCENDING_KEY"
    )
    fun `should use correct sort type`(isHitRankRelevant: String, expected: String) = runTest {
        runUnitTest(robot) {
            Given {
                stubOriginalSearchOptions(
                    hashMapOf(
                        CapiApiParams.IS_HIT_RANK_RELEVANT to isHitRankRelevant,
                    )
                )
            }
            When { appendRequiredCapiSearchOptions() }
            Then { checkQueryParam(CapiApiParams.SORT_TYPE, expected) }
        }
    }

    private class Robot: BaseRobot {

        private lateinit var originalQueryParams: QueryParams
        private lateinit var actualSrpCapiQueryParamsResult: QueryParams

        private lateinit var tested: SrpCapiQueryParamsFactory

        override fun setup() {
            tested = SrpCapiQueryParamsFactory()
        }

        fun stubOriginalSearchOptions(originalParams: QueryParams) {
            originalQueryParams = originalParams
        }

        fun appendRequiredCapiSearchOptions() {
            actualSrpCapiQueryParamsResult = tested.appendRequiredCapiSearchOptions(originalQueryParams)
        }

        fun checkQueryParam(
            paramKey: String,
            expectedParamValue: String?
        ) {
            assertEquals(expectedParamValue, actualSrpCapiQueryParamsResult[paramKey])
        }

        fun checkQueryParamsSize(expected: Int) {
            assertEquals(expected, actualSrpCapiQueryParamsResult.size)
        }

    }
}