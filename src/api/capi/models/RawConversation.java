package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;


@Root(name ="user-conversation", strict = false)
@Namespace(reference = Namespaces.USER, prefix = Namespaces.Prefix.USER)
public class RawConversation {

    public static final String TO_OWNER = "TO_OWNER";
    public static final String TO_BUYER = "TO_BUYER";

    @ElementList(entry = "user-message", inline = true)
    @Namespace(reference = Namespaces.USER)
    public List<RawMessage> rawMessages = new ArrayList<>();

    @Attribute(required = false)
    public String locale;

    @Attribute(required = false)
    public String version;

    @Attribute(name = "uid", required = false)
    public String id;

    @Element(name = "ad-id")
    @Namespace(reference = Namespaces.USER)
    public String adId;

    @Element(name = "ad-owner-id")
    @Namespace(reference = Namespaces.USER)
    public String adOwnerId;

    @Element(name = "ad-owner-email", required = false)
    @Namespace(reference = Namespaces.USER)
    public String adOwnerEmail;

    @Element(name = "ad-owner-name", required = false)
    @Namespace(reference = Namespaces.USER)
    @Nullable
    public String adOwnerName;

    @Element(name = "ad-replier-id", required = false)
    @Namespace(reference = Namespaces.USER)
    public String adReplierId;

    @Element(name = "ad-replier-email", required = false)
    @Namespace(reference = Namespaces.USER)
    public String adReplierEmail;

    @Element(name = "ad-replier-name", required = false)
    @Namespace(reference = Namespaces.USER)
    @Nullable
    public String adReplierName;

    @Element(name = "ad-subject", required = false)
    @Namespace(reference = Namespaces.USER)
    public String adSubject;

    @Element(name = "ad-first-img-url", required = false)
    @Namespace(reference = Namespaces.USER)
    @Nullable
    public String adImgUrl;

    @Element(name = "num-unread-msg")
    @Namespace(reference = Namespaces.USER)
    public int unreadCount;

    @Element(name = "flagged-buyer", required = false)
    @Namespace(reference = Namespaces.USER)
    public boolean buyerFlagged;

    @Element(name = "flagged-seller", required = false)
    @Namespace(reference = Namespaces.USER)
    public boolean sellerFlagged;

    @Element(name = "conversation-creation-id", required = false)
    @Namespace(reference = Namespaces.USER)
    public String conversationId;
}

