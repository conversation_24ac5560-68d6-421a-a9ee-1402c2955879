package features.homeFeed

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.homeFeed.HomeFeedApiParams
import com.gumtree.mobile.api.homeFeed.api.HomeFeedApi
import com.gumtree.mobile.features.homeFeed.HomeFeedListingsService
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import kotlin.test.assertEquals

class HomeFeedListingsServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use service to get users all home feed ads`() = runTest {
        runUnitTest(robot) {
            When { getUserHomeFeed() }
            Then { checkApiGetUserHomeFeed() }
        }
    }

    @Test
    fun `should send the expected home feed search options`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubLatitude(DataFactory.SOME_LOCATION_LAT) }
            Given { stubLongitude(DataFactory.SOME_LOCATION_LNG) }
            Given { stubPageSize(DEFAULT_PAGE_SIZE) }
            Given { stubPageNumber(DataFactory.SOME_PAGE_NUMBER) }
            When { getUserHomeFeed() }
            Then { checkLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Then { checkUserId(DataFactory.SOME_USER_ID) }
            Then { checkLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkPageSize(DEFAULT_PAGE_SIZE) }
            Then { checkPageNumber(DataFactory.SOME_PAGE_NUMBER) }
        }
    }

    private class Robot: BaseRobot {
        private val homeFeedApi: HomeFeedApi = mockk(relaxed = true)
        private val apiHeaders: ApiHeaders = mockk(relaxed = true)
        private val searchOptions: HashMap<String, String?> = hashMapOf()

        private lateinit var tested: HomeFeedListingsService

        override fun setup() {
            tested = HomeFeedListingsService(homeFeedApi)
            searchOptions.clear()
        }

        fun stubLocationId(locationId: String) {
            searchOptions[HomeFeedApiParams.LOCATION_ID] = locationId
        }

        fun stubUserId(userId: String) {
            searchOptions[HomeFeedApiParams.USER_ID] = userId
        }

        fun stubLatitude(latitude: String) {
            searchOptions[HomeFeedApiParams.LATITUDE] = latitude
        }

        fun stubLongitude(longitude: String) {
            searchOptions[HomeFeedApiParams.LONGITUDE] = longitude
        }

        fun stubPageSize(size: String) {
            searchOptions[HomeFeedApiParams.PAGE_SIZE] = size
        }

        fun stubPageNumber(pageNumber: String) {
            searchOptions[HomeFeedApiParams.PAGE_NUM] = pageNumber
        }

        suspend fun getUserHomeFeed() {
            tested.getUserHomeFeedListings(apiHeaders, searchOptions)
        }

        fun checkApiGetUserHomeFeed() {
            coVerify { homeFeedApi.getUserHomeFeedListings(apiHeaders, searchOptions) }
        }

        fun checkLocationId(expected: String) {
            assertEquals(expected, searchOptions[HomeFeedApiParams.LOCATION_ID])
        }

        fun checkUserId(expected: String) {
            assertEquals(expected, searchOptions[HomeFeedApiParams.USER_ID])
        }

        fun checkLatitude(expected: String) {
            assertEquals(expected, searchOptions[HomeFeedApiParams.LATITUDE])
        }

        fun checkLongitude(expected: String) {
            assertEquals(expected, searchOptions[HomeFeedApiParams.LONGITUDE])
        }

        fun checkPageSize(expected: String) {
            assertEquals(expected, searchOptions[HomeFeedApiParams.PAGE_SIZE])
        }

        fun checkPageNumber(expected: String) {
            assertEquals(expected, searchOptions[HomeFeedApiParams.PAGE_NUM])
        }
    }
}