package com.gumtree.mobile.features.screens.factories

import com.gumtree.mobile.features.screens.layoutsData.IconCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem

class IconFactory {
    fun buildCenterIconCard(
        resource: IconCardDto.Resource,
        size: IconCardDto.Size,
    ): UiItem {
        return IconCardDto.Center(
            resource = resource,
            size = size,
        )
    }
}
