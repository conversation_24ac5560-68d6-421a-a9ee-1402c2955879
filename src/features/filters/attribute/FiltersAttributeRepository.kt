package com.gumtree.mobile.features.filters.attribute

import com.gumtree.mobile.features.filters.FiltersCategoryAttributesCache
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiQueryParams

/**
 * Handles all CRUD operations about the Filters attribute value selection feature
 */
fun interface FiltersAttributeRepository {

    /**
     * Read Filter attribute values screen
     * @param filterParameters - The filter attribute request query parameters
     * @return - the Filter attribute values screen response data ready to be returned to the client
     */
    suspend fun readScreen(filterParameters: QueryParams): ScreenResponse
}

class DefaultFiltersAttributeRepository(
    private val filtersAttributeValuesProvider: FiltersAttributeValuesProvider,
    private val filtersCategoryAttributesCache: FiltersCategoryAttributesCache,
) : FiltersAttributeRepository {

    override suspend fun readScreen(filterParameters: QueryParams): ScreenResponse {
        val categoryId = filterParameters[ApiQueryParams.CATEGORY_ID]
        val attributeName = filterParameters[ApiQueryParams.NAME]
        val attributeSelectedValue = filterParameters[ApiQueryParams.VALUE]
        val rootAttributeName = filterParameters[ApiQueryParams.ROOT_NAME]
        val rootAttributeSelectedValue = filterParameters[ApiQueryParams.ROOT_VALUE]
        val dependentAttributeName = filterParameters[ApiQueryParams.DEPENDENT_NAME]
        val dependentAttributeSelectedValue = filterParameters[ApiQueryParams.DEPENDENT_VALUE]
        val filterAttribute = filtersCategoryAttributesCache.findAttributeByName(attributeName, categoryId)
        val rootFilterAttribute = filtersCategoryAttributesCache.findAttributeByName(rootAttributeName)
        val dependentFilterAttribute = filtersCategoryAttributesCache.findAttributeByName(dependentAttributeName)
        val attributeValueRows = filtersAttributeValuesProvider.createAttributeValueRows(
            categoryId,
            attributeName,
            attributeSelectedValue,
            rootAttributeName,
            rootAttributeSelectedValue,
            dependentAttributeName,
            dependentAttributeSelectedValue,
            filterAttribute,
            rootFilterAttribute,
            dependentFilterAttribute,
        )
        val screenTitle = FiltersAttributeScreenUiConfiguration.getFilterAttributeValuesScreenTitle(filterAttribute)

        val searchAndAdditionalRows = filtersAttributeValuesProvider.getSearchAndAdditionalRows(attributeName)
        return ScreenResponse(
            portraitData = listOfNotNull(
                *searchAndAdditionalRows.toTypedArray(),
                *attributeValueRows,
            ),
            title = screenTitle,
        )
    }
}
