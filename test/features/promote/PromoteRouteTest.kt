package features.promote

import com.gumtree.mobile.features.promote.PROMOTE_SCREEN_PATH
import com.gumtree.mobile.features.promote.PromoteRepository
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiQueryParams
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest
import kotlin.test.assertEquals

class PromoteRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET promote request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadResponse() }
            When { getPromoteScreen(DataFactory.SOME_AD_ID) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET promote request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(UnauthorisedException()) }
            When { getPromoteScreen(DataFactory.SOME_AD_ID) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should return error when ad id param is missing`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(UnauthorisedException()) }
            When { getPromoteScreen(null) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    private inner class Robot: BaseRouteRobot() {

        val repository: PromoteRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryReadResponse() {
            coEvery { repository.readScreen(any(), any()) } returns ScreenResponse(
                portraitData = emptyList(),
            )
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery {
                repository.readScreen(any(), any())
            } throws error
        }

        suspend fun getPromoteScreen(id: String?) {
            actualResponse = client.get(PROMOTE_SCREEN_PATH) {
                id?.let {
                    parameter(ApiQueryParams.AD_ID, id)
                }
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }
    }
}
