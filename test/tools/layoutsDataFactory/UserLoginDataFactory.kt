package tools.layoutsDataFactory

import com.gumtree.mobile.api.userService.models.RawUserServiceUserType
import com.gumtree.mobile.features.login.UserLoginDto
import tools.DataFactory

object UserLoginDataFactory {

    fun create(
        userId: String = DataFactory.SOME_USER_ID,
        userEmail: String = DataFactory.SOME_USER_EMAIL,
        userDisplayName: String = DataFactory.SOME_USER_DISPLAY_NAME,
        userToken: String = DataFactory.SOME_TOKEN,
        accountId: String = DataFactory.SOME_ACCOUNT_ID,
        userAuthorization: String = DataFactory.SOME_USER_AUTHORIZATION,
        issuedDate: String = DataFactory.SOME_USER_LOGIN_ISSUED_DATE,
        userType: RawUserServiceUserType = RawUserServiceUserType.STANDARD,
    ): UserLoginDto {
        return UserLoginDto(
            userId = userId,
            userEmail = userEmail,
            userDisplayName = userDisplayName,
            userToken = userToken,
            accountId = accountId,
            userAuthorization = userAuthorization,
            issuedDate = issuedDate,
            userType = userType.toString(),
        )
    }
}
