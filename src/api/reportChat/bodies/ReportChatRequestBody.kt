package com.gumtree.mobile.api.reportChat.bodies

import com.google.gson.annotations.SerializedName

data class ReportChatRequestBody(
    @SerializedName("converseeUserId")
    val converseeUserId: Int,
    @SerializedName("conversationId")
    val conversationId: String,
    @SerializedName("advertId")
    val advertId: Int,
    @SerializedName("reasons")
    val reasons: List<RawReportChatReason>,
    @SerializedName("comment")
    val comment: String? = null,
)

enum class RawReportChatReason {
    FRAUD_SCAM,
    ABUSE_HARASSMENT,
    ADULT_INAPPROPRIATE,
    PHISHING_ATTEMPT,
    SPAM,
    POLICY_VIOLATION,
    NO_REPLY,
    NO_SHOW,
}
