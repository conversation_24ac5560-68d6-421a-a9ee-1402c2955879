package features.sellerProfile.extensions

import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.features.screens.layoutsData.SellerProfileCardDto
import com.gumtree.mobile.features.sellerProfile.extensions.toSellerProfileCardDto
import com.gumtree.mobile.utils.CurrentDateProvider
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.ZonedDateTimeFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.runUnitTest
import java.time.LocalDate
import java.time.ZoneId
import java.util.*

class RawPapiUserProfileExtensionsTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return the seller name`() {
        runUnitTest(robot) {
            Given { stubSellerProfile(firstName = DataFactory.ANOTHER_USER_FIRST_NAME) }
            When { toSellerProfileCardDto() }
            Then { checkSellerName(DataFactory.ANOTHER_USER_FIRST_NAME) }
        }
    }

    @Test
    fun `should return the seller names abbreviation`() {
        runUnitTest(robot) {
            Given { stubSellerProfile(firstName = "John", lastName = "Johnson") }
            When { toSellerProfileCardDto() }
            Then { checkSellerAbbreviation("JJ") }

            Given { stubSellerProfile(firstName = "Merry", lastName = "An") }
            When { toSellerProfileCardDto() }
            Then { checkSellerAbbreviation("MA") }

            Given { stubSellerProfile(firstName = "Merry", lastName = "") }
            When { toSellerProfileCardDto() }
            Then { checkSellerAbbreviation("M") }

            Given { stubSellerProfile(firstName = "Steven12345", lastName = "") }
            When { toSellerProfileCardDto() }
            Then { checkSellerAbbreviation("S") }
        }
    }

    @Test
    fun `should return the seller average rating`() {
        runUnitTest(robot) {
            Given { stubSellerProfile(averageRating = 2.7f) }
            When { toSellerProfileCardDto() }
            Then { checkSellerAverageRating(2.7f) }

            Given { stubSellerProfile(averageRating = 3.5f) }
            When { toSellerProfileCardDto() }
            Then { checkSellerAverageRating(3.5f) }

            Given { stubSellerProfile(averageRating = 5.0f) }
            When { toSellerProfileCardDto() }
            Then { checkSellerAverageRating(5.0f) }
        }
    }

    @Test
    fun `should return the seller total ratings`() {
        runUnitTest(robot) {
//            Given { stubSellerType(VipScreenUiConfiguration.TRADE_SELLER) }
            Given { stubSellerProfile(totalRatings = 1) }
            When { toSellerProfileCardDto() }
            Then { checkSellerTotalRatings(1) }

            Given { stubSellerProfile(totalRatings = 7) }
            When { toSellerProfileCardDto() }
            Then { checkSellerTotalRatings(7) }

            Given { stubSellerProfile(totalRatings = 15) }
            When { toSellerProfileCardDto() }
            Then { checkSellerTotalRatings(15) }

            Given { stubSellerProfile(totalRatings = 120) }
            When { toSellerProfileCardDto() }
            Then { checkSellerTotalRatings(120) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualSellerProfileCardDtoResult: SellerProfileCardDto

        private val dateFormatter = createUKDateTimeFormatter("yyyy-MM-dd'T'HH:mm:ss")
        val currentYear by lazy { Calendar.getInstance().get(Calendar.YEAR) }

        private lateinit var testSubject: RawPapiUserProfile

        override fun setup() {
            mockkObject(CurrentDateProvider)
            every { CurrentDateProvider.getCurrentDate() } returns ZonedDateTimeFactory.createZonedDate("$currentYear-12-18T06:00:00")
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubSellerProfile(
            firstName: String = DataFactory.SOME_USER_FIRST_NAME,
            lastName: String = DataFactory.SOME_USER_LAST_NAME,
            averageRating: Float = 0.0f,
            totalRatings: Int = 0,
            registrationDate: String = "2001-12-18T06:00:00"
        ) {
            testSubject = RawPapiUserProfileFactory.createRawUserProfileWithRating(
                displayName = firstName,
                lastName = lastName,
                registrationDate = Date.from(
                    LocalDate.parse(registrationDate, dateFormatter).atStartOfDay(ZoneId.systemDefault()).toInstant()
                ),
                averageRating = averageRating,
                totalRatingsCount = totalRatings,
            )
        }

        fun toSellerProfileCardDto() {
            actualSellerProfileCardDtoResult = testSubject.toSellerProfileCardDto()
        }

        fun checkSellerName(expected: String?) {
            assertEquals(expected, actualSellerProfileCardDtoResult.sellerName)
        }

        fun checkSellerAbbreviation(expected: String?) {
            assertEquals(expected, actualSellerProfileCardDtoResult.sellerAbbreviation)
        }

        fun checkSellerAverageRating(expected: Float) {
            assertEquals(expected, actualSellerProfileCardDtoResult.sellerAverageRating)
        }

        fun checkSellerTotalRatings(expected: Int) {
            assertEquals(expected, actualSellerProfileCardDtoResult.sellerTotalRatings)
        }
    }
}