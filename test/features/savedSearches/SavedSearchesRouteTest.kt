package features.savedSearches

import com.gumtree.mobile.features.savedSearches.SAVED_SEARCHES_DELETE_PATH
import com.gumtree.mobile.features.savedSearches.SAVED_SEARCHES_PATH
import com.gumtree.mobile.features.savedSearches.SAVED_SEARCHES_SCREEN_PATH
import com.gumtree.mobile.features.savedSearches.SavedSearchDto
import com.gumtree.mobile.features.savedSearches.SavedSearchRequest
import com.gumtree.mobile.features.savedSearches.SavedSearchResponseDto
import com.gumtree.mobile.features.savedSearches.SavedSearchesRepository
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.ID_PATH
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.jsonHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest
import kotlin.test.assertEquals


class SavedSearchesRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET saved searches request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubSavedSearchesData() }
            When { getSavedSearches() }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET saved searches request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadResponseError(UnauthorisedException()) }
            When { getSavedSearches() }
        }
    }

    @Test
    fun `should complete GET saved searches screen request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getSavedSearchesScreen(1, 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { getSavedSearchesScreen(2, 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { getSavedSearchesScreen(11, 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET saved searches screen request with success when no page and size provided`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getSavedSearchesScreen() }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { getSavedSearchesScreen() }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET saved searches screen request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(UnauthorisedException()) }
            When { getSavedSearchesScreen() }
        }
    }

    @Test
    fun `should complete POST saved searches screen request with success when no page and size provided`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreeWithNewListingsIndicationResponse() }
            When { postSavedSearchesScreen() }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete POST saved searches screen request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreeWithNewListingsIndicationResponse() }
            When { postSavedSearchesScreen(page = 1, size = 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { postSavedSearchesScreen(page = 2, size = 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { postSavedSearchesScreen(page = 10, size = 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete POST saved searches screen request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { readScreenWithNewListingsIndicationError(UnauthorisedException()) }
            When { postSavedSearchesScreen() }
        }
    }

    @Test
    fun `should complete POST new saved search request with success and status code Created`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubSavedSearchResponse() }
            When {
                createSavedSearch(
                    hashMapOf(
                        "zipcode" to DataFactory.SOME_ZIP_CODE,
                        "distance" to DataFactory.SOME_DISTANCE,
                        "categoryId" to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Created) }
        }
    }

    @Test
    fun `should complete POST new saved search request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateResponseError(UnauthorisedException()) }
            When {
                createSavedSearch(
                    hashMapOf(
                        "zipcode" to DataFactory.SOME_ZIP_CODE,
                        "distance" to DataFactory.SOME_DISTANCE,
                        "categoryId" to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
        }
    }

    @Test
    fun `should complete DELETE saved search request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            When { deleteSavedSearch(DataFactory.SOME_SAVED_SEARCH_ID) }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete DELETE saved search request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryDeleteSavedSearchResponseError(UnauthorisedException()) }
            When { deleteSavedSearch(DataFactory.SOME_SAVED_SEARCH_ID) }
        }
    }

    @Test
    fun `should complete DELETE saved search request with error when saved search id path value is NOT in expected format`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When { deleteSavedSearch(DataFactory.SOME_AD_ID) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: SavedSearchesRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse
        private lateinit var savedSearchesData: List<SavedSearchDto>
        private lateinit var screenPortraitData: List<RowLayout<UiItem>>
        private var screenLandscapeData: List<RowLayout<UiItem>>? = null

        fun stubRepositoryReadResponseError(error: Throwable) {
            coEvery { repository.read(any()) } throws error
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery {
                repository.readScreen(any(), any(), any(), any())
            } throws error
        }

        fun readScreenWithNewListingsIndicationError(error: Throwable) {
            coEvery {
                repository.readScreenWithNewListingsIndication(any(), any(), any(), any(), any())
            } throws error
        }

        fun stubSavedSearchResponse() {
            coEvery {
                repository.create(any(), any())
            } returns SavedSearchResponseDto(
                id = DataFactory.anyString()
            )
        }

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any(), any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadScreeWithNewListingsIndicationResponse() {
            coEvery {
                repository.readScreenWithNewListingsIndication(any(), any(), any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryCreateResponseError(error: Throwable) {
            coEvery {
                repository.create(any(), any())
            } throws error
        }

        fun stubRepositoryDeleteSavedSearchResponseError(error: Throwable) {
            coEvery {
                repository.delete(any(), any(), any())
            } throws error
        }

        fun stubSavedSearchesData() {
            savedSearchesData = emptyList()
        }

        fun stubScreenPortraitData() {
            screenPortraitData = emptyList()
        }

        fun stubScreenLandscapeData() {
            screenLandscapeData = emptyList()
        }

        suspend fun getSavedSearches() {
            actualResponse = client.get(SAVED_SEARCHES_PATH) { }
        }

        suspend fun getSavedSearchesScreen(
            page: Int? = null,
            size: Int? = null
        ) {
            actualResponse = client.get(SAVED_SEARCHES_SCREEN_PATH) {
                parameter(ApiQueryParams.PAGE, page)
                parameter(ApiQueryParams.SIZE, size)
            }
        }

        suspend fun postSavedSearchesScreen(
            clientSavedSearchesCache: HashMap<String, String>? = null,
            page: Int? = null,
            size: Int? = null,
        ) {
            actualResponse = client.post(SAVED_SEARCHES_SCREEN_PATH) {
                parameter(ApiQueryParams.PAGE, page)
                parameter(ApiQueryParams.SIZE, size)
                clientSavedSearchesCache?.let {
                    setBody(it)
                    contentType(ContentType.Application.Json)
                }
            }
        }

        suspend fun createSavedSearch(savedSearchParams: HashMap<String, String>) {
            actualResponse = client.post(SAVED_SEARCHES_PATH) {
                setBody(SavedSearchRequest(params = savedSearchParams))
                contentType(ContentType.Application.Json)
            }
        }

        suspend fun deleteSavedSearch(savedSearchId: String) {
            actualResponse = client.delete(SAVED_SEARCHES_DELETE_PATH.replace("{$ID_PATH}", savedSearchId))
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }
}
