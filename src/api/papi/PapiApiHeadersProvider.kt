package com.gumtree.mobile.api.papi

import com.google.appengine.repackaged.com.google.common.net.HttpHeaders.KEEP_ALIVE
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.common.putOptionalAuthorisationHeadersIfProvided
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.extensions.getOrEmpty
import com.gumtree.mobile.utils.extensions.getOrThrowUnauthorised
import io.ktor.client.utils.CacheControl.NO_CACHE
import io.ktor.http.Headers

const val ACCEPT_LANGUAGE = "Accept-Language"
const val ECG_APP_VER = "X-ECG-App-Version"
const val ECG_PLATFORM = "X-ECG-Platform"
const val ECG_UDID = "X-ECG-UDID"
const val ECG_USER_AUTH = "X-ECG-Authorization-User"
const val CONNECTION = "Connection"
const val PROXY_CONNECTION = "Proxy-Connection"
const val PRAGMA = "Pragma"
const val AUTHORIZATION = "Authorization"
const val X_ECG_EXPERIMENTS = "X-ECG-Experiments"
const val X_THREATMETRIX_SESSION_ID = "X-THREATMETRIX-SESSION-ID"
const val USER_AGENT = "User-Agent"

const val PAPI_DEFAULT_LANGUAGE = "en-GB"
const val PAPI_DEFAULT_AUTHORIZATION = "Basic bW9iaWxlOjBudGgzbTB2MyE="
const val PAPI_USER_AGENT = "mobile-apps-bff"

class PapiHeadersProvider : ApiHeadersProvider {

    override fun createUnAuthorisedHeaders(callHeaders: Headers, addOptionalAuth: Boolean): ApiHeaders =
        ApiHeaders().apply {
            putAll(createPapiDefaultHeaders(callHeaders.getOrEmpty(ApiHeaderParams.PLATFORM)))

            put(ECG_APP_VER, callHeaders.getOrEmpty(ApiHeaderParams.APP_VERSION))
            put(ECG_PLATFORM, callHeaders.getOrEmpty(ApiHeaderParams.PLATFORM))
            put(ECG_UDID, callHeaders.getOrEmpty(ApiHeaderParams.UDID))
            put(X_ECG_EXPERIMENTS, callHeaders.getOrEmpty(ApiHeaderParams.EXPERIMENTS))
            put(X_THREATMETRIX_SESSION_ID, callHeaders.getOrEmpty(ApiHeaderParams.THREATMETRIX_SESSION))
            putOptionalAuthorisationHeadersIfProvided(
                callHeaders.getOrEmpty(ApiHeaderParams.AUTHORISATION_USER_EMAIL),
                callHeaders.getOrEmpty(ApiHeaderParams.AUTHORISATION_USER_TOKEN),
                addOptionalAuth,
            )
        }

    override fun createAuthorisedHeaders(callHeaders: Headers): ApiHeaders =
        ApiHeaders().apply {
            putAll(createPapiDefaultHeaders(callHeaders.getOrEmpty(ApiHeaderParams.PLATFORM)))

            put(ECG_APP_VER, callHeaders.getOrEmpty(ApiHeaderParams.APP_VERSION))
            put(ECG_PLATFORM, callHeaders.getOrEmpty(ApiHeaderParams.PLATFORM))
            put(ECG_UDID, callHeaders.getOrEmpty(ApiHeaderParams.UDID))
            put(X_ECG_EXPERIMENTS, callHeaders.getOrEmpty(ApiHeaderParams.EXPERIMENTS))
            put(X_THREATMETRIX_SESSION_ID, callHeaders.getOrEmpty(ApiHeaderParams.THREATMETRIX_SESSION))

            put(AUTHORIZATION, PAPI_DEFAULT_AUTHORIZATION)
            put(
                ECG_USER_AUTH,
                "email=\"${callHeaders.getOrThrowUnauthorised(ApiHeaderParams.AUTHORISATION_USER_EMAIL)}\", " +
                    "token=\"${callHeaders.getOrThrowUnauthorised(ApiHeaderParams.AUTHORISATION_USER_TOKEN)}\"",
            )
        }

    private fun createPapiDefaultHeaders(platform: String) = mapOf(
        ACCEPT_LANGUAGE to PAPI_DEFAULT_LANGUAGE,
        CONNECTION to KEEP_ALIVE,
        PROXY_CONNECTION to KEEP_ALIVE,
        PRAGMA to NO_CACHE,
        USER_AGENT to "$PAPI_USER_AGENT-$platform",
    )
}
