package features.categories

import com.gumtree.mobile.api.categories.models.RawCategory
import com.gumtree.mobile.features.categories.CategoriesTreeMapper
import com.gumtree.mobile.features.categories.CategoryDto
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCategoryFactory
import tools.runUnitTest

class CategoriesTreeMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should map raw category to root category only`() {
        runUnitTest(robot) {
            Given {
                stubRawCategoryTree(
                    rootCategoryId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    rootCategoryText = "All categories",
                    rootCategoryChildren = null
                )
            }
            When { map() }
            Then {
                checkTreeTopCategory(
                    expectedId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    expectedText = "All categories",
                    expectedChildrenSize = null
                )
            }
        }
    }

    @Test
    fun `should map raw category to root category and couple of children categories`() {
        runUnitTest(robot) {
            Given {
                stubRawCategoryTree(
                    rootCategoryId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    rootCategoryText = "All categories",
                    rootCategoryChildren = listOf(
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_CARS_CATEGORY_ID,
                            categoryText = "Cars",
                            categoryChildren = null
                        ),
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_PROPERTY_CATEGORY_ID,
                            categoryText = "Properties",
                            categoryChildren = null
                        )
                    )
                )
            }
            When { map() }
            Then {
                checkTreeTopCategory(
                    expectedId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    expectedText = "All categories",
                    expectedChildrenSize = 2
                )
            }
            Then {
                checkCategory(
                    childPosition = 0,
                    expectedId = DataFactory.SOME_CARS_CATEGORY_ID,
                    expectedText = "Cars",
                    expectedChildrenSize = null
                )
            }
            Then {
                checkCategory(
                    childPosition = 1,
                    expectedId = DataFactory.SOME_PROPERTY_CATEGORY_ID,
                    expectedText = "Properties",
                    expectedChildrenSize = null
                )
            }
        }
    }

    @Test
    fun `should map raw category to root category and few more children categories`() {
        runUnitTest(robot) {
            Given {
                stubRawCategoryTree(
                    rootCategoryId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    rootCategoryText = "All categories",
                    rootCategoryChildren = listOf(
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_FOR_SALE_CATEGORY_ID,
                            categoryText = "For Sale",
                            categoryChildren = null
                        ),
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_CARS_CATEGORY_ID,
                            categoryText = "Cars",
                            categoryChildren = null
                        ),
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_JOBS_CATEGORY_ID,
                            categoryText = "Jobs",
                            categoryChildren = null
                        ),
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_PROPERTY_CATEGORY_ID,
                            categoryText = "Properties",
                            categoryChildren = null
                        )
                    )
                )
            }
            When { map() }
            Then {
                checkTreeTopCategory(
                    expectedId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    expectedText = "All categories",
                    expectedChildrenSize = 4
                )
            }
            Then {
                checkCategory(
                    childPosition = 0,
                    expectedId = DataFactory.SOME_FOR_SALE_CATEGORY_ID,
                    expectedText = "For Sale",
                    expectedChildrenSize = null
                )
            }
            Then {
                checkCategory(
                    childPosition = 1,
                    expectedId = DataFactory.SOME_CARS_CATEGORY_ID,
                    expectedText = "Cars",
                    expectedChildrenSize = null
                )
            }
            Then {
                checkCategory(
                    childPosition = 2,
                    expectedId = DataFactory.SOME_JOBS_CATEGORY_ID,
                    expectedText = "Jobs",
                    expectedChildrenSize = null
                )
            }
            Then {
                checkCategory(
                    childPosition = 3,
                    expectedId = DataFactory.SOME_PROPERTY_CATEGORY_ID,
                    expectedText = "Properties",
                    expectedChildrenSize = null
                )
            }
        }
    }

    @Test
    fun `should map raw category to root category and deeper children categories nesting`() {
        runUnitTest(robot) {
            Given {
                stubRawCategoryTree(
                    rootCategoryId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    rootCategoryText = "All categories",
                    rootCategoryChildren = listOf(
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_CARS_CATEGORY_ID,
                            categoryText = "Cars",
                            categoryChildren = listOf(
                                RawCategoryFactory.createRawCategory(
                                    categoryId = DataFactory.SOME_CAR_PARTS_CATEGORY_ID,
                                    categoryText = "Car Parts",
                                    categoryChildren = null
                                )
                            )
                        ),
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_JOBS_CATEGORY_ID,
                            categoryText = "Jobs",
                            categoryChildren = listOf(
                                RawCategoryFactory.createRawCategory(
                                    categoryId = DataFactory.SOME_IT_JOBS_CATEGORY_ID,
                                    categoryText = "IT Jobs",
                                    categoryChildren = null
                                )
                            )
                        ),
                        RawCategoryFactory.createRawCategory(
                            categoryId = DataFactory.SOME_FOR_SALE_CATEGORY_ID,
                            categoryText = "For Sale",
                            categoryChildren = null
                        )
                    )
                )
            }
            When { map() }
            Then {
                checkTreeTopCategory(
                    expectedId = DataFactory.SOME_ROOT_CATEGORY_ID,
                    expectedText = "All categories",
                    expectedChildrenSize = 3
                )
            }
            Then {
                checkCategory(
                    childPosition = 0,
                    expectedId = DataFactory.SOME_CARS_CATEGORY_ID,
                    expectedText = "Cars",
                    expectedChildrenSize = 1
                )
            }
            Then {
                checkCategory(
                    childPosition = 1,
                    expectedId = DataFactory.SOME_JOBS_CATEGORY_ID,
                    expectedText = "Jobs",
                    expectedChildrenSize = 1
                )
            }
            Then {
                checkCategory(
                    childPosition = 2,
                    expectedId = DataFactory.SOME_FOR_SALE_CATEGORY_ID,
                    expectedText = "For Sale",
                    expectedChildrenSize = null
                )
            }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualCategoryTreeResult: CategoryDto
        private lateinit var rawData: RawCategory

        private lateinit var tested: CategoriesTreeMapper

        override fun setup() {
            tested = CategoriesTreeMapper()
        }

        fun stubRawCategoryTree(
            rootCategoryId: String,
            rootCategoryText: String,
            rootCategoryChildren: List<RawCategory>?,
        ) {
            val rawRootCategory = RawCategoryFactory.createRawCategory(
                categoryId = rootCategoryId,
                categoryText = rootCategoryText,
                categoryChildren = rootCategoryChildren
            )
            rawData = rawRootCategory
        }

        fun map() {
            actualCategoryTreeResult = tested.map(rawData)
        }

        fun checkTreeTopCategory(
            expectedId: String,
            expectedText: String,
            expectedChildrenSize: Int?
        ) {
            assertEquals(expectedId, actualCategoryTreeResult.id)
            assertEquals(expectedText, actualCategoryTreeResult.text)
            assertEquals(expectedChildrenSize, actualCategoryTreeResult.children?.size)
        }

        fun checkCategory(
            childPosition: Int,
            expectedId: String,
            expectedText: String,
            expectedChildrenSize: Int?
        ) {
            assertEquals(expectedId, actualCategoryTreeResult.children?.get(childPosition)?.id)
            assertEquals(expectedText, actualCategoryTreeResult.children?.get(childPosition)?.text)
            assertEquals(expectedChildrenSize, actualCategoryTreeResult.children?.get(childPosition)?.children?.size)
        }
    }
}