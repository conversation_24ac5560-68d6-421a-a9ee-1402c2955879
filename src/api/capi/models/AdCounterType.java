package api.capi.models;

import java.util.HashMap;
import java.util.Map;

public enum AdCounterType {
    map,
    phone,
    vip,
    reply,
    vip_click,
    VIP_CLICK_OUT,
    FEATURED_URL_CLICK;

    private final static Map<String, AdCounterType> CONSTANTS = new HashMap<>();

    static {
        for (AdCounterType c: values()) {
            CONSTANTS.put(c.name(), c);
        }
    }

    public static AdCounterType fromValue(String value) {
        AdCounterType constant = CONSTANTS.get(value);
        if (constant == null) {
            throw new IllegalArgumentException(value);
        } else {
            return constant;
        }
    }
}
