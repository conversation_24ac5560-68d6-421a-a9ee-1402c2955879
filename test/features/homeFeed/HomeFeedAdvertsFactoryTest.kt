package features.homeFeed

import com.gumtree.mobile.adverts.gam.GAMAdvertAttribute
import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.adverts.gam.GAMAdvertSize
import com.gumtree.mobile.adverts.gam.GAM_HOME_FEED_PAGE_TYPE
import com.gumtree.mobile.features.homeFeed.GAM_ANDROID_HOME_FEED_UNIT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_ATTAPPTR_HPF_PLACEMENT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_ATTAPPTR_HP_TOP_PLACEMENT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_ATTAPPTR_MIDDLE_1_PLACEMENT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_ATTAPPTR_MIDDLE_2_PLACEMENT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_ATTAPPTR_MIDDLE_3_PLACEMENT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_HPTO_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_MIDDLE_1_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_MIDDLE_2_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_MIDDLE_3_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_TOP_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_IOS_HOME_FEED_UNIT_ID
import com.gumtree.mobile.features.homeFeed.HomeFeedAdvertsFactory
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class HomeFeedAdvertsFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return home feed hpto advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_HPTO_SLOT,
                    )
                )
            }
            When { buildHomeFeedHptoAdvert() }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_HOME_FEED_UNIT_ID + GAM_HOME_FEED_HPTO_SLOT) }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_HOME_FEED_UNIT_ID + GAM_HOME_FEED_HPTO_SLOT) }
            Then { checkGAMAdvertSlotName(GAM_HOME_FEED_HPTO_SLOT) }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad300x250) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_HPTO_SLOT,
                    )
                )
            }
        }
    }

    @Test
    fun `should return home feed top advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_TOP_SLOT,
                    )
                )
            }
            When { buildHomeFeedTopAdvert() }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_HOME_FEED_UNIT_ID + GAM_HOME_FEED_TOP_SLOT) }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_HOME_FEED_UNIT_ID + GAM_HOME_FEED_TOP_SLOT) }
            Then { checkGAMAdvertSlotName(GAM_HOME_FEED_TOP_SLOT) }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad300x250) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_HOME_FEED_ATTAPPTR_HP_TOP_PLACEMENT_ID) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_TOP_SLOT,
                    )
                )
            }
        }
    }

    @Test
    fun `should return home feed middle1 advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "0",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_MIDDLE_1_SLOT,
                    )
                )
            }
            When { buildHomeFeedMiddle1Advert() }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_HOME_FEED_UNIT_ID + GAM_HOME_FEED_MIDDLE_1_SLOT) }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_HOME_FEED_UNIT_ID + GAM_HOME_FEED_MIDDLE_1_SLOT) }
            Then { checkGAMAdvertSlotName(GAM_HOME_FEED_MIDDLE_1_SLOT) }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad300x250) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_HOME_FEED_ATTAPPTR_MIDDLE_1_PLACEMENT_ID) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "0",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_MIDDLE_1_SLOT,
                    )
                )
            }
        }
    }

    @Test
    fun `should return home feed middle2 advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "0",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_MIDDLE_2_SLOT,
                    )
                )
            }
            When { buildHomeFeedMiddle2Advert() }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_HOME_FEED_UNIT_ID + GAM_HOME_FEED_MIDDLE_2_SLOT) }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_HOME_FEED_UNIT_ID + GAM_HOME_FEED_MIDDLE_2_SLOT) }
            Then { checkGAMAdvertSlotName(GAM_HOME_FEED_MIDDLE_2_SLOT) }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad300x250) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_HOME_FEED_ATTAPPTR_MIDDLE_2_PLACEMENT_ID) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "0",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_MIDDLE_2_SLOT,
                    )
                )
            }
        }
    }

    @Test
    fun `should return home feed middle3 advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "0",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_MIDDLE_3_SLOT,
                    )
                )
            }
            When { buildHomeFeedMiddle3Advert(DataFactory.SOME_GAM_ADVERT_KEY) }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_HOME_FEED_UNIT_ID + GAM_HOME_FEED_MIDDLE_3_SLOT) }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_HOME_FEED_UNIT_ID + GAM_HOME_FEED_MIDDLE_3_SLOT) }
            Then { checkGAMAdvertSlotName(GAM_HOME_FEED_MIDDLE_3_SLOT) }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad300x250) }
            Then { checkGAMAdvertKey(DataFactory.SOME_GAM_ADVERT_KEY) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_HOME_FEED_ATTAPPTR_MIDDLE_3_PLACEMENT_ID) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_HOME_FEED_PAGE_TYPE,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "0",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to GAM_HOME_FEED_MIDDLE_3_SLOT,
                    )
                )
            }
        }
    }

    class Robot: BaseRobot {
        private lateinit var actualGAMAdvertDtoResult: GAMAdvertDto
        private lateinit var gamAdvertAttributes: GAMAdvertAttributes

        private var testSubject = HomeFeedAdvertsFactory()

        fun stubGAMAdvertAttributes(attributes: GAMAdvertAttributes) {
            gamAdvertAttributes = attributes
        }

        fun buildHomeFeedHptoAdvert() {
            actualGAMAdvertDtoResult = testSubject.buildHomeFeedHptoAdvert(gamAdvertAttributes) as GAMAdvertDto
        }

        fun buildHomeFeedTopAdvert() {
            actualGAMAdvertDtoResult = testSubject.buildHomeFeedTopAdvert(gamAdvertAttributes) as GAMAdvertDto
        }

        fun buildHomeFeedMiddle1Advert() {
            actualGAMAdvertDtoResult = testSubject.buildHomeFeedMiddle1Advert(gamAdvertAttributes) as GAMAdvertDto
        }

        fun buildHomeFeedMiddle2Advert() {
            actualGAMAdvertDtoResult = testSubject.buildHomeFeedMiddle2Advert(gamAdvertAttributes) as GAMAdvertDto
        }

        fun buildHomeFeedMiddle3Advert(key: String) {
            actualGAMAdvertDtoResult = testSubject.buildHomeFeedMiddle3Advert(gamAdvertAttributes, key) as GAMAdvertDto
        }

        fun checkGAMAdvertAndroidUnitId(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.androidUnitId)
        }

        fun checkGAMAdvertIOSUnitId(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.iosUnitId)
        }

        fun checkGAMAdvertSlotName(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.slotName)
        }

        fun checkGAMAdvertDisplaySizeAtPosition(
            position: Int,
            expected: GAMAdvertSize
        ) {
            assertEquals(expected, actualGAMAdvertDtoResult.displaySize[position])
        }

        fun checkGAMAdvertAttributes(expected: GAMAdvertAttributes) {
            assertEquals(expected, actualGAMAdvertDtoResult.attributes)
        }

        fun checkGAMAdvertKey(expected: String?) {
            assertEquals(expected, actualGAMAdvertDtoResult.key)
        }

        fun checkGAMAdvertAddApptrPlacementId(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.addApptrPlacementId)
        }
    }
}