package features.sellerProfile.v2

import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.sellerProfile.v2.SELLER_PROFILE_LISTINGS_SCREEN_PATH
import com.gumtree.mobile.features.sellerProfile.v2.SELLER_PROFILE_REVIEWS_SCREEN_PATH
import com.gumtree.mobile.features.sellerProfile.v2.SELLER_PROFILE_SCREEN_PATH
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileRepository
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiQueryParams
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class SellerProfileRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET seller profile screen request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getSellerProfileScreen(DataFactory.SOME_USER_ID, DataFactory.SOME_USER_PUBLIC_ID) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET seller profile screen request with IllegalArgumentException error if userId is NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getSellerProfileScreen(null, DataFactory.SOME_USER_PUBLIC_ID) }
        }
    }

    @Test
    fun `should complete GET seller profile screen request with IllegalArgumentException error if public userId is NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getSellerProfileScreen(DataFactory.SOME_USER_ID, null) }
        }
    }

    @Test
    fun `should complete GET seller profile screen request with error`() = runRouteTestForException(module, RuntimeException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(RuntimeException()) }
            When { getSellerProfileScreen(DataFactory.SOME_USER_ID, DataFactory.SOME_USER_PUBLIC_ID) }
        }
    }

    @ParameterizedTest
    @CsvSource("1", "2", "3", "4", "5")
    fun `should complete GET seller profile listings screen request with success`(page: Int) = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadListingsScreenResponse() }
            When { getSellerProfileListingsScreen(DataFactory.SOME_USER_ID, page) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET seller profile listings screen request with success when no page is provided`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadListingsScreenResponse() }
            When { getSellerProfileListingsScreen(DataFactory.SOME_USER_ID, null) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET seller profile listings request with IllegalArgumentException error if userId is NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadListingsScreenResponse() }
            When { getSellerProfileListingsScreen(null, 1) }
        }
    }

    @Test
    fun `should complete GET seller profile listings screen request with error`() = runRouteTestForException(module, RuntimeException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadListingsScreenResponseError(RuntimeException()) }
            When { getSellerProfileListingsScreen(DataFactory.SOME_USER_ID, 1) }
        }
    }

    @ParameterizedTest
    @CsvSource("1", "2", "3", "4", "5")
    fun `should complete GET seller profile reviews screen request with success`(page: Int) = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadReviewsScreenResponse() }
            When { getSellerProfileReviewsScreen(DataFactory.SOME_USER_ID, DataFactory.SOME_USER_PUBLIC_ID, page) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET seller profile reviews screen request with success when no page is provided`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadReviewsScreenResponse() }
            When { getSellerProfileReviewsScreen(DataFactory.SOME_USER_ID, DataFactory.SOME_USER_PUBLIC_ID, null) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET seller profile reviews request with IllegalArgumentException error if userId is NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadReviewsScreenResponse() }
            When { getSellerProfileReviewsScreen(null, DataFactory.SOME_USER_PUBLIC_ID, 1) }
        }
    }

    @Test
    fun `should complete GET seller profile reviews request with IllegalArgumentException error if public userId is NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadReviewsScreenResponse() }
            When { getSellerProfileReviewsScreen(DataFactory.SOME_USER_ID, null, 1) }
        }
    }

    @Test
    fun `should complete GET seller profile reviews screen request with error`() = runRouteTestForException(module, RuntimeException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadReviewsScreenResponseError(RuntimeException()) }
            When { getSellerProfileReviewsScreen(DataFactory.SOME_USER_ID, DataFactory.SOME_USER_PUBLIC_ID, 1) }
        }
    }

    private class Robot: BaseRouteRobot() {
        private lateinit var actualResponse: HttpResponse

        val repository: SellerProfileRepository = mockk(relaxed = true)
        private lateinit var screenPortraitData: List<RowLayout<UiItem>>
        private var screenLandscapeData: List<RowLayout<UiItem>>? = null

        fun stubScreenPortraitData() {
            screenPortraitData = emptyList()
        }

        fun stubScreenLandscapeData() {
            screenLandscapeData = null
        }

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery {
                repository.readScreen(any(), any(), any())
            } throws error
        }

        fun stubRepositoryReadListingsScreenResponse() {
            coEvery {
                repository.readListingsScreen(any(), any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadListingsScreenResponseError(error: Throwable) {
            coEvery {
                repository.readListingsScreen(any(), any(), any(), any())
            } throws error
        }

        fun stubRepositoryReadReviewsScreenResponse() {
            coEvery {
                repository.readReviewsScreen(any(), any(), any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData
            )
        }

        fun stubRepositoryReadReviewsScreenResponseError(error: Throwable) {
            coEvery {
                repository.readReviewsScreen(any(), any(), any(), any(), any())
            } throws error
        }

        suspend fun getSellerProfileScreen(
            userId: String?,
            publicUserId: String?,
        ) {
            actualResponse = client.get(SELLER_PROFILE_SCREEN_PATH) {
                parameter(ApiQueryParams.USER_ID, userId)
                parameter(ApiQueryParams.PUBLIC_USER_ID, publicUserId)
            }
        }

        suspend fun getSellerProfileListingsScreen(
            userId: String?,
            page: Int? = null,
        ) {
            actualResponse = client.get(SELLER_PROFILE_LISTINGS_SCREEN_PATH) {
                parameter(ApiQueryParams.USER_ID, userId)
                parameter(ApiQueryParams.PAGE, page)
            }
        }

        suspend fun getSellerProfileReviewsScreen(
            userId: String?,
            publicUserId: String?,
            page: Int? = null,
        ) {
            actualResponse = client.get(SELLER_PROFILE_REVIEWS_SCREEN_PATH) {
                parameter(ApiQueryParams.USER_ID, userId)
                parameter(ApiQueryParams.PUBLIC_USER_ID, publicUserId)
                parameter(ApiQueryParams.PAGE, page)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }
    }

}