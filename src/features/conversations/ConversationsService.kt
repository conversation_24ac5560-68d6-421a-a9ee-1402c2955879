package com.gumtree.mobile.features.conversations

import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi

class ConversationsService(
    private val capiConversationApi: CapiConversationApi,
    private val conversationsApi: ConversationsApi,
    private val coreChatAuthApi: CoreChatAuthApi,
) : CapiConversationApi by capiConversationApi,
    ConversationsApi by conversationsApi,
    CoreChatAuthApi by coreChatAuthApi
