package com.gumtree.mobile.api.crm.api

import com.google.gson.annotations.SerializedName
import com.gumtree.mobile.api.common.ApiHeaders
import retrofit2.http.HeaderMap
import retrofit2.http.POST

fun interface CrmApi {

    @POST("/api/contact/id")
    suspend fun getSalesForceCrmKey(
        @HeaderMap authorisationHeaders: ApiHeaders,
    ): ContactIdResponse
}

data class ContactIdResponse(@SerializedName("contactId") val contactId: String)
