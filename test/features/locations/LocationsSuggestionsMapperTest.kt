package features.locations

import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.locations.models.RawLocationSuggestions
import com.gumtree.mobile.features.locations.LocationsSuggestionsMapper
import com.gumtree.mobile.features.locations.SimpleLocationDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawLocationFactory
import tools.runUnitTest

class LocationsSuggestionsMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should map RawLocationSuggestions of LOCATION type into list with Locations`() {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    47010,
                    "University Area, Belfast",
                    RawLocation.Type.location,
                    null,
                    51.464198,
                    -0.164329,
                    "SW119NA"
                )
            }
            Given {
                stubRawLocationSuggestion(
                    11002668,
                    "Undy, Monmouthshire",
                    RawLocation.Type.location,
                    null,
                    50.834233,
                    -0.136921,
                    "SA21NA"
                )
            }
            When { map() }
            Then { checkLocationSuggestionsSize(2) }

            Then { checkLocationId(0, "47010") }
            Then { checkLocationName(0, "University Area, Belfast") }
            Then { checkLocationLatitude(0, "51.464198") }
            Then { checkLocationLongitude(0, "-0.164329") }

            Then { checkLocationId(1, "11002668") }
            Then { checkLocationName(1, "Undy, Monmouthshire") }
            Then { checkLocationLatitude(1, "50.834233") }
            Then { checkLocationLongitude(1, "-0.136921") }
        }
    }

    @Test
    fun `should map RawLocationSuggestions of POSTCODE type into list with Locations`() {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    159,
                    "SW151AA",
                    RawLocation.Type.postcode,
                    159,
                    50.834233,
                    -0.136921,
                    postcode = "SW151AA"
                )
            }
            Given {
                stubRawLocationSuggestion(
                    159,
                    "SW151AB",
                    RawLocation.Type.postcode,
                    159,
                    51.535259,
                    -3.608263,
                    "SW151AB",
                )
            }
            Given {
                stubRawLocationSuggestion(
                    159,
                    "SW151AD",
                    RawLocation.Type.postcode,
                    159,
                    51.130725,
                    -2.997806,
                    "SW151AD",
                )
            }
            When { map() }
            Then { checkLocationSuggestionsSize(3) }

            Then { checkLocationId(0, "159") }
            Then { checkLocationName(0, "SW151AA") }
            Then { checkLocationLatitude(0, "50.834233") }
            Then { checkLocationLongitude(0, "-0.136921") }

            Then { checkLocationId(1, "159") }
            Then { checkLocationName(1, "SW151AB") }
            Then { checkLocationLatitude(1, "51.535259") }
            Then { checkLocationLongitude(1, "-3.608263") }

            Then { checkLocationId(2, "159") }
            Then { checkLocationName(2, "SW151AD") }
            Then { checkLocationLatitude(2, "51.130725") }
            Then { checkLocationLongitude(2, "-2.997806") }
        }
    }

    @Test
    fun `should map RawLocationSuggestions of OUTCODE type into list with Locations`() {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    12345,
                    "SW12",
                    RawLocation.Type.outcode,
                    12345,
                    51.130725,
                    -2.997806,
                    "SW12",
                )
            }
            When { map() }
            Then { checkLocationSuggestionsSize(1) }

            Then { checkLocationId(0, "12345") }
            Then { checkLocationName(0, "SW12") }
            Then { checkLocationLatitude(0, "51.130725") }
            Then { checkLocationLongitude(0, "-2.997806") }
        }
    }

    @Test
    fun `should map RawLocationSuggestions of LOCATIONS and POSTCODE types into list with Locations`() {
        runUnitTest(robot) {
            Given {
                stubRawLocationSuggestion(
                    10000387,
                    "Swindon",
                    RawLocation.Type.location,
                    null,
                    51.130725,
                    -2.997806,
                    "SW9BW",
                )
            }
            Given {
                stubRawLocationSuggestion(
                    159,
                    "SW151AB",
                    RawLocation.Type.postcode,
                    159,
                    51.457895,
                    -0.11591,
                    "SW151AB"
                )
            }
            When { map() }
            Then { checkLocationSuggestionsSize(2) }

            Then { checkLocationId(0, "10000387") }
            Then { checkLocationName(0, "Swindon") }
            Then { checkLocationLatitude(0, "51.130725") }
            Then { checkLocationLongitude(0, "-2.997806") }

            Then { checkLocationId(1, "159") }
            Then { checkLocationName(1, "SW151AB") }
            Then { checkLocationLatitude(1, "51.457895") }
            Then { checkLocationLongitude(1, "-0.11591") }
        }
    }

    @Test
    fun `should map empty RawLocationSuggestions into empty list with Locations`() {
        runUnitTest(robot) {
            Given { stubEmptyRawLocationSuggestions() }
            When { map() }
            Then { checkLocationSuggestionsSize(0) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualLocationSuggestionsResult: List<SimpleLocationDto>
        private lateinit var rawData: RawLocationSuggestions
        private val locationSuggestionOptions = mutableListOf<RawLocation>()

        private lateinit var testSubject: LocationsSuggestionsMapper

        override fun setup() {
            testSubject = LocationsSuggestionsMapper()
            locationSuggestionOptions.clear()
        }

        fun stubRawLocationSuggestion(
            id: Int,
            name: String,
            type: RawLocation.Type,
            locationId: Int?,
            latitude: Double?,
            longitude: Double?,
            postcode: String?,
        ) {
            val rawLocationSuggestion = RawLocationFactory.createRawLocation(id, name, type, locationId, latitude, longitude, postcode)
            locationSuggestionOptions.add(rawLocationSuggestion)
        }

        fun stubEmptyRawLocationSuggestions() {
            rawData = RawLocationSuggestions(emptyList())
        }

        fun map() {
            rawData = RawLocationSuggestions(options = locationSuggestionOptions)
            actualLocationSuggestionsResult = testSubject.map(rawData)
        }

        fun checkLocationSuggestionsSize(expected: Int) {
            assertEquals(expected, actualLocationSuggestionsResult.size)
        }

        fun checkLocationId(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, actualLocationSuggestionsResult[position].id)
        }

        fun checkLocationName(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, actualLocationSuggestionsResult[position].name)
        }

        fun checkLocationLatitude(
            position: Int,
            expected: String?
        ) {
            assertEquals(expected?.toDouble(), actualLocationSuggestionsResult[position].latitude)
        }

        fun checkLocationLongitude(
            position: Int,
            expected: String?
        ) {
            assertEquals(expected?.toDouble(), actualLocationSuggestionsResult[position].longitude)
        }
    }
}