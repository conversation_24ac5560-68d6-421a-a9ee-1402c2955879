# @name GET Category Landing Screen for categoryId only

GET {{hostV1}}/category-landing/screen?categoryId={{category_id}}
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Category Landing Screen for categoryId and locationId

GET {{hostV1}}/category-landing/screen?categoryId={{category_id}}&locationId={{location_id}}&locationType={{location_type}}
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Cars Category Landing Screen with vehicle_make only

GET {{hostV1}}/category-landing/screen?categoryId=9311&vehicle_make={{vehicle_make}}
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Cars Category Landing Screen with locationId, vehicle_make and vehicle_model

GET {{hostV1}}/category-landing/screen?categoryId=9311&locationId={{location_id}}&locationType={{location_type}}&vehicle_make={{vehicle_make}}&vehicle_model={{vehicle_model}}
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}