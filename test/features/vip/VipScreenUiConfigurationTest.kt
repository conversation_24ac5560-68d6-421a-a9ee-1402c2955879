package features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.capi.models.RawCapiAdStatus
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration.VIP_POSTED_SINCE_NOW_TEXT
import com.gumtree.mobile.responses.ContentNotAvailableException
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import tools.runUnitTestForException

class VipScreenUiConfigurationTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return Vip screen description label text`() {
        runUnitTest(robot) {
            When { getDescriptionLabelText() }
            Then { checkDescriptionLabelText(VipScreenUiConfiguration.DESCRIPTION_LABEL_TEXT) }
        }
    }

    @Test
    fun `should return Vip screen week time ago text`() {
        runUnitTest(robot) {
            Given { stubTimeAgo(1) }
            When { getWeekText() }
            Then { checkTimeAgoText("Posted 1 week ago") }

            Given { stubTimeAgo(2) }
            When { getWeekText() }
            Then { checkTimeAgoText("Posted 2 weeks ago") }

            Given { stubTimeAgo(5) }
            When { getWeekText() }
            Then { checkTimeAgoText("Posted 5 weeks ago") }
        }
    }

    @Test
    fun `should return Vip screen day time ago text`() {
        runUnitTest(robot) {
            Given { stubTimeAgo(1) }
            When { getDayText() }
            Then { checkTimeAgoText("Posted 1 day ago") }

            Given { stubTimeAgo(2) }
            When { getDayText() }
            Then { checkTimeAgoText("Posted 2 days ago") }

            Given { stubTimeAgo(5) }
            When { getDayText() }
            Then { checkTimeAgoText("Posted 5 days ago") }
        }
    }

    @Test
    fun `should return Vip screen hour time ago text`() {
        runUnitTest(robot) {
            Given { stubTimeAgo(1) }
            When { getHourText() }
            Then { checkTimeAgoText("Posted 1 hour ago") }

            Given { stubTimeAgo(2) }
            When { getHourText() }
            Then { checkTimeAgoText("Posted 2 hours ago") }

            Given { stubTimeAgo(4) }
            When { getHourText() }
            Then { checkTimeAgoText("Posted 4 hours ago") }
        }
    }

    @Test
    fun `should return Vip screen minute time ago text`() {
        runUnitTest(robot) {
            Given { stubTimeAgo(1) }
            When { getMinuteText() }
            Then { checkTimeAgoText("Posted 1 minute ago") }

            Given { stubTimeAgo(5) }
            When { getMinuteText() }
            Then { checkTimeAgoText("Posted 5 minutes ago") }

            Given { stubTimeAgo(43) }
            When { getMinuteText() }
            Then { checkTimeAgoText("Posted 43 minutes ago") }
        }
    }

    @Test
    fun `should return Vip screen NOW time ago text`() {
        runUnitTest(robot) {
            When { getSecondText() }
            Then { checkTimeAgoText(VIP_POSTED_SINCE_NOW_TEXT) }
        }
    }

    @Test
    fun `should return empty string for private seller type seller type`() {
        runUnitTest(robot) {
            When { getSellerType(true) }
            Then { checkSellerType("") }
        }
    }

    @Test
    fun `should return empty string for trade seller type seller type`() {
        runUnitTest(robot) {
            When { getSellerType(false) }
            Then { checkSellerType("") }
        }
    }

    @Test
    fun `should return specification unit`() {
        runUnitTest(robot) {
            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_MILEAGE) }
            Then { checkSpecificationUnit("miles") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_LUGGAGE_CAPACITY) }
            Then { checkSpecificationUnit("litres") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_ENGINE_POWER) }
            Then { checkSpecificationUnit("bhp") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_ENGINE_SIZE) }
            Then { checkSpecificationUnit("cc") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_BROCHURE_ENGINE_SIZE) }
            Then { checkSpecificationUnit("L") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_TOP_SPEED) }
            Then { checkSpecificationUnit("mph") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_ACCELERATION_0_62) }
            Then { checkSpecificationUnit("seconds") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_AVERAGE_MPG) }
            Then { checkSpecificationUnit("mpg") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_FUEL_CAPACITY) }
            Then { checkSpecificationUnit("litres") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_URBAN_MPG) }
            Then { checkSpecificationUnit("mpg") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_EXTRA_URBAN_MPG) }
            Then { checkSpecificationUnit("mpg") }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_EMISSION) }
            Then { checkSpecificationUnit("g/km") }
        }
    }

    @Test
    fun `should NOT return specification unit`() {
        runUnitTest(robot) {
            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_SUNROOF) }
            Then { checkSpecificationUnit(null) }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_REGISTRATION) }
            Then { checkSpecificationUnit(null) }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_COLOUR) }
            Then { checkSpecificationUnit(null) }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_VEHICLE_DOORS) }
            Then { checkSpecificationUnit(null) }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_DAYTIME_RUNNING_LIGHTS) }
            Then { checkSpecificationUnit(null) }

            When { getSpecificationUnit(RawCapiAd.ATTRIBUTE_CRUISE_CONTROL) }
            Then { checkSpecificationUnit(null) }
        }
    }

    @Test
    fun `should proceed with ad presentation without error if ad has status different than deleted`() {
        runUnitTest(robot) {
            When { proceedWithAdPresentationOrThrowError("ACTIVE") }
            When { proceedWithAdPresentationOrThrowError("PENDING") }
            When { proceedWithAdPresentationOrThrowError("INACTIVE") }
            When { proceedWithAdPresentationOrThrowError("PAUSED") }
            When { proceedWithAdPresentationOrThrowError("PAYABLE") }
            When { proceedWithAdPresentationOrThrowError("UPLOADING") }
            When { proceedWithAdPresentationOrThrowError("EXPIRED") }
            When { proceedWithAdPresentationOrThrowError("ARCHIVED") }
            When { proceedWithAdPresentationOrThrowError("CREATED") }
        }
    }

    @Test
    fun `should throw ContentNotAvailableException if ad has status DELETED`() {
        runUnitTestForException(robot, ContentNotAvailableException::class) {
            When { proceedWithAdPresentationOrThrowError("DELETED") }
        }
    }

    @Test
    fun `should throw ContentNotAvailableException if ad has status DELETED_CS`() {
        runUnitTestForException(robot, ContentNotAvailableException::class) {
            When { proceedWithAdPresentationOrThrowError("DELETED_CS") }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualDescriptionLabelText: String
        private lateinit var actualTimeAgoText: String
        private lateinit var actualSellerTypeResult: String
        private var actualSpecificationUnitResult: String? = null
        private var timeAgo: Long = 0L

        private lateinit var testSubject: VipScreenUiConfiguration

        override fun setup() {
            testSubject = VipScreenUiConfiguration
        }

        fun stubTimeAgo(time: Long) {
            timeAgo = time
        }

        fun getSpecificationUnit(attribute: String) {
            actualSpecificationUnitResult = testSubject.SPECIFICATION_UNITS[attribute]
        }

        fun getDescriptionLabelText() {
            actualDescriptionLabelText = testSubject.DESCRIPTION_LABEL_TEXT
        }

        fun getWeekText() {
            actualTimeAgoText = testSubject.getWeekText(timeAgo)
        }

        fun getDayText() {
            actualTimeAgoText = testSubject.getDayText(timeAgo)
        }

        fun getHourText() {
            actualTimeAgoText = testSubject.getHourText(timeAgo)
        }

        fun getMinuteText() {
            actualTimeAgoText = testSubject.getMinuteText(timeAgo)
        }

        fun getSecondText() {
            actualTimeAgoText = testSubject.getSecondText()
        }

        fun getSellerType(isPrivate: Boolean) {
            actualSellerTypeResult = testSubject.getSellerTypeText(isPrivate)
        }

        fun proceedWithAdPresentationOrThrowError(statusValue: String) {
            testSubject.proceedWithAdPresentationOrThrowError(RawCapiAdStatus(statusValue))
        }

        fun checkDescriptionLabelText(expected: String) {
            assertEquals(expected, actualDescriptionLabelText)
        }

        fun checkTimeAgoText(expected: String) {
            assertEquals(expected, actualTimeAgoText)
        }

        fun checkSellerType(expected: String) {
            assertEquals(expected, actualSellerTypeResult)
        }

        fun checkSpecificationUnit(expected: String?) {
            assertEquals(expected, actualSpecificationUnitResult)
        }
    }
}