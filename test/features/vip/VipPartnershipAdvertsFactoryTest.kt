package features.vip

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdSlot
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.PartnershipAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.vip.VipPartnershipAdvertsFactory
import com.gumtree.mobile.features.vip.VipPartnershipAnalyticsProvider
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.utils.extensions.data.APP_VERSION_QUERY_PARAM
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class VipPartnershipAdvertsFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return 1 Ad partnership advert row with expected data if there is 1 partnership advert slot`() {
        runUnitTest(robot) {
            Given {
                stubRawPartnershipAdvertSlots(
                    listOf(
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        }
                    )
                )
            }
            When { buildPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.IOS)) }
            Then { checkPartnershipAdvertRowsSize(1) }
            Then { checkPartnershipAdvertRowTypeAtPosition(0, RowLayoutType.PARTNERSHIP_ADVERT_ROW) }
            Then { checkPartnershipAdvertCardIconUrlAtPosition(0, DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL) }
            Then { checkPartnershipAdvertCardTextAtPosition(0, DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT) }
            Then { checkPartnershipAdvertCardWebUrlAtPosition(0, DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL + "&$APP_VERSION_QUERY_PARAM=${DataFactory.SOME_APP_VERSION}") }
        }
    }

    @Test
    fun `should return 2 Ad partnership advert rows with expected data if there are 2 partnership advert slots`() {
        runUnitTest(robot) {
            Given {
                stubRawPartnershipAdvertSlots(
                    listOf(
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        },
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        }
                    )
                )
            }
            When { buildPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform =  ClientPlatform.IOS)) }
            Then { checkPartnershipAdvertRowsSize(2) }
            Then { checkPartnershipAdvertRowTypeAtPosition(0, RowLayoutType.PARTNERSHIP_ADVERT_ROW) }
            Then { checkPartnershipAdvertCardIconUrlAtPosition(0, DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL) }
            Then { checkPartnershipAdvertCardTextAtPosition(0, DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT) }
            Then { checkPartnershipAdvertCardWebUrlAtPosition(0, DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL + "&$APP_VERSION_QUERY_PARAM=${DataFactory.SOME_APP_VERSION}") }

            Then { checkPartnershipAdvertRowTypeAtPosition(1, RowLayoutType.PARTNERSHIP_ADVERT_ROW) }
            Then { checkPartnershipAdvertCardIconUrlAtPosition(1, DataFactory.ANOTHER_PARTNERSHIP_ADVERT_ICON_URL) }
            Then { checkPartnershipAdvertCardTextAtPosition(1, DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TEXT) }
            Then { checkPartnershipAdvertCardWebUrlAtPosition(1, DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TARGET_URL + "&$APP_VERSION_QUERY_PARAM=${DataFactory.SOME_APP_VERSION}") }
        }
    }

    @Test
    fun `should NOT return Ad partnership advert rows if slots are empty`() {
        runUnitTest(robot) {
            Given { stubRawPartnershipAdvertSlots(emptyList()) }
            When { buildPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.IOS)) }
            Then { checkPartnershipAdvertRowsSize(0) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualPartnershipAdvertRowsResult: Array<RowLayout<UiItem>?>

        private lateinit var rawAd: RawCapiAd

        private val testSubject = VipPartnershipAdvertsFactory(VipPartnershipAnalyticsProvider())

        fun stubRawPartnershipAdvertSlots(adSlots: List<RawCapiAdSlot>) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adSlots = adSlots)
        }

        fun buildPartnershipAdvertRows(clientSemantics: ClientSemantics) {
            actualPartnershipAdvertRowsResult = testSubject.buildPartnershipAdvertRows(rawAd, clientSemantics)
        }

        fun checkPartnershipAdvertRowsSize(expected: Int) {
            assertEquals(expected, actualPartnershipAdvertRowsResult.size)
        }

        fun checkPartnershipAdvertRowTypeAtPosition(
            position: Int,
            expected: RowLayoutType,
        ) {
            assertEquals(expected, actualPartnershipAdvertRowsResult[position]?.type)
        }

        fun checkPartnershipAdvertCardIconUrlAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualPartnershipAdvertRowsResult[position]?.data?.get(0) as PartnershipAdvertDto).iconUrl)
        }

        fun checkPartnershipAdvertCardTextAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualPartnershipAdvertRowsResult[position]?.data?.get(0) as PartnershipAdvertDto).text)
        }

        fun checkPartnershipAdvertCardWebUrlAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualPartnershipAdvertRowsResult[position]?.data?.get(0) as PartnershipAdvertDto).webUrl)
        }
    }
}