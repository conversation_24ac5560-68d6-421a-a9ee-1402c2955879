package tools.layoutsDataFactory

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.ID_KEY
import com.gumtree.mobile.common.analytics.PRICE_KEY
import tools.DataFactory

object AnalyticsEventFactory {

    /**
     * Generate an AnalyticsEventData for testing
     * @param eventName - the analytics event name
     * @param eventParams - the analytics event params
     * @return - The AnalyticsEventData instance
     */
    fun createAnalyticsEvent(
        eventName: String = DataFactory.SOME_ANALYTICS_EVENT_NAME,
        eventParams: Map<String, String>? = null,
    ): AnalyticsEventData {
        return AnalyticsEventData(
            eventName = eventName,
            parameters = eventParams,
        )
    }

    fun createAnalyticsEventParams(
        vararg eventParams: Pair<String, String> = arrayOf(
            Pair(ID_KEY, DataFactory.SOME_AD_ID),
            Pair(PRICE_KEY, DataFactory.SOME_AD_PRICE),
        ),
    ): Map<String, String> {
        return eventParams.toMap()
    }
}
