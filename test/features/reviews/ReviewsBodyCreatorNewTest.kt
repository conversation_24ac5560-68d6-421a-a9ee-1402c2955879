package features.reviews

import api.capi.models.RawConversation
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.api.reviews.bodies.RawFeedback
import com.gumtree.mobile.api.reviews.bodies.RawPositiveTag
import com.gumtree.mobile.api.reviews.bodies.RawNegativeTag
import com.gumtree.mobile.api.reviews.bodies.RawCreateReviewRequestBody
import com.gumtree.mobile.api.reviews.models.RawReview
import com.gumtree.mobile.features.reviews.CreateConversationReviewRequest
import com.gumtree.mobile.features.reviews.ReviewsBodyCreator
import com.gumtree.mobile.features.reviews.ReviewsTag
import com.gumtree.mobile.utils.CategoryDefaults
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiConversationsFactory
import tools.rawDataFactory.RawFlatAdsFactory
import tools.runUnitTest

class ReviewsBodyCreatorNewTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should create conversation review body payload with rating only`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.SOME_AD_ID,
                    adTitle = DataFactory.SOME_AD_TITLE,
                    categoryId = CategoryDefaults.BABY_AND_KIDS.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.ANOTHER_RATING,
                    review = CreateConversationReviewRequest.Review(positiveTags = null, negativeTags = null)
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.ANOTHER_RATING) }
            Then { checkCategoryId(CategoryDefaults.BABY_AND_KIDS.id) }
            Then { checkItemId(DataFactory.SOME_AD_ID) }
            Then { checkItemTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then { checkReviewFeedback(null) }
        }
    }

    @Test
    fun `should create conversation review body payload with rating only when NONE_OF_THESE tag sent`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.SOME_AD_ID,
                    adTitle = DataFactory.SOME_AD_TITLE,
                    categoryId = CategoryDefaults.BABY_AND_KIDS.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.ANOTHER_RATING,
                    review = CreateConversationReviewRequest.Review(
                        positiveTags = listOf(ReviewsTag.NONE_OF_THESE.name),
                        negativeTags = listOf(ReviewsTag.NONE_OF_THESE.name),
                    )
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.ANOTHER_RATING) }
            Then { checkCategoryId(CategoryDefaults.BABY_AND_KIDS.id) }
            Then { checkItemId(DataFactory.SOME_AD_ID) }
            Then { checkItemTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then {
                checkReviewFeedback(
                    RawFeedback(
                        tags = RawFeedback.RawTags(
                            positive = emptyList(),
                            negative = emptyList(),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should create conversation review body payload with rating and positive review tags`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.SOME_AD_ID,
                    adTitle = DataFactory.SOME_AD_TITLE,
                    categoryId = CategoryDefaults.FOR_SALE.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.SOME_RATING,
                    review = CreateConversationReviewRequest.Review(
                        positiveTags = listOfNotNull(ReviewsTag.FRIENDLY.name, ReviewsTag.POLITE.name,),
                        negativeTags = emptyList(),
                    )
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.SOME_RATING) }
            Then { checkCategoryId(CategoryDefaults.FOR_SALE.id) }
            Then { checkItemId(DataFactory.SOME_AD_ID) }
            Then { checkItemTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then {
                checkReviewFeedback(
                    RawFeedback(
                        tags = RawFeedback.RawTags(
                            positive = listOf(
                                RawPositiveTag.Friendly,
                                RawPositiveTag.Polite,
                            ),
                            negative = emptyList(),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should create conversation review body payload with rating and negative review tags`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.SOME_AD_ID,
                    adTitle = DataFactory.SOME_AD_TITLE,
                    categoryId = CategoryDefaults.FOR_SALE.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.ANOTHER_RATING,
                    review = CreateConversationReviewRequest.Review(
                        positiveTags = emptyList(),
                        negativeTags = listOfNotNull(ReviewsTag.RUDE.name, ReviewsTag.CANCELLED_OFFER.name),
                    )
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.ANOTHER_RATING) }
            Then { checkCategoryId(CategoryDefaults.FOR_SALE.id) }
            Then { checkItemId(DataFactory.SOME_AD_ID) }
            Then { checkItemTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then {
                checkReviewFeedback(
                    RawFeedback(
                        tags = RawFeedback.RawTags(
                            positive = emptyList(),
                            negative = listOf(
                                RawNegativeTag.Rude,
                                RawNegativeTag.CancelledOffer,
                            ),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should create conversation review body payload with rating, positive and negative review tags`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.ANOTHER_AD_ID,
                    adTitle = DataFactory.ANOTHER_AD_TITLE,
                    categoryId = CategoryDefaults.SPORTS_AND_LEISURE.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.SOME_RATING,
                    review = CreateConversationReviewRequest.Review(
                        positiveTags = listOfNotNull(ReviewsTag.HELPFUL.name, ReviewsTag.ITEM_AS_DESCRIBED.name,),
                        negativeTags = listOfNotNull(ReviewsTag.RUDE.name, ReviewsTag.CANCELLED_OFFER.name,),
                    )
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.SOME_RATING) }
            Then { checkCategoryId(CategoryDefaults.SPORTS_AND_LEISURE.id) }
            Then { checkItemId(DataFactory.ANOTHER_AD_ID) }
            Then { checkItemTitle(DataFactory.ANOTHER_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then {
                checkReviewFeedback(
                    RawFeedback(
                        tags = RawFeedback.RawTags(
                            positive = listOf(
                                RawPositiveTag.Helpful,
                                RawPositiveTag.ItemAsDescribed,
                            ),
                            negative = listOf(
                                RawNegativeTag.Rude,
                                RawNegativeTag.CancelledOffer,
                            ),
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should create conversation review body payload with empty tags`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.SOME_AD_ID,
                    adTitle = DataFactory.SOME_AD_TITLE,
                    categoryId = CategoryDefaults.BABY_AND_KIDS.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.ANOTHER_RATING,
                    review = CreateConversationReviewRequest.Review(positiveTags = emptyList(), negativeTags = emptyList())
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.ANOTHER_RATING) }
            Then { checkCategoryId(CategoryDefaults.BABY_AND_KIDS.id) }
            Then { checkItemId(DataFactory.SOME_AD_ID) }
            Then { checkItemTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then { checkReviewFeedback(null) }
        }
    }

    @Test
    fun `should create conversation review body payload with rating, all positive and all negative review tags`() {
        runUnitTest(robot) {
            Given { stubRawConversation() }
            Given {
                stubRawAdDetails(
                    adId = DataFactory.ANOTHER_AD_ID,
                    adTitle = DataFactory.ANOTHER_AD_TITLE,
                    categoryId = CategoryDefaults.SPORTS_AND_LEISURE.id,
                )
            }
            Given {
                stubCreateConversationReviewRequest(
                    rating = DataFactory.SOME_RATING,
                    review = CreateConversationReviewRequest.Review(
                        positiveTags = listOfNotNull(
                            ReviewsTag.FRIENDLY.name,
                            ReviewsTag.POLITE.name,
                            ReviewsTag.HELPFUL.name,
                            ReviewsTag.SPEEDY_RESPONDER.name,
                            ReviewsTag.ITEM_AS_DESCRIBED.name,
                            ReviewsTag.QUICK_TRANSACTION.name,
                            ReviewsTag.SHOWED_UP_ON_TIME.name,
                            ReviewsTag.FAIR_NEGOTIATION.name,
                        ),
                        negativeTags = listOfNotNull(
                            ReviewsTag.RUDE.name,
                            ReviewsTag.UNHELPFUL.name,
                            ReviewsTag.UNRESPONSIVE.name,
                            ReviewsTag.ITEM_NOT_AS_DESCRIBED.name,
                            ReviewsTag.CANCELLED_OFFER.name,
                            ReviewsTag.DIDN_T_SHOW_UP.name,
                            ReviewsTag.TOO_MUCH_HAGGLING.name,
                        ),
                    )
                )
            }
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Given { stubUsername(DataFactory.SOME_USER_EMAIL) }
            When { create() }
            Then { checkReviewerId(DataFactory.SOME_USER_ID) }
            Then { checkRevieweeId(DataFactory.ANOTHER_USER_ID) }
            Then { checkRating(DataFactory.SOME_RATING) }
            Then { checkCategoryId(CategoryDefaults.SPORTS_AND_LEISURE.id) }
            Then { checkItemId(DataFactory.ANOTHER_AD_ID) }
            Then { checkItemTitle(DataFactory.ANOTHER_AD_TITLE) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
            Then { checkReviewMetaAttributes(null) }
            Then {
                checkReviewFeedback(
                    RawFeedback(
                        tags = RawFeedback.RawTags(
                            positive = listOf(
                                RawPositiveTag.Friendly,
                                RawPositiveTag.Polite,
                                RawPositiveTag.Helpful,
                                RawPositiveTag.SpeedyResponder,
                                RawPositiveTag.ItemAsDescribed,
                                RawPositiveTag.QuickTransaction,
                                RawPositiveTag.ShowedUpOnTime,
                                RawPositiveTag.FairNegotiation,
                            ),
                            negative = listOf(
                                RawNegativeTag.Rude,
                                RawNegativeTag.Unhelpful,
                                RawNegativeTag.Unresponsive,
                                RawNegativeTag.ItemNotAsDescribed,
                                RawNegativeTag.CancelledOffer,
                                RawNegativeTag.DidntShowUp,
                                RawNegativeTag.TooMuchHaggling,
                            ),
                        )
                    )
                )
            }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualRawCreateReviewRequestBodyResult: RawCreateReviewRequestBody
        private lateinit var createConversationReviewRequest: CreateConversationReviewRequest
        private lateinit var rawConversation: RawConversation
        private lateinit var rawAdDetails: RawFlatAd
        private lateinit var reviewerId: String
        private lateinit var revieweeId: String
        private lateinit var userName: String

        private lateinit var testSubject: ReviewsBodyCreator

        override fun setup() {
            testSubject = ReviewsBodyCreator()
        }

        fun stubCreateConversationReviewRequest(
            rating: Int,
            review: CreateConversationReviewRequest.Review?,
        ) {
            createConversationReviewRequest = CreateConversationReviewRequest(
                rating = rating,
                review = review,
            )
        }

        fun stubRawConversation() {
            rawConversation = RawCapiConversationsFactory.createRawConversation()
        }

        fun stubRawAdDetails(
            adId: String,
            adTitle: String,
            categoryId: String
        ) {
            rawAdDetails = RawFlatAdsFactory.createRawFlatAd(
                adId = adId,
                title = adTitle,
                categoryId = categoryId,
            )
        }

        fun stubReviewerId(id: String) {
            this.reviewerId = id
        }

        fun stubRevieweeId(id: String) {
            this.revieweeId = id
        }

        fun stubUsername(userName: String) {
            this.userName= userName
        }


        fun create() {
            actualRawCreateReviewRequestBodyResult = testSubject.createNew(
                request = createConversationReviewRequest,
                rawConversationData = rawConversation,
                rawAdDetailsData = rawAdDetails,
                reviewerId = reviewerId.toInt(),
                revieweeId = revieweeId.toInt(),
                userName = userName,
            )
        }

        fun checkRating(expected: Int) {
            assertEquals(expected, actualRawCreateReviewRequestBodyResult.rating)
        }

        fun checkReviewerId(expected: String) {
            assertEquals(expected.toInt(), actualRawCreateReviewRequestBodyResult.reviewerId)
        }

        fun checkRevieweeId(expected: String) {
            assertEquals(expected.toInt(), actualRawCreateReviewRequestBodyResult.revieweeId)
        }

        fun checkCategoryId(expected: String) {
            assertEquals(expected.toInt(), actualRawCreateReviewRequestBodyResult.categoryId)
        }

        fun checkItemId(expected: String) {
            assertEquals(expected.toInt(), actualRawCreateReviewRequestBodyResult.itemId)
        }

        fun checkItemTitle(expected: String) {
            assertEquals(expected, actualRawCreateReviewRequestBodyResult.itemTitle)
        }

        fun checkReviewDirection(expected: RawReview.Direction) {
            assertEquals(expected, actualRawCreateReviewRequestBodyResult.direction)
        }

        fun checkReviewMetaAttributes(expected: RawCreateReviewRequestBody.RawMetaAttributes?) {
            assertEquals(expected, actualRawCreateReviewRequestBodyResult.metaAttributes)
        }

        fun checkReviewFeedback(expected: RawFeedback?) {
            assertEquals(expected, actualRawCreateReviewRequestBodyResult.feedback)
        }
    }
}