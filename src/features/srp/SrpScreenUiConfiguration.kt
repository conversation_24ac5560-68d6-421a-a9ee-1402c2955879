package com.gumtree.mobile.features.srp

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isZero

/**
 * Defines the SRP screen UI components configurations
 */

object SrpScreenUiConfiguration {
    const val NEARBY_ADS_THRESHOLD = 100

    const val SAVE_SEARCH_BANNER_TITLE = "Save this search and get notified of new results"
    const val SAVE_SEARCH_BUTTON_TITLE = "Save search"
    const val NO_RESULTS_BUTTON_TITLE = "Save search"
    const val NO_RESULTS_SUBTITLE = "Save this search to receive email alerts when new items are available."
    const val NO_RESULTS_TITLE_FOR_SEARCH_QUERY = "0 results for"
    const val NO_RESULTS_TITLE_FOR_CATEGORY = "0 results in"
    const val ALL_CATEGORIES_TITLE = "All categories"
    const val RELEVANCE_DESCENDING_KEY = "RELEVANCE_DESCENDING"
    const val RELEVANCE_DESCENDING_VALUE = "Most relevant first"
    const val DATE_DESCENDING_KEY = "DATE_DESCENDING"
    const val DATE_DESCENDING_VALUE = "Newest first"
    const val PRICE_DESCENDING_KEY = "PRICE_DESCENDING"
    const val PRICE_DESCENDING_VALUE = "Price: high to low"
    const val PRICE_ASCENDING_KEY = "PRICE_ASCENDING"
    const val PRICE_ASCENDING_VALUE = "Price: low to high"
    const val DISTANCE_ASCENDING_KEY = "DISTANCE_ASCENDING"
    const val DISTANCE_ASCENDING_VALUE = "Nearest first"
    const val FILTER_CHIP_TITLE = "Filter"
    const val PRICE_MIN_AND_MAX_CHIP_TITLE = "Price: £"
    const val PRICE_ONLY_MIN_CHIP_TITLE = "Price: min £"
    const val PRICE_ONLY_MAX_CHIP_TITLE = "Price: max £"
    const val EXTENDED_RESULTS_TITLE = "Results from outside your search"

    /**
     * Return the number of additional results string
     * @param numberOfResults - the total number of extended results
     * @return - The formatted number of results string
     */
    fun createExtendedResultsText(numberOfResults: Int): String {
        return if (numberOfResults == 1) {
            "Here is $numberOfResults additional result in your extended area"
        } else {
            "Here are $numberOfResults additional results in your extended area"
        }
    }

    /**
     * Format the SRP filters size (the display text)
     * @param filtersSize - the size of the SRP filters
     * @return - The formatted filters count as String
     */
    fun formatFiltersCount(filtersSize: Int): String {
        return when (filtersSize) {
            ZERO -> FILTER_CHIP_TITLE
            else -> "$FILTER_CHIP_TITLE ($filtersSize)"
        }
    }

    /**
     * Format the SRP price chip title
     * @param minPrice - the search params minPrice
     * @param maxPrice - the search params maxPrice
     */
    fun formatPriceChipTitle(
        minPrice: String?,
        maxPrice: String?,
    ): String? {
        return when {
            minPrice.isNotNull() && maxPrice.isNotNull() -> "$PRICE_MIN_AND_MAX_CHIP_TITLE$minPrice - $maxPrice"
            minPrice.isNotNull() -> "$PRICE_ONLY_MIN_CHIP_TITLE$minPrice"
            maxPrice.isNotNull() -> "$PRICE_ONLY_MAX_CHIP_TITLE$maxPrice"
            else -> null
        }
    }

    /**
     * Indicates the page where Saved Searches banner should/could appear
     */
    fun shouldSavedSearchesBannerRowAppearOnPage(page: String, totalResults: Int) = page.isZero() && totalResults > ZERO

    /**
     * Indicates the page where No results row should appear
     */
    fun shouldNoResultsRowAppearOnPage(page: String, totalResults: Int) = page.isZero() && totalResults == ZERO

    /**
     * Indicates whether the sticky bar should appear
     */
    fun shouldStickyBarAppearOnPage(page: String) = page.isZero()

    /**
     * Indicates whether the SRP sort header should appear
     */
    fun shouldSortHeaderAppear(totalResults: Int) = totalResults > ZERO

    /**
     * Get the SRP listings grid sizes per categoryId
     * @param categoryId - the SRP category
     * @param isVehiclesSingleColumnLayout - whether single column layout should apply
     * @return - instance of GridSizes
     */
    fun getListingsGridSizes(
        categoryId: String,
        isVehiclesSingleColumnLayout: Boolean,
    ): GridSizes {
        return when {
            CategoriesTreeCache.isVehicles(categoryId) && isVehiclesSingleColumnLayout -> SrpListingGridSizes.Cars()
            else -> SrpListingGridSizes.Default()
        }
    }

    /**
     * Get the service listings grid sizes (one item per row)
     * @return - instance of GridSizes for service listings
     */
    fun getServiceListingsGridSizes(): GridSizes {
        return SrpListingGridSizes.Service()
    }
}
