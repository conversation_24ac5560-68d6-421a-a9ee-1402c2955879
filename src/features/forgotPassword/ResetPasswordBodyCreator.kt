package com.gumtree.mobile.features.forgotPassword

import com.gumtree.mobile.api.userService.bodies.UserServiceChangePasswordBody
import com.gumtree.mobile.utils.extensions.getClientSemantics
import io.ktor.http.Headers

class ResetPasswordBodyCreator {

    fun create(
        callHeaders: Headers,
        resetPasswordRequest: ResetPasswordRequest,
    ): UserServiceChangePasswordBody {
        return UserServiceChangePasswordBody(
            password = resetPasswordRequest.userSecret,
            confirmedPassword = resetPasswordRequest.userSecret,
            verificationKey = resetPasswordRequest.resetToken,
            verificationKeyType = UserServiceChangePasswordBody.VerificationKeyType.RESET_PASSWORD_KEY,
            client = callHeaders.getClientSemantics().platform.name,
        )
    }
}
