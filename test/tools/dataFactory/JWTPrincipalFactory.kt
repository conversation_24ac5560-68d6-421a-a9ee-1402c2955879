package tools.dataFactory

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTDecodeException
import com.auth0.jwt.impl.JWTParser
import com.auth0.jwt.interfaces.Claim
import com.auth0.jwt.interfaces.DecodedJWT
import com.auth0.jwt.interfaces.Header
import com.auth0.jwt.interfaces.Payload
import com.gumtree.mobile.common.JTW_AUDIENCE_KEY
import com.gumtree.mobile.common.JTW_ISSUER_KEY
import com.gumtree.mobile.common.JTW_REALM_KEY
import com.gumtree.mobile.common.JTW_SECRET_KEY
import com.gumtree.mobile.common.JWTConfig
import io.ktor.server.application.Application
import io.ktor.server.auth.jwt.JWTPrincipal
import java.io.Serializable
import java.nio.charset.StandardCharsets
import java.time.Instant
import java.util.*

object JWTPrincipalFactory {

    fun create(jwt: String): JWTPrincipal {
        return JWTPrincipal(JWTDecoderForTests(jwt))
    }
}

class JWTDecoderForTests(
    converter: JWTParser,
    jwt: String,
) : DecodedJWT, Serializable {
    private val parts: Array<String> = TokenUtils.splitToken(jwt)
    private val header: Header
    private val payload: Payload

    constructor(jwt: String) : this(JWTParser(), jwt)

    init {
        val headerJson: String
        val payloadJson: String
        try {
            headerJson = String(Base64.getUrlDecoder().decode(parts[0]), StandardCharsets.UTF_8)
            payloadJson = String(Base64.getUrlDecoder().decode(parts[1]), StandardCharsets.UTF_8)
        } catch (e: NullPointerException) {
            throw JWTDecodeException("The UTF-8 Charset isn't initialized.", e)
        } catch (e: java.lang.IllegalArgumentException) {
            throw JWTDecodeException("The input is not a valid base 64 encoded string.", e)
        }

        this.header = converter.parseHeader(headerJson)
        this.payload = converter.parsePayload(payloadJson)
    }

    override fun getAlgorithm(): String {
        return header.algorithm
    }

    override fun getType(): String {
        return header.type
    }

    override fun getContentType(): String {
        return header.contentType
    }

    override fun getKeyId(): String {
        return header.keyId
    }

    override fun getHeaderClaim(name: String): Claim {
        return header.getHeaderClaim(name)
    }

    override fun getIssuer(): String {
        return payload.issuer
    }

    override fun getSubject(): String {
        return payload.subject
    }

    override fun getAudience(): List<String> {
        return payload.audience
    }

    override fun getExpiresAt(): Date {
        return payload.expiresAt
    }

    override fun getExpiresAtAsInstant(): Instant {
        return payload.expiresAtAsInstant
    }

    override fun getNotBefore(): Date {
        return payload.notBefore
    }

    override fun getNotBeforeAsInstant(): Instant {
        return payload.notBeforeAsInstant
    }

    override fun getIssuedAt(): Date {
        return payload.issuedAt
    }

    override fun getIssuedAtAsInstant(): Instant {
        return payload.issuedAtAsInstant
    }

    override fun getId(): String {
        return payload.id
    }

    override fun getClaim(name: String): Claim {
        return payload.getClaim(name)
    }

    override fun getClaims(): Map<String, Claim> {
        return payload.claims
    }

    override fun getHeader(): String {
        return parts[0]
    }

    override fun getPayload(): String {
        return parts[1]
    }

    override fun getSignature(): String {
        return parts[2]
    }

    override fun getToken(): String {
        return String.format(
            "%s.%s.%s",
            parts[0], parts[1], parts[2]
        )
    }

    companion object {
        private const val serialVersionUID = 1873362438023312895L
    }
}

internal object TokenUtils {
    @Throws(JWTDecodeException::class)
    fun splitToken(token: String): Array<String> {
        if (token == null) {
            throw JWTDecodeException("The token is null.")
        } else {
            val delimiter = '.'
            val firstPeriodIndex = token.indexOf(delimiter)
            if (firstPeriodIndex == -1) {
                throw wrongNumberOfParts(0)
            } else {
                val secondPeriodIndex = token.indexOf(delimiter, firstPeriodIndex + 1)
                if (secondPeriodIndex == -1) {
                    throw wrongNumberOfParts(2)
                } else if (token.indexOf(delimiter, secondPeriodIndex + 1) != -1) {
                    throw wrongNumberOfParts("> 3")
                } else {
                    return arrayOf(
                        token.substring(0, firstPeriodIndex),
                        token.substring(firstPeriodIndex + 1, secondPeriodIndex),
                        token.substring(secondPeriodIndex + 1),
                    )
                }
            }
        }
    }

    private fun wrongNumberOfParts(partCount: Any): JWTDecodeException {
        return JWTDecodeException(String.format("The token was expected to have 3 parts, but got %s.", partCount))
    }
}

class TestJWTConfigWithExpire(application: Application) : JWTConfig {
    override val audience = application.environment.config.property(JTW_AUDIENCE_KEY).getString()
    override val issuer = application.environment.config.property(JTW_ISSUER_KEY).getString()
    override val realm = application.environment.config.property(JTW_REALM_KEY).getString()
    override val secret = application.environment.config.property(JTW_SECRET_KEY).getString()
    override val expiresAt: Date? = Date(System.currentTimeMillis() + 60000)
    override val algorithm = Algorithm.HMAC256(secret)
    override val verifier: JWTVerifier = JWT
        .require(algorithm)
        .withAudience(audience)
        .withIssuer(issuer)
        .build()
}
