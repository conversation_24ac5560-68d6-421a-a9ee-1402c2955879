openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-home-feed
  license:
    name: MIT
tags:
  - name: BFF-HomeFeed
    description: Mobile Apps BFF HomeFeed API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    UserIdQuery:
      in: query
      name: userId
      description: User ID
      required: false
      schema:
        type: integer
        format: int64
    LocationIdQuery:
      in: query
      name: locationId
      description: Location ID
      required: false
      schema:
        type: integer
        format: int64
    LocationTypeQuery:
      in: query
      name: locationType
      required: false
      schema:
        type: string
        enum:
          - LOCATION
          - POSTCODE
          - OUTCODE
    PageQuery:
      in: query
      name: page
      description: Specify which page of the list with items
      required: false
      schema:
        type: integer
        format: int32
        default: 0
  schemas:
    ScreenResponse:
      type: object
      properties:
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        landscapeData:
          $ref: '#/components/schemas/RowLayoutItems'
        nextPage:
          type: string
        stickybar:
          $ref: '#/components/schemas/StickyBar'
        topBar:
          $ref: '#/components/schemas/TopBar'
        gamAdvertsData:
          type: array
          items:
            $ref: '#/components/schemas/GAMAdvertDto'
      required:
        - portraitData
    RowLayoutItems:
      type: array
      items:
        $ref: '#/components/schemas/Row'
    Row:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_ROW
            - ADVERTISING_ROW
            - HOME_FEED_SAVED_SEARCHES_ROW
            - TODAY_PICKS_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/ListingCardDto'
              - $ref: '#/components/schemas/GAMAdvertDto'
              - $ref: '#/components/schemas/SavedSearchCardDto'
              - $ref: '#/components/schemas/ViewMoreCardDto'
              - $ref: '#/components/schemas/TodayPicksCardDto'
        scrollingBehaviour:
          type: string
          enum:
            - COLLAPSE_AT_TOP
            - COLLAPSE_ANYWHERE
        topDivider:
          type: boolean
        bottomDivider:
          type: boolean
      required:
        - type
        - data
    SavedSearchCardDto:
      type: object
      properties:
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        subtitle:
          type: string
      required:
        - title
        - destination
    ViewMoreCardDto:
      type: object
      properties:
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - title
        - destination
    ListingCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_CARD
        adId:
          type: integer
          format: int64
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        location:
          type: string
        price:
          type: string #review the price type later
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
        isUrgent:
          type: boolean
        isFeatured:
          type: boolean
      required:
        - type
        - adId
        - title
        - destination
    GAMAdvertDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - GAM_ADVERT
        androidUnitId:
          type: string
        iosUnitId:
          type: string
        slotName:
          type: string
        displaySize:
          type: string
          enum:
            - Ad300x250
            - Ad320x50
        pageUrl:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        key:
          type: string
        addApptrPlacementId:
          type: string
      required:
        - type
        - androidUnitId
        - iosUnitId
        - slotName
        - displaySize
        - attributes
    TodayPicksCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - TODAY_PICKS_CARD
        text:
          type: string
        location:
          type: object
          properties:
            text:
              type: string
            destination:
              $ref: '#/components/schemas/DestinationDto'
      required:
        - type
        - text
        - location
    Image:
      type: object
      properties:
        url:
          type: string
        width:
          type: integer
        height:
          type: integer
      required:
        - url
    StickyBar:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/ChipsRow'
          - $ref: '#/components/schemas/CarouselRow'
    TopBar:
      type: object
      properties:
        scrollingCollapseBehaviour:
          type: string
          enum:
            - STICK_TO_TOP
            - COLLAPSE_AT_TOP,
            - COLLAPSE_ANYWHERE,
        rows:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/ChipsRow'
              - $ref: '#/components/schemas/CarouselRow'
      required:
        - rows
    ChipsRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - CHIPS_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/CapsuleChipDto'
    CarouselRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - CAROUSEL_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/CategoryCardDto'
    CapsuleChipDto:
      type: object
      properties:
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        analyticsEventData:
          $ref: '#/components/schemas/AnalyticsEventData'
      required:
        - title
        - destination
    CategoryCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - CATEGORY_CARD
        title:
          type: string
        icon:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        analyticsEventData:
          $ref: '#/components/schemas/AnalyticsEventData'
      required:
        - type
        - title
        - icon
        - destination
        - analyticsEventData
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
    AnalyticsEventData:
      type: object
      properties:
        eventName:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
      required:
        - eventName

    Error:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
      required:
        - code
        - message
paths:
  /home-feed/screen:
    get:
      summary: Get the Mobile apps HomeFeed screen (contains all UI items about the HomeFeed screen)
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/UserIdQuery"
        - $ref: "#/components/parameters/LocationIdQuery"
        - $ref: "#/components/parameters/PageQuery"
      responses:
        '200':
          description: The mobile apps HomeFeed screen UI ready to be rendered on the users device
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
