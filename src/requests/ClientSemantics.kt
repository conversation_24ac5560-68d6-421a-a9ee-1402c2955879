package com.gumtree.mobile.requests

import com.gumtree.mobile.api.common.EMPTY_STRING

/**
 * Data class for the client semantics - app version, OS version, platform, device name
 */
data class ClientSemantics(
    val appVersion: String = EMPTY_STRING,
    val isAppDebugMode: Boolean = false,
    val osVersion: String = EMPTY_STRING,
    val device: String = EMPTY_STRING,
    val platform: ClientPlatform = ClientPlatform.UNKNOWN,
)
