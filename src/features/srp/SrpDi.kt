package com.gumtree.mobile.features.srp

import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.di.CAPI_HEADERS_PROVIDER
import com.gumtree.mobile.di.SEARCH_RESULTS_PAGE_CALCULATOR
import com.gumtree.mobile.di.VIP_DATE_FORMATTER
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.SrpPageCalculator
import com.gumtree.mobile.utils.extensions.getFromKoin
import features.srp.SrpAdjustTrackingDataProvider
import org.koin.core.qualifier.named
import org.koin.dsl.module

/**
 * Provides all dependencies about the SRP feature
 */
val srpModule = module {
    factory {
        SrpAnalyticsProvider(
            commonAnalyticsProvider = getFromKoin(),
            rawLocationFetcher = getFromKoin(),
            srpSortHeaderFactory = getFromKoin(),
        )
    }
    single<PageCalculator>(named(SEARCH_RESULTS_PAGE_CALCULATOR)) { SrpPageCalculator() }
    single { SrpCapiQueryParamsFactory() }
    single { BingAdUrlProvider() }
    single { SrpAdvertsFactory(bingAdUrlProvider = getFromKoin()) }
    single { SrpDominantCategoryHandler(categoryApi = getFromKoin()) }
    single { SrpZipcodeHandler() }
    single { SrpTotalResultsMapper() }
    single { SrpTimeAgoFormatter(getFromKoin(VIP_DATE_FORMATTER)) }
    single {
        SrpListingMapper(
            srpAnalyticsProvider = getFromKoin(),
            srpTimeAgoFormatter = getFromKoin(),
        )
    }
    single { SrpChipsFactory(rawLocationFetcher = getFromKoin()) }
    single { SrpSortHeaderFactory() }
    single { SrpAdvertsProvider(srpAdvertsFactory = getFromKoin(), gamAdvertUtils = getFromKoin()) }
    single { SrpStickyBarProvider(srpChipsFactory = getFromKoin(), srpSortHeaderFactory = getFromKoin()) }
    single { SrpListingsProvider(srpListingMapper = getFromKoin()) }
    single { SrpSavedSearchesBannerProvider() }
    single { SrpExtendedSearchAdsProvider(srpListingMapper = getFromKoin(), srpService = getFromKoin()) }
    single { SrpAdjustTrackingDataProvider() }
    single<ApiHeadersProvider> { CapiHeadersProvider() }
    single {
        SrpButtonUIProvider(
            srpAnalyticsProvider = getFromKoin(),
            srpAdjustTrackingDataProvider = getFromKoin(),
        )
    }
    single<SrpRepository> {
        DefaultSrpRepository(
            srpService = getFromKoin(),
            srpDominantCategoryHandler = getFromKoin(),
            srpZipcodeHandler = getFromKoin(),
            srpTotalResultsMapper = getFromKoin(),
            srpPageCalculator = getFromKoin(SEARCH_RESULTS_PAGE_CALCULATOR),
            srpStickyBarProvider = getFromKoin(),
            srpListingsProvider = getFromKoin(),
            srpSavedSearchesBannerProvider = getFromKoin(),
            srpAdvertsProvider = getFromKoin(),
            srpAnalyticsProvider = getFromKoin(),
            rawLocationFetcher = getFromKoin(),
            capiHeadersProvider = getFromKoin(CAPI_HEADERS_PROVIDER),
            srpExtendedSearchAdsProvider = getFromKoin(),
            srpAdjustTrackingDataProvider = getFromKoin(),
            dispatcherProvider = getFromKoin(),
            srpButtonUIProvider = getFromKoin(),
            userProfileService = getFromKoin(),
        )
    }
    single {
        SrpService(
            capiSearchApi = getFromKoin(),
            srpCapiQueryParamsFactory = getFromKoin(),
            locationsApi = getFromKoin(),
        )
    }
}
