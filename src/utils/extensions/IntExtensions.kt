package com.gumtree.mobile.utils.extensions

import com.gumtree.mobile.api.common.ZERO
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * Compare this number with another number
 * @param number - the number to compare against
 * @return - true if this number is greater than the number otherwise false
 */
fun Int?.is<PERSON><PERSON><PERSON><PERSON><PERSON>(number: Int?): Bo<PERSON>an {
    return when {
        this.isNotNull() && number.isNotNull() -> this > number
        else -> false
    }
}

private const val PERCENTAGE = 100
private const val PERCENTAGE_DIVIDE_SCALE = 5
infix fun Int?.percentOf(number: Int): Int {
    return when {
        isNotNull() ->
            (
                toBigDecimal().divide(
                    number.toBigDecimal(),
                    PERCENTAGE_DIVIDE_SCALE,
                    RoundingMode.HALF_UP,
                ) * BigDecimal(PERCENTAGE)
                ).toInt()
        else -> ZERO
    }
}
