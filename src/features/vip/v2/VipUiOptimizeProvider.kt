package com.gumtree.mobile.features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.layoutsData.DividerCardDto
import com.gumtree.mobile.features.screens.layoutsData.NewSellerLink
import com.gumtree.mobile.features.screens.layoutsData.SellerLink
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute

/**
 * Provider of the VIP UI optimization Views
 */
class VipUiOptimizeProvider(
    private val vipUiOptimizeMapper: VipUiOptimizeMapper,
    private val vipAnalyticsProvider: VipAnalyticsProvider,
) {
    fun getVipOverviewRow(
        rawAdDetailsData: RawCapiAd,
        sellerLink: SellerLink?,
        sinceDate: String? = null,
    ): RowLayout<UiItem> {
        return vipUiOptimizeMapper.mapOverview(rawAdDetailsData, sellerLink, sinceDate)
    }

    fun getVipPortfolioImageRow(
        rawAdDetailsData: RawCapiAd,
        gamAdvertDto: VipImageCardDto? = null,
    ): RowLayout<UiItem> {
        return vipUiOptimizeMapper.mapPortfolioImage(
            rawAdDetailsData,
            gamAdvertDto,
            vipAnalyticsProvider.getImageClickEvent(),
        )
    }

    fun getVipSectionTabsRow(tabIndexMap: Map<String, Int>): RowLayout<UiItem> {
        return vipUiOptimizeMapper.mapSectionTabs(tabIndexMap)
    }

    fun getVipPublisherInfo(
        rawAdDetailsData: RawCapiAd,
    ): RowLayout<UiItem> {
        val destination = DestinationRoute.SELLER_PROFILE.build(
            ApiQueryParams.USER_ID to rawAdDetailsData.userId,
            ApiQueryParams.PUBLIC_USER_ID to rawAdDetailsData.userPublicId,
        )

        val sellerLink = NewSellerLink(
            text = rawAdDetailsData.posterName ?: "",
            sellerDestination = destination,
        )

        return vipUiOptimizeMapper.mapPublisherInfo(
            rawAdDetailsData = rawAdDetailsData,
            sellerLink = sellerLink,
            destination = destination,
        )
    }

    fun createDividerRow(
        thickness: DividerCardDto.Thickness = DividerCardDto.Thickness.LIGHT,
        topMargin: Space = Space.SMALL,
        leftMargin: Space = Space.SMALL,
        rightMargin: Space = Space.SMALL,
        bottomMargin: Space = Space.SMALL,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.DIVIDER_ROW,
            data = listOf(
                DividerCardDto(data = DividerCardDto.DividerData(thickness = thickness)),
            ),
            style = Style(
                spacing = Style.Spacing(
                    margins = Style.Spacing.Dimension(
                        top = topMargin,
                        left = leftMargin,
                        right = rightMargin,
                        bottom = bottomMargin,
                    ),
                ),
            ),
        )
    }
}
