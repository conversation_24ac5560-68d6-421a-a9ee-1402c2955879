package utils.extensions

import com.gumtree.mobile.utils.extensions.runCatchingWithLog
import com.gumtree.mobile.utils.extensions.runCatchingWithoutLog
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class ResultExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should return successful result with logging and prefix`() {
        runUnitTest(robot) {
            When { runCatchingWithLoggingAndPrefix(prefix = DataFactory.anyString(), block = { DataFactory.anyInt() }) }
            Then { checkRunCatchResultIsSuccess() }
        }
    }

    @Test
    fun `should return failure result with logging and prefix`() {
        runUnitTest(robot) {
            When { runCatchingWithLoggingAndPrefix(prefix = DataFactory.anyString(), block = { throw NumberFormatException() }) }
            Then { checkRunCatchResultIsFailure() }
        }
    }

    @Test
    fun `should return successful result with logging`() {
        runUnitTest(robot) {
            When { runCatchingWithLogging(block = { DataFactory.anyInt() }) }
            Then { checkRunCatchResultIsSuccess() }
        }
    }

    @Test
    fun `should return failure result with logging`() {
        runUnitTest(robot) {
            When { runCatchingWithLogging(block = { throw NumberFormatException() }) }
            Then { checkRunCatchResultIsFailure() }
        }
    }

    @Test
    fun `should return successful result without logging`() {
        runUnitTest(robot) {
            When { runCatchingWithoutLogging(block = { DataFactory.anyInt() }) }
            Then { checkRunCatchResultIsSuccess() }
        }
    }

    @Test
    fun `should return failure result without logging`() {
        runUnitTest(robot) {
            When { runCatchingWithoutLogging(block = { throw NumberFormatException() }) }
            Then { checkRunCatchResultIsFailure() }
        }
    }

    private class Robot: BaseRobot {
        private var actualRunCatchingResult: Result<Int>? = null

        fun runCatchingWithLoggingAndPrefix(prefix: String?, block: () -> Int) {
            actualRunCatchingResult = runCatchingWithLog(errorPrefix = prefix, block = { block() })
        }

        fun runCatchingWithLogging(block: () -> Int) {
            actualRunCatchingResult = runCatchingWithLog(block = block)
        }

        fun runCatchingWithoutLogging(block: () -> Int) {
            actualRunCatchingResult = runCatchingWithoutLog(block = block)
        }

        fun checkRunCatchResultIsSuccess() {
            assertTrue(actualRunCatchingResult?.isSuccess ?: false)
        }

        fun checkRunCatchResultIsFailure() {
            assertTrue(actualRunCatchingResult?.isFailure ?: false)
        }
    }
}