package com.gumtree.mobile.api.searchSuggestion.models

import com.google.gson.annotations.SerializedName

data class RawSearchSuggestionsResponse(
    @SerializedName("reqId")
    val reqId: String,
    @SerializedName("hits")
    val hits: List<RawSearchSuggestionsHit>,
)

data class RawSearchSuggestionsHit(
    @SerializedName("completion")
    val completion: String,
    @SerializedName("wordLength")
    val wordLength: Int,
    @SerializedName("categories")
    val categories: List<RawSearchSuggestionCategory>,
)

data class RawSearchSuggestionCategory(
    @SerializedName("id")
    val id: Int,
    @SerializedName("canonicalName")
    val canonicalName: String,
    @SerializedName("localizedNames")
    val localizedNames: Map<String, String>,
) {
    val britishName get() = localizedNames["en_GB"] ?: localizedNames.values.firstOrNull().orEmpty()
}
