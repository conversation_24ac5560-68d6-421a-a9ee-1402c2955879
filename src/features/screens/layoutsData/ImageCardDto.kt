package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.common.Image
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed interface ImageCardDto : UiItem {
    @SerialName("images")
    val images: List<Image>?

    @Serializable
    @SerialName("IMAGE_CARD")
    data class Default(
        override val images: List<Image>?,
    ) : ImageCardDto

    @Serializable
    @SerialName("LOCAL_IMAGE_CARD")
    data class Local(
        @SerialName("resource")
        val resource: Resource,
        override val images: List<Image>? = null,
    ) : ImageCardDto {
        enum class Resource {
            CARS_LANDING,
        }
    }
}
