package api.reviews.models

import com.gumtree.mobile.api.reviews.models.RawReview
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class RawReviewTest {

    private val robot = Robot()

    @ParameterizedTest
    @CsvSource(
        "LIVE, LIVE",
        "DELAYED, DELAYED",
        "UNKNOWN, UNKNOWN",
        "EXPIRED, UNKNOWN",
        "'', UNKNOWN"
    )
    fun `should return Review status from String`(str: String, expected: RawReview.Status) {
        runUnitTest(robot) {
            When { statusFromString(str) }
            Then { checkReviewStatus(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "B2S, B2S",
        "S2B, S2B",
        "UNKNOWN, UNKNOWN",
        "NEW, UNKNOWN",
        "'', UNKNOWN"
    )
    fun `should return Review direction from String`(str: String, expected: RawReview.Direction) {
        runUnitTest(robot) {
            When { directionFromString(str) }
            Then { checkReviewDirection(expected) }
        }
    }

    @Test
    fun `should return S2B Review direction when conversation listing is mine`() {
        runUnitTest(robot) {
            When { fromIsMyAd(true) }
            Then { checkReviewDirection(RawReview.Direction.S2B) }
        }
    }

    @Test
    fun `should return B2S Review direction when conversation listing is NOT mine`() {
        runUnitTest(robot) {
            When { fromIsMyAd(false) }
            Then { checkReviewDirection(RawReview.Direction.B2S) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualStatusResult: RawReview.Status
        private lateinit var actualDirectionResult: RawReview.Direction

        fun statusFromString(reviewStatusStringValue: String) {
            actualStatusResult = RawReview.Status.fromString(reviewStatusStringValue)
        }

        fun fromIsMyAd(isAboutMyListing: Boolean) {
            actualDirectionResult = RawReview.Direction.fromIsMyAd(isAboutMyListing)
        }

        fun directionFromString(reviewDirectionStringValue: String) {
            actualDirectionResult = RawReview.Direction.fromString(reviewDirectionStringValue)
        }

        fun checkReviewStatus(expected: RawReview.Status) {
            assertEquals(expected, actualStatusResult)
        }

        fun checkReviewDirection(expected: RawReview.Direction) {
            assertEquals(expected, actualDirectionResult)
        }
    }
}