package features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.vip.VipAdjustTrackingDataProvider
import common.AdjustTrackingData
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory

class VipAdjustTrackingDataProviderTest {

    private val robot = Robot()

    @Test
    fun `should return correct screen tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAd(DataFactory.SOME_AD_ID) }
            When { getScreenTrackingData() }
            Then { checkTrackingDataToken(VipAdjustTrackingDataProvider.TOKEN_SCREEN_EVENT) }
            Then { checkTrackingDataParameter(VipAdjustTrackingDataProvider.LISTING_ID_KEY, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should return correct share tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAd(DataFactory.SOME_AD_ID) }
            When { getShareTrackingData() }
            Then { checkTrackingDataToken(VipAdjustTrackingDataProvider.TOKEN_SHARE_EVENT) }
            Then { checkTrackingDataParameter(VipAdjustTrackingDataProvider.LISTING_ID_KEY, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should return correct call seller tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAd(DataFactory.SOME_AD_ID) }
            When { getCallSellerTrackingData() }
            Then { checkTrackingDataToken(VipAdjustTrackingDataProvider.TOKEN_PHONE_EVENT) }
            Then { checkTrackingDataParameter(VipAdjustTrackingDataProvider.LISTING_ID_KEY, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should return correct contact link tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAd(DataFactory.SOME_AD_ID) }
            When { getContactLinkTrackingData() }
            Then { checkTrackingDataToken(VipAdjustTrackingDataProvider.TOKEN_CONTACT_LINK_EVENT) }
            Then { checkTrackingDataParameter(VipAdjustTrackingDataProvider.LISTING_ID_KEY, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should return correct start chat tracking data`() = runTest {
        runUnitTest(robot) {
            Given { stubRawCapiAd(DataFactory.SOME_AD_ID) }
            When { getStartChatTrackingData() }
            Then { checkTrackingDataToken(VipAdjustTrackingDataProvider.TOKEN_START_CHAT) }
            Then { checkTrackingDataParameter(VipAdjustTrackingDataProvider.LISTING_ID_KEY, DataFactory.SOME_AD_ID) }
        }
    }

    inner class Robot : BaseRobot {
        private lateinit var rawCapiAd: RawCapiAd
        private var actualTrackingDataResult: AdjustTrackingData? = null

        private val testSubject = VipAdjustTrackingDataProvider()

        fun stubRawCapiAd(adId: String) {
            this.rawCapiAd = RawCapiAdsFactory.createRawCapiAd(adId = adId)
        }

        fun getScreenTrackingData() {
            actualTrackingDataResult = testSubject.getScreenTrackingData(rawCapiAd)
        }

        fun getShareTrackingData() {
            actualTrackingDataResult = testSubject.getShareTrackingData(rawCapiAd)
        }

        fun getCallSellerTrackingData() {
            actualTrackingDataResult = testSubject.getCallSellerEvent(rawCapiAd)
        }

        fun getContactLinkTrackingData() {
            actualTrackingDataResult = testSubject.getContactLinkEvent(rawCapiAd)
        }

        fun getStartChatTrackingData() {
            actualTrackingDataResult = testSubject.getStartChatEvent(rawCapiAd)
        }

        fun checkTrackingDataToken(expected: String) {
            assertEquals(expected, actualTrackingDataResult?.eventToken)
        }

        fun checkTrackingDataParameter(key: String, expected: String) {
            assertEquals(expected, actualTrackingDataResult?.parameters?.get(key))
        }
    }
}
