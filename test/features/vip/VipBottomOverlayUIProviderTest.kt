package features.vip

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiContactMethod
import api.capi.models.RawConversationList
import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.ContactMethodCard
import com.gumtree.mobile.features.screens.layoutsData.PhoneActionDto
import com.gumtree.mobile.features.screens.layoutsData.StartConversationActionDto
import com.gumtree.mobile.features.screens.layoutsData.VipButtonCardDto
import com.gumtree.mobile.features.vip.VipAdjustTrackingDataProvider
import com.gumtree.mobile.features.vip.VipAnalyticsProvider
import com.gumtree.mobile.features.vip.VipBottomOverlayUIProvider
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.responses.BottomOverlay
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertInstanceOf
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawCapiContactMethodFactory
import tools.rawDataFactory.RawCapiConversationsFactory
import utils.TestDispatcherProvider

class VipBottomOverlayUIProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return null for DELETE ad`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithChatMethod(status = AdStatus.DELETED) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should return null for EXPIRED ad`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithChatMethod(status = AdStatus.EXPIRED) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should return Phone contact method`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneMethod() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, VipButtonCardDto::class.java) }
            Then { checkPhoneActionAdjustTrackingData() }
        }
    }

    @Test
    fun `should return email CTA as Request a quote for Services`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithEmailMethod() }
            When { stubIsService(true) }
            When { stubHeaders(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodLabelAtPosition(0, VipScreenUiConfiguration.CTA_REQUEST_QUOTE) }
        }
    }

    @Test
    fun `should return email CTA as Enquire for other then Services category`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithEmailMethod() }
            When { stubIsService(false) }
            When { stubHeaders(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodLabelAtPosition(0, DataFactory.EMAIL_CTA) }
        }
    }

    @Test
    fun `should return Conversation contact method`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithChatMethod() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, ContactMethodCard::class.java) }
            Then { checkStartConversationActionAdjustTrackingData() }
        }
    }

    @Test
    fun `should return Continue Conversation contact method`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithChatMethod() }
            Given { stubHeaders(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            Given { stubHasExistingConversationWithSeller() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, ContactMethodCard.ContinueConversationCardDto::class.java) }
        }
    }

    @Test
    fun `should return Message contact method`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithChatMethod(categoryId = "5213") }
            Given { stubIsServiceOrCommunity(true) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, ContactMethodCard.StartConversationCardDto::class.java) }
            Then { checkStartConversationActionAdjustTrackingData() }
        }
    }

    @Test
    fun `should return two contact method (Phone and Message) within one UI item (card)`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneAndChatContactMethods(categoryId = "5213") }
            Given { stubIsServiceOrCommunity(true) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, ContactMethodCard.StartConversationCardDto::class.java) }
            Then { checkActionLeftAdjustTrackingData() }
            Then { checkActionLeftAnalyticsEventData() }
        }
    }

    @Test
    fun `should return two contact method (Phone and Email)`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneAndEmailContactMethods() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(2) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, VipButtonCardDto::class.java) }
            Then { checkContactMethodRowDataTypeAtPosition(1, VipButtonCardDto::class.java) }
        }
    }

    fun `should return two contact method if BE returning three with weblink`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneEmailAndLinkContactMethods() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(2) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, VipButtonCardDto::class.java) }
            Then { checkContactMethodRowDataTypeAtPosition(1, VipButtonCardDto::class.java) }
        }
    }

    @Test
    fun `should return (Phone and Email) with Email label as Request a quote`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneAndEmailContactMethods() }
            When { stubIsService(true) }
            When { stubHeaders(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(2) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodLabelAtPosition(1, VipScreenUiConfiguration.CTA_REQUEST_QUOTE)}
        }
    }

    @Test
    fun `should return (Phone and Email) with default email CTA`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneAndEmailContactMethods() }
            When { stubIsService(false) }
            When { stubHeaders(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(2) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodLabelAtPosition(1, DataFactory.EMAIL_CTA)}
        }
    }

    @Test
    fun `should return (Phone and Email) with email label as Message`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneAndEmailContactMethods(isMotorAd = true) }
            When { stubHeaders(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(2) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodLabelAtPosition(0, DataFactory.PHONE_MOTORS_CTA)}
            Then { checkContactMethodLabelAtPosition(1, DataFactory.EMAIL_MOTORS_CTA)}
        }
    }

    @Test
    fun `should return two contact method (Phone and Chat)`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneAndChatContactMethods() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
            Then { checkBottomOverlayType(RowLayoutType.CONTACT_METHOD_ROW) }
            Then { checkContactMethodRowDataTypeAtPosition(0, ContactMethodCard::class.java) }
        }
    }

    @Test
    fun `should return null`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithoutContactMethod() }
            When { buildContactMethod() }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should return null for own ads`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithPhoneMethod() }
            When { buildContactMethod(isUserOwnAd = true) }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    private class Robot : BaseRobot {
        private val conversationApi: CapiConversationApi = mockk(relaxed = true)
        private var callHeaders: Headers = mockk(relaxed = true)
        private val capiHeadersProvider = CapiHeadersProvider()
        private val vipAnalyticsProvider: VipAnalyticsProvider = mockk(relaxed = true)
        private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider = mockk(relaxed = true)
        private lateinit var testSubject: VipBottomOverlayUIProvider
        private lateinit var rawAd: RawCapiAd
        private var bottomOverlay: BottomOverlay? = null

        override fun setup() {
            testSubject = VipBottomOverlayUIProvider(
                conversationApi,
                capiHeadersProvider,
                vipAnalyticsProvider,
                vipAdjustTrackingDataProvider,
                TestDispatcherProvider()
            )
            mockkObject(CategoriesTreeCache)
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubRawAdWithoutContactMethod() {
            rawAd = RawCapiAdsFactory.createRawCapiAd()
        }

        fun stubRawAdWithPhoneMethod() {
            val phoneContactMethod = RawCapiContactMethodFactory.contactMethod("Call", RawCapiContactMethod.PHONE)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(phoneContactMethod))
        }

        fun stubRawAdWithEmailMethod() {
            val emailContactMethod = RawCapiContactMethodFactory.contactMethod(DataFactory.EMAIL_CTA, RawCapiContactMethod.EMAIL)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(emailContactMethod))
        }

        fun stubRawAdWithChatMethod(
            categoryId: String = DataFactory.SOME_AD_CATEGORY_ID,
            status: AdStatus = AdStatus.ACTIVE
        ) {
            val phoneContactMethod =
                RawCapiContactMethodFactory.contactMethod("Continue conversation", RawCapiContactMethod.CHAT)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(phoneContactMethod), categoryId, status)
        }

        fun stubRawAdWithPhoneAndEmailContactMethods(isMotorAd: Boolean = false) {
            val phoneContactMethod = RawCapiContactMethodFactory.contactMethod(DataFactory.PHONE_MOTORS_CTA, RawCapiContactMethod.PHONE)
            val emailContactMethod = RawCapiContactMethodFactory.contactMethod(DataFactory.EMAIL_CTA, RawCapiContactMethod.EMAIL)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(phoneContactMethod, emailContactMethod), isMotorAd = isMotorAd)
        }

        fun stubRawAdWithPhoneEmailAndLinkContactMethods() {
            val phoneContactMethod = RawCapiContactMethodFactory.contactMethod("Call", RawCapiContactMethod.PHONE)
            val emailContactMethod = RawCapiContactMethodFactory.contactMethod(DataFactory.EMAIL_CTA, RawCapiContactMethod.EMAIL)
            val weblinkMethod = RawCapiContactMethodFactory.contactMethod("Website", RawCapiContactMethod.WEBLINK)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(phoneContactMethod, emailContactMethod, weblinkMethod))
        }

        fun stubRawAdWithPhoneAndChatContactMethods(categoryId: String = DataFactory.SOME_AD_CATEGORY_ID) {
            val phoneContactMethod = RawCapiContactMethodFactory.contactMethod("Call", RawCapiContactMethod.PHONE)
            val emailContactMethod = RawCapiContactMethodFactory.contactMethod("chat", RawCapiContactMethod.CHAT)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(
                listOf(phoneContactMethod, emailContactMethod),
                categoryId
            )
        }

        fun stubHeaders(email: String, token: String, experiments: String = "") {
            callHeaders = Headers.build {
                this[ApiHeaderParams.AUTHORISATION_USER_EMAIL] = email
                this[ApiHeaderParams.AUTHORISATION_USER_TOKEN] = token
                this[ApiHeaderParams.EXPERIMENTS] = experiments
            }
        }

        fun stubHasExistingConversationWithSeller() {
            coEvery { conversationApi.getAllConversations(any(), any(), any()) } returns RawConversationList().apply {
                this.rawConversations = listOf(
                    RawCapiConversationsFactory.createRawConversation(
                        conversationId = DataFactory.SOME_CONVERSATION_ID,
                        conversationAdId = DataFactory.SOME_AD_ID,
                        conversationAdReplierEmail = DataFactory.SOME_USER_EMAIL
                    )
                )
            }
        }

        fun stubIsServiceOrCommunity(isEnabled: Boolean = false) {
            every { CategoriesTreeCache.isServiceOrCommunity(any()) } returns isEnabled
        }

        fun stubIsService(isEnabled: Boolean = false) {
            every { CategoriesTreeCache.isServices(any()) } returns isEnabled
        }

        suspend fun buildContactMethod(isUserOwnAd: Boolean = false) {
            bottomOverlay = testSubject.createContactMethod(rawAd, callHeaders, isUserOwnAd)
        }

        fun checkBottomOverlayRowIsNotNull() {
            assertNotNull(bottomOverlay)
        }

        fun checkBottomOverlayRowIsNull() {
            assertNull(bottomOverlay)
        }

        fun checkBottomOverlayRowCount(expected: Int) {
            assertEquals(expected, bottomOverlay?.first()?.data?.size)
        }

        fun checkBottomOverlayType(expected: RowLayoutType) {
            assertEquals(expected, bottomOverlay?.first()?.type)
        }

        fun <T> checkContactMethodRowDataTypeAtPosition(
            position: Int,
            expected: Class<T>,
        ) {
            assertInstanceOf(expected, bottomOverlay?.first()?.data?.get(position))
        }

        fun checkContactMethodLabelAtPosition(
            position: Int,
            expected: String,
        ) {
            val label = (bottomOverlay?.first()
                ?.data
                ?.get(position) as? VipButtonCardDto)?.text
            assertEquals(expected, label)
        }

        fun checkPhoneActionAdjustTrackingData() {
            val action = (bottomOverlay?.first()
                ?.data
                ?.first() as? VipButtonCardDto)?.action as? PhoneActionDto
            assertNotNull(action?.adjustTrackingData)
        }

        fun checkStartConversationActionAdjustTrackingData() {
            val action =
                (bottomOverlay?.first()
                    ?.data
                    ?.first() as? ContactMethodCard.StartConversationCardDto)?.action as? StartConversationActionDto
            assertNotNull(action?.adjustTrackingData)
        }

        fun checkActionLeftAdjustTrackingData() {
            val action =
                (bottomOverlay?.first()
                    ?.data
                    ?.first() as? ContactMethodCard)?.actionLeft
            assertNotNull(action?.adjustTrackingData)
        }

        fun checkActionLeftAnalyticsEventData() {
            val action =
                (bottomOverlay?.first()
                    ?.data
                    ?.first() as? ContactMethodCard)?.actionLeft
            assertNotNull(action?.analyticsEventData)
        }
    }
}
