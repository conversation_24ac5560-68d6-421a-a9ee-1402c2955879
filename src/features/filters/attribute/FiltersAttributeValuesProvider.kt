package com.gumtree.mobile.features.filters.attribute

import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import com.gumtree.mobile.features.filters.attribute.FiltersAttributeScreenUiConfiguration.ALL_BREEDS_TITLE
import com.gumtree.mobile.features.filters.attribute.FiltersAttributeScreenUiConfiguration.DOG_BREED_SEARCH_PLACEHOLDER
import com.gumtree.mobile.features.filters.getFilterAttributeValuesByRootAttributeValue
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.LinkFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.FilterAttributeSearchCardDto
import com.gumtree.mobile.features.screens.layoutsData.FilterAttributeValueCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.isNotNull
import org.jetbrains.annotations.VisibleForTesting

@VisibleForTesting
const val DOG_BREED_ATTRIBUTE_NAME = "dog_breed"

/**
 * Provider of the Filter attribute value Views
 */
class FiltersAttributeValuesProvider(
    private val linkFactory: LinkFactory,
    private val titleFactory: TitleFactory,
) {

    /**
     * Get all Filters attribute value RowLayouts
     * @return - The Array with all filter attribute value RowLayouts
     */
    fun createAttributeValueRows(
        categoryId: String?,
        attributeName: String?,
        attributeSelectedValue: String?,
        rootAttributeName: String?,
        rootAttributeSelectedValue: String?,
        dependentAttributeName: String?,
        dependentAttributeSelectedValue: String?,
        filterAttribute: FiltersCategoryAttributeDto?,
        rootFilterAttribute: FiltersCategoryAttributeDto?,
        dependentFilterAttribute: FiltersCategoryAttributeDto?,
    ): Array<RowLayout<UiItem>> {
        val filterAttributeValues = filterAttribute.getFilterAttributeValuesByRootAttributeValue(
            rootAttributeSelectedValue,
        )
        return when {
            attributeName.isNotNull() ->
                filterAttributeValues
                    ?.map {
                        val isSelected = createIsSelected(it, attributeSelectedValue)
                        when {
                            dependentAttributeName.isNotNull() && it.isAny() -> createAnyFilterAttributeValueCardRowForDependentAttribute(
                                categoryId,
                                attributeName,
                                dependentAttributeName,
                                it,
                                isSelected,
                            )
                            rootAttributeName.isNotNull() && it.isAny() -> createAnyFilterAttributeValueCardRowForRootAttribute(
                                categoryId,
                                rootAttributeName,
                                rootAttributeSelectedValue,
                                attributeName,
                                it,
                                rootFilterAttribute?.values,
                                isSelected,
                            )
                            dependentAttributeName.isNotNull() -> createAttributeLinkRow(
                                categoryId,
                                attributeName,
                                attributeSelectedValue,
                                dependentAttributeName,
                                dependentAttributeSelectedValue,
                                it,
                                dependentFilterAttribute?.values,
                                isSelected,
                            )
                            else -> createFilterAttributeValueCardRow(
                                categoryId,
                                attributeName,
                                rootAttributeName,
                                rootAttributeSelectedValue,
                                it,
                                rootFilterAttribute?.values,
                                isSelected,
                            )
                        }
                    }
                    ?.toTypedArray()
                    ?: createDefaultAttributeValueRows()
            else -> createDefaultAttributeValueRows()
        }
    }

    /**
     * Create a Filter attribute value RowLayout about dependent attribute case (value selection first screen)
     * @param categoryId - the category id
     * @param attributeName - the attribute name
     * @param dependentAttributeName -  the dependant attribute name in case of dependent attributes (like vehicle_make and vehicle_model)
     * @param attributeValue -  the attribute data (label, value, options etc.)
     * @param isSelected -  indicate if attribute value should be presented as selected
     * @return - The Filter attribute value RowLayout
     */
    private fun createAnyFilterAttributeValueCardRowForDependentAttribute(
        categoryId: String?,
        attributeName: String,
        dependentAttributeName: String?,
        attributeValue: CategoryAttributesCache.Data.Value,
        isSelected: Boolean,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.FILTER_ATTRIBUTE_VALUE_ROW,
            data = listOf(
                FilterAttributeValueCardDto(
                    text = attributeValue.label,
                    isSelected = isSelected,
                    action = FilterAttributeValueCardDto.Action(
                        selectedValue = null,
                        params = listOfNotNull(attributeName, dependentAttributeName),
                        destination = DestinationRoute.SET_FILTER.build(
                            ApiQueryParams.NAME to attributeName,
                            ApiQueryParams.DEPENDENT_NAME to dependentAttributeName,
                            ApiQueryParams.CATEGORY_ID to categoryId,
                        ),
                    ),
                ),
            ),
        )
    }

    /**
     * Create a Filter attribute "Any" value RowLayout about root attribute case (value selection second screen)
     * @param categoryId - the category id
     * @param attributeName - the attribute name
     * @param attributeSelectedValue - the attribute selected value
     * @param dependentAttributeName -  the dependant attribute name in case of dependent attributes (like vehicle_make and vehicle_model)
     * @param attributeValue -  the attribute data (label, value, options etc.)
     * @param filterAttributeValues -  the filter attribute list with all possible values
     * @param isSelected -  indicate if attribute value should be presented as selected
     * @return - The Filter attribute "Any" value RowLayout
     */
    private fun createAnyFilterAttributeValueCardRowForRootAttribute(
        categoryId: String?,
        attributeName: String,
        attributeSelectedValue: String?,
        dependentAttributeName: String?,
        attributeValue: CategoryAttributesCache.Data.Value,
        filterAttributeValues: List<CategoryAttributesCache.Data.Value>?,
        isSelected: Boolean,
    ): RowLayout<UiItem> {
        val filterAttributeSelectedValueLabel = filterAttributeValues?.firstOrNull { it.value == attributeSelectedValue }?.label
        val selectedValue = when {
            attributeSelectedValue.isNotNull() -> listOfNotNull(
                createFilterAttributeActionValue(attributeSelectedValue, filterAttributeSelectedValueLabel),
            )
            else -> null
        }
        return RowLayout(
            type = RowLayoutType.FILTER_ATTRIBUTE_VALUE_ROW,
            data = listOf(
                FilterAttributeValueCardDto(
                    text = attributeValue.label,
                    isSelected = isSelected,
                    action = FilterAttributeValueCardDto.Action(
                        selectedValue = selectedValue,
                        params = listOfNotNull(attributeName, dependentAttributeName),
                        destination = DestinationRoute.SET_FILTER.build(
                            ApiQueryParams.NAME to attributeName,
                            ApiQueryParams.VALUE to attributeSelectedValue,
                            ApiQueryParams.DEPENDENT_NAME to dependentAttributeName,
                            ApiQueryParams.CATEGORY_ID to categoryId,
                        ),
                    ),
                ),
            ),
        )
    }

    /**
     * Create a Filter attribute link RowLayout
     * @param categoryId - the category id
     * @param attributeName - the attribute name
     * @param attributeSelectedValue - the attribute selected value
     * @param dependentAttributeName -  the dependant attribute name in case of dependent attributes (like vehicle_make and vehicle_model)
     * @param dependentAttributeSelectedValue -  the dependant attribute selected value
     * @param attributeValue -  the attribute data (label, value, options etc.)
     * @param dependentAttributeValues -  the dependent attribute list with all possible values
     * @return - The Filter attribute link RowLayout
     */
    private fun createAttributeLinkRow(
        categoryId: String?,
        attributeName: String,
        attributeSelectedValue: String?,
        dependentAttributeName: String?,
        dependentAttributeSelectedValue: String?,
        attributeValue: CategoryAttributesCache.Data.Value,
        dependentAttributeValues: List<CategoryAttributesCache.Data.Value>?,
        isSelected: Boolean,
    ): RowLayout<UiItem> {
        val dependentFilterAttributeSelectedValueLabel = when {
            isSelected ->
                dependentAttributeValues
                    ?.firstOrNull { it.value == dependentAttributeSelectedValue && it.dependentValue == attributeSelectedValue }
                    ?.label
            else -> null
        }
        val destinationValue = when {
            isSelected -> dependentAttributeSelectedValue
            else -> null
        }
        return RowLayout(
            type = RowLayoutType.LINK_ROW,
            data = listOf(
                linkFactory.buildFilterAttributeLinkCard(
                    linkDisplayText = attributeValue.label,
                    destination = DestinationRoute.SET_FILTER.build(
                        ApiQueryParams.NAME to dependentAttributeName,
                        ApiQueryParams.VALUE to destinationValue,
                        ApiQueryParams.ROOT_NAME to attributeName,
                        ApiQueryParams.ROOT_VALUE to attributeValue.value,
                        ApiQueryParams.CATEGORY_ID to categoryId,
                    ),
                    isSelected = isSelected,
                    linkSelectedValue = dependentFilterAttributeSelectedValueLabel,
                ),
            ),
        )
    }

    /**
     * Create a Filter attribute value RowLayout
     * @param categoryId - the category id
     * @param attributeName - the attribute name
     * @param attributeSelectedValue - the attribute selected value
     * @param rootAttributeName -  the root attribute name in case of dependent attributes (like vehicle_make and vehicle_model)
     * @param rootAttributeSelectedValue -  the root attribute selected value
     * @param attributeValue -  the attribute data (label, value, options etc.)
     * @param rootAttributeValues -  the root attribute list with all possible values
     * @return - The Filter attribute value RowLayout
     */
    private fun createFilterAttributeValueCardRow(
        categoryId: String?,
        attributeName: String?,
        rootAttributeName: String?,
        rootAttributeSelectedValue: String?,
        attributeValue: CategoryAttributesCache.Data.Value,
        rootAttributeValues: List<CategoryAttributesCache.Data.Value>?,
        isSelected: Boolean,
    ): RowLayout<UiItem> {
        val rootFilterAttributeSelectedValueLabel = rootAttributeValues?.firstOrNull {
            it.value == rootAttributeSelectedValue
        }?.label
        val destinationNameQueryParam = when {
            rootAttributeName.isNotNull() -> rootAttributeName
            else -> attributeName
        }
        val destinationValueQueryParam = when {
            rootAttributeSelectedValue.isNotNull() -> rootAttributeSelectedValue
            attributeValue.isAny() -> null
            else -> attributeValue.value
        }
        val destinationDependentNameQueryParam = when {
            rootAttributeName.isNotNull() -> attributeName
            else -> null
        }
        val destinationDependentValueQueryParam = when {
            rootAttributeName.isNotNull() -> attributeValue.value
            else -> null
        }
        val selectedValues = createFilterAttributeCardSelectedValues(
            attributeValue,
            rootAttributeSelectedValue,
            rootFilterAttributeSelectedValueLabel,
        )
        return RowLayout(
            type = RowLayoutType.FILTER_ATTRIBUTE_VALUE_ROW,
            data = listOf(
                FilterAttributeValueCardDto(
                    text = attributeValue.label,
                    isSelected = isSelected,
                    action = FilterAttributeValueCardDto.Action(
                        selectedValue = selectedValues,
                        params = listOfNotNull(rootAttributeName, attributeName),
                        destination = DestinationRoute.SET_FILTER.build(
                            ApiQueryParams.NAME to destinationNameQueryParam,
                            ApiQueryParams.VALUE to destinationValueQueryParam,
                            ApiQueryParams.DEPENDENT_NAME to destinationDependentNameQueryParam,
                            ApiQueryParams.DEPENDENT_VALUE to destinationDependentValueQueryParam,
                            ApiQueryParams.CATEGORY_ID to categoryId,
                        ),
                    ),
                ),
            ),
        )
    }

    /**
     * By default, the attribute values array is empty
     */
    private fun createDefaultAttributeValueRows(): Array<RowLayout<UiItem>> {
        return emptyArray()
    }

    /**
     * Create an instance of key -> value representing a filter attribute action value
     * @param name - the key of the filter attribute value
     * @param value - the value of the filter attribute value
     * @return - map of filter attribute key and value or NULL if either name or value is NULL
     */
    private fun createFilterAttributeActionValue(
        name: String?,
        value: String?,
    ): Map<String, String>? {
        return when {
            name.isNotNull() && value.isNotNull() -> mapOf(name to value)
            else -> null
        }
    }

    /**
     * Creates the isSelected property
     * @param attributeValue - the cached attribute value
     * @param attributeSelectedValue - the actual selected value provided by the client
     * @return - If attributeSelectedValue != NULL then the attribute value is selected
     * If attributeSelectedValue == NULL then the attribute value is selected only if the attributeValue is about ANY
     * In any other case the attribute value is not selected
     */
    private fun createIsSelected(
        attributeValue: CategoryAttributesCache.Data.Value,
        attributeSelectedValue: String?,
    ): Boolean {
        return when {
            attributeSelectedValue.isNotNull() -> attributeValue.value == attributeSelectedValue
            else -> attributeValue.isAny()
        }
    }

    /**
     * Create Filter attribute card selectedValues list
     * @param attributeValue - the attribute value
     * @param rootAttributeSelectedValue - the root attribute selected value
     * @param rootFilterAttributeSelectedValueLabel - the root attribute selected value label
     * @return - the list with selected value/values or NULL in case of ANY attribute value
     */
    private fun createFilterAttributeCardSelectedValues(
        attributeValue: CategoryAttributesCache.Data.Value,
        rootAttributeSelectedValue: String?,
        rootFilterAttributeSelectedValueLabel: String?,
    ): List<Map<String, String>>? {
        return when {
            attributeValue.isAny() -> null
            else -> listOfNotNull(
                createFilterAttributeActionValue(rootAttributeSelectedValue, rootFilterAttributeSelectedValueLabel),
                createFilterAttributeActionValue(attributeValue.value, attributeValue.label),
            )
        }
    }

    /**
     * Gets the applicable search row and anything else to go before the main content
     * In the case of dog breeds, this means a search bar, and popular breeds sections, with titles
     */
    fun getSearchAndAdditionalRows(attributeName: String?): List<RowLayout<UiItem>> {
        val additionalRows = mutableListOf<RowLayout<UiItem>>()
        when (attributeName) {
            DOG_BREED_ATTRIBUTE_NAME -> {
                additionalRows.add(createSearchRow(DOG_BREED_SEARCH_PLACEHOLDER))
                additionalRows.add(createAttributeTitleRow(ALL_BREEDS_TITLE))
            }
        }

        return additionalRows
    }

    private fun createSearchRow(
        placeholderText: String,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.FILTER_ATTRIBUTE_SEARCH_ROW,
            data = listOf(
                FilterAttributeSearchCardDto(
                    placeholderText = placeholderText,
                ),
            ),
        )
    }

    private fun createAttributeTitleRow(title: String): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.TITLE_ROW,
            data = listOf(
                titleFactory.buildLeftTitleCard(
                    titleText = title,
                    titleSize = TitleCardDto.Size.SMALL,
                ),
            ),
        )
    }
}
