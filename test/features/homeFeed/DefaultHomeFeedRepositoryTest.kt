package features.homeFeed

import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.api.capi.models.SavedSearchStatus
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.categories.CategoryAnalyticsProvider
import com.gumtree.mobile.features.homeFeed.DefaultHomeFeedRepository
import com.gumtree.mobile.features.homeFeed.HomeFeedAdvertsFactory
import com.gumtree.mobile.features.homeFeed.HomeFeedAdvertsProvider
import com.gumtree.mobile.features.homeFeed.HomeFeedCarouselProvider
import com.gumtree.mobile.features.homeFeed.HomeFeedChipsFactory
import com.gumtree.mobile.features.homeFeed.HomeFeedListingsMapper
import com.gumtree.mobile.features.homeFeed.HomeFeedListingsService
import com.gumtree.mobile.features.homeFeed.HomeFeedPapiQueryParamsFactory
import com.gumtree.mobile.features.homeFeed.HomeFeedSavedSearchesMapper
import com.gumtree.mobile.features.homeFeed.HomeFeedSavedSearchesService
import com.gumtree.mobile.features.homeFeed.HomeFeedStickyBarProvider
import com.gumtree.mobile.features.homeFeed.HomeFeedTopBarProvider
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.InfinitePageCalculator
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.factories.CategoryCardFactory
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.providers.TodayPicksProvider
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.StickyBar
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import io.ktor.http.Headers
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiSavedSearchesFactory
import tools.rawDataFactory.RawHomeFeedAdsFactory
import utils.TestDispatcherProvider


/**
 * See the BFF Wiki https://github.com/gumtree-tech/mobile-apps-bff/wiki/HomeFeed-screen-adverts-UI-Configurations#landscape-orientation
 */
class DefaultHomeFeedRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return today picks row`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            When { getHomeFeedTodayPicksRow("0") }
            Then { checkTodayPicksRowIsNotNull() }
        }
    }

    @Test
    fun `should NOT return today picks row on page 1, 2, 3 etc`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            When { getHomeFeedTodayPicksRow("1") }
            Then { checkTodayPicksRowIsNull() }

            When { getHomeFeedTodayPicksRow("2") }
            Then { checkTodayPicksRowIsNull() }

            When { getHomeFeedTodayPicksRow("3") }
            Then { checkTodayPicksRowIsNull() }
        }
    }

    @Test
    fun `should return home feed saved searches on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawSavedSearches(15, listOf(SavedSearchStatus.ACTIVE)) }
            When { getHomeFeedSavedSearchesRow() }
            Then { checkHomeFeedSavedSearchesRowIsNotNull() }
        }
    }

    @Test
    fun `should NOT return home feed saved searches on page 0 when NO email header`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("0") }
            Given { stubCapiUnAuthHeaders() }
            When { getHomeFeedSavedSearchesRow() }
            Then { checkHomeFeedSavedSearchesRowIsNull() }
        }
    }

    @Test
    fun `should NOT return home feed saved searches on page 0 when NO token header`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubPage("0") }
            Given { stubCapiUnAuthHeaders() }
            When { getHomeFeedSavedSearchesRow() }
            Then { checkHomeFeedSavedSearchesRowIsNull() }
        }
    }

    @Test
    fun `should NOT return home feed saved searches on page 1, 2, 3 etc`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("1") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            When { getHomeFeedSavedSearchesRow() }
            Then { checkHomeFeedSavedSearchesRowIsNull() }

            Given { stubPage("2") }
            When { getHomeFeedSavedSearchesRow() }
            Then { checkHomeFeedSavedSearchesRowIsNull() }

            Given { stubPage("3") }
            When { getHomeFeedSavedSearchesRow() }
            Then { checkHomeFeedSavedSearchesRowIsNull() }
        }
    }

    @Test
    fun `should return home feed non sticky sticky bar on page 0 with chips row when NOT in categories experiment`() =
        runTest {
            runUnitTest(robot) {
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.TEN) }
                Given { stubPage("0") }
                Given { stubCategoryExperimentVariant(Variant.A) }
                When { getHomeFeedStickyBar() }
                Then { checkStickyBarIsNotNull() }
                Then { checkStickyBarScrollingBehaviour(null) }
                Then { checkStickyBarChipsSize(9) }
                Then { checkStickyBarRowLayoutTypeAtPosition(0, RowLayoutType.CHIPS_ROW) }
            }
        }

    @Test
    fun `should NOT return home feed sticky bar on page 1, 2, 3 etc`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }

            Given { stubPage("1") }
            When { getHomeFeedStickyBar() }
            Then { checkStickyBarIsNull() }

            Given { stubPage("2") }
            When { getHomeFeedStickyBar() }
            Then { checkStickyBarIsNull() }

            Given { stubPage("3") }
            When { getHomeFeedStickyBar() }
            Then { checkStickyBarIsNull() }
        }
    }

    @Test
    fun `should return home feed screen with sticky bar and nextPage on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("0") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawSavedSearches(8, listOf(SavedSearchStatus.ACTIVE)) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkScreenResponseStickyBarIsNotNull() }
            Then { checkNextPage("page=1&size=20&locationId=${DataFactory.SOME_AD_LOCATION_ID}&userId=${DataFactory.SOME_USER_ID}&locationType=LOCATION") }
        }
    }

    @Test
    fun `should NOT call HomeTopBarProvider on page 1, 2, 3 etc`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }

            Given { stubPage("1") }
            When { readScreen() }
            Then { checkTopBarProviderNotCalled() }

            Given { stubPage("2") }
            When { readScreen() }
            Then { checkTopBarProviderNotCalled() }

            Given { stubPage("3") }
            When { readScreen() }
            Then { checkTopBarProviderNotCalled() }
        }
    }

    @Test
    fun `should call HomeFeedTopBarProvider on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubLocationData(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubCategoryExperimentVariant(Variant.C) }
            Given { stubCallHeader("Experiments", "GTB-263.C") }
            When { readScreen() }
            Then { checkTopBarProviderCalled() }
        }
    }

    @Test
    fun `should return home feed screen with nextPage and without sticky bar on page 1`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("1") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkScreenResponseStickyBarIsNull() }
            Then { checkNextPage("page=2&size=20&locationId=${DataFactory.SOME_AD_LOCATION_ID}&userId=${DataFactory.SOME_USER_ID}&locationType=LOCATION") }
        }
    }

    @Test
    fun `should return home feed screen with empty portrait data, without landscape data, without nextPage and without sticky bar on page 5`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
                Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
                Given { stubPage("5") }
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.TEN) }
                Given { stubUserId(DataFactory.SOME_USER_ID) }
                Given { stubRawHomeFeedListings(0) }
                When { readScreen() }
                Then { checkScreenResponseStickyBarIsNull() }
                Then { checkPortraitScreenResponseListingsDataSize(0) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
                Then { checkNextPage(null) }
            }
        }

    @Test
    fun `should return the screen rows on the expected positions on page 0 when user authenticated in PORTRAIT orientation`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
                Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
                Given { stubPage("0") }
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.TEN) }
                Given { stubUserId(DataFactory.SOME_USER_ID) }
                Given { stubRawSavedSearches(8, listOf(SavedSearchStatus.ACTIVE)) }
                Given { stubRawHomeFeedListings(20) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.HOME_FEED_SAVED_SEARCHES_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.TODAY_PICKS_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(12, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(13, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(14, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(15) }
            }
        }

    @Test
    fun `should return the screen rows on the expected positions on page 0 when user NOT authenticated in PORTRAIT orientation`() =
        runTest {
            runUnitTest(robot) {
                Given { stubPage("0") }
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.TEN) }
                Given { stubUserId(DataFactory.SOME_USER_ID) }
                Given { stubRawSavedSearches(8, listOf(SavedSearchStatus.ACTIVE)) }
                Given { stubRawHomeFeedListings(20) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.TODAY_PICKS_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(12, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(13, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(14) }
            }
        }

    @Test
    fun `should return the screen rows on the expected positions on page 1 in PORTRAIT orientation`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("1") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.ADVERTISING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(12) }
        }
    }

    @Test
    fun `should return the screen rows on the expected positions on page 2 in PORTRAIT orientation`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("2") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.ADVERTISING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(12) }
        }
    }

    @Test
    fun `should return the screen rows on the expected positions on page 0 when user authenticated in LANDSCAPE orientation`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
                Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
                Given { stubPage("0") }
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.TEN) }
                Given { stubUserId(DataFactory.SOME_USER_ID) }
                Given { stubRawSavedSearches(8, listOf(SavedSearchStatus.ACTIVE)) }
                Given { stubRawHomeFeedListings(20) }
                When { readScreen() }
                Then { checkLandscapeScreenResponseRowAtPosition(0, RowLayoutType.ADVERTISING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(1, RowLayoutType.HOME_FEED_SAVED_SEARCHES_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(2, RowLayoutType.TODAY_PICKS_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(8, RowLayoutType.ADVERTISING_ROW) }
                Then { checkLandscapeScreenResponseListingsDataSize(9) }
            }
        }

    @Test
    fun `should return the screen rows on the expected positions on page 0 when user NOT authenticated in LANDSCAPE orientation`() =
        runTest {
            runUnitTest(robot) {
                Given { stubPage("0") }
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.TEN) }
                Given { stubUserId(DataFactory.SOME_USER_ID) }
                Given { stubRawSavedSearches(8, listOf(SavedSearchStatus.ACTIVE)) }
                Given { stubRawHomeFeedListings(20) }
                When { readScreen() }
                Then { checkLandscapeScreenResponseRowAtPosition(0, RowLayoutType.ADVERTISING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(1, RowLayoutType.TODAY_PICKS_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
                Then { checkLandscapeScreenResponseRowAtPosition(7, RowLayoutType.ADVERTISING_ROW) }
                Then { checkLandscapeScreenResponseListingsDataSize(8) }
            }
        }

    @Test
    fun `should return the screen rows on the expected positions on page 1 in LANDSCAPE orientation`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("1") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkLandscapeScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
            Then { checkLandscapeScreenResponseListingsDataSize(6) }
        }
    }

    @Test
    fun `should return the screen rows on the expected positions on page 2 in LANDSCAPE orientation`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("2") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkLandscapeScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
            Then { checkLandscapeScreenResponseListingsDataSize(6) }
        }
    }

    @Test
    fun `should return the screen rows on the expected positions on page 3 in LANDSCAPE orientation`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("3") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkLandscapeScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
            Then { checkLandscapeScreenResponseListingsDataSize(6) }
        }
    }

    @Test
    fun `should return the screen rows on the expected positions on page 4 in LANDSCAPE orientation`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubCallHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            Given { stubPage("4") }
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubRawHomeFeedListings(20) }
            When { readScreen() }
            Then { checkLandscapeScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkLandscapeScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
            Then { checkLandscapeScreenResponseListingsDataSize(6) }
        }
    }

    @Test
    fun `should NOT return home feed listings`() = runTest {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.TEN) }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPage("5") }
            Given { stubRawHomeFeedListings(0) }
            When { getHomeFeedListingRows() }
            Then { checkListingsPortraitDataSize(0) }
            Then { checkListingsLandscapeDataSize(null) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualScreenResponseResult: ScreenResponse
        private lateinit var actualHomeFeedListingsRowsResult: Pair<PortraitData, LandscapeData?>
        private var actualTodayPicksRowResult: RowLayout<UiItem>? = null
        private var actualSavedSearchesRowResult: RowLayout<UiItem>? = null
        private var actualStickyBarResult: StickyBar? = null

        private val homeFeedListingsService: HomeFeedListingsService = mockk(relaxed = true)
        private val homeFeedSavedSearchesService: HomeFeedSavedSearchesService = mockk(relaxed = true)
        private val papiHeadersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val capiHeadersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)
        private val capiAuthHeaders: ApiHeaders = mockk(relaxed = true)
        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val rawLocationFetcher: RawLocationFetcher = mockk(relaxed = true)

        private val homeFeedPapiQueryParamsFactory = HomeFeedPapiQueryParamsFactory()
        private val pageCalculator = InfinitePageCalculator()
        private val homeFeedSavedSearchesMapper =
            HomeFeedSavedSearchesMapper(locationsApi = locationsApi) // todo: is there a better way to do this? could we just mock the mapper here instead
        private val homeFeedListingsMapper = HomeFeedListingsMapper(analyticsProvider = mockk(relaxed = true))
        private val todayPicksProvider = TodayPicksProvider(rawLocationFetcher)
        private val homeFeedAdvertsProvider = HomeFeedAdvertsProvider(HomeFeedAdvertsFactory(), GAMAdvertUtils())
        private val categoryAnalyticsProvider = CategoryAnalyticsProvider()
        private val homeFeedStickyBarProvider = HomeFeedStickyBarProvider(
            homeFeedChipsFactory = HomeFeedChipsFactory(analyticsProvider = categoryAnalyticsProvider),
        )

        private val homeFeedTopBarProvider: HomeFeedTopBarProvider = mockk(relaxed = true)

        private lateinit var locationId: String
        private lateinit var locationType: LocationType
        private lateinit var locationName: String
        private lateinit var distance: Distance
        private var userId: String? = null
        private var latitude: String? = null
        private var longitude: String? = null
        private lateinit var page: String
        private var categoriesVariant: Variant? = null

        private lateinit var testSubject: DefaultHomeFeedRepository

        override fun setup() {
            testSubject = spyk(
                DefaultHomeFeedRepository(
                    homeFeedListingsService,
                    homeFeedListingsMapper,
                    homeFeedSavedSearchesService,
                    homeFeedSavedSearchesMapper,
                    homeFeedPapiQueryParamsFactory,
                    pageCalculator,
                    todayPicksProvider,
                    homeFeedStickyBarProvider,
                    homeFeedAdvertsProvider,
                    papiHeadersProvider,
                    capiHeadersProvider,
                    rawLocationFetcher,
                    TestDispatcherProvider(),
                    homeFeedTopBarProvider,
                )
            )
        }

        fun stubCapiUnAuthHeaders() {
            every { capiHeadersProvider.createUnAuthorisedHeaders(callHeaders) } returns capiAuthHeaders
        }

        fun stubCallHeader(
            headerKey: String,
            headerValue: String?,
        ) {
            every { callHeaders[headerKey] } returns headerValue
        }

        fun stubRawHomeFeedListings(number: Int) {
            val rawListingAds = RawHomeFeedAdsFactory.createHomeFeedRawPapiAdList(number)
            coEvery { homeFeedListingsService.getUserHomeFeedListings(any(), any()) } returns rawListingAds
        }

        fun stubLocationId(locationId: String) {
            this.locationId = locationId
        }

        fun stubLocationType(locationType: LocationType) {
            this.locationType = locationType
        }

        fun stubLocationName(locationName: String) {
            this.locationName = locationName
        }

        fun stubLocationData(locationName: String) {
            coEvery { rawLocationFetcher.fetchByLocationIdAndType(any(), any()) } returns RawLocation(
                123,
                locationName,
                type = RawLocation.Type.location
            )
        }

        fun stubDistance(distance: Distance) {
            this.distance = distance
        }

        fun stubUserId(userId: String?) {
            this.userId = userId
        }

        fun stubPage(page: String) {
            this.page = page
        }

        fun stubRawSavedSearches(
            number: Int,
            savedSearchStatuses: List<SavedSearchStatus> = emptyList(),
        ) {
            val rawSavedSearches = RawCapiSavedSearchesFactory.createRawSavedSearchList(number, savedSearchStatuses)
            coEvery { homeFeedSavedSearchesService.getUserSavedSearches(any(), any(), any()) } returns rawSavedSearches
        }

        fun stubCategoryExperimentVariant(variant: Variant?) {
            categoriesVariant = variant
        }

        suspend fun readScreen() {
            actualScreenResponseResult = testSubject.readScreen(
                callHeaders,
                locationId,
                locationType,
                distance,
                userId,
                page,
                DEFAULT_PAGE_SIZE,
            )
        }

        suspend fun getHomeFeedListingRows() {
            actualHomeFeedListingsRowsResult = testSubject.getHomeFeedListingRows(
                callHeaders,
                locationId,
                userId,
                latitude,
                longitude,
                page,
                DEFAULT_PAGE_SIZE,
            )
        }

        suspend fun getHomeFeedTodayPicksRow(page: String) {
            actualTodayPicksRowResult = testSubject.getHomeFeedTodayPicksRow(page, locationId, locationType, distance)
        }

        suspend fun getHomeFeedSavedSearchesRow() {
            actualSavedSearchesRowResult = testSubject.getHomeFeedSavedSearchesRow(page, callHeaders)
        }

        fun getHomeFeedStickyBar() {
            actualStickyBarResult = testSubject.getHomeFeedStickyBar(
                page = page,
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            )
        }

        fun checkHomeFeedSavedSearchesRowIsNotNull() {
            assertNotNull(actualSavedSearchesRowResult)
        }

        fun checkHomeFeedSavedSearchesRowIsNull() {
            assertNull(actualSavedSearchesRowResult)
        }

        fun checkListingsPortraitDataSize(expected: Int) {
            assertEquals(expected, actualHomeFeedListingsRowsResult.first.size)
        }

        fun checkListingsLandscapeDataSize(expected: Int?) {
            assertEquals(expected, actualHomeFeedListingsRowsResult.second?.size)
        }

        fun checkNextPage(expected: String?) {
            assertEquals(expected, actualScreenResponseResult.nextPage)
        }

        fun checkStickyBarIsNotNull() {
            assertNotNull(actualStickyBarResult)
        }

        fun checkStickyBarIsNull() {
            assertNull(actualStickyBarResult)
        }

        fun checkStickyBarChipsSize(expected: Int) {
            assertEquals(expected, actualStickyBarResult?.get(0)?.data?.size)
        }

        fun checkStickyBarScrollingBehaviour(expected: ScrollingCollapseBehaviour?) {
            assertEquals(expected, actualStickyBarResult?.get(0)?.scrollingBehaviour)
        }

        fun checkStickyBarRowLayoutTypeAtPosition(
            position: Int,
            expected: RowLayoutType,
        ) {
            assertEquals(expected, actualStickyBarResult?.get(position)?.type)
        }

        fun checkTodayPicksRowIsNull() {
            assertNull(actualTodayPicksRowResult)
        }

        fun checkTodayPicksRowIsNotNull() {
            assertNotNull(actualTodayPicksRowResult)
        }

        fun checkScreenResponseStickyBarIsNotNull() {
            assertNotNull(actualScreenResponseResult.stickyBar)
        }

        fun checkScreenResponseStickyBarIsNull() {
            assertNull(actualScreenResponseResult.stickyBar)
        }

        fun checkPortraitScreenResponseListingsDataSize(expected: Int) {
            assertEquals(expected, actualScreenResponseResult.portraitData.size)
        }

        fun checkLandscapeScreenResponseListingsDataSize(expected: Int?) {
            assertEquals(expected, actualScreenResponseResult.landscapeData?.size)
        }

        fun checkPortraitScreenResponseRowAtPosition(
            position: Int,
            expectedType: RowLayoutType,
        ) {
            assertEquals(expectedType, actualScreenResponseResult.portraitData[position].type)
        }

        fun checkLandscapeScreenResponseRowAtPosition(
            position: Int,
            expectedType: RowLayoutType,
        ) {
            assertEquals(expectedType, actualScreenResponseResult.landscapeData?.get(position)?.type)
        }

        fun checkTopBarProviderCalled() {
            verify {
                homeFeedTopBarProvider.createHomeFeedTopBar(
                    locationId = locationId,
                    locationType = locationType,
                    locationName = locationName,
                    distance = distance,
                    categoriesExperimentVariant = categoriesVariant,
                )
            }
        }

        fun checkTopBarProviderNotCalled() {
            verify(exactly = 0) {
                homeFeedTopBarProvider.createHomeFeedTopBar(
                    locationId = any(),
                    locationType = any(),
                    locationName = any(),
                    distance = any(),
                    categoriesExperimentVariant = any(),
                )
            }
        }
    }
}