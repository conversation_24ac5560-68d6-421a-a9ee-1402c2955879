package com.gumtree.mobile.responses

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.layoutsData.EmptyCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.ViewMoreCardDto
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.utils.extensions.thisOrNullIfEmpty

/**
 * Wrap the screen data with the necessary Rows (see RowLayoutType for all row types)
 * @param gridSizes - the GridSizes where the supported Orientations should be specified
 * @param layoutType - the Layout type of the Rows (if not specified will be LISTING_ROW)
 * @param bottomDivider - indicates when the RowLayout needs bottom divider
 * @return - A Pair of lists (portrait and landscape) with all Row types ready-made for the screen response.
 * The second item in the Pair (the landscape data) could be NULL if gridSizes.getLandscapeGridSize() is 0
 */

inline fun <reified T : UiItem> List<T>.wrapScreenDataWithRowLayouts(
    gridSizes: GridSizes,
    layoutType: RowLayoutType = RowLayoutType.LISTING_ROW,
    bottomDivider: Boolean? = null,
    style: Style? = null,
): Pair<List<RowLayout<T>>, List<RowLayout<T>>?> {
    val portraitRowGridLayoutSize = gridSizes.getPortraitGridSize()
    val landscapeRowGridLayoutSize = gridSizes.getLandscapeGridSize()
    val portraitResult = mutableMapOf<Int, RowLayout<T>>()
    val landscapeResult = mutableMapOf<Int, RowLayout<T>>()
    val defaultRow = RowLayout(
        type = layoutType,
        data = emptyList<T>(),
        bottomDivider = bottomDivider,
        style = style,
    )

    var portraitCurrentRowIndex = ZERO
    var landscapeCurrentRowIndex = ZERO
    this.forEachIndexed { index, uiItem ->
        if (portraitRowGridLayoutSize > ZERO) {
            if (index % portraitRowGridLayoutSize == ZERO) {
                portraitCurrentRowIndex++
            }
            val portraitCurrentRow = portraitResult.getOrDefault(portraitCurrentRowIndex, defaultRow)
            val reminder = if (size < portraitRowGridLayoutSize) {
                portraitRowGridLayoutSize - size
            } else {
                (portraitRowGridLayoutSize * portraitCurrentRowIndex) % size
            }

            portraitResult[portraitCurrentRowIndex] = portraitCurrentRow.copy(
                data = portraitCurrentRow.data + uiItem,
            ).addEmptyUiItemsIfRequired(
                isLastUiItem = index == size - 1,
                reminder = reminder,
            )
        }

        if (landscapeRowGridLayoutSize > ZERO) {
            if (index % landscapeRowGridLayoutSize == ZERO) {
                landscapeCurrentRowIndex++
            }
            val landscapeCurrentRow = landscapeResult.getOrDefault(landscapeCurrentRowIndex, defaultRow)
            val reminder = if (size < landscapeRowGridLayoutSize) {
                landscapeRowGridLayoutSize - size
            } else {
                (landscapeRowGridLayoutSize * landscapeCurrentRowIndex) % size
            }

            landscapeResult[landscapeCurrentRowIndex] = landscapeCurrentRow.copy(
                data = landscapeCurrentRow.data + uiItem,
            ).addEmptyUiItemsIfRequired(
                isLastUiItem = index == size - 1,
                reminder = reminder,
            )
        }
    }

    return Pair(
        portraitResult.values.toList(),
        if (landscapeResult.values.isNotEmpty()) landscapeResult.values.toList() else null, // we shouldn't return Landscape list if empty
    )
}

/**
 * Append single RowLayout into portrait and landscape data lists. It will append the RowLayout only if NOT null
 * @param position - the position where the RowLayout item should be added
 * @param rowLayout - the RowLayout item
 * @return - A new Pair of portrait and landscape data lists with the appended RowLayout if RowLayout is NOT null,
 * otherwise it'll return the same Pair of portrait and landscape data
 */
inline fun <reified T : UiItem> Pair<List<RowLayout<T>>, List<RowLayout<T>>?>.appendRowLayoutAtPosition(
    position: Int,
    rowLayout: RowLayout<T>?,
): Pair<List<RowLayout<T>>, List<RowLayout<T>>?> {
    return rowLayout?.let {
        val newPortraitData = first.toMutableList()
        val newLandscapeData = second?.toMutableList()
        newPortraitData
            // append RowLayout at position only if the list is NOT empty and has more items than the position of the RowLayout
            .takeIf { newPortraitData.isNotEmpty() && newPortraitData.size >= position }
            ?.add(position, it)
        newLandscapeData
            // append RowLayout at position only if the list is NOT empty and has more items than the position of the RowLayout
            ?.takeIf { newLandscapeData.isNotEmpty() && newLandscapeData.size >= position }
            ?.add(position, it)
        Pair(
            first = newPortraitData,
            second = newLandscapeData,
        )
    } ?: this
}

/**
 * If list with UiItems has more items than the allowed limit, it will append ViewMoreCard UiItem to the end of list and will filter all items after the limit
 * @param limit - the limit of the list
 * @param viewMoreText - the ViewMoreCard text
 * @param viewMoreDestination - the ViewMoreCard destination
 * @return - A new list with the appended ViewMoreCard UiItem to the end if the original list has more UiItems than the limit,
 * otherwise it'll return the same (original) list and ViewMoreCard UiItem isn't going to be appended
 */
fun List<UiItem>?.appendViewMoreCardIfRequired(
    limit: Int,
    viewMoreText: String,
    viewMoreDestination: DestinationDto,
): List<UiItem>? {
    return this
        ?.takeIf { size > limit }
        ?.take(limit)
        ?.toMutableList()
        ?.apply {
            add(
                ViewMoreCardDto(
                    title = viewMoreText,
                    destination = viewMoreDestination,
                ),
            )
        }
        ?: this?.thisOrNullIfEmpty()
}

/**
 * The method adds EmptyCardDto/EmptyCardDtos to the RowLayout (this) data list if required.
 * There are cases/screens where the mobile apps need to render GridView items,
 * but the number of items is not filling the entire GridView well (usually the last row of the GridView is not full of items).
 * In such cases the mobile apps need to use EmptyCardDto UiItems to fill the GridView well.
 * For example if the last RowLayout of a GridView contains only one UiItem, but the GridView is meant to have two columns (two UiItems per RowLayout),
 * we need to add one EmptyCardDto in the RowLayout and make that GridView row full.
 * @param isLastUiItem - indicates if the entire list with UiItems comes to the end (only in this case we need to try to add EmptyCardDto)
 * @param reminder - indicates of how many EmptyCardDtos we need to add. If reminder == 0 we shouldn't add any EmptyCardDto.
 * @return - The same RowLayout (this) if isLastUiItem == false OR reminder == 0.
 *           New RowLayout instance containing the original RowLayout data + all required/necessary EmptyCardDtos to fill the lacking number of UiItem on this RowLayout
 */
inline fun <reified T : UiItem> RowLayout<T>.addEmptyUiItemsIfRequired(
    isLastUiItem: Boolean,
    reminder: Int,
): RowLayout<T> {
    if (isLastUiItem && reminder > ZERO) {
        val emptyCard = EmptyCardDto() as T
        val emptyCardsList = List(reminder) { emptyCard }
        return copy(data = data + emptyCardsList)
    }
    return this
}
