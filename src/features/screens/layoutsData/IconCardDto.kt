package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed interface IconCardDto : UiItem {
    @SerialName("resource")
    val resource: Resource

    @SerialName("size")
    val size: Size

    @Serializable
    @SerialName("ICON_CENTER_CARD")
    data class Center(
        override val resource: Resource,
        override val size: Size,
    ) : IconCardDto

    enum class Resource {
        THUMBS_UP,
    }

    enum class Size {
        LARGE, // 128dp
        MEDIUM, // 64dp
        SMALL, // 32dp
    }
}
