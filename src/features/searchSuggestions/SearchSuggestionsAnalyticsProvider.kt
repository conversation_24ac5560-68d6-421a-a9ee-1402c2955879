package com.gumtree.mobile.features.searchSuggestions

import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider

const val ANALYTICS_SUBMIT_SUGGESTED_LISTING_SEARCH_EVENT_NAME = "submit_suggested_listing_search"
const val ANALYTICS_SUGGESTED_SUGGESTION_TYPE_VALUE = "suggested"
const val ANALYTICS_SEARCH_TERM__SEARCH_FORMAT_VALUE = "search_term"

class SearchSuggestionsAnalyticsProvider(
    commonAnalyticsProvider: CommonAnalyticsProvider,
) : CommonAnalyticsProvider by commonAnalyticsProvider {

    companion object {
        fun getSearchSuggestionParams(
            searchSuggestionCategory: String,
            searchSuggestionSearchTermSelected: String,
        ): Map<String, String> {
            return mapOf(
                AnalyticsParams.Search.CATEGORY_ID to searchSuggestionCategory,
                AnalyticsParams.Search.TERM_SELECTED to searchSuggestionSearchTermSelected,
            )
        }
    }

    fun getSubmitSuggestedListingSearchEvent(): String = ANALYTICS_SUBMIT_SUGGESTED_LISTING_SEARCH_EVENT_NAME

    fun getScreenParams(
        searchTerm: String,
    ): Map<String, String> {
        return mapOf(
            AnalyticsParams.Search.FORMAT to ANALYTICS_SEARCH_TERM__SEARCH_FORMAT_VALUE,
            AnalyticsParams.Search.TERM to searchTerm,
            AnalyticsParams.Search.SUGGESTION_TYPE to ANALYTICS_SUGGESTED_SUGGESTION_TYPE_VALUE,
        )
    }
}
