package com.gumtree.mobile.features.homeFeed

import com.gumtree.mobile.api.homeFeed.models.RawHomeFeedAdList
import com.gumtree.mobile.common.analytics.AD_PREFIX
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.layoutsData.ListingCardDto
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.wrapScreenDataWithRowLayouts
import com.gumtree.mobile.utils.extensions.data.toListingCardDto

/**
 * Mapping the HomeFeed raw data (PAPI data) into HomeFeed mobile application data
 */
class HomeFeedListingsMapper(private val analyticsProvider: HomeFeedAnalyticsProvider) {

    fun map(
        rawData: RawHomeFeedAdList,
        listingGridSizes: GridSizes,
    ): Pair<PortraitData, LandscapeData?> {
        val listWithHomeFeedCards: List<ListingCardDto> = rawData.ads.map {
            it.toListingCardDto(analyticsProvider.getClickListingEvent(AD_PREFIX, it))
        }

        return listWithHomeFeedCards.wrapScreenDataWithRowLayouts(listingGridSizes)
    }
}
