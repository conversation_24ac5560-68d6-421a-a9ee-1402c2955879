package com.gumtree.mobile.api.capi.apis

import api.capi.bodies.RawFlagAdBody
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.CapiEndpointUrls
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.PUT
import retrofit2.http.Path

interface CapiUserApi {
    @PUT(CapiEndpointUrls.FLAG_AD_ENDPOINT)
    suspend fun reportListing(
        @Path(CapiApiParams.AD_ID) adId: String,
        @Body body: RawFlagAdBody,
    ): Response<Unit>
}
