package utils.extensions.data

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.layoutsData.ChipDto
import com.gumtree.mobile.features.screens.layoutsData.IconType
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.utils.extensions.data.toVipCarChipDto
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.annotations.ParallelTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawCapiAttributeFactory

@ParallelTest
class RawCapiAdVipCarExtensionsTest {

    private val robot = Robot()

    @ParameterizedTest
    @CsvSource(
        "${RawCapiAd.ATTRIBUTE_VEHICLE_REGISTRATION}, ${DataFactory.SOME_VEHICLE_REG_YEAR}, CALENDAR",
        "${RawCapiAd.ATTRIBUTE_VEHICLE_MILEAGE}, ${DataFactory.SOME_VEHICLE_MILLAGE}, SPEEDO",
        "${RawCapiAd.ATTRIBUTE_VEHICLE_TRANSMISSION}, ${DataFactory.SOME_VEHICLE_TRANSMISSION}, GEARBOX",
        "${RawCapiAd.ATTRIBUTE_VEHICLE_FUEL}, ${DataFactory.SOME_VEHICLE_FUEL_TYPE}, FUELPUMP",
        "${RawCapiAd.ATTRIBUTE_SELLER_TYPE}, ${VipScreenUiConfiguration.TRADE_SELLER}, GARAGE",
    )
    fun `should return VIP car attribute chip`(attributeName: String, attributeLabel: String, attributeIcon: IconType) {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    RawCapiAdsFactory.createRawCapiAd(
                        attributes = listOf(
                            RawCapiAttributeFactory.createRawCapiAttribute(
                                name = attributeName,
                                label = attributeLabel,
                            ),
                        ),
                    )
                )
            }
            When { toVipCarChipDto(attributeName, attributeIcon) }
            Then { checkChipDto(ChipDto.Attribute(title = attributeLabel.uppercase(), iconType = attributeIcon)) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "${RawCapiAd.ATTRIBUTE_VEHICLE_REGISTRATION}, ${DataFactory.SOME_VEHICLE_REG_YEAR}, CALENDAR",
        "${RawCapiAd.ATTRIBUTE_VEHICLE_MILEAGE}, ${DataFactory.SOME_VEHICLE_MILLAGE}, SPEEDO",
        "${RawCapiAd.ATTRIBUTE_VEHICLE_TRANSMISSION}, ${DataFactory.SOME_VEHICLE_TRANSMISSION}, GEARBOX",
        "${RawCapiAd.ATTRIBUTE_VEHICLE_FUEL}, ${DataFactory.SOME_VEHICLE_FUEL_TYPE}, FUELPUMP",
        "${RawCapiAd.ATTRIBUTE_SELLER_TYPE}, ${VipScreenUiConfiguration.TRADE_SELLER}, GARAGE",
    )
    fun `should NOT return VIP car attribute chip`(attributeName: String, attributeLabel: String, attributeIcon: IconType) {
        runUnitTest(robot) {
            Given { stubRawAd(RawCapiAdsFactory.createRawCapiAd()) }
            When { toVipCarChipDto(attributeName, attributeIcon) }
            Then { checkChipDto(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualChipResult: ChipDto? = null

        private lateinit var testSubject: RawCapiAd

        fun stubRawAd(adSlot: RawCapiAd) {
            testSubject = adSlot
        }

        fun toVipCarChipDto(attributeName: String, attributeIcon: IconType) {
            actualChipResult = testSubject.toVipCarChipDto(attributeName, attributeIcon)
        }

        fun checkChipDto(expected: ChipDto.Attribute?) {
            assertEquals(expected, actualChipResult)
        }
    }
}