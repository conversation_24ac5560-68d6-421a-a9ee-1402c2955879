package features.vip.v2

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdSlot
import api.capi.models.RawCapiContactMethod
import api.phoneNumber.api.PhoneNumberApi
import api.similarItems.api.SimilarItemsApi
import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.capi.apis.CapiAdsApi
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.capi.apis.CapiReplyApi
import com.gumtree.mobile.api.capi.models.RawCapiAdLink
import com.gumtree.mobile.api.capi.models.RawCapiAddress
import com.gumtree.mobile.api.capi.models.RawSkillList
import com.gumtree.mobile.api.common.UserProfileService
import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.locations.models.RawLocationSuggestions
import com.gumtree.mobile.api.papi.PapiHeadersProvider
import com.gumtree.mobile.api.partnerships.api.PartnershipAdsApi
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import com.gumtree.mobile.api.userProfile.api.UserProfileApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.factories.SpaceRowFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.ExternalPartnershipAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.FavoriteToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.OtherInfoCardDto
import com.gumtree.mobile.features.screens.layoutsData.ReportAdToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ShareToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipDescriptionCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipLocationCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipMapCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipPriceCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipSellerSkillsCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipTitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.OverviewCardDto
import com.gumtree.mobile.features.screens.layoutsData.OverviewCardData
import com.gumtree.mobile.features.screens.layoutsData.PortfolioImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.PostPublisherInfoCardDto
import com.gumtree.mobile.features.screens.layoutsData.PostPublisherInfoCardData
import com.gumtree.mobile.features.screens.layoutsData.VipSectionTabsCardDto
import com.gumtree.mobile.features.screens.layoutsData.DividerCardDto
import com.gumtree.mobile.features.screens.layoutsData.OverviewCardItemDto
import com.gumtree.mobile.features.screens.layoutsData.PoweredByCardDto
import com.gumtree.mobile.features.screens.layoutsData.RatingCardDto
import com.gumtree.mobile.features.screens.layoutsData.TabItem
import com.gumtree.mobile.features.sellerProfile.v2.DefaultSellerProfileActiveStatusFetcher
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.features.vip.v2.DefaultVipRepository
import com.gumtree.mobile.features.vip.v2.OtherInfoMapper
import com.gumtree.mobile.features.vip.v2.StaticMapFactory
import com.gumtree.mobile.features.vip.v2.VipAdjustTrackingDataProvider
import com.gumtree.mobile.features.vip.v2.VipAdvertsFactory
import com.gumtree.mobile.features.vip.v2.VipAdvertsProvider
import com.gumtree.mobile.features.vip.v2.VipAnalyticsProvider
import com.gumtree.mobile.features.vip.v2.VipBottomOverlayUIProvider
import com.gumtree.mobile.features.vip.v2.VipDefaultsUIProvider
import com.gumtree.mobile.features.vip.v2.VipDescriptionMapper
import com.gumtree.mobile.features.vip.v2.VipHpiFactory
import com.gumtree.mobile.features.vip.v2.VipImageGalleryMapper
import com.gumtree.mobile.features.vip.v2.VipLocationMapper
import com.gumtree.mobile.features.vip.v2.VipMapMapper
import com.gumtree.mobile.features.vip.v2.VipPartnershipAdvertsFactory
import com.gumtree.mobile.features.vip.v2.VipPartnershipAnalyticsProvider
import com.gumtree.mobile.features.vip.v2.VipPostedSinceMapper
import com.gumtree.mobile.features.vip.v2.VipPriceMapper
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration.VIP_POSTED_SINCE_DATE_FORMAT
import com.gumtree.mobile.features.vip.v2.VipSellerProfileMapper
import com.gumtree.mobile.features.vip.v2.VipService
import com.gumtree.mobile.features.vip.v2.VipSimilarItemsMapper
import com.gumtree.mobile.features.vip.v2.VipSpecificationFactory
import com.gumtree.mobile.features.vip.v2.VipTimeAgoFormatter
import com.gumtree.mobile.features.vip.v2.VipTitleMapper
import com.gumtree.mobile.features.vip.v2.VipToolbarActionsProvider
import com.gumtree.mobile.features.vip.v2.VipUiOptimizeMapper
import com.gumtree.mobile.features.vip.v2.VipUiOptimizeProvider
import com.gumtree.mobile.features.vip.v2.VipVerticalsUIProvider
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.responses.BottomOverlay
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import com.gumtree.mobile.utils.extensions.data.MAP_RADIUS_IN_METERS
import io.ktor.http.Headers
import io.ktor.http.HeadersBuilder
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawCapiContactMethodFactory
import tools.rawDataFactory.RawLocationFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.runUnitTest
import utils.TestDispatcherProvider
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.routes.ApiHeaderParams
import tools.ClientExperimentsFactory

class DefaultVipRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return Ad image gallery row`() {
        runUnitTest(robot) {
            Given { stubRawAdImage(DataFactory.SOME_AD_IMAGE_URL) }
            When { getVipImagesRow() }
            Then { checkVipImagesRowLayoutType() }
            Then { checkVipImagesRowDataImage(DataFactory.largeSquareImages(DataFactory.SOME_BASE_IMAGE_URL)) }
        }
    }

    @Test
    fun `should return Ad image gallery row with empty data`() {
        runUnitTest(robot) {
            Given { stubRawAdImage(null) }
            When { getVipImagesRow() }
            Then { checkVipImagesRowIsEmpty() }
        }
    }

    @Test
    fun `should return Ad title row`() {
        runUnitTest(robot) {
            Given { stubRawAdTitle(DataFactory.SOME_AD_TITLE) }
            When { getVipTitleRow() }
            Then { checkVipTitleRowLayoutType() }
            Then { checkVipTitleRowDataText(DataFactory.SOME_AD_TITLE) }
        }
    }

    @Test
    fun `should return Ad price row`() {
        runUnitTest(robot) {
            Given { stubRawAdPrice(DataFactory.SOME_AD_PRICE) }
            When { getVipPriceRow() }
            Then { checkVipPriceRowLayoutType() }
            Then { checkVipPriceRowDataText("£${DataFactory.SOME_AD_PRICE}") }
        }
    }

    @Test
    fun `should NOT return Ad price row`() {
        runUnitTest(robot) {
            Given { stubRawAdPrice("09sds") }
            When { getVipPriceRow() }
            Then { checkVipPriceRowIsNull() }
        }
    }

    @Test
    fun `should return Ad location row`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowLayoutType() }
            Then { checkVipLocationRowDataLocationText("Manchester") }
            Then { checkVipLocationRowDataLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkVipLocationRowDataLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkVipLocationRowDataRadius(MAP_RADIUS_IN_METERS) }
        }
    }

    @Test
    fun `should NOT return Ad location row if city is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = null,
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Ad location row if latitude or longitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = null,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowIsNull() }

            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = null,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipLocationRow() }
            Then { checkVipLocationRowIsNull() }
        }
    }

    @Test
    fun `should return Ad description row`() {
        runUnitTest(robot) {
            Given { stubRawAdDescription(DataFactory.SOME_AD_DESCRIPTION) }
            When { getVipDescriptionRow() }
            Then { checkVipDescriptionRowLayoutType() }
            Then { checkVipDescriptionRowDataLabel(VipScreenUiConfiguration.DESCRIPTION_LABEL_TEXT) }
            Then { checkVipDescriptionRowDataText(DataFactory.SOME_AD_DESCRIPTION) }
        }
    }

    @Test
    fun `should return Ad posted since row`() {
        runUnitTest(robot) {
            Given { stubRawAdAge(DataFactory.SOME_AD_START_DATE_TIME) }
            When { getVipPostedSinceRow() }
            Then { checkVipPostedSinceRowLayoutType() }
        }
    }

    @Test
    fun `should NOT return Ad posted since row`() {
        runUnitTest(robot) {
            Given { stubRawAdAge("2e2ee2e23") }
            When { getVipPostedSinceRow() }
            Then { checkVipPostedSinceRowIsNull() }
        }
    }

    @Test
    fun `should return Ad partnership row`() {
        runUnitTest(robot) {
            Given { stubRawPartnershipData(DataFactory.SOME_AD_TITLE, DataFactory.SOME_EXTERNAL_URL) }
            When { getPartnershipRow() }
            Then { checkVipPartnershipRowLayoutType() }
            Then { checkVipPartnershipRowText(DataFactory.SOME_AD_TITLE) }
            Then { checkVipPartnershipRowWebUrl(DataFactory.SOME_EXTERNAL_URL) }
        }
    }

    @Test
    fun `should return Ad Map row`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowLayoutType() }
            Then { checkVipMapRowDataIsInteractive(false) }
            Then { checkVipMapRowDataLocationLabel("London") }
            Then { checkVipMapRowDataLocationTypeLabel(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapRowDataLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkVipMapRowDataLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkVipMapRowDataRadius(MAP_RADIUS_IN_METERS) }
        }
    }

    @Test
    fun `should NOT return Ad Map row if visibleOnMap is false`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = false.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Ad Map row if city is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = null,
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Ad Map row if latitude or longitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Bristol",
                        latitude = null,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }

            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Bristol",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = null,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getVipMapRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should return 1 Ad partnership advert row if there is 1 partnership advert slot`() {
        runUnitTest(robot) {
            Given {
                stubRawPartnershipAdvertSlots(
                    listOf(
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        }
                    )
                )
            }
            When { getVipPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkVipPartnershipAdvertRowsSize(1) }
        }
    }

    @Test
    fun `should return 2 Ad partnership advert rows if there are 2 partnership advert slots`() {
        runUnitTest(robot) {
            Given {
                stubRawPartnershipAdvertSlots(
                    listOf(
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.SOME_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.SOME_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        },
                        RawCapiAdSlot().apply {
                            vipCustomTab = RawCapiAdSlot.RawVipCustomTab().apply {
                                labelIcon = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_ICON_URL
                                labelText = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TEXT
                                targetURL = DataFactory.ANOTHER_PARTNERSHIP_ADVERT_TARGET_URL
                            }
                        }
                    )
                )
            }
            When { getVipPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkVipPartnershipAdvertRowsSize(2) }
        }
    }

    @Test
    fun `should NOT return Ad partnership advert rows if slots are empty`() {
        runUnitTest(robot) {
            Given { stubRawPartnershipAdvertSlots(emptyList()) }
            When { getVipPartnershipAdvertRows(ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION, platform = ClientPlatform.ANDROID)) }
            Then { checkVipPartnershipAdvertRowsSize(0) }
        }
    }

    @Test
    fun `should return Ad ID row`() {
        runUnitTest(robot) {
            Given { stubRawAdId(DataFactory.SOME_AD_ID) }
            When { getVipAdIdRow() }
            Then { checkVipOtherInfoRowLayoutType() }
            Then { checkVipOtherInfoRowDataText("Ad id: ${DataFactory.SOME_AD_ID}") }
        }
    }

    @Test
    fun `should return VAT row`() {
        runUnitTest(robot) {
            Given { stubRawAdVAT(DataFactory.SOME_VAT) }
            When { getVipVATRow() }
            Then { checkVipOtherInfoRowLayoutType() }
            Then { checkVipOtherInfoRowDataText("VAT Reg No: ${DataFactory.SOME_VAT}") }
        }
    }

    @Test
    fun `should return toolbar with ShareAction and ReportAdAction`() {
        runUnitTest(robot) {
            Given {
                stubRawAdIdAndShareLink(
                    DataFactory.SOME_AD_ID,
                    "https://www.gumtree.com/p/for-sale/native-indian-girl-costume/1444698872"
                )
            }
            When { getVipToolbar() }
            Then { checkVipToolbarActionsSize(2) }
            Then { checkVipToolbarActionTypeAtPosition(0, ShareToolbarActionDto::class.java) }
            Then { checkVipToolbarActionTypeAtPosition(1, ReportAdToolbarActionDto::class.java) }
        }
    }

    @Test
    fun `should return toolbar with ReportAdAction if share url is NOT found`() {
        runUnitTest(robot) {
            Given { stubRawAdIdAndShareLink(DataFactory.SOME_AD_ID, null) }
            When { getVipToolbar() }
            Then { checkVipToolbarActionsSize(1) }
            Then { checkVipToolbarActionTypeAtPosition(0, ReportAdToolbarActionDto::class.java) }
        }
    }

    @Test
    fun `should NOT include FavoriteAction in toolbar when experiment is not B variant`() {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given {
                stubRawAdIdAndShareLink(
                    DataFactory.SOME_AD_ID,
                    "https://www.gumtree.com/p/for-sale/native-indian-girl-costume/1444698872"
                )
            }
            When { getVipToolbarWithExperimentVariant(Variant.A) }
            Then { checkVipToolbarActionsSize(2) }
            Then { checkVipToolbarActionTypeAtPosition(0, ShareToolbarActionDto::class.java) }
            Then { checkVipToolbarActionTypeAtPosition(1, ReportAdToolbarActionDto::class.java) }
        }
    }

    @Test
    fun `should include FavoriteAction in toolbar when experiment is B variant and category is services`() {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given {
                stubRawAdIdAndShareLink(
                    DataFactory.SOME_AD_ID,
                    "https://www.gumtree.com/p/for-sale/native-indian-girl-costume/1444698872"
                )
            }
            When { getVipToolbarWithExperimentVariant(Variant.B) }
            Then { checkVipToolbarActionsSize(3) }
            Then { checkVipToolbarActionTypeAtPosition(0, ShareToolbarActionDto::class.java) }
            Then { checkVipToolbarActionTypeAtPosition(1, ReportAdToolbarActionDto::class.java) }
            Then { checkVipToolbarActionTypeAtPosition(2, FavoriteToolbarActionDto::class.java) }
        }
    }

    @Test
    fun `should return BottomOverlay`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithContactMethods() }
            When { getBottomOverlay() }
            Then { checkBottomOverlayRowIsNotNull() }
            Then { checkBottomOverlayRowCount(1) }
        }
    }

    @Test
    fun `should not return BottomOverlay`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdId(adId = DataFactory.SOME_AD_ID) }
            When { getBottomOverlay() }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should not return BottomOverlay for user own ads`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdId(adId = DataFactory.SOME_AD_ID) }
            When { getBottomOverlay(isUserOwnAd = true) }
            Then { checkBottomOverlayRowIsNull() }
        }
    }

    @Test
    fun `should not include price in similar items when ad is from services category`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            Given { stubCapiAdsApiResponse() }
            When { readScreen() }
            Then { checkSimilarAdsCalledWithIncludesPrice(false) }
        }
    }

    @Test
    fun `should get space row`() = runTest {
        runUnitTest(robot) {
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            Given { stubCapiAdsApiResponse() }
            When { readScreen() }
            Then { checkSpaceRow() }
        }
    }

    @Test
    fun `should have a space row in the last position`() = runTest {
        runUnitTest(robot) {
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            Given { stubCapiAdsApiResponse() }
            When { readScreen() }
            Then { checkSpaceRowLast() }
        }
    }

    @Test
    fun `should return location title row`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "Manchester",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            When { getLocationTitleRow() }
            Then { checkVipLocationTitleRowLayoutType() }
            Then {
                checkVipLocationTitleRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.SMALL,
                        ),
                    ),
                )
            }
        }
    }

    @Test
    fun `should return Ad seller skills row`() = runTest {
        runUnitTest(robot) {
            Given { stubRawSkillList() }
            When { getVipSellerSkillsRow() }
            Then { checkVipSellerSkillsRowLayoutType()}
            Then { checkVipSellerSkillsRowDataText() }
        }
    }

    @Test
    fun `should call getServiceVipScreen when ad is from services category`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given {
                stubRawAdAddress(
                    RawCapiAddress(
                        city = "London",
                        latitude = DataFactory.SOME_LOCATION_LAT,
                        longitude = DataFactory.SOME_LOCATION_LNG,
                    ),
                    visibleOnMap = true.toString()
                )
            }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkServiceVipScreenCalled() }
        }
    }

    @Test
    fun `should include publisher info in service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkPublisherInfoIncluded() }
        }
    }

    @Test
    fun `should include overview row in service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkOverviewRowIncluded() }
        }
    }

    @Test
    fun `should include portfolio image row in service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkPortfolioImageRowIncluded() }
        }
    }

    @Test
    fun `should include section tabs in service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkSectionTabsIncluded() }
        }
    }

    @Test
    fun `should include divider row in service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkDividerRowIncluded() }
        }
    }

    @Test
    fun `should build correct tab index map for service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkTabIndexMapBuilt() }
        }
    }

    @Test
    fun `should insert section tabs at correct position in service vip screen`() = runTest {
        runUnitTest(robot) {
            Given { stubIsServices(true) }
            Given { stubIsProperties(false) }
            Given { stubRawBaseUserProfile() }
            Given { stubUserProfileData(null) }
            Given { stubLocationsResponse(listOf(RawLocationFactory.createRawLocation())) }
            Given { stubServiceVipScreenData() }
            Given { stubVipUiOptimizeProvider() }
            Given { stubCapiAdsApiResponse() }
            Given { stubExperimentsWithVariantB() }
            When { readScreen() }
            Then { checkSectionTabsPosition() }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualToolbarResult: ToolbarDto
        private var actualBottomOverlayResult: BottomOverlay? = null
        private lateinit var actualScreenResponse: ScreenResponse
        private var actualVipImagesRowResult: RowLayout<UiItem>? = null
        private lateinit var actualVipTitleRowResult: RowLayout<UiItem>
        private var actualVipPriceRowResult: RowLayout<UiItem>? = null
        private var actualVipLocationRowResult: RowLayout<UiItem>? = null
        private lateinit var actualVipDescriptionRowResult: RowLayout<UiItem>
        private var actualVipPostedSinceRowResult: RowLayout<UiItem>? = null
        private var actualVipMapRowResult: RowLayout<UiItem>? = null
        private lateinit var actualVipPartnershipRowResults: RowLayout<UiItem>
        private lateinit var actualVipPartnershipAdvertRowsResult: Array<RowLayout<UiItem>?>
        private lateinit var actualAdIdRowResult: RowLayout<UiItem>
        private var actualVipLocationTitleRowResult: RowLayout<UiItem>? = null
        private var actualVipSellerSkillsRowResult: RowLayout<UiItem>? = null
        private lateinit var rawSkillList: RawSkillList

        private lateinit var headersBuilder: HeadersBuilder
        private lateinit var rawAd: RawCapiAd
        private lateinit var rawPartnershipData: PartnershipDetailsResponse
        private var userProfileData: UserProfileData? = null

        private val capiAdsApi: CapiAdsApi = mockk(relaxed = true)
        private val capiReplyApi: CapiReplyApi = mockk(relaxed = true)
        private val conversationApi: CapiConversationApi = mockk(relaxed = true)
        private val papiSimilarItemsApi: SimilarItemsApi = mockk(relaxed = true)
        private val userProfileApi: UserProfileApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val staticMapFactory: StaticMapFactory = mockk(relaxed = true)
        private val phoneNumberApi: PhoneNumberApi = mockk(relaxed = true)
        private val partnershipAdsApi: PartnershipAdsApi = mockk(relaxed = true)
        private val conversationsApi: ConversationsApi = mockk(relaxed = true)
        private val coreChatAuthApi: CoreChatAuthApi = mockk(relaxed = true)
        private val vipAnalyticsProvider: VipAnalyticsProvider = mockk(relaxed = true)
        private val spaceRowFactory: SpaceRowFactory = SpaceRowFactory()
        private val spaceRowFactorySpy = spyk(spaceRowFactory)

        private val vipTimeAgoFormatter =
            spyk(VipTimeAgoFormatter(createUKDateTimeFormatter(VIP_POSTED_SINCE_DATE_FORMAT)))
        private val sellerProfileActiveStatusFormatter = SellerProfileActiveStatusFormatter(
            createUKDateTimeFormatter(
                SellerProfileScreenUiConfiguration.SELLER_PROFILE_ACTIVE_STATUS_DATE_FORMAT
            )
        )
        private val vipService =
            VipService(
                capiAdsApi,
                capiReplyApi,
                papiSimilarItemsApi,
                userProfileApi,
                phoneNumberApi,
                partnershipAdsApi,
            )
        private val userProfileService = UserProfileService(userServiceApi = mockk(relaxed = true))
        private val vipSpecificationFactory = VipSpecificationFactory()
        private val vipHpiFactory = VipHpiFactory()
        private val titleFactory = TitleFactory()
        private val vipPartnershipAnalyticsProvider = VipPartnershipAnalyticsProvider()
        private val vipVerticalsUIProvider = VipVerticalsUIProvider(
            vipSpecificationFactory,
            vipHpiFactory,
            titleFactory,
            vipPartnershipAnalyticsProvider
        )
        private val vipPartnershipAdvertsFactory = VipPartnershipAdvertsFactory(VipPartnershipAnalyticsProvider())
        private val vipImageGalleryMapper = VipImageGalleryMapper()
        private val vipTitleMapper = VipTitleMapper()
        private val vipPriceMapper = VipPriceMapper()
        private val vipLocationMapper = VipLocationMapper(locationsApi)
        private val vipDescriptionMapper = VipDescriptionMapper()
        private val vipPostedSinceMapper = VipPostedSinceMapper(vipTimeAgoFormatter)
        private val vipSellerProfileMapper = VipSellerProfileMapper(sellerProfileActiveStatusFormatter)
        private val vipMapMapper = VipMapMapper(locationsApi = locationsApi, staticMapFactory = staticMapFactory)
        private val vipSimilarItemsMapper = spyk(VipSimilarItemsMapper(vipAnalyticsProvider = vipAnalyticsProvider))
        private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider = mockk(relaxed = true)
        private val vipUiOptimizeProvider: VipUiOptimizeProvider = mockk(relaxed = true)
        private val otherInfoMapper = OtherInfoMapper()
        private val vipUiOptimizeMapper = VipUiOptimizeMapper()
        private val vipToolbarActionsProvider = VipToolbarActionsProvider(vipAdjustTrackingDataProvider)
        private val capiHeadersProvider = CapiHeadersProvider()
        private val papiHeadersProvider = PapiHeadersProvider()
        private val callHeaders: Headers = mockk(relaxed = true)
        private val vipAdvertsProvider = VipAdvertsProvider(VipAdvertsFactory(), GAMAdvertUtils(CategoriesTreeCache))
        private val rawLocationFetcher = RawLocationFactory.createRawLocationFetcher()
        private val sellerProfileActiveStatusFetcher =
            DefaultSellerProfileActiveStatusFetcher(userServiceApi, coreChatAuthApi, conversationsApi)
        private val vipDefaultsUIProvider = VipDefaultsUIProvider(
            vipImageGalleryMapper,
            vipTitleMapper,
            vipPriceMapper,
            vipLocationMapper,
            vipDescriptionMapper,
            vipPostedSinceMapper,
            vipSellerProfileMapper,
            vipMapMapper,
            vipPartnershipAdvertsFactory,
            vipSimilarItemsMapper,
            otherInfoMapper,
            vipToolbarActionsProvider,
            vipAnalyticsProvider,
            titleFactory,
            TestDispatcherProvider()
        )

        private val bottomOverlayUIProvider = VipBottomOverlayUIProvider(
            conversationApi,
            capiHeadersProvider,
            vipAnalyticsProvider,
            vipAdjustTrackingDataProvider,
            TestDispatcherProvider(),
        )

        private lateinit var testSubject: DefaultVipRepository

        override fun setup() {
            testSubject = DefaultVipRepository(
                vipService,
                userProfileService,
                vipDefaultsUIProvider,
                bottomOverlayUIProvider,
                vipVerticalsUIProvider,
                vipAdvertsProvider,
                rawLocationFetcher,
                sellerProfileActiveStatusFetcher,
                papiHeadersProvider,
                capiHeadersProvider,
                vipAnalyticsProvider,
                vipAdjustTrackingDataProvider,
                TestDispatcherProvider(),
                spaceRowFactorySpy,
                vipUiOptimizeProvider,
            )
            headersBuilder = HeadersBuilder()
            mockkObject(CategoriesTreeCache)
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubCapiAdsApiResponse() {
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawBaseUserProfile() {
            coEvery { userProfileApi.getBaseUserProfile(any(), any()) } returns RawPapiUserProfileFactory.createRawUserProfile()
        }

        fun stubUserProfileData(userProfile: UserProfileData?) {
            userProfileData = userProfile
        }

        fun stubLocationsResponse(locationSuggestions: List<RawLocation>) {
            coEvery { locationsApi.getLocationSuggestions(any()) } returns RawLocationSuggestions(locationSuggestions)
        }

        fun stubRawAdId(adId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adId = adId)
        }

        fun stubIsServices(isServices: Boolean) {
            every { CategoriesTreeCache.isServices(any()) } returns isServices
        }

        fun stubIsProperties(isProperties: Boolean) {
            every { CategoriesTreeCache.isProperties(any()) } returns isProperties
        }

        fun stubRawAdVAT(vat: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(vat = vat)
        }

        fun stubRawAdImage(imageUrl: String?) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(imageUrl = imageUrl)
        }

        fun stubRawAdTitle(title: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(title = title)
        }

        fun stubRawAdPrice(price: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(price = price)
        }

        fun stubRawAdAddress(address: RawCapiAddress, visibleOnMap: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(address = address, visibleOnMap = visibleOnMap)
        }

        fun stubRawPartnershipAdvertSlots(adSlots: List<RawCapiAdSlot>) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adSlots = adSlots)
        }

        fun stubRawAdDescription(description: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(description = description)
        }

        fun stubRawAdAge(adAge: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(age = adAge)
        }

        fun stubRawAdIdAndShareLink(
            adId: String,
            shareLink: String?
        ) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(
                adId = adId,
                links = listOf(RawCapiAdLink(href = shareLink, rel = "self-public-website"))
            )
        }

        fun stubRawAdWithContactMethods() {
            val contactMethod = RawCapiContactMethodFactory.contactMethod("chat", RawCapiContactMethod.CHAT)
            rawAd = RawCapiAdsFactory.createRawAdWithContactMethods(listOf(contactMethod))
        }

        fun stubRawPartnershipData(
            title: String,
            url: String
        ) {
            rawPartnershipData = PartnershipDetailsResponse(
                id = DataFactory.SOME_AD_ID,
                icon = DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL,
                ctaText = title,
                clickOutUrl = url
            )
        }

        fun stubRawSkillList() {
            rawSkillList = RawSkillList(
                isEdited = false,
                skills = arrayListOf("Skill 1", "Skill 2", "Skill 3")
            )
        }

        fun getVipImagesRow() {
            actualVipImagesRowResult = vipDefaultsUIProvider.getVipImagesRow(rawAd)
        }

        fun getVipTitleRow() {
            actualVipTitleRowResult = vipDefaultsUIProvider.getVipTitleRow(rawAd)
        }

        fun getVipPriceRow() {
            actualVipPriceRowResult = vipDefaultsUIProvider.getVipPriceRow(rawAd)
        }

        suspend fun getVipLocationRow() {
            actualVipLocationRowResult = vipDefaultsUIProvider.getVipLocationRow(rawAd)
        }

        fun getVipDescriptionRow() {
            actualVipDescriptionRowResult = vipDefaultsUIProvider.getVipDescriptionRow(rawAd)
        }

        fun getVipPostedSinceRow() {
            actualVipPostedSinceRowResult = vipDefaultsUIProvider.getVipPostedSinceRow(rawAd)
        }

        fun getPartnershipRow() {
            actualVipPartnershipRowResults = vipDefaultsUIProvider.getPartnershipRow(rawPartnershipData)
        }

        suspend fun getVipMapRow() {
            actualVipMapRowResult = vipDefaultsUIProvider.getVipMapRow(rawAd)
        }

        fun getVipPartnershipAdvertRows(clientSemantics: ClientSemantics) {
            actualVipPartnershipAdvertRowsResult = vipDefaultsUIProvider.getVipPartnershipAdvertRows(
                rawAd,
                clientSemantics,
            )
        }

        fun getVipAdIdRow() {
            actualAdIdRowResult = vipDefaultsUIProvider.getVipAdIdRow(rawAd)
        }

        fun getVipVATRow() {
            actualAdIdRowResult = vipDefaultsUIProvider.getVipVATRow(rawAd)!!
        }

        fun getVipToolbar(isUserOwnAd: Boolean = false) {
            val experiments = ClientExperimentsFactory.createExperiments(Experiment.SERVICE_NEW_UI to Variant.B)
            actualToolbarResult = vipDefaultsUIProvider.getVipToolbar(rawAd, isUserOwnAd, experiments)
        }

        fun getVipToolbarWithExperimentVariant(variant: Variant, isUserOwnAd: Boolean = false) {
            val experiments = ClientExperimentsFactory.createExperiments(Experiment.SERVICE_NEW_UI to variant)
            actualToolbarResult = vipDefaultsUIProvider.getVipToolbar(rawAd, isUserOwnAd, experiments)
        }

        suspend fun getBottomOverlay(isUserOwnAd: Boolean = false) {
            actualBottomOverlayResult = bottomOverlayUIProvider.createContactMethod(rawAd, callHeaders, isUserOwnAd)
        }

        suspend fun readScreen() {
            actualScreenResponse = testSubject.readScreen(callHeaders, rawAd.id, userProfileData)
        }

        fun getLocationTitleRow() {
            actualVipLocationTitleRowResult = vipDefaultsUIProvider.getLocationTitleRow(rawAd)
        }

        fun getVipSellerSkillsRow() {
            actualVipSellerSkillsRowResult = vipDefaultsUIProvider.getVipSellerSkillsRow(rawSkillList)
        }

        fun checkVipImagesRowLayoutType() {
            assertEquals(RowLayoutType.VIP_IMAGE_GALLERY_ROW, actualVipImagesRowResult?.type)
        }

        fun checkVipImagesRowDataImage(expected: List<Image>) {
            assertEquals(expected, (actualVipImagesRowResult?.data?.first() as? VipImageCardDto)?.images)
        }

        fun checkVipImagesRowIsEmpty() {
            assertEquals(0, actualVipImagesRowResult?.data?.size)
        }

        fun checkVipTitleRowLayoutType() {
            assertEquals(RowLayoutType.VIP_TITLE_ROW, actualVipTitleRowResult.type)
        }

        fun checkVipTitleRowDataText(expected: String) {
            assertEquals(expected, (actualVipTitleRowResult.data.first() as? VipTitleCardDto)?.text)
        }

        fun checkVipPriceRowLayoutType() {
            assertEquals(RowLayoutType.VIP_PRICE_ROW, actualVipPriceRowResult?.type)
        }

        fun checkVipPriceRowDataText(expected: String) {
            assertEquals(expected, (actualVipPriceRowResult?.data?.first() as? VipPriceCardDto)?.text)
        }

        fun checkVipPriceRowIsNull() {
            assertNull(actualVipPriceRowResult)
        }

        fun checkVipLocationRowLayoutType() {
            assertEquals(RowLayoutType.VIP_LOCATION_ROW, actualVipLocationRowResult?.type)
        }

        fun checkVipLocationRowDataLocationText(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.text)
        }

        fun checkVipLocationRowDataLatitude(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.latitude)
        }

        fun checkVipLocationRowDataLongitude(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.longitude)
        }

        fun checkVipLocationRowDataRadius(expected: String) {
            assertEquals(expected, (actualVipLocationRowResult?.data?.first() as? VipLocationCardDto)?.radius)
        }

        fun checkVipLocationRowIsNull() {
            assertNull(actualVipLocationRowResult)
        }

        fun checkVipDescriptionRowLayoutType() {
            assertEquals(RowLayoutType.VIP_DESCRIPTION_ROW, actualVipDescriptionRowResult.type)
        }

        fun checkVipDescriptionRowDataLabel(expected: String) {
            assertEquals(expected, (actualVipDescriptionRowResult.data.first() as? VipDescriptionCardDto)?.label)
        }

        fun checkVipDescriptionRowDataText(expected: String) {
            assertEquals(expected, (actualVipDescriptionRowResult.data.first() as? VipDescriptionCardDto)?.text)
        }

        fun checkVipPartnershipRowText(expected: String) {
            assertEquals(expected, (actualVipPartnershipRowResults.data.first() as? ExternalPartnershipAdvertDto)?.text)
        }

        fun checkVipPartnershipRowWebUrl(expected: String) {
            assertEquals(
                expected,
                (actualVipPartnershipRowResults.data.first() as? ExternalPartnershipAdvertDto)?.webUrl
            )
        }

        fun checkVipPartnershipRowLayoutType() {
            assertEquals(RowLayoutType.EXTERNAL_PARTNERSHIP_ADVERT_ROW, actualVipPartnershipRowResults?.type)
        }

        fun checkVipPostedSinceRowLayoutType() {
            assertEquals(RowLayoutType.VIP_POSTED_SINCE_ROW, actualVipPostedSinceRowResult?.type)
        }

        fun checkVipPostedSinceRowIsNull() {
            assertNull(actualVipPostedSinceRowResult)
        }

        fun checkVipMapRowLayoutType() {
            assertEquals(RowLayoutType.VIP_MAP_ROW, actualVipMapRowResult?.type)
        }

        fun checkVipLocationTitleRowLayoutType() {
            assertEquals(RowLayoutType.TITLE_ROW, actualVipLocationTitleRowResult?.type)
        }

        fun checkVipLocationTitleRowStyle(expected: Style) {
            assertEquals(expected, actualVipLocationTitleRowResult?.style)
        }

        fun checkVipMapRowDataIsInteractive(expected: Boolean) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.isInteractive)
        }

        fun checkVipMapRowDataLocationLabel(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.locationLabel)
        }

        fun checkVipMapRowDataLocationTypeLabel(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.locationTypeLabel)
        }

        fun checkVipMapRowDataLatitude(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.latitude)
        }

        fun checkVipMapRowDataLongitude(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.longitude)
        }

        fun checkVipMapRowDataRadius(expected: String) {
            assertEquals(expected, (actualVipMapRowResult?.data?.first() as? VipMapCardDto)?.radius)
        }

        fun checkVipMapRowIsNull() {
            assertNull(actualVipMapRowResult)
        }

        fun checkVipOtherInfoRowLayoutType() {
            assertEquals(RowLayoutType.OTHER_INFO_ROW, actualAdIdRowResult.type)
        }

        fun checkVipOtherInfoRowDataText(expected: String) {
            assertEquals(expected, (actualAdIdRowResult.data.first() as? OtherInfoCardDto)?.text)
        }

        fun checkVipToolbarActionsSize(expected: Int) {
            assertEquals(expected, actualToolbarResult.actions.size)
        }

        fun <T> checkVipToolbarActionTypeAtPosition(
            position: Int,
            expected: T
        ) {
            assertEquals(expected, actualToolbarResult.actions[position].javaClass)
        }

        fun checkBottomOverlayRowIsNotNull() {
            assertNotNull(actualBottomOverlayResult)
        }

        fun checkBottomOverlayRowIsNull() {
            assertNull(actualBottomOverlayResult)
        }

        fun checkBottomOverlayRowCount(expected: Int) {
            assertEquals(expected, actualBottomOverlayResult?.first()?.data?.size)
        }

        fun checkVipPartnershipAdvertRowsSize(expected: Int) {
            assertEquals(expected, actualVipPartnershipAdvertRowsResult.size)
        }

        fun checkSimilarAdsCalledWithIncludesPrice(includePrice: Boolean) {
            verify { vipSimilarItemsMapper.map(any(), any(), any(), includePrice) }
        }

        fun checkSpaceRow() {
            verify { spaceRowFactorySpy.createSpaceRow(height = Space.MEDIUM) }
        }


        fun checkSpaceRowLast() {
            assertEquals(RowLayoutType.SPACE_ROW, actualScreenResponse.portraitData.last().type)
        }

        fun checkVipSellerSkillsRowDataText() {
            val skillsCard = actualVipSellerSkillsRowResult?.data?.first() as? VipSellerSkillsCardDto
            assertEquals("Services", skillsCard?.label)
            assertEquals(8, skillsCard?.displayCount)
            assertEquals(3, skillsCard?.sellerSkills?.size)
            assertEquals("Skill 1", skillsCard?.sellerSkills?.get(0)?.title)
            assertEquals("Skill 2", skillsCard?.sellerSkills?.get(1)?.title)
            assertEquals("Skill 3", skillsCard?.sellerSkills?.get(2)?.title)
        }

        fun checkVipSellerSkillsRowLayoutType() {
            assertEquals(RowLayoutType.VIP_SELLER_SKILLS_ROW, actualVipSellerSkillsRowResult?.type)
        }

        fun checkServiceVipScreenCalled() {
            val portraitData = actualScreenResponse.portraitData
            assertTrue(portraitData.any { it.type == RowLayoutType.POST_PUBLISHER_INFO_ROW })
            assertTrue(portraitData.any { it.type == RowLayoutType.OVERVIEW_ROW })
            assertTrue(portraitData.any { it.type == RowLayoutType.PORTFOLIO_IMAGE_ROW })
            assertTrue(portraitData.any { it.type == RowLayoutType.VIP_SECTION_TABS_ROW })
            assertTrue(portraitData.any { it.type == RowLayoutType.DIVIDER_ROW })
        }

        fun checkPublisherInfoIncluded() {
            val publisherInfoRow = actualScreenResponse.portraitData.find { it.type == RowLayoutType.POST_PUBLISHER_INFO_ROW }
            assertNotNull(publisherInfoRow)
            val publisherInfoCard = publisherInfoRow?.data?.first() as? PostPublisherInfoCardDto
            assertNotNull(publisherInfoCard)
            assertNotNull(publisherInfoCard?.data?.sellerLink)
            assertNotNull(publisherInfoCard?.data?.destination)
        }

        fun checkOverviewRowIncluded() {
            val overviewRow = actualScreenResponse.portraitData.find { it.type == RowLayoutType.OVERVIEW_ROW }
            assertNotNull(overviewRow)
            val overviewCard = overviewRow?.data?.first() as? OverviewCardDto
            assertNotNull(overviewCard)
            assertEquals("Overview", overviewCard?.data?.label)
            assertTrue(overviewCard?.data?.list?.isNotEmpty() == true)
        }

        fun checkPortfolioImageRowIncluded() {
            val portfolioRow = actualScreenResponse.portraitData.find { it.type == RowLayoutType.PORTFOLIO_IMAGE_ROW }
            assertNotNull(portfolioRow)
            val portfolioCard = portfolioRow?.data?.first() as? PortfolioImageCardDto
            assertNotNull(portfolioCard)
            assertEquals("Portfolio", portfolioCard?.label)
        }

        fun checkSectionTabsIncluded() {
            val sectionTabsRow = actualScreenResponse.portraitData.find { it.type == RowLayoutType.VIP_SECTION_TABS_ROW }
            assertNotNull(sectionTabsRow)
            val sectionTabsCard = sectionTabsRow?.data?.first() as? VipSectionTabsCardDto
            assertNotNull(sectionTabsCard)
            assertTrue(sectionTabsCard?.isSticky == true)
            assertTrue(sectionTabsCard?.tabs?.isNotEmpty() == true)
        }

        fun checkDividerRowIncluded() {
            val dividerRow = actualScreenResponse.portraitData.find { it.type == RowLayoutType.DIVIDER_ROW }
            assertNotNull(dividerRow)
            val dividerCard = dividerRow?.data?.first() as? DividerCardDto
            assertNotNull(dividerCard)
        }

        fun checkTabIndexMapBuilt() {
            val sectionTabsRow = actualScreenResponse.portraitData.find { it.type == RowLayoutType.VIP_SECTION_TABS_ROW }
            val sectionTabsCard = sectionTabsRow?.data?.first() as? VipSectionTabsCardDto
            assertNotNull(sectionTabsCard)

            val tabLabels = sectionTabsCard?.tabs?.map { it.label } ?: emptyList()
            assertTrue(tabLabels.contains("Overview") || tabLabels.contains("Portfolio") || tabLabels.contains("Services") || tabLabels.contains("Description"))
        }

        fun checkSectionTabsPosition() {
            val portraitData = actualScreenResponse.portraitData
            val sectionTabsIndex = portraitData.indexOfFirst { it.type == RowLayoutType.VIP_SECTION_TABS_ROW }
            val publisherInfoIndex = portraitData.indexOfFirst { it.type == RowLayoutType.POST_PUBLISHER_INFO_ROW }

            assertEquals(2, sectionTabsIndex)
            assertTrue(sectionTabsIndex > publisherInfoIndex)
        }

        fun stubServiceVipScreenData() {
            rawAd = RawCapiAdsFactory.createRawCapiAd(
                age = DataFactory.SOME_AD_START_DATE_TIME,
                address = RawCapiAddress(
                    city = "London",
                    latitude = DataFactory.SOME_LOCATION_LAT,
                    longitude = DataFactory.SOME_LOCATION_LNG,
                ),
                visibleOnMap = true.toString()
            )
        }

        fun stubVipUiOptimizeProvider() {
            // Mock publisher info
            every {
                vipUiOptimizeProvider.getVipPublisherInfo(any())
            } returns RowLayout(
                type = RowLayoutType.POST_PUBLISHER_INFO_ROW,
                data = listOf(
                    PostPublisherInfoCardDto(
                        data = PostPublisherInfoCardData(
                            sellerLink = mockk(relaxed = true),
                            destination = mockk(relaxed = true),
                            avatar = emptyList(),
                            rating = RatingCardDto(
                                data = RatingCardDto.RatingData(
                                    score = 3.5,
                                    reviewCount = 5
                                )
                            ),
                            poweredBy = PoweredByCardDto(
                                data = PoweredByCardDto.PoweredByData(
                                    isHidden = false,
                                    prefix = "Powered by",
                                    by = "GT",
                                    infoIconVisible = true
                                )
                            )
                        )
                    )
                ),
                style = Style(spacing = createSpacing(topMargin = Space.SMALL))
            )

            // Mock overview row
            every {
                vipUiOptimizeProvider.getVipOverviewRow(any(), any(), any())
            } returns RowLayout(
                type = RowLayoutType.OVERVIEW_ROW,
                data = listOf(
                    OverviewCardDto(
                        data = OverviewCardData(
                            label = "Overview",
                            list = listOf(
                                OverviewCardItemDto("Verified", "LOGO_VERIFIED", "LOGO_INFO"),
                                OverviewCardItemDto("Posting since 2023", "LOGO_POSTING_TIME", "")
                            )
                        )
                    )
                )
            )

            // Mock portfolio image row
            every {
                vipUiOptimizeProvider.getVipPortfolioImageRow(any(), any())
            } returns RowLayout(
                type = RowLayoutType.PORTFOLIO_IMAGE_ROW,
                data = listOf(
                    PortfolioImageCardDto(
                        label = "Portfolio",
                        imageCards = listOf(
                            VipImageCardDto(
                                images = listOf(Image("https://example.com/image.jpg", 120, 120)),
                            )
                        )
                    )
                )
            )

            // Mock section tabs row
            every {
                vipUiOptimizeProvider.getVipSectionTabsRow(any())
            } returns RowLayout(
                type = RowLayoutType.VIP_SECTION_TABS_ROW,
                data = listOf(
                    VipSectionTabsCardDto(
                        isSticky = true,
                        tabs = listOf(
                            TabItem("Overview", 0),
                            TabItem("Portfolio", 1),
                            TabItem("Services", 2),
                            TabItem("Description", 3)
                        )
                    )
                )
            )

            // Mock divider row
            every {
                vipUiOptimizeProvider.createDividerRow(any(), any(), any(), any(), any())
            } returns RowLayout(
                type = RowLayoutType.DIVIDER_ROW,
                data = listOf(DividerCardDto(data = DividerCardDto.DividerData(DividerCardDto.Thickness.LIGHT))),
                style = Style(spacing = createSpacing(
                    topMargin = Space.SMALL,
                    leftMargin = Space.SMALL,
                    rightMargin = Space.SMALL,
                    bottomMargin = Space.SMALL
                ))
            )
        }

        fun stubExperimentsWithVariantB() {
            val experiments = ClientExperimentsFactory.createExperiments(
                Experiment.SERVICE_NEW_UI to Variant.B
            )
            every { callHeaders[ApiHeaderParams.EXPERIMENTS] } returns experiments.toGamExperimentsString()
        }
    }
}
