package com.gumtree.mobile.utils.extensions.data

import api.capi.models.RawConversation
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.ImageType
import com.gumtree.mobile.features.conversations.ConversationsTimeAgoFormatter
import com.gumtree.mobile.features.screens.layoutsData.ConversationCardDto
import com.gumtree.mobile.features.screens.layoutsData.ConversationMessageDto
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute

/**
 * Mapping between RawConversation data and ConversationCard
 * @param timeAgoFormatter - the dates formatter
 * @param currentDate - the current date
 * @param myUsername - Current user's email address
 * @return - an instance of ConversationCard created from RawConversation data
 */
fun RawConversation.toConversationCard(
    timeAgoFormatter: ConversationsTimeAgoFormatter,
    currentDate: String,
    myUsername: String,
): ConversationCardDto {
    return ConversationCardDto(
        conversationId = id,
        title = findConversationCounterPartyDisplayName(myUsername, null),
        images = adImgUrl?.let { ImageType.THUMBNAIL_SQUARE.toImageArray(it) },
        unreadMessages = unreadCount,
        lastMessage = ConversationMessageDto(
            type = ConversationMessageDto.Type.TEXT, // Todo change the implementation to determinate also image last message (Capi is returning wrong last messages at the moment)
            postTime = timeAgoFormatter.getTimeAgoLabel(rawMessages.lastOrNull()?.postTimestamp, currentDate),
            content = rawMessages.lastOrNull()?.message,
        ),
        destination = DestinationRoute.CONVERSATION.build(ApiQueryParams.CONVERSATION_ID to id),
    )
}

/**
 * Compares myUsername with adOwnerEmail
 * @param myUsername my username
 * @return - true when myUsername == adOwnerEmail, otherwise false
 */
fun RawConversation.isConversationAboutMyPosting(myUsername: String): Boolean {
    return myUsername == adOwnerEmail
}

/**
 * Find the counterparty user id
 * @param myUsername - my username
 * @return - if the conversation is about my posting, user id is the adReplierId
 * if the conversation is NOT about my posting, user id is the adOwnerId
 */
fun RawConversation.findConversationCounterPartyUserId(myUsername: String): String {
    return when {
        isConversationAboutMyPosting(myUsername) -> adReplierId
        else -> adOwnerId
    }
}

fun RawConversation.findConversationMyUserId(myUsername: String): String {
    return when {
        isConversationAboutMyPosting(myUsername) -> adOwnerId
        else -> adReplierId
    }
}

/**
 * Find the counterparty user email to show view profile option
 * @param myUsername - my username
 * @return - if the conversation is about my posting the user email is the adReplierEmail
 * if the conversation is NOT about my posting the user email is the adOwnerEmail
 */
fun RawConversation.findConversationCounterPartyEmail(myUsername: String): String {
    return when {
        isConversationAboutMyPosting(myUsername) -> adReplierEmail
        else -> adOwnerEmail
    }
}

/**
 * Find the counterparty user display name to block
 * @param myUsername - my username
 * @return - if the conversation is about my posting the block user display name is the adReplierName
 * if the conversation is NOT about my posting the block display name is the adOwnerName
 * If any of the adReplierName or adOwnerName is NULL it will return an empty String
 */
fun RawConversation.findConversationCounterPartyDisplayName(myUsername: String, sellerDisplayName: String?): String {
    return when {
        isConversationAboutMyPosting(myUsername) -> adReplierName
        else -> adOwnerName.orEmpty().ifBlank { sellerDisplayName.orEmpty() }
    } ?: EMPTY_STRING
}
