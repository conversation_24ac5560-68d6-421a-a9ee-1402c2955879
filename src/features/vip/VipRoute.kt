package com.gumtree.mobile.features.vip

import com.gumtree.mobile.plugins.AUTH_USER_PROFILE
import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ID_PATH
import com.gumtree.mobile.utils.extensions.getHeaderOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.readBodyParamOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.readPathParam
import com.gumtree.mobile.utils.extensions.readUserProfileData
import com.gumtree.mobile.utils.extensions.respondCreated
import com.gumtree.mobile.utils.extensions.respondSuccess
import com.gumtree.mobile.utils.regexes.RegEx
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import org.koin.ktor.ext.inject

const val VIP_PATH = "$API_V1_MAIN_PATH/vip"
const val VIP_SCREEN_PATH = "$VIP_PATH/screen/{$ID_PATH}"
const val VIP_PHONE_PATH = "$VIP_PATH/phone/{$ID_PATH}"
const val VIP_REPLY_PATH = "$VIP_PATH/reply/{$ID_PATH}"
const val PHONE_CONTACT_METHOD = "phone"

fun Route.vipRoute() {
    val screenRepository by inject<VipRepository>()
    val contactMethodRepository by inject<VipContactMethodRepository>()

    authenticate(AUTH_USER_PROFILE, optional = true) {
        get(VIP_SCREEN_PATH) {
            val callHeaders: Headers = call.request.headers
            val adId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
            val userProfile = call.readUserProfileData()
            val response = screenRepository.readScreen(callHeaders, adId, userProfile)

            call.respondSuccess(response)
        }
    }

    get(VIP_PHONE_PATH) {
        call.getHeaderOrThrowBadRequest(ApiHeaderParams.THREATMETRIX_SESSION)
        val callHeaders: Headers = call.request.headers
        val adId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        val phoneNumber = contactMethodRepository.readPhoneNumber(callHeaders, adId, PHONE_CONTACT_METHOD)
        call.respondSuccess(VipPhoneNumberDto(phoneNumber))
    }

    post(VIP_REPLY_PATH) {
        val callHeaders: Headers = call.request.headers
        val adId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        val request = call.readBodyParamOrThrowBadRequest<ReplyToAdRequest>()
        contactMethodRepository.replyToAd(callHeaders, adId, request)

        call.respondCreated()
    }
}
