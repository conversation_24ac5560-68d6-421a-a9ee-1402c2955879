package features.crm

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.crm.api.CrmApi
import com.gumtree.mobile.features.crm.CrmService
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class CrmServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use API to get sales force contact key`() = runTest {
        runUnitTest(robot) {
            When { getSalesForceCrmKey() }
            Then { checkApiGetSalesForceCrmKey() }
        }
    }

    private class Robot: BaseRobot {
        private val papiCrmApi: CrmApi = mockk(relaxed = true)
        private val authHeaders: ApiHeaders = mockk(relaxed = true)

        private lateinit var testSubject: CrmService

        override fun setup() {
            testSubject = CrmService(papiCrmApi)
        }

        suspend fun getSalesForceCrmKey() {
            testSubject.getSalesForceCrmKey(authHeaders)
        }

        fun checkApiGetSalesForceCrmKey() {
            coVerify { papiCrmApi.getSalesForceCrmKey(authHeaders) }
        }
    }

}