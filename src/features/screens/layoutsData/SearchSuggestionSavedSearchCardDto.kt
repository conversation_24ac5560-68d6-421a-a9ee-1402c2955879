package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.routes.DestinationDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("SEARCH_SUGGESTIONS_SAVED_SEARCH_CARD")
data class SearchSuggestionSavedSearchCardDto(
    @SerialName("id")
    var id: String,
    @SerialName("title")
    val title: String,
    @SerialName("subtitle")
    val subtitle: String?,
    @SerialName("params")
    val params: String,
    @SerialName("destination")
    val destination: DestinationDto,
) : UiItem
