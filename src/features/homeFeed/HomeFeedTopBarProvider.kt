package com.gumtree.mobile.features.homeFeed

import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.TopBar

class HomeFeedTopBarProvider(
    private val homeFeedChipsFactory: HomeFeedChipsFactory,
    private val homeFeedCarouselProvider: HomeFeedCarouselProvider,
) {

    fun createHomeFeedTopBar(
        locationId: String,
        locationType: LocationType,
        locationName: String,
        distance: Distance,
        categoriesExperimentVariant: Variant?,
    ): TopBar {
        val topBarRow: RowLayout<UiItem> = when (categoriesExperimentVariant) {
            Variant.B, Variant.C -> generateHomeFeedCategoriesCarousel(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            )

            else -> generateHomeFeedCategoryChips(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            )
        }

        val scrollingCollapseBehaviour: ScrollingCollapseBehaviour? = when (categoriesExperimentVariant) {
            Variant.C, Variant.D -> ScrollingCollapseBehaviour.STICK_TO_TOP
            else -> null
        }

        return TopBar(
            scrollingCollapseBehaviour = scrollingCollapseBehaviour,
            rows = listOf(topBarRow),
        )
    }

    private fun generateHomeFeedCategoryChips(
        locationId: String,
        locationType: LocationType,
        locationName: String,
        distance: Distance,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.CHIPS_ROW,
            bottomDivider = true,
            data = homeFeedChipsFactory.buildHomeFeedChips(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            ),
        )
    }

    private fun generateHomeFeedCategoriesCarousel(
        locationId: String,
        locationType: LocationType,
        locationName: String,
        distance: Distance,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.CAROUSEL_ROW,
            data = homeFeedCarouselProvider.buildHomeFeedCarousel(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            ),
        )
    }
}
