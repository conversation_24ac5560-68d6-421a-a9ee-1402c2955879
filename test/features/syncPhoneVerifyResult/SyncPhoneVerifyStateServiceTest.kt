package features.syncPhoneVerifyResult

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.seller.api.SellerApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import com.gumtree.mobile.api.userService.models.RawUserServiceUserType
import com.gumtree.mobile.features.syncPhoneVerifyResult.SyncPhoneVerifyStateService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import retrofit2.Response
import tools.BaseRobot
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import kotlin.test.assertEquals
import okhttp3.ResponseBody

class SyncPhoneVerifyStateServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should delegate syncVerifyFinishedToSeller to sellerApi`() = runTest {
        runUnitTest(robot) {
            Given { stubSuccessfulSellerApiResponse() }
            Given { stubUserServiceApiResponse() }
            When { syncVerifyFinishedToSeller() }
            Then { checkSellerApiCalled() }
            Then { checkResponseSuccessful() }
        }
    }

    private class Robot : BaseRobot {

        private lateinit var testSubject: SyncPhoneVerifyStateService
        private val sellerApi: SellerApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val apiHeaders: ApiHeaders = mockk(relaxed = true)

        private val userId = "testUserId"
        private val emailAddress = "<EMAIL>"
        private val advertId = "testAdvertId"
        private var actualResponse: Response<ResponseBody>? = null

        override fun setup() {
            testSubject = SyncPhoneVerifyStateService(sellerApi, userServiceApi)
        }

        fun stubSuccessfulSellerApiResponse() {
            coEvery {
                sellerApi.syncVerifyFinishedToSeller(userId, emailAddress, advertId, apiHeaders)
            } returns Response.success("".toResponseBody(null))
        }

        fun stubUserServiceApiResponse() {
            coEvery {
                userServiceApi.getUserDetails(emailAddress)
            } returns RawUserServiceUserDetails(
                userId = "1234",
                firstName = "name1",
                lastName = "name2",
                userEmail = "email",
                userType = RawUserServiceUserType.STANDARD,
                registrationDate = "123",
                accountIds = listOf("123"),
                phoneNumber = "1234",
                postingSinceDate = "1"
            )
        }

        suspend fun syncVerifyFinishedToSeller() {
            actualResponse = testSubject.syncVerifyFinishedToSeller(
                userId,
                emailAddress,
                advertId,
                apiHeaders
            )
        }

        fun checkSellerApiCalled() {
            coVerify(exactly = 1) {
                sellerApi.syncVerifyFinishedToSeller(userId, emailAddress, advertId, apiHeaders)
            }
        }

        fun checkResponseSuccessful() {
            assertEquals(true, actualResponse?.isSuccessful)
        }
    }
}