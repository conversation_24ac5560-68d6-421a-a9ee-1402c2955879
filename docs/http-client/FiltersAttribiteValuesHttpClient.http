# @name GET Filters Attribute Values Screen

GET {{hostV1}}/filters-attribute/screen?name=vehicle_registration_year&value=up_to_1
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Filters Attribute Values Screen for dependent attribute with ANY selected value

GET {{hostV1}}/filters-attribute/screen?name=vehicle_make&dependentName=vehicle_model
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Filters Attribute Values Screen for dependent attribute with some selected value

GET {{hostV1}}/filters-attribute/screen?name=vehicle_make&value={{vehicle_make}}&dependentName=vehicle_model
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Filters Attribute Values Screen for dependent attribute with some selected value and dependent selected value

GET {{hostV1}}/filters-attribute/screen?name=vehicle_make&value={{vehicle_make}}&dependentName=vehicle_model&dependentValue={{vehicle_model}}
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}

###
# @name GET Filters Attribute Values Screen for root attribute

GET {{hostV1}}/filters-attribute/screen?name=vehicle_model&value={{vehicle_model}}&rootName=vehicle_make&rootValue={{vehicle_make}}
App-Version: {{app_version}}
Experiments: {{experiments}}
UDID: {{udid}}
