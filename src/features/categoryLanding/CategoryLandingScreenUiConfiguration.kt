package com.gumtree.mobile.features.categoryLanding

import com.gumtree.mobile.getImageAssetUrl

/**
 * Defines the Category Landing screen UI components/configurations
 */

object CategoryLandingScreenUiConfiguration {
    const val CARS_SCREEN_TITLE = "Cars"

    const val SEARCH_TEXT = "Search"
    const val FIND_YOUR_DREAM_CAR_TEXT = "Find your dream car"
    const val CARS_BY_TYPE_TEXT = "Cars by type"
    const val MAKE_DROPDOWN_TITLE_TEXT = "Make"
    const val MODEL_DROPDOWN_TITLE_TEXT = "Model"
    const val PRICE_DROPDOWN_TITLE_TEXT = "Price"
    val CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_1 = getImageAssetUrl("cars-landing/100_1.png")
    val CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_2 = getImageAssetUrl("cars-landing/100_2.png")
    val CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_3 = getImageAssetUrl("cars-landing/100_3.png")
    val CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_4 = getImageAssetUrl("cars-landing/100_4.png")

    /**
     * The Category landing screen similar items limit
     */
    const val CATEGORY_SIMILAR_ITEMS_DEFAULT_LIMIT = 6

    val CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_URLS = listOfNotNull(
        CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_1,
        CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_2,
        CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_3,
        CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_X_4,
    )
}
