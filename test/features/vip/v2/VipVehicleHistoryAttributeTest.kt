package features.vip.v2

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAd.ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR
import api.capi.models.RawCapiAd.ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE
import api.capi.models.RawCapiAd.ATTRIBUTE_VEHICLE_UK_MODEL
import com.gumtree.mobile.features.vip.v2.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class VipVehicleHistoryAttributeTest {

    private val robot = Robot()

    @Test
    fun `should return attribute vehicle not stolen with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryRequiredAttributes() }
            When { getVehicleHistoryAttribute(RawCapiAd.ATTRIBUTE_VEHICLE_NOT_STOLEN) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_NOT_STOLEN_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(true) }
            Then { checkVehicleHistoryAttributeIsPass(true) }
            Then { checkVehicleHistoryAttributeDescription(null) }
        }
    }

    @Test
    fun `should return attribute vehicle not scrapped with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryRequiredAttributes() }
            When { getVehicleHistoryAttribute(RawCapiAd.ATTRIBUTE_VEHICLE_NOT_SCRAPPED) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_NOT_SCRAPPED_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(true) }
            Then { checkVehicleHistoryAttributeIsPass(true) }
            Then { checkVehicleHistoryAttributeDescription(null) }
        }
    }

    @Test
    fun `should return attribute vehicle not exported with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryRequiredAttributes() }
            When { getVehicleHistoryAttribute(RawCapiAd.ATTRIBUTE_VEHICLE_NOT_EXPORTED) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_NOT_EXPORTED_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(true) }
            Then { checkVehicleHistoryAttributeIsPass(true) }
            Then { checkVehicleHistoryAttributeDescription(null) }
        }
    }

    @Test
    fun `should return attribute vehicle not category A or B with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryOptionalAttributes() }
            When { getVehicleHistoryAttribute(RawCapiAd.ATTRIBUTE_VEHICLE_NOT_CAT_A_OR_B) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_NOT_CAT_A_OR_B_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(false) }
            Then { checkVehicleHistoryAttributeIsPass(false) }
            Then { checkVehicleHistoryAttributeDescription(null) }
        }
    }

    @Test
    fun `should return attribute vehicle not category C or D with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryOptionalAttributes() }
            When { getVehicleHistoryAttribute(RawCapiAd.ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(false) }
            Then { checkVehicleHistoryAttributeIsPass(false) }
            Then { checkVehicleHistoryAttributeDescription(ATTRIBUTE_VEHICLE_NOT_CAT_C_OR_D_DESCRIPTION) }
        }
    }

    @Test
    fun `should return attribute vehicle not category S or N with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryOptionalAttributes() }
            When { getVehicleHistoryAttribute(RawCapiAd.ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(false) }
            Then { checkVehicleHistoryAttributeIsPass(false) }
            Then { checkVehicleHistoryAttributeDescription(ATTRIBUTE_VEHICLE_NOT_CAT_S_OR_N_DESCRIPTION) }
        }
    }

    @Test
    fun `should return attribute vehicle UK model with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryOptionalAttributes() }
            When { getVehicleHistoryAttribute(ATTRIBUTE_VEHICLE_UK_MODEL) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_UK_MODEL_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(false) }
            Then { checkVehicleHistoryAttributeIsPass(false) }
            Then { checkVehicleHistoryAttributeDescription(ATTRIBUTE_VEHICLE_UK_MODEL_DESCRIPTION) }
        }
    }

    @Test
    fun `should return attribute vehicle original number plate with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryOptionalAttributes() }
            When { getVehicleHistoryAttribute(ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(false) }
            Then { checkVehicleHistoryAttributeIsPass(false) }
            Then { checkVehicleHistoryAttributeDescription(ATTRIBUTE_VEHICLE_ORIGINAL_NUMBER_PLATE_DESCRIPTION) }
        }
    }

    @Test
    fun `should return attribute vehicle original colour with expected initial data`() {
        runUnitTest(robot) {
            Given { stubVehicleHistoryOptionalAttributes() }
            When { getVehicleHistoryAttribute(ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR) }
            Then { checkVehicleHistoryAttributeTitle(ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR_TITLE) }
            Then { checkVehicleHistoryAttributeIsRequired(false) }
            Then { checkVehicleHistoryAttributeIsPass(false) }
            Then { checkVehicleHistoryAttributeDescription(ATTRIBUTE_VEHICLE_ORIGINAL_COLOUR_DESCRIPTION) }
        }
    }

    private class Robot: BaseRobot {
        private var actualVipVehicleHistoryAttribute: VipVehicleHistoryAttribute? = null

        private lateinit var testSubject: Map<String, VipVehicleHistoryAttribute>

        fun stubVehicleHistoryRequiredAttributes() {
            testSubject = vehicleHistoryRequiredAttributes
        }

        fun stubVehicleHistoryOptionalAttributes() {
            testSubject = vehicleHistoryOptionalAttributes
        }

        fun getVehicleHistoryAttribute(attributeKey: String) {
            actualVipVehicleHistoryAttribute = testSubject[attributeKey]
        }

        fun checkVehicleHistoryAttributeTitle(expected: String) {
            assertEquals(expected, actualVipVehicleHistoryAttribute?.title)
        }

        fun checkVehicleHistoryAttributeIsRequired(expected: Boolean) {
            assertEquals(expected, actualVipVehicleHistoryAttribute?.required)
        }

        fun checkVehicleHistoryAttributeIsPass(expected: Boolean) {
            assertEquals(expected, actualVipVehicleHistoryAttribute?.pass)
        }

        fun checkVehicleHistoryAttributeDescription(expected: String?) {
            assertEquals(expected, actualVipVehicleHistoryAttribute?.description)
        }
    }
}