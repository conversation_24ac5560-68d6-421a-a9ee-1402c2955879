package com.gumtree.mobile.features.registration

import com.gumtree.mobile.utils.createUKDateTimeFormatter
import java.time.LocalDate
import java.time.ZoneOffset

const val DOB_ISO_FORMAT = "yyyy-MM-dd"
private const val MINIMUM_AGE = 16L

class DateOfBirthValidator {

    private val dateFormater = createUKDateTimeFormatter(datePattern = DOB_ISO_FORMAT)

    fun isValid(dateOfBirth: String?): Boolean {
        return dateOfBirth?.let {
            runCatching {
                val parsedDate = LocalDate.parse(it, dateFormater)
                val now = LocalDate.now(ZoneOffset.UTC)
                val minimumAge = now.minusYears(MINIMUM_AGE)

                parsedDate.isBefore(now) && (parsedDate.isBefore(minimumAge) || parsedDate.isEqual(minimumAge))
            }.getOrElse { false } // Any parsing exception means invalid date format
        } ?: true // If dateOfBirth is null, consider it valid as it is an optional field
    }
}
