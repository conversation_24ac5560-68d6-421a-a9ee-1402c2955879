package features.favourites

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.apis.CapiFavoritesApi
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.favouriteAdverts.api.FavouriteAdvertsApi
import com.gumtree.mobile.api.favouriteAdverts.bodies.RawAddFavouriteAdvertBody
import com.gumtree.mobile.api.favouriteAdverts.bodies.RawRemoveFavouriteAdvertBody
import com.gumtree.mobile.features.favourites.FavouritesService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory

class FavouritesServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use service to add new favorite ad`() = runTest {
        runUnitTest(robot) {
            When { addAdToUsersFavorites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkApiAddAd(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should use favourite adverts API to add new favorite ad`() = runTest {
        val body = RawAddFavouriteAdvertBody(email = DataFactory.SOME_USER_EMAIL)
        runUnitTest(robot) {
            When { addFavouriteAdvert(DataFactory.SOME_USER_ID, DataFactory.SOME_AD_ID, body) }
            Then { checkApiAddFavouriteAdvert(DataFactory.SOME_USER_ID, DataFactory.SOME_AD_ID, body) }
        }
    }

    @Test
    fun `should use service to get users all favorite ads`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawFavouriteAds(24, listOf(AdStatus.ACTIVE, AdStatus.EXPIRED)) }
            When { getUsersFavorites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkApiGetUserFavorites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should slice all pages of the users favorite ads`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawFavouriteAds(30, listOf(AdStatus.ACTIVE, AdStatus.EXPIRED)) }
            When { getUsersFavorites(DataFactory.SOME_USER_EMAIL, "0", DataFactory.SOME_SIZE) }
            Then { checkUsersFavoritesPageSize(20) }

            Given { stubNumberOfRandomRawFavouriteAds(30, listOf(AdStatus.ACTIVE, AdStatus.EXPIRED)) }
            When { getUsersFavorites(DataFactory.SOME_USER_EMAIL, "1", DataFactory.SOME_SIZE) }
            Then { checkUsersFavoritesPageSize(10) }

            Given { stubNumberOfRandomRawFavouriteAds(30, listOf(AdStatus.ACTIVE, AdStatus.EXPIRED)) }
            When { getUsersFavorites(DataFactory.SOME_USER_EMAIL, "2", DataFactory.SOME_SIZE) }
            Then { checkUsersFavoritesPageSize(0) }
        }
    }

    @Test
    fun `should use service to delete favorite ad`() = runTest {
        runUnitTest(robot) {
            When { deleteAdFromUsersFavorites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkApiDeleteFavoriteAd(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should use favourite adverts API to delete favorite ad`() = runTest {
        val body = RawRemoveFavouriteAdvertBody(email = DataFactory.SOME_USER_EMAIL)
        runUnitTest(robot) {
            When { deleteFavouriteAdvert(DataFactory.SOME_USER_ID, DataFactory.SOME_AD_ID, body) }
            Then { checkApiDeleteFavouriteAdvert(DataFactory.SOME_USER_ID, DataFactory.SOME_AD_ID, body) }
        }
    }

    private class Robot: BaseRobot {
        private val favoritesApi: CapiFavoritesApi = mockk(relaxed = true)
        private val favouriteAdvertsApi: FavouriteAdvertsApi = mockk(relaxed = true)
        private val authHeaders: ApiHeaders = mockk(relaxed = true)

        private lateinit var actualRawCapiAdList: RawCapiAdList

        private lateinit var testSubject: FavouritesService

        override fun setup() {
            testSubject = FavouritesService(favoritesApi, favouriteAdvertsApi)
        }

        fun stubNumberOfRandomRawFavouriteAds(
            number: Int,
            adStatuses: List<AdStatus> = emptyList()
        ) {
            coEvery { favoritesApi.getUsersFavorites(any(), any(), any(), any()) } returns
                RawCapiAdsFactory.createRawCapiAdList(number, adStatuses)
        }

        suspend fun addAdToUsersFavorites(
            username: String,
            adId: String
        ) {
            testSubject.addAdToUsersFavorites(authHeaders, username, adId)
        }

        suspend fun addFavouriteAdvert(
            userId: String,
            adId: String,
            body: RawAddFavouriteAdvertBody,
        ) {
            testSubject.addFavouriteAdvert(userId, adId, body)
        }

        suspend fun getUsersFavorites(
            username: String,
            page: String,
            size: String
        ) {
            actualRawCapiAdList = testSubject.getUsersFavorites(authHeaders, username, page, size)
        }

        suspend fun deleteAdFromUsersFavorites(
            username: String,
            adId: String
        ) {
            testSubject.deleteAdFromUsersFavorites(authHeaders, username, adId)
        }

        suspend fun deleteFavouriteAdvert(
            userId: String,
            adId: String,
            body: RawRemoveFavouriteAdvertBody,
        ) {
            testSubject.deleteFavouriteAdvert(userId, adId, body)
        }

        fun checkApiAddAd(
            expectedUsername: String,
            expectedAdId: String
        ) {
            coVerify { favoritesApi.addAdToUsersFavorites(authHeaders, expectedUsername, expectedAdId) }
        }

        fun checkApiAddFavouriteAdvert(
            expectedUserId: String,
            expectedAdId: String,
            expectedBody: RawAddFavouriteAdvertBody,
        ) {
            coVerify { favouriteAdvertsApi.addFavouriteAdvert(expectedUserId, expectedAdId, expectedBody) }
        }

        fun checkApiGetUserFavorites(
            expectedUsername: String,
            expectedPaged: String,
            expectedSize: String,
        ) {
            coVerify { favoritesApi.getUsersFavorites(authHeaders, expectedUsername, expectedPaged, expectedSize) }
        }

        fun checkApiDeleteFavoriteAd(
            expectedUsername: String,
            expectedAdId: String
        ) {
            coVerify { favoritesApi.deleteAdFromUsersFavorites(authHeaders, expectedUsername, expectedAdId) }
        }

        fun checkApiDeleteFavouriteAdvert(
            expectedUserId: String,
            expectedAdId: String,
            expectedBody: RawRemoveFavouriteAdvertBody,
        ) {
            coVerify { favouriteAdvertsApi.deleteFavouriteAdvert(expectedUserId, expectedAdId, expectedBody) }
        }

        fun checkUsersFavoritesPageSize(expectedSize: Int) {
            assertEquals(expectedSize, actualRawCapiAdList.rawAds?.size)
        }
    }
}