package com.gumtree.mobile.utils

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.utils.extensions.isNull

/**
 * typealias for page start and end indexes (first is the pageStartIndex, second is the pageEndIndex)
 */
typealias PageIndexes = Pair<Int?, Int?>

/**
 * Functionality to cut slices (create paging) of lists
 */
fun interface PageSlicer {
    /**
     * Calculate the startIndex and the endIndex of the page
     * @return - pair of the start and the end indexes of the page.
     * The start index could be NULL if the start of the page (page * size) exceeds or equals the total List size
     * The end index could be NULL if the start index is NULL
     */
    fun getPageIndexes(): PageIndexes
}

/**
 * The default page slicer cuts pages from the start of a List
 * @property page - the required page
 * @property size - the size of the page
 * @property totalListSize - the total size of the original list with data
 */
class DefaultPageSlicer(
    private val page: Int,
    private val size: Int,
    private val totalListSize: Int,
) : PageSlicer {

    override fun getPageIndexes(): PageIndexes {
        val pageStartIndex = when {
            page * size >= totalListSize -> null
            else -> page * size
        }
        val pageEndIndex = when {
            pageStartIndex.isNull() -> null
            (page + 1) * size > totalListSize -> (totalListSize - 1)
            else -> ((page + 1) * size) - 1
        }

        return PageIndexes(pageStartIndex, pageEndIndex)
    }
}

/**
 * The tailed page slicer cuts pages from the end of a List
 * @property page - the tailed page
 * @property size - the size of the tailed page
 * @property totalListSize - the total size of the original list with data
 */
class TailedPageSlicer(
    private val page: Int,
    private val size: Int,
    private val totalListSize: Int,
) : PageSlicer {

    /**
     * Calculate the startIndex and the endIndex of the tailed page
     * @return - pair of the start and the end indexes of the tailed page.
     * The start index could be NULL if the start of the page (page * size) exceeds or equals the totalListSize
     * The end index could be NULL if the start index is NULL
     */
    override fun getPageIndexes(): PageIndexes {
        val pageStartIndex = totalListSize - ((page + 1) * size)
        val pageEndIndex = totalListSize - (page * size) - 1
        return when {
            pageStartIndex <= ZERO && pageEndIndex <= ZERO -> PageIndexes(null, null)
            ZERO in pageStartIndex..<pageEndIndex -> PageIndexes(ZERO, pageEndIndex)
            else -> PageIndexes(pageStartIndex, pageEndIndex)
        }
    }
}
