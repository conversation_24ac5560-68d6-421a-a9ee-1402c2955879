package com.gumtree.mobile.features.categoryLanding

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.features.srp.SrpCapiQueryParamsFactory
import com.gumtree.mobile.responses.QueryParams

/**
 * The service delegates the data fetching to CategoryApi
 */
class CategoryLandingService(
    private val capiSearchApi: CapiSearchApi,
    private val srpCapiQueryParamsFactory: SrpCapiQueryParamsFactory,
) : CapiSearchApi by capiSearchApi {

    /**
     * We need to override CapiSearchApi.searchAds(), because we need to append some additional common CAPI search params
     * @param authorisationHeaders - the CAPI headers
     * @param searchOptions - the search request options
     * @param page - the search page (should be always 0 for similar ads)
     * @param pageSize - the page size
     */
    override suspend fun searchAds(
        authorisationHeaders: ApiHeaders,
        searchOptions: QueryParams,
        page: String,
        pageSize: String,
    ): RawCapiAdList {
        val allCapiQueryParams = srpCapiQueryParamsFactory.appendRequiredCapiSearchOptions(searchOptions)
        return capiSearchApi.searchAds(
            authorisationHeaders,
            allCapiQueryParams,
            page,
            pageSize,
        )
    }
}
