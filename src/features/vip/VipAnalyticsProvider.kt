package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.conversations.models.RawUserInfo
import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider
import com.gumtree.mobile.common.analytics.LISTING_PREFIX
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFormatter
import com.gumtree.mobile.utils.extensions.toBaseUrl
import org.jetbrains.annotations.VisibleForTesting

const val ANALYTICS_VIEW_LISTING_EVENT_NAME = "view_listing"
const val ANALYTICS_CALL_SELLER_EVENT_NAME = "contact_seller_call"
const val ANALYTICS_CONTACT_SELLER_LINK_EVENT_NAME = "contact_seller_external"
const val ANALYTICS_START_CHAT_EVENT_NAME = "contact_seller_chat"
const val ANALYTICS_PARTNER_ADS_CLICK_EVENT_NAME = "click_on_partner_ads"
const val ANALYTICS_CONTACT_SELLER_EXTERNAL_EVENT_NAME = "contact_seller_external"
const val ANALYTICS_PARTNER_ADS_BANNER_TYPE_VALUE = "Compact"

class VipAnalyticsProvider(
    commonAnalyticsProvider: CommonAnalyticsProvider,
    private val activeStatusFormatter: SellerProfileActiveStatusFormatter,
) : CommonAnalyticsProvider by commonAnalyticsProvider {

    fun getScreenViewEvent(): AnalyticsEventData {
        return AnalyticsEventData(ANALYTICS_VIEW_LISTING_EVENT_NAME)
    }

    fun getStartChatEvent(): AnalyticsEventData {
        return AnalyticsEventData(ANALYTICS_START_CHAT_EVENT_NAME)
    }

    fun getCallSellerEvent(): AnalyticsEventData {
        return AnalyticsEventData(ANALYTICS_CALL_SELLER_EVENT_NAME)
    }

    fun getContactLinkEvent(): AnalyticsEventData {
        return AnalyticsEventData(ANALYTICS_CONTACT_SELLER_LINK_EVENT_NAME)
    }

    fun getPartnerAdsClickEvent(partnershipDetails: PartnershipDetailsResponse): AnalyticsEventData {
        return AnalyticsEventData(
            eventName = ANALYTICS_PARTNER_ADS_CLICK_EVENT_NAME,
            parameters = mapOf(
                ANALYTICS_ADVERTISING_PARTNER_NAME_KEY to partnershipDetails.id,
                ANALYTICS_ADVERTISING_LINK_URL to partnershipDetails.clickOutUrl,
                ANALYTICS_ADVERTISING_LINK_DOMAIN to "${partnershipDetails.clickOutUrl.toBaseUrl().first}://${partnershipDetails.clickOutUrl.toBaseUrl().second}",
                ANALYTICS_ADVERTISING_BANNER_TYPE_KEY to ANALYTICS_PARTNER_ADS_BANNER_TYPE_VALUE,
            ),
        )
    }

    fun getSellerContactExternalEvent(
        analyticsScreenParams: Map<String, String>,
        linkUrl: String?,
        linkText: String,
    ): AnalyticsEventData {
        return AnalyticsEventData(
            eventName = ANALYTICS_CONTACT_SELLER_EXTERNAL_EVENT_NAME,
            parameters = analyticsScreenParams + mapOf(
                AnalyticsParams.VIP.URL_PARTNER to "emg",
                ANALYTICS_ADVERTISING_LINK_URL to linkUrl.orEmpty(),
                ANALYTICS_ADVERTISING_LINK_DOMAIN to linkUrl?.let { "${it.toBaseUrl().first}://${it.toBaseUrl().second}" }
                    .orEmpty(),
                ANALYTICS_ADVERTISING_LINK_TEXT to linkText,
            ),
        )
    }

    fun getScreenParams(
        rawAdDetails: RawCapiAd,
        rawUserProfileData: RawPapiUserProfile?,
        rawUserInfoData: RawUserInfo?,
    ): Map<String, String> {
        val commonListingParams = getCommonListingParams(LISTING_PREFIX, rawAdDetails)

        return commonListingParams + mapOf(
            // the below to parameters are not in commonListingParams as they are different from the ad/web implementation
            ANALYTICS_LISTING_CATEGORY_KEY to rawAdDetails.category.name.orEmpty(),
            ANALYTICS_LISTING_CATEGORY_ID_KEY to rawAdDetails.category.id.orEmpty(),
            AnalyticsParams.User.RATING_AVERAGE to rawUserProfileData?.rating?.averageRating?.toString().orEmpty(),
            AnalyticsParams.User.RATING_COUNT to rawUserProfileData?.rating?.ratingCounts?.total?.toString().orEmpty(),
            AnalyticsParams.User.LAST_ACTIVE to activeStatusFormatter.getTimeAgoLabel(rawUserInfoData?.lastActive).orEmpty(),
            ANALYTICS_SELLER_ID to rawAdDetails.userId.orEmpty(),
            ANALYTICS_LISTING_PARTNER to ListingPartner.getListingPartnerValue(rawAdDetails.userId.orEmpty()),
        )
    }
}

private const val ANALYTICS_LISTING_CATEGORY_KEY = "listing_category"
private const val ANALYTICS_LISTING_CATEGORY_ID_KEY = "listing_category_id"
private const val ANALYTICS_ADVERTISING_PARTNER_NAME_KEY = "advertising_partner_name"
private const val ANALYTICS_ADVERTISING_BANNER_TYPE_KEY = "advertising_banner_type"
private const val ANALYTICS_ADVERTISING_LINK_URL = "link_url"
private const val ANALYTICS_ADVERTISING_LINK_DOMAIN = "link_domain"
private const val ANALYTICS_ADVERTISING_LINK_TEXT = "link_text"

@VisibleForTesting
const val ANALYTICS_SELLER_ID = "seller_user_id"

@VisibleForTesting
const val ANALYTICS_LISTING_PARTNER = "listing_partner"

enum class ListingPartner(val userId: String, val paramValue: String) {
    MOTORS("53458489", "motors"),
    ;

    companion object {
        fun getListingPartnerValue(userId: String): String = entries.find { it.userId == userId }?.paramValue.orEmpty()
    }
}
