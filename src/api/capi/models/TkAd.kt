package com.gumtree.mobile.api.capi.models

import com.gumtree.mobile.api.capi.Namespaces
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.tickaroo.tikxml.annotation.Attribute
import com.tickaroo.tikxml.annotation.Element
import com.tickaroo.tikxml.annotation.Path
import com.tickaroo.tikxml.annotation.PropertyElement
import com.tickaroo.tikxml.annotation.TextContent
import com.tickaroo.tikxml.annotation.Xml

@Xml(
    name = "ad:ad",
    writeNamespaces = [
        Namespaces.NS_AD,
        Namespaces.NS_CATEGORY,
        Namespaces.NS_LOCATION,
        Namespaces.NS_ATTRIBUTE,
        Namespaces.NS_TYPES,
        Namespaces.NS_PICTURE,
        Namespaces.NS_USER,
        Namespaces.NS_FEATURE,
    ],
)
data class TkAd(

    @Attribute(name = "locale")
    val locale: String? = null,

    @Attribute(name = "version")
    val version: String? = null,

    @Attribute(name = "id")
    val id: String? = null,

    @PropertyElement(name = "ad:title")
    val title: String? = null,

    @PropertyElement(name = "ad:description")
    val description: String? = null,

    @Element(name = "cat:category")
    val category: TkAdCategory? = null,

    @Path("loc:locations")
    @Element(name = "loc:location")
    val locations: List<TkAdLocation>? = null,

    @Element(name = "ad:ad-type")
    val type: TkAdType? = null,

    @PropertyElement(name = "ad:phone")
    val phone: String? = null,

    @PropertyElement(name = "ad:email")
    val email: String? = null,

    @PropertyElement(name = "ad:visible-on-map")
    val visibleOnMap: String? = null,

    @PropertyElement(name = "ad:poster-contact-name")
    val posterName: String? = null,

    @PropertyElement(name = "ad:poster-contact-email")
    val posterEmail: String? = null,

    @Element(name = "ad:ad-address")
    val address: TkAdAddress? = null,

    @Element(name = "ad:price")
    val price: TkAdPrice? = null,

    @PropertyElement(name = "ad:highest-price")
    val highestPrice: String? = null,

    @Element(name = "ad:price-frequency")
    val priceFrequency: TkAdPriceFrequency? = null,

    @Path("pic:pictures")
    @Element(name = "pic:picture")
    val pictures: List<TkAdPicture>? = null,

    @Path("attr:attributes")
    @Element
    val attributes: List<TkAdAttribute>? = null,

    @PropertyElement(name = "ad:start-date-time")
    val startDateTime: String? = null,

    @PropertyElement(name = "ad:end-date-time")
    val endDateTime: String? = null,

    @PropertyElement(name = "ad:modification-date-time")
    val modificationDateTime: String? = null,

    @PropertyElement(name = "ad:rank")
    val rank: String? = null,

    @PropertyElement(name = "ad:view-ad-count")
    val viewAdCount: String? = null,

    @PropertyElement(name = "ad:map-view-count")
    val mapViewCount: String? = null,

    @PropertyElement(name = "ad:phone-click-count")
    val phoneClickCount: String? = null,

    @PropertyElement(name = "ad:ad-external-reference-id")
    val externalReferenceId: String? = null,

    @PropertyElement(name = "ad:ad-source-id")
    val sourceId: String? = null,

    @PropertyElement(name = "ad:neighborhood")
    val neighborhood: String? = null,

    @PropertyElement(name = "ad:user-id")
    val userId: String? = null,

    @PropertyElement(name = "ad:account-id")
    val accountId: String? = null,

    @PropertyElement(name = "ad:account-vat")
    val accountVAT: String? = null,

    @PropertyElement(name = "ad:replyTemplate")
    val replyTemplate: String? = null,

    @PropertyElement(name = "ad:region")
    val region: String? = null,

    @Element(name = "ad:ad-status")
    val status: TkAdStatus? = null,

    @Element(name = "ad:link")
    val links: List<TkAdLink>? = null,

    @Element(name = "user:user-logos")
    val userLogos: TkUserLogos? = null,

    @Element(name = "ad:extended-info")
    val extendedInfo: TkAdExtendedInfo? = null,

    @Element(name = "ad:price-options")
    val priceOptions: TkAdPriceOptions? = null,

    @Element(name = "ad:contact-methods")
    val contactMethods: TkAdContactMethods? = null,

    @Element(name = "feat:features-active")
    val featuresActive: TkFeaturesActive? = null,

    @Element(name = "feat:feature-group-active")
    val featuresGroupActive: TkFeatureGroupActive? = null,

    @Path("ad:adSlots")
    @Element(name = "ad:adSlot")
    val adSlots: List<TkAdSlot>? = null,
)

@Xml(name = "ad:price-frequency")
data class TkAdPriceFrequency(
    @Element(name = "types:value")
    val value: TkTypeValue? = null,
)

@Xml(name = "ad:adSlot")
data class TkAdSlot(
    @Element(name = "ad:adSlotType")
    val adSlotType: TkAdSlotType? = null,

    @Element(name = "ad:SrpTreebay")
    val srpTreebay: TkAdSrpTreebay? = null,

    @Element(name = "ad:vipCustomTab")
    val vipCustomTab: TkAdVipCustomTab? = null,

    @Element(name = "ad:vipInline")
    val vipInline: TkAdVipInline? = null,
)

@Xml(name = "ad:adSlotType")
data class TkAdSlotType(
    @PropertyElement(name = "ad:value")
    val value: String? = null,
)

@Xml(name = "ad:SrpTreebay")
data class TkAdSrpTreebay(
    @Attribute(name = "searchUrl")
    val searchUrl: String? = null,

    @Attribute(name = "itemUrl")
    val itemUrl: String? = null,
)

@Xml(name = "ad:vipCustomTab")
data class TkAdVipCustomTab(
    @Attribute(name = "labelIcon")
    val labelIcon: String? = null,

    @Attribute(name = "labelText")
    val labelText: String? = null,

    @Attribute(name = "id")
    val id: String? = null,

    @Attribute(name = "targetURL")
    val targetURL: String? = null,
)

@Xml(name = "ad:vipInline")
data class TkAdVipInline(
    @Attribute(name = "position")
    val position: String? = null,
)

@Xml(name = "feat:feature-group-active")
data class TkFeatureGroupActive(
    @Attribute(name = "display")
    val display: Boolean? = null,

    @Attribute(name = "name")
    val name: String? = null,

    @Attribute(name = "group")
    val group: String? = null,

    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,
)

@Xml(name = "feat:features-active")
data class TkFeaturesActive(
    @Element(name = "feat:feature-active")
    val feature: List<TkFeatureActive>? = null,
)

@Xml(name = "feat:feature-active")
data class TkFeatureActive(
    @Attribute(name = "display")
    val display: String? = null,

    @Attribute(name = "name")
    val name: String? = null,

    @PropertyElement(name = "feat:start-date-time")
    val startDateTime: String? = null,

    @PropertyElement(name = "feat:end-date-time")
    val endDateTime: String? = null,
)

@Xml(name = "ad:contact-methods")
data class TkAdContactMethods(
    @Element(name = "ad:contact-method")
    val contactMethods: List<TkAdContactMethod>? = null,
)

@Xml(name = "ad:contact-method")
data class TkAdContactMethod(
    @Attribute(name = "label")
    val label: String? = EMPTY_STRING,

    @Attribute(name = "enabled")
    val enabled: String? = null,

    @Attribute(name = "name")
    val name: String? = null,
)

@Xml(name = "ad:price-options")
data class TkAdPriceOptions(
    @Element(name = "ad:price-option")
    val priceOptions: List<TkAdPriceOption>? = null,
)

@Xml(name = "ad:price-option")
data class TkAdPriceOption(
    @Element(name = "ad:property")
    val adProperties: List<TkAdProperty>? = null,

    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,

    @Attribute(name = "name")
    val name: String? = null,
)

@Xml(name = "ad:extended-info")
data class TkAdExtendedInfo(
    @Attribute(name = "locale")
    val locale: String? = null,

    @Attribute(name = "version")
    val version: String? = null,

    @Attribute(name = "parent-id")
    val parentId: String? = null,

    @Element
    val propertyGroups: List<TkAdPropertyGroup>? = null,

    @PropertyElement(name = "ad:property-value")
    val propertyValue: String? = null,

    @PropertyElement(name = "ad:property-display")
    val propertyDisplay: String? = null,

    @Element
    val extendedInfoLinks: List<TkAdLink>?,
)

@Xml(name = "ad:link")
data class TkAdLink(

    @Attribute(name = "name")
    val name: String? = null,

    @Attribute(name = "href")
    val href: String? = null,

    @Attribute(name = "rel")
    val rel: String? = null,
)

@Xml(name = "ad:property-group")
data class TkAdPropertyGroup(
    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,

    @Attribute(name = "name")
    val name: String? = null,

    @Element(name = "ad:property-group")
    val propertyGroups: List<TkAdPropertyGroup>? = null,

    @Element(name = "ad:property")
    val properties: List<TkAdProperty>? = null,
)

@Xml(name = "ad:property")
data class TkAdProperty(
    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,

    @Attribute(name = "sub-type")
    val subType: String? = null,

    @Attribute(name = "name")
    val name: String? = null,

    @Element(name = "ad:value")
    val values: List<TkAdpropertyValue>? = null,
)

@Xml(name = "ad:value")
data class TkAdpropertyValue(
    @TextContent
    val value: String? = null,

    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,
)

@Xml(name = "user:user-logos")
data class TkUserLogos(
    @Element(name = "pic:picture")
    val userPicture: List<TkUserPicture>? = null,
)

@Xml(name = "pic:picture")
data class TkUserPicture(
    @Attribute(name = "rel")
    val rel: String? = null,

    @Element(name = "pic:link")
    val userPictureLinks: List<TkAdPictureLink>? = null,
)

@Xml(name = "ad:ad-status")
data class TkAdStatus(
    @PropertyElement(name = "ad:value")
    val value: String? = null,
)

@Xml(name = "attr:attribute")
data class TkAdAttribute(
    @Attribute(name = "name")
    val name: String? = null,

    @Attribute(name = "type")
    val type: String? = null,

    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,

    @Attribute(name = "sub-type")
    val subType: String? = null,

    @Attribute(name = "dependent-name")
    val dependentName: String? = null,

    @Attribute(name = "parent-name")
    val parentName: String? = null,

    @Attribute(name = "search-style")
    val searchStyle: String? = null,

    @Attribute(name = "search-param")
    val searchParam: String? = null,

    @Attribute(name = "write")
    val write: String? = null,

    @Attribute(name = "read")
    val read: String? = null,

    @Element(name = "attr:value")
    val valueElements: List<TkAdValueElement>? = null,

    @Element(name = "attr:supported-value")
    val supportedValues: List<TkAdSupportedValue>? = null,
)

@Xml(name = "attr:supported-value")
data class TkAdSupportedValue(
    @TextContent
    val value: String? = null,

    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,
)

@Xml(name = "attr:value")
data class TkAdValueElement(
    @TextContent
    val value: String? = null,

    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,
)

@Xml(name = "pic:picture")
data class TkAdPicture(
    @Element(name = "pic:link")
    val links: List<TkAdPictureLink>? = null,
)

@Xml(name = "pic:link")
data class TkAdPictureLink(
    @Attribute(name = "href")
    val href: String? = null,

    @Attribute(name = "rel")
    val rel: String? = null,
)

@Xml(name = "ad:ad-address")
data class TkAdAddress(
    @PropertyElement(name = "types:full-address")
    val fullAddress: String? = null,

    @PropertyElement(name = "types:street")
    val street: String? = null,

    @PropertyElement(name = "types:city")
    val city: String? = null,

    @PropertyElement(name = "types:radius")
    val radius: String? = null,

    @PropertyElement(name = "types:state")
    val state: String? = null,

    @PropertyElement(name = "types:zip-code")
    val zipCode: String? = null,

    @PropertyElement(name = "types:visible-on-map")
    val visibleOnMap: String? = null,

    @PropertyElement(name = "types:country")
    val country: String? = null,

    @PropertyElement(name = "types:longitude")
    val longitude: String? = null,

    @PropertyElement(name = "types:latitude")
    val latitude: String? = null,
)

@Xml(name = "ad:price")
data class TkAdPrice(
    @PropertyElement(name = "types:amount")
    val amount: String? = null,

    @Element(name = "types:price-type")
    val priceType: TkPriceType? = null,

    @Element(name = "types:currency-iso-code")
    val currencyIsoCode: TkCurrencyIsoCode? = null,
)

@Xml(name = "types:price-type")
data class TkPriceType(
    @Element(name = "types:value")
    val value: TkTypeValue? = null,
)

@Xml(name = "types:currency-iso-code")
data class TkCurrencyIsoCode(
    @Element(name = "types:value")
    val value: TkTypeValue? = null,
)

@Xml(name = "types:value")
data class TkTypeValue(
    @Attribute(name = "localized-label")
    val localizedLabel: String? = null,

    @TextContent
    val value: String? = null,
)

@Xml(name = "ad:type")
data class TkAdType(
    @PropertyElement(name = "ad:value")
    val value: String? = null,
)

@Xml(name = "cat:category")
data class TkAdCategory(
    @Attribute(name = "id")
    val id: String? = null,

    @PropertyElement(name = "cat:id-name")
    val idName: String? = null,

    @PropertyElement(name = "cat:localized-name")
    val localizedName: String? = null,

    @PropertyElement(name = "cat:max-location-level")
    val maxLocationLevel: String? = null,

    @Element
    val childCategories: List<TkAdCategory>? = null,

    @Element(name = "cat:flags")
    val flags: TkAdCategoryFlag? = null,
)

@Xml
data class TkAdCategoryFlag(
    @Element(name = "cat:flag")
    val flags: List<String>? = listOf(),
)

@Xml(name = "loc:location")
data class TkAdLocation(
    @Attribute(name = "id")
    val id: String? = null,

    @PropertyElement(name = "loc:id-name")
    val idName: String? = null,

    @PropertyElement(name = "loc:localized-name")
    val localizedName: String? = null,

    @PropertyElement(name = "loc:longitude")
    val longitude: String? = null,

    @PropertyElement(name = "loc:latitude")
    val latitude: String? = null,

    @PropertyElement(name = "loc:parent-id")
    val parentId: String? = null,

    @PropertyElement(name = "loc:children-count")
    val childrenCount: Int? = null,
)
