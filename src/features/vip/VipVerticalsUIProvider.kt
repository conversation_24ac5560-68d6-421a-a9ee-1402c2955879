package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.DestinationActionDto
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipButtonCardDto
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.runOrNull

class VipVerticalsUIProvider(
    private val vipSpecificationFactory: VipSpecificationFactory,
    private val vipHpiFactory: VipHpiFactory,
    private val titleFactory: TitleFactory,
    private val vipPartnershipAnalyticsProvider: VipPartnershipAnalyticsProvider,
) {

    fun getVipCarsChipsRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem> {
        return vipSpecificationFactory.buildVipCarsChipsRow(rawAdDetailsData)
    }

    fun getVipCarsSpecificationRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem> {
        return vipSpecificationFactory.buildVipCarsSpecificationsRow(rawAdDetailsData)
    }

    fun getVipCarsHpiCheckRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem>? {
        return vipHpiFactory.buildVipCarsHpiCheckRow(rawAdDetailsData)
    }

    fun getVipPetsKeyInfoRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem> {
        return vipSpecificationFactory.buildVipPetsKeyInfoRow(rawAdDetailsData)
    }

    fun getVipMobilePhonesKeyInfoRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem>? {
        return runOrNull(CategoriesTreeCache.isMobilePhones(rawAdDetailsData.category.id)) {
            vipSpecificationFactory.buildVipMobilePhonesKeyInfoRow(rawAdDetailsData)
        }
    }

    fun getVipPropertiesKeyInfoRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem> {
        return vipSpecificationFactory.buildVipPropertiesKeyInfoRow(rawAdDetailsData)
    }

    fun getVipJobsKeyInfoRow(rawAdDetailsData: RawCapiAd): RowLayout<UiItem> {
        return vipSpecificationFactory.buildVipJobsKeyInfoRow(rawAdDetailsData)
    }

    fun getVipCarsSpecificationTitleRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.TITLE_ROW,
            data = listOf(
                titleFactory.buildLeftTitleCard(
                    titleText = VipScreenUiConfiguration.SPECIFICATIONS_ROW_TITLE,
                    titleSize = TitleCardDto.Size.SMALL,
                ),
            ),
        )
    }

    fun getVipCarsHpiTitleRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.TITLE_IMAGE_ROW,
            data = listOf(
                titleFactory.buildTitleImageCard(
                    titleText = VipScreenUiConfiguration.HPI_ROW_TITLE_TEXT,
                    titleIcon = TitleImageCardDto.Icon.HPI,
                ),
            ),
        )
    }

    fun getVipCarsHpiButtonRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.VIP_BUTTON_ROW,
            data = listOf(
                VipButtonCardDto(
                    text = VipScreenUiConfiguration.VIP_HPI_GET_FULL_HISTORY_TEXT,
                    size = VipButtonCardDto.Size.LARGE,
                    buttonType = VipButtonCardDto.Type.SECONDARY,
                    iconType = null,
                    action = DestinationActionDto(
                        destination = DestinationRoute.HPI_GET_FULL_HISTORY.buildExternal(
                            displayDialog = true,
                            analyticsData = vipPartnershipAnalyticsProvider.getHPIClickEvent(),
                        ),
                    ),
                ),
            ),
        )
    }
}
