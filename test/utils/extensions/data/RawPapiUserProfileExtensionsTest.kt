package utils.extensions.data

import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.features.screens.layoutsData.SellerLink
import com.gumtree.mobile.features.screens.layoutsData.VipSellerProfileCardDto
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.CurrentDateProvider
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import com.gumtree.mobile.utils.extensions.data.toVipSellerProfileCardDto
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.ZonedDateTimeFactory
import tools.layoutsDataFactory.SellerLinkFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.runUnitTest
import java.time.LocalDate
import java.time.ZoneId
import java.util.*

class RawPapiUserProfileExtensionsTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return the VIP seller name`() {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerProfile(firstName = DataFactory.ANOTHER_USER_FIRST_NAME) }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerName(DataFactory.ANOTHER_USER_FIRST_NAME) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "John, Johnson, JJ",
        "Merry, An, MA",
        "Merry, '', M",
        "Steven12345, '', S",
    )
    fun `should return the VIP seller names abbreviation`(firstName: String, lastName: String, expected: String) {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.TRADE_SELLER) }
            Given { stubSellerProfile(firstName = firstName, lastName = lastName) }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerAbbreviation(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2.7f, 2.7f",
        "3.5f, 3.5f",
        "5.0f, 5.0f",
    )
    fun `should return the VIP seller average rating`(averageRating: Float, expected: Float) {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerProfile(averageRating = averageRating) }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerAverageRating(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "1, 1",
        "7, 7",
        "15, 15",
        "120, 120",
    )
    fun `should return the VIP seller total ratings`(totalRatings: Int, expected: Int) {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.TRADE_SELLER) }
            Given { stubSellerProfile(totalRatings = totalRatings) }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerTotalRatings(expected) }
        }
    }

    @Test
    fun `should return the VIP seller type`() {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerProfile() }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2005-08-22T06:00:00, Member since 2005",
        "2013-11-04T06:00:00, Member since 2013",
        "2015-12-10T06:00:00, Member since 2015",
        "2017-02-02T06:00:00, Member since 2017",
        "2021-03-16T06:00:00, Member since 2021",
        "2022-05-21T06:00:00, Member since 2022",
    )
    fun `should format date as VIP Member since for past years`(registrationDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerProfile(registrationDate = registrationDate) }
            When { toVipSellerProfileCardDto() }
            Then { checkVipMemberSinceLabel(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "01-10T05:00:00, Member since January",
        "03-31T06:00:55, Member since March",
        "06-15T06:00:11, Member since June",
        "08-22T06:23:25, Member since August",
        "10-20T06:45:00, Member since October",
        "12-24T10:10:00, Member since December",
    )
    fun `should format date as VIP Member since for current year`(registrationDateSuffix: String, expectedPrefix: String) {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }

            Given { stubSellerProfile(registrationDate = "$currentYear-$registrationDateSuffix") }
            When { toVipSellerProfileCardDto() }
            Then { checkVipMemberSinceLabel("$expectedPrefix $currentYear") }
        }
    }

    @Test
    fun `should return VIP seller profile with seller profile destination`() {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerProfile(registrationDate = "$currentYear-01-10T06:00:00") }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerProfileDestinationRoute(DestinationRoute.SELLER_PROFILE.screenName) }
        }
    }

    @Test
    fun `VIP seller profile with expected seller link`() {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerLink(SellerLinkFactory.createLink()) }
            Given { stubSellerProfile() }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerProfileSellerLink(SellerLinkFactory.createLink()) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "${DataFactory.SOME_SELLER_ACTIVE_STATUS_TEXT}, ${DataFactory.SOME_SELLER_ACTIVE_STATUS_TEXT}",
        "${DataFactory.ANOTHER_SELLER_ACTIVE_STATUS_TEXT}, ${DataFactory.ANOTHER_SELLER_ACTIVE_STATUS_TEXT}",
        " ,  ",
    )
    fun `should return VIP seller profile with expected seller active status`(status: String?, expected: String?) {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerActiveStatus(status) }
            Given { stubSellerProfile() }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerProfileActiveStatus(expected) }
        }
    }

    @Test
    fun `should return VIP seller profile with email verified text`() {
        runUnitTest(robot) {
            Given { stubSellerType(VipScreenUiConfiguration.PRIVATE_SELLER) }
            Given { stubSellerProfile() }
            When { toVipSellerProfileCardDto() }
            Then { checkVipSellerProfileEmailVerified(SellerProfileScreenUiConfiguration.EMAIL_ADDRESS_VERIFIED_TEXT) }
        }
    }

    private class Robot: BaseRobot {
        private val dateFormatter = createUKDateTimeFormatter("yyyy-MM-dd'T'HH:mm:ss")
//        private val dateFormatter = createUKDateTimeFormatter("dd/MM/yyyy")
        val currentYear by lazy { Calendar.getInstance().get(Calendar.YEAR) }

        private lateinit var actualVipSellerProfileCardDtoResult: VipSellerProfileCardDto

        private lateinit var rawSellerProfile: RawPapiUserProfile
        private lateinit var sellerType: String
        private var sellerActiveStatus: String? = null
        private var sellerLink: SellerLink? = null

        override fun setup() {
            mockkObject(CurrentDateProvider)
            every { CurrentDateProvider.getCurrentDate() } returns ZonedDateTimeFactory.createZonedDate("$currentYear-12-18T06:00:00")
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubSellerProfile(
            firstName: String = DataFactory.SOME_USER_FIRST_NAME,
            lastName: String = DataFactory.SOME_USER_LAST_NAME,
            averageRating: Float = 0.0f,
            totalRatings: Int = 0,
            registrationDate: String = "2001-12-18T06:00:00"
        ) {
            rawSellerProfile = RawPapiUserProfileFactory.createRawUserProfileWithRating(
                displayName = firstName,
                lastName = lastName,
                registrationDate = Date.from(LocalDate.parse(registrationDate, dateFormatter).atStartOfDay(ZoneId.systemDefault()).toInstant()),
                averageRating = averageRating,
                totalRatingsCount = totalRatings,
            )
        }

        fun stubSellerType(type: String) {
            sellerType = type
        }

        fun stubSellerActiveStatus(status: String?) {
            sellerActiveStatus = status
        }

        fun stubSellerLink(link: SellerLink?) {
            sellerLink = link
        }

        fun toVipSellerProfileCardDto() {
            actualVipSellerProfileCardDtoResult = rawSellerProfile.toVipSellerProfileCardDto(
                sellerType,
                DestinationRoute.SELLER_PROFILE.build(),
                sellerActiveStatus,
                sellerLink,
            )
        }

        fun checkVipSellerName(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerName)
        }

        fun checkVipSellerAbbreviation(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerAbbreviation)
        }

        fun checkVipSellerAverageRating(expected: Float) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerAverageRating)
        }

        fun checkVipSellerTotalRatings(expected: Int) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerTotalRatings)
        }

        fun checkVipSellerType(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerType)
        }

        fun checkVipMemberSinceLabel(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerMembership)
        }

        fun checkVipSellerProfileDestinationRoute(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.destination?.route)
        }

        fun checkVipSellerProfileActiveStatus(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerActiveStatus)
        }

        fun checkVipSellerProfileEmailVerified(expected: String?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerEmailVerified)
        }

        fun checkVipSellerProfileSellerLink(expected: SellerLink?) {
            assertEquals(expected, actualVipSellerProfileCardDtoResult.sellerLink)
        }
    }
}