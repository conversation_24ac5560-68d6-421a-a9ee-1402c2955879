package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.routes.DestinationDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("LISTING_CARD")
data class ListingCardDto(
    @SerialName("adId")
    val adId: String,
    @SerialName("title")
    val title: String,
    @SerialName("destination")
    val destination: DestinationDto?,
    @SerialName("location")
    val location: String? = null,
    @SerialName("price")
    val price: String? = null,
    @SerialName("images")
    val images: List<Image>? = null,
    @SerialName("isFeatured")
    val isFeatured: Boolean? = null,
    @SerialName("isUrgent")
    val isUrgent: Boolean? = null,
    @SerialName("status")
    val status: AdStatus? = null,
    @SerialName("favouriteAnalyticsEventData")
    val favouriteAnalyticsEventData: AnalyticsEventData? = null,
    @SerialName("impressAnalyticsEventData")
    val impressAnalyticsEventData: AnalyticsEventData? = null,
    @SerialName("analyticsParameters")
    val analyticsParameters: Map<String, String>? = null,
) : UiItem

@Serializable
@SerialName("LISTING_CARD_LARGE")
data class LargeListingCardDto(
    @SerialName("adId")
    val adId: String,
    @SerialName("title")
    val title: String,
    @SerialName("destination")
    val destination: DestinationDto?,
    @SerialName("location")
    val location: String? = null,
    @Deprecated("Use `priceInfo` instead", ReplaceWith("priceInfo"))
    @SerialName("price")
    val price: String? = null,
    @SerialName("priceInfo")
    val priceInfo: PriceInfoDto? = null,
    @SerialName("images")
    val images: List<Image>? = null,
    @SerialName("isFeatured")
    val isFeatured: Boolean? = null,
    @SerialName("isUrgent")
    val isUrgent: Boolean? = null,
    @SerialName("status")
    val status: AdStatus? = null,
    @SerialName("favouriteAnalyticsEventData")
    val favouriteAnalyticsEventData: AnalyticsEventData? = null,
    @SerialName("impressAnalyticsEventData")
    val impressAnalyticsEventData: AnalyticsEventData? = null,
    @SerialName("timePostedLabel")
    val timePostedLabel: String,
    @SerialName("attributes")
    val attributes: List<LabelDto>,
    @SerialName("analyticsParameters")
    val analyticsParameters: Map<String, String>? = null,
) : UiItem

@Serializable
data class PriceInfoDto(
    @SerialName("amount")
    val amount: String,
    @SerialName("vat")
    val vat: String?,
    @SerialName("tooltipText")
    val tooltipText: String?,
)
