package tools

import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.http.Headers
import io.ktor.http.HeadersBuilder

/**
 * Generate call headers data for testing
 */
object CallHeadersFactory {

    fun createUnAuthHeaders(
        vararg additionalHeaders: Pair<String, String>,
    ): Headers {
        return HeadersBuilder().apply {
            additionalHeaders.forEach {
                append(it.first, it.second)
            }
        }.build()
    }

    fun createAuthHeaders(
        vararg additionalHeaders: Pair<String, String>,
    ): Headers {
        return HeadersBuilder().apply {
            append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL)
            append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN)
            additionalHeaders.forEach {
                append(it.first, it.second)
            }
        }.build()
    }
}
