package features.vip

import com.gumtree.mobile.features.vip.ListingPartner
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EmptySource
import org.junit.jupiter.params.provider.ValueSource
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class ListingPartnerTest {
    private val robot = Robot()

    @Test
    fun `should return motors listing partner name`() {
        runUnitTest(robot) {
            Given { stubUserId("53458489") }
            When { getListingPartnerValue() }
            Then { checkPartnerName("motors") }
        }
    }

    @ParameterizedTest
    @EmptySource
    @ValueSource(strings = arrayOf("  ", "\t", "\n", "534584"))
    fun `should return empty string when empty or other userId`(userId: String) {
        runUnitTest(robot) {
            Given { stubUserId(userId) }
            When { getListingPartnerValue() }
            Then { checkPartnerName("") }
        }
    }

    inner class Robot: BaseRobot {
        private lateinit var userId: String
        private lateinit var actualPartnerName: String

        fun stubUserId(userId: String) {
            this.userId = userId
        }

        fun getListingPartnerValue() {
            actualPartnerName = ListingPartner.getListingPartnerValue(userId)
        }

        fun checkPartnerName(expected: String) {
            assertEquals(expected, actualPartnerName)
        }
    }
}
