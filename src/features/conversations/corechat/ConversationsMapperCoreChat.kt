package com.gumtree.mobile.features.conversations.corechat

import com.gumtree.mobile.api.conversations.models.RawMessageId
import com.gumtree.mobile.features.conversations.ConversationsTimeAgoFormatter
import com.gumtree.mobile.features.conversations.CreateConversationResponseDto
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.wrapScreenDataWithRowLayouts
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.data.toConversationCard
import features.conversations.EnrichedConversation

/**
 * Mapping the RawConversationList data (core chat data) into conversations mobile application data
 * @property conversationsTimeAgoFormatter - the conversation date time ago formatter,
 * to calculate the time difference between the last message post timestamp and the current date (now)
 */
class ConversationsMapperCoreChat(private val conversationsTimeAgoFormatter: ConversationsTimeAgoFormatter) {

    fun mapScreen(
        enrichedConversations: List<EnrichedConversation>,
        listingGridSizes: GridSizes,
    ): Pair<PortraitData, LandscapeData?> {
        val now = conversationsTimeAgoFormatter.getCurrentDate()

        val listWithConversationCards = enrichedConversations.map {
            it.toConversationCard(conversationsTimeAgoFormatter, now)
        }

        return listWithConversationCards.wrapScreenDataWithRowLayouts(
            listingGridSizes,
            RowLayoutType.CONVERSATION_ROW,
        )
    }

    fun mapCreateConversationResponse(
        rawData: RawMessageId,
    ): CreateConversationResponseDto {
        return CreateConversationResponseDto(
            destination = DestinationRoute.CONVERSATION.build(
                ApiQueryParams.CONVERSATION_ID to rawData.id,
            ),
        )
    }
}
