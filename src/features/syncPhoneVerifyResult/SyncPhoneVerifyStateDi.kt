package com.gumtree.mobile.features.syncPhoneVerifyResult

import com.gumtree.mobile.di.SELLER_HEADERS_PROVIDER
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Provides all dependencies for Seller profile feature
 */
val syncPhoneVerifyStateModule = module {

    single { SyncResponseMapper() }

    single<SyncPhoneVerifyStateRepository> {
        DefaultSyncPhoneVerifyStateRepository(
            syncPhoneVerifyStateService = getFromKoin(),
            syncResponseMapper = getFromKoin(),
            sellerHeadersProvider = getFromKoin(SELLER_HEADERS_PROVIDER),
        )
    }
    single {
        SyncPhoneVerifyStateService(
            sellerApi = getFromKoin(),
            userServiceApi = getFromKoin(),
        )
    }
}
