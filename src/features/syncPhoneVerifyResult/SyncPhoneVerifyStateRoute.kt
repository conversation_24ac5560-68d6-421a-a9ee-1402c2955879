package com.gumtree.mobile.features.syncPhoneVerifyResult

import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.readQueryParam
import com.gumtree.mobile.utils.extensions.respondSuccess
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import org.koin.ktor.ext.inject

const val SYNC_PHONE_VERIFY_RESULT_PATH = "$API_V1_MAIN_PATH/syncphoneverifystate"
const val SYNC_PHONE_VERIFY_SCREEN_PATH = "$SYNC_PHONE_VERIFY_RESULT_PATH/sync"

fun Route.syncPhoneVerifyStateRoute() {
    val repository by inject<SyncPhoneVerifyStateRepository>()

    get(SYNC_PHONE_VERIFY_SCREEN_PATH) {
        val callHeaders: Headers = call.request.headers
        val userId: String = callHeaders[ApiQueryParams.USER_ID].toString()
        val emailAddress: String = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL].toString()
        val adId: String = call.readQueryParam(ApiQueryParams.AD_ID, true)
        val editorId: String = call.readQueryParam(ApiQueryParams.EDITOR_ID, false)
        val response = repository.readScreen(callHeaders, userId, emailAddress, adId, editorId)

        call.respondSuccess(response)
    }
}
