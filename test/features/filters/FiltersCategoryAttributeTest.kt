package features.filters

import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import com.gumtree.mobile.features.screens.layoutsData.InputCardDto
import com.gumtree.mobile.responses.QueryParams
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.FiltersCategoryAttributesFactory
import tools.runUnitTest

class FiltersCategoryAttributeTest {

    private val robot = Robot()

    @Test
    fun `should find category attribute when attribute exist`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributes() }
            When { findAttributeByName("price") }
            Then { checkFiltersCategoryAttributeIsNotNull() }

            When { findAttributeByName("seller_type") }
            Then { checkFiltersCategoryAttributeIsNotNull() }

            When { findAttributeByName("lawn_mower_type") }
            Then { checkFiltersCategoryAttributeIsNotNull() }
        }
    }

    @Test
    fun `should NOT find category attribute when attribute does NOT exist`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributes() }
            When { findAttributeByName("pricee") }
            Then { checkFiltersCategoryAttributeIsNull() }

            When { findAttributeByName("TYP") }
            Then { checkFiltersCategoryAttributeIsNull() }

            When { findAttributeByName("mower") }
            Then { checkFiltersCategoryAttributeIsNull() }
        }
    }

    @Test
    fun `should return category attribute with currency input type`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttribute(
                    FiltersCategoryAttributesFactory.createFilterAttribute(
                        attributeDataType = CategoryAttributesCache.Data.Type.CURRENCY
                    )
                )
            }
            When { toInputType() }
            Then { checkFiltersCategoryAttributeInputType(InputCardDto.InputType.CURRENCY) }
        }
    }

    @Test
    fun `should return category attribute with number input type`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttribute(
                    FiltersCategoryAttributesFactory.createFilterAttribute(
                        attributeDataType = CategoryAttributesCache.Data.Type.ENUM
                    )
                )
            }
            When { toInputType() }
            Then { checkFiltersCategoryAttributeInputType(InputCardDto.InputType.NUMBER) }
        }
    }

    @Test
    fun `should return category attribute with selected value when value exist in the params`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttribute(
                    FiltersCategoryAttributesFactory.createFilterAttribute(
                        attributeName = "seller_type",
                        attributeValues = listOf(
                            CategoryAttributesCache.Data.Value(
                                label = "Business",
                                value = "trade"
                            ),
                            CategoryAttributesCache.Data.Value(
                                label = "Private",
                                value = "private"
                            )
                        )
                    )
                )
            }
            When { toSelectedValue(hashMapOf("seller_type" to "trade")) }
            Then { checkFilterCategoryAttributeSelectedValue("trade", "Business") }
        }
    }

    @Test
    fun `should return category attribute without selected value when value does NOT exist in the params`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttribute(
                    FiltersCategoryAttributesFactory.createFilterAttribute(
                        attributeName = "seller_type",
                        attributeValues = listOf(
                            CategoryAttributesCache.Data.Value(
                                label = "Business",
                                value = "trade"
                            ),
                            CategoryAttributesCache.Data.Value(
                                label = "Private",
                                value = "private"
                            )
                        )
                    )
                )
            }
            When { toSelectedValue(hashMapOf("vehicle_make" to "bmw")) }
            Then { checkFilterCategoryAttributeSelectedValue("bmw", null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualFiltersCategoryAttributeDtoResult: FiltersCategoryAttributeDto? = null
        private var actualFilterCategoryAttributeSelectedValueResult: Map<String, String>? = null
        private lateinit var actualFilterCategoryAttributeInputTypeResult: InputCardDto.InputType

        private lateinit var allFiltersCategoryAttributes: List<FiltersCategoryAttributeDto>

        private lateinit var testSubject: FiltersCategoryAttributeDto

        fun stubFiltersCategoryAttributes() {
            allFiltersCategoryAttributes = FiltersCategoryAttributesFactory.createFiltersAttributes()
        }

        fun stubFiltersCategoryAttribute(attribute: FiltersCategoryAttributeDto) {
            testSubject = attribute
        }

        fun findAttributeByName(attributeName: String) {
            actualFiltersCategoryAttributeDtoResult = FiltersCategoryAttributeDto.findAttributeByName(
                FiltersCategoryAttributesFactory.createFiltersAttributes(),
                attributeName
            )
        }

        fun toInputType() {
            actualFilterCategoryAttributeInputTypeResult = testSubject.toInputType()
        }

        fun toSelectedValue(params: QueryParams) {
            actualFilterCategoryAttributeSelectedValueResult = testSubject.toSelectedValue(params)
        }

        fun checkFiltersCategoryAttributeIsNotNull() {
            assertNotNull(actualFiltersCategoryAttributeDtoResult)
        }

        fun checkFiltersCategoryAttributeIsNull() {
            assertNull(actualFiltersCategoryAttributeDtoResult)
        }

        fun checkFiltersCategoryAttributeInputType(expected: InputCardDto.InputType) {
            assertEquals(expected, actualFilterCategoryAttributeInputTypeResult)
        }

        fun checkFilterCategoryAttributeSelectedValue(
            key: String,
            expected: String?
        ) {
            assertEquals(expected, actualFilterCategoryAttributeSelectedValueResult?.get(key))
        }
    }
}