package features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipMapCardDto
import com.gumtree.mobile.features.vip.VipMapMapper
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.StaticMapFactory
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.coroutines.runUnitTest

class VipMapMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return Vip map row if all address properties are provided`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    DataFactory.SOME_AD_LOCATION,
                    DataFactory.SOME_LOCATION_LAT,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { map() }
            Then { checkVipMapRowIsNotNull() }
            Then { checkVipMapRowType(RowLayoutType.VIP_MAP_ROW) }
        }
    }

    @Test
    fun `should NOT return Vip map row if city is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    null,
                    DataFactory.SOME_LOCATION_LAT,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { map() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Vip map row if address latitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    DataFactory.SOME_AD_LOCATION,
                    null,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { map() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should NOT return Vip map row if longitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    DataFactory.SOME_AD_LOCATION,
                    DataFactory.SOME_LOCATION_LAT,
                    null,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { map() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should return Vip map data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAd(
                    DataFactory.SOME_AD_LOCATION,
                    DataFactory.SOME_LOCATION_LAT,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { map() }
            Then { checkVipMapRowDataCount(1) }
            Then { checkVipMapRowDataType(VipMapCardDto::class.java) }
            Then { checkVipMapCardDtoIsInteractive(false) }
            Then { checkVipMapCardDtoLocationLabel(DataFactory.SOME_AD_LOCATION) }
            Then { checkVipMapCardDtoLocationTypeLabel(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapCardDtoLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkVipMapCardDtoLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkVipMapCardDtoRadius(DataFactory.SOME_LOCATION_RADIUS) }
        }
    }

    private class Robot: BaseRobot {
        private var actualVipMapCardDtoResult: RowLayout<UiItem>? = null

        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val staticMapFactory: StaticMapFactory = mockk(relaxed = true)
        private lateinit var rawAd: RawCapiAd

        private lateinit var testSubject: VipMapMapper

        override fun setup() {
            testSubject = VipMapMapper(locationsApi = locationsApi, staticMapFactory = staticMapFactory)
        }

        fun stubRawAd(
            city: String?,
            latitude: String?,
            longitude: String?,
            radius: String?
        ) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAddress(city, latitude, longitude, radius)
        }

        suspend fun map() {
            actualVipMapCardDtoResult = testSubject.map(rawAd)
        }

        fun checkVipMapRowIsNotNull() {
            assertNotNull(actualVipMapCardDtoResult)
        }

        fun checkVipMapRowIsNull() {
            assertNull(actualVipMapCardDtoResult)
        }

        fun checkVipMapRowType(expected: RowLayoutType) {
            assertEquals(expected, actualVipMapCardDtoResult?.type)
        }

        fun checkVipMapRowDataCount(expected: Int) {
            assertEquals(expected, actualVipMapCardDtoResult?.data?.size)
        }

        fun checkVipMapRowDataType(expected: Class<VipMapCardDto>) {
            assertInstanceOf(expected, actualVipMapCardDtoResult?.data?.first())
        }

        fun checkVipMapCardDtoIsInteractive(expected: Boolean) {
            assertEquals(expected, (actualVipMapCardDtoResult?.data?.first() as VipMapCardDto?)?.isInteractive)
        }

        fun checkVipMapCardDtoLocationLabel(expected: String) {
            assertEquals(expected, (actualVipMapCardDtoResult?.data?.first() as VipMapCardDto?)?.locationLabel)
        }

        fun checkVipMapCardDtoLocationTypeLabel(expected: String) {
            assertEquals(expected, (actualVipMapCardDtoResult?.data?.first() as VipMapCardDto?)?.locationTypeLabel)
        }

        fun checkVipMapCardDtoLatitude(expected: String) {
            assertEquals(expected, (actualVipMapCardDtoResult?.data?.first() as VipMapCardDto?)?.latitude)
        }

        fun checkVipMapCardDtoLongitude(expected: String) {
            assertEquals(expected, (actualVipMapCardDtoResult?.data?.first() as VipMapCardDto?)?.longitude)
        }

        fun checkVipMapCardDtoRadius(expected: String) {
            assertEquals(expected, (actualVipMapCardDtoResult?.data?.first() as VipMapCardDto?)?.radius)
        }

    }
}
