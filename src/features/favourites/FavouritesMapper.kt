package com.gumtree.mobile.features.favourites

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.common.analytics.AD_PREFIX
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.wrapScreenDataWithRowLayouts
import com.gumtree.mobile.utils.extensions.data.toListingCardDto

/**
 * Mapping the favourites raw data (CAPI data) into favourites mobile application data
 */
class FavouritesMapper(private val commonAnalyticsProvider: CommonAnalyticsProvider) {

    fun map(
        rawData: RawCapiAdList,
    ): List<String> {
        return rawData.rawAds?.let { ads ->
            ads.mapNotNull { capiAd ->
                when (capiAd.status.value) {
                    AdStatus.ACTIVE.toString(),
                    AdStatus.EXPIRED.toString(),
                    -> capiAd.id
                    else -> null
                }
            }
        } ?: emptyList()
    }

    fun mapScreen(
        rawData: RawCapiAdList,
        listingGridSizes: GridSizes,
    ): Pair<PortraitData, LandscapeData?> {
        val listWithFavouritesCards = rawData.rawAds?.mapNotNull {
            when (it.status.value) {
                AdStatus.ACTIVE.toString(),
                AdStatus.EXPIRED.toString(),
                -> it.toListingCardDto(commonAnalyticsProvider.getClickListingEvent(AD_PREFIX, it))
                else -> null
            }
        }
        return when (listWithFavouritesCards) {
            null -> Pair(emptyList(), null)
            else -> listWithFavouritesCards.wrapScreenDataWithRowLayouts(listingGridSizes)
        }
    }
}
