package features.blockUser

import com.gumtree.mobile.features.blockUser.BLOCK_USER_PATH
import com.gumtree.mobile.features.blockUser.BlockUserRepository
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ID_PATH
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.jsonHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class BlockUserRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete POST block user request with success and status code Created`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When { createBlockUser(DataFactory.SOME_USER_ID) }
            Then { checkResponseStatus(HttpStatusCode.Created) }
        }
    }

    @Test
    fun `should complete POST block user request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateBlockUserResponseError(UnauthorisedException()) }
            When { createBlockUser(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should complete POST block user request with error when user id path value is NOT in expected format`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When { createBlockUser(DataFactory.SOME_SAVED_SEARCH_ID) }
        }
    }

    @Test
    fun `should complete DELETE block user request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            When { deleteBlockUser(DataFactory.SOME_USER_ID) }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete DELETE block user request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryDeleteBlockUserResponseError(UnauthorisedException()) }
            When { deleteBlockUser(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should complete DELETE block user request with error when user id path value is NOT in expected format`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When { deleteBlockUser(DataFactory.SOME_CONVERSATION_ID) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: BlockUserRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryCreateBlockUserResponseError(error: Throwable) {
            coEvery { repository.create(any(), any()) } throws error
        }

        fun stubRepositoryDeleteBlockUserResponseError(error: Throwable) {
            coEvery { repository.delete(any(), any()) } throws error
        }

        suspend fun createBlockUser(userIdToBlock: String) {
            actualResponse = client.post(BLOCK_USER_PATH.replace("{$ID_PATH}", userIdToBlock))
        }

        suspend fun deleteBlockUser(userIdToUnBlock: String) {
            actualResponse = client.delete(BLOCK_USER_PATH.replace("{$ID_PATH}", userIdToUnBlock))
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }
}