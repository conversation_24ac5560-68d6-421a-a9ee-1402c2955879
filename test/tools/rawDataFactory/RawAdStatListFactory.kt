package tools.rawDataFactory

import api.capi.models.RawAdStat
import com.gumtree.mobile.features.myGumtree.MyGumtreeScreenUiConfiguration.AD_PAGE_VIEW

object RawAdStatListFactory {

    fun createAdStatList(vararg stats: Pair<String, String>): List<RawAdStat> {
        return stats.map {
            RawAdStat().apply {
                type = it.first
                value = it.second
            }
        }
    }

    fun createAdStat(id: String = "1", type: String = AD_PAGE_VIEW, value: String = id): RawAdStat {
        val adStat = RawAdStat()
        adStat.adId = id
        adStat.type = type
        adStat.value = value

        return adStat
    }
}
