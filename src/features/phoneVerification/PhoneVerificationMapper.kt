package com.gumtree.mobile.features.phoneVerification

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.CHECK_SCENE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.SEND_PAGE_SUBTITLE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.SEND_PAGE_TITLE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.SEND_SCENE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.SUCCESS_CODE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.VERIFY_PAGE_CODE_TIPS
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.VERIFY_PAGE_SUBTITLE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.VERIFY_PAGE_TITLE
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.getCommonHelpScreen
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.sendPageButton
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.sendPagePhone
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.sendPageResend
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.verifyPageButton
import com.gumtree.mobile.features.phoneVerification.PhoneVerificationScreenUiConfiguration.verifyPageResend
import com.gumtree.mobile.features.phoneVerification.model.PhoneVerifyConfigResponseDto
import com.gumtree.mobile.features.phoneVerification.model.PhoneVerifyConfigResponseDto.Phone
import com.gumtree.mobile.features.phoneVerification.model.PhoneVerifyConfigResponseDto.ScreenData

class PhoneVerificationMapper {

    fun map(pageType: String, adInfo: Map<String, String>): PhoneVerifyConfigResponseDto {
        if (pageType == SEND_SCENE) {
            return PhoneVerifyConfigResponseDto(
                code = SUCCESS_CODE,
                data = ScreenData(
                    title = SEND_PAGE_TITLE,
                    subTitle = SEND_PAGE_SUBTITLE,
                    codeTips = EMPTY_STRING,
                    phone = sendPagePhone(),
                    button = sendPageButton(),
                    help = getCommonHelpScreen(),
                    resend = sendPageResend(),
                    analyticsParameters = AnalyticsEventData(
                        "",
                        adInfo,
                    ),
                ),
            )
        }

        if (pageType == CHECK_SCENE) {
            return PhoneVerifyConfigResponseDto(
                code = SUCCESS_CODE,
                data = ScreenData(
                    title = VERIFY_PAGE_TITLE,
                    subTitle = VERIFY_PAGE_SUBTITLE,
                    codeTips = VERIFY_PAGE_CODE_TIPS,
                    phone = Phone(),
                    button = verifyPageButton(),
                    help = getCommonHelpScreen(),
                    resend = verifyPageResend(),
                    analyticsParameters = AnalyticsEventData(
                        "",
                        adInfo,
                    ),
                ),
            )
        }

        return PhoneVerifyConfigResponseDto(code = null, data = null)
    }
}
