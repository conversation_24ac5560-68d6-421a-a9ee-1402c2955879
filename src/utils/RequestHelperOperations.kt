package com.gumtree.mobile.utils

import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.extensions.containsAuthorisationHeaders
import com.gumtree.mobile.utils.extensions.runCatchingWithLog
import com.gumtree.mobile.utils.extensions.runCatchingWithoutLog
import io.ktor.http.Headers
import org.slf4j.LoggerFactory

val bffLogger = LoggerFactory.getLogger("BFFLogger") // General BFF logger

/**
 * Wrapper function to execute a Http request. If the request fails for some reason the method shall return null
 * @param enableLogging - enable the logging of the exception message - true by default
 * @param errorLogPrefix - append prefix on the logging of the exception message if needed
 * @param block - the actual Http request block
 * @return - the request response object or null if the request fails
 */
suspend fun <T> handleRequestOrNull(
    enableLogging: Boolean = true,
    errorLogPrefix: String? = null,
    block: suspend () -> T,
): T? {
    return when {
        enableLogging -> runCatchingWithLog(errorLogPrefix) {
            block()
        }.getOrNull()
        else -> runCatchingWithoutLog {
            block()
        }.getOrNull()
    }
}

/**
 * Wrapper function to execute a Http authenticated request.
 * If the request does not have auth headers (email + token) this function will return straight NULL (no internal request shall be made)
 * If the request fails for another reason the method shall return null
 * @param callHeaders - the original (client's) Http request headers
 * @param block - the actual Http request block
 * @return - the request response object or null if the request fails
 */
suspend fun <T> handleAuthRequestOrNull(callHeaders: Headers, block: suspend () -> T): T? {
    return when {
        callHeaders.containsAuthorisationHeaders() -> runCatchingWithLog { block() }.getOrNull()
        else -> null
    }
}

/**
 * Wrapper function to execute a Http authenticated request.
 * If the request does not have auth headers (email + token) this function will throw UnauthorisedException
 * If the request fails for another reason the method shall throw the request failure reason/error
 * @param callHeaders - the original (client's) Http request headers
 * @param block - the actual Http request block
 * @return - the request response object or throws error
 */
suspend fun <T> handleAuthRequestOrThrow(callHeaders: Headers, block: suspend () -> T): T {
    return when {
        callHeaders.containsAuthorisationHeaders() -> runCatchingWithLog { block() }.getOrThrow()
        else -> throw UnauthorisedException().also {
            bffLogger.info(
                "handleAuthRequest " +
                    "usr lth: ${callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL]?.length}, " +
                    "token lth: ${callHeaders[ApiHeaderParams.AUTHORISATION_USER_TOKEN]?.length}",
            )
        }
    }
}
