package cache

import com.gumtree.mobile.cache.CATEGORIES_TREE_CACHE_RESOURCE_PATH
import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.cache.FILTERS_CATEGORY_ATTRIBUTES_CACHE_RESOURCE_PATH
import com.gumtree.mobile.cache.IndexedTreeCache
import com.gumtree.mobile.cache.createFileInputStream
import com.gumtree.mobile.cache.initialiseCategoriesTreeCache
import com.gumtree.mobile.cache.initialiseFiltersCategoryAttributesCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import io.ktor.server.application.*
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class ApplicationResourcesCacheTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should initialise categories tree cache data`() {
        runUnitTest(robot) {
            Given { stubCategoriesFileInputStream() }
            When { initialiseCategoriesTreeCache() }
            Then { checkSetCategoriesTreeCacheData() }
        }
    }

    @Test
    fun `should initialise filters category attributes tree cache data`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesFileInputStream() }
            When { initialiseFiltersCategoryAttributesCache() }
            Then { checkSetFiltersCategoryAttributesCacheData() }
        }
    }

    @Test
    fun `should NOT initialise categories tree cache data`() {
        runUnitTest(robot) {
            Given { stubCategoriesFileInputStreamError() }
            When { initialiseCategoriesTreeCache() }
            Then { checkSetCategoriesTreeCacheDataIsNull() }
        }
    }

    @Test
    fun `should NOT initialise filters category attributes tree cache data`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesFileInputStreamError() }
            When { initialiseFiltersCategoryAttributesCache() }
            Then { checkSetFiltersCategoryAttributesCacheDataIsNull() }
        }
    }

    private class Robot: BaseRobot {
        private val categoryTreeCache: IndexedTreeCache<CategoryDto> = mockk(relaxed = true)
        private val filtersCategoryAttributesCache: CategoryAttributesCache<FiltersCategoryAttributeDto> = mockk(relaxed = true)

        private val testSubject: Application = mockk(relaxed = true)

        override fun setup() {
            mockkStatic("com.gumtree.mobile.cache.ApplicationResourcesCacheKt")
        }

        override fun tearsDown() {
            unmockkStatic("com.gumtree.mobile.cache.ApplicationResourcesCacheKt")
        }

        fun stubCategoriesFileInputStream() {
            every { any<Application>().createFileInputStream(CATEGORIES_TREE_CACHE_RESOURCE_PATH) } returns
                "{\"id\": \"1\",\"text\": \"All Categories\", \"seoDisplayName\": \"All Classifieds\",\"idName\": \"all\"}".byteInputStream()
        }

        fun stubFiltersCategoryAttributesFileInputStream() {
            every { any<Application>().createFileInputStream(FILTERS_CATEGORY_ATTRIBUTES_CACHE_RESOURCE_PATH) } returns
                "[{\"type\": \"FILTER_CATEGORY_ATTRIBUTE\",\"name\": \"price\",\"label\": \"Price\",\"dataType\": \"CURRENCY\",\"presentationType\": \"DOUBLE_INPUT\",\"values\": [],\"categoryIds\": [4]}]".byteInputStream()
        }

        fun stubCategoriesFileInputStreamError() {
            every { any<Application>().createFileInputStream(CATEGORIES_TREE_CACHE_RESOURCE_PATH) } returns null
        }

        fun stubFiltersCategoryAttributesFileInputStreamError() {
            every { any<Application>().createFileInputStream(FILTERS_CATEGORY_ATTRIBUTES_CACHE_RESOURCE_PATH) } returns null
        }

        fun initialiseCategoriesTreeCache() {
            testSubject.initialiseCategoriesTreeCache(categoryTreeCache)
        }

        fun initialiseFiltersCategoryAttributesCache() {
            testSubject.initialiseFiltersCategoryAttributesCache(filtersCategoryAttributesCache)
        }

        fun checkSetCategoriesTreeCacheData() {
            verify { categoryTreeCache.data = any() }
        }

        fun checkSetFiltersCategoryAttributesCacheData() {
            verify { filtersCategoryAttributesCache.data = any() }
        }

        fun checkSetCategoriesTreeCacheDataIsNull() {
            categoryTreeCache.data = null
        }

        fun checkSetFiltersCategoryAttributesCacheDataIsNull() {
            filtersCategoryAttributesCache.data = null
        }
    }
}