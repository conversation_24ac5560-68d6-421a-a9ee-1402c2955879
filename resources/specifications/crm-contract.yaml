openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-crm
  license:
    name: MIT
tags:
  - name: BFF-Crm
    description: Mobile Apps BFF Crm API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  securitySchemes:
    ApiKeyAuthUserEmail:
      type: apiKey
      description: Security API key to authorize requests with an user email
      in: header
      name: Authorisation-User-Email
    ApiKeyAuthUserToken:
      type: apiKey
      description: Security API key to authorize requests with an user token
      in: header
      name: Authorisation-User-Token
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
  schemas:
    Error:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
      required:
        - code
        - message
    CrmDto:
      type: object
      properties:
        salesForceContactKey:
          type: string
security:
  - ApiKeyAuthUserEmail: []
    ApiKeyAuthUserToken: []
paths:
  /crm:
    get:
      summary: Get the user's Crm details
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      responses:
        '200':
          description: The User Crm data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CrmDto"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"