package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.features.screens.layoutsData.VipDescriptionCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipLocationCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipMapCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipPostedSinceCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipPriceCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipTitleCardDto
import com.gumtree.mobile.features.vip.v2.StaticMapFactory
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration.VIP_POSTED_SINCE_DATE_FORMAT
import com.gumtree.mobile.features.vip.v2.VipTimeAgoFormatter
import com.gumtree.mobile.features.vip.v2.toVipMapCardDto
import com.gumtree.mobile.features.vip.v2.toVipPostedSinceCardDto
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory

@ParallelTest
class RawCapiAdVipExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should map raw ad into vip posted since card with expected data`() {
        tools.runUnitTest(robot) {
            Given { stubCurrentDate("2023-09-31T15:31:18.655+03:00") }
            Given { stubRawAdAge("2023-08-14T13:21:22.243+01:00") }
            When { toVipPostedSinceCardDto() }
            Then { checkVipPostedSinceCardDtoText("Posted 6 weeks ago") }
        }
    }

    @Test
    fun `should NOT map raw ad into vip posted since card if ad age is NOT recognised`() {
        tools.runUnitTest(robot) {
            Given { stubCurrentDate("2023-09-31T15:31:18.655+03:00") }
            Given { stubRawAdAge("08-14T13:asds") }
            When { toVipPostedSinceCardDto() }
            Then { checkVipPostedSinceCardIsNull() }
        }
    }

    @Test
    fun `should map raw ad into vip map card with expected data`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdLocation(
                    DataFactory.SOME_AD_LOCATION,
                    DataFactory.SOME_LOCATION_LAT,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { toVipMapCardDto(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapCardDtoIsInteractive(false) }
            Then { checkVipMapCardDtoLocationLabel(DataFactory.SOME_AD_LOCATION) }
            Then { checkVipMapCardDtoLocationTypeLabel(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapCardDtoLatitude(DataFactory.SOME_LOCATION_LAT) }
            Then { checkVipMapCardDtoLongitude(DataFactory.SOME_LOCATION_LNG) }
            Then { checkVipMapCardDtoRadius(DataFactory.SOME_LOCATION_RADIUS) }
        }
    }

    @Test
    fun `should NOT map raw ad into vip map card if city is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdLocation(
                    null,
                    DataFactory.SOME_LOCATION_LAT,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { toVipMapCardDto(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapCardDtoIsNull() }
        }
    }

    @Test
    fun `should NOT map raw ad into vip map card if latitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdLocation(
                    DataFactory.SOME_AD_LOCATION,
                    null,
                    DataFactory.SOME_LOCATION_LNG,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { toVipMapCardDto(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapCardDtoIsNull() }
        }
    }

    @Test
    fun `should NOT map raw ad into vip map card if longitude is NULL`() = runTest {
        runUnitTest(robot) {
            Given {
                stubRawAdLocation(
                    DataFactory.SOME_AD_LOCATION,
                    DataFactory.SOME_LOCATION_LAT,
                    null,
                    DataFactory.SOME_LOCATION_RADIUS,
                )
            }
            When { toVipMapCardDto(VipScreenUiConfiguration.LOCATION_TYPE_LABEL_TEXT) }
            Then { checkVipMapCardDtoIsNull() }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualVipTitleCardDtoResult: VipTitleCardDto
        private lateinit var actualVipDescriptionCardDtoResult: VipDescriptionCardDto
        private var actualVipPriceCardDtoResult: VipPriceCardDto? = null
        private var actualVipLocationCardDtoResult: VipLocationCardDto? = null
        private var actualVipPostedSinceCardDtoResult: VipPostedSinceCardDto? = null
        private var actualVipMapCardDtoResult: VipMapCardDto? = null

        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val staticMapFactory: StaticMapFactory = mockk(relaxed = true)
        private val vipTimeAgoFormatter = spyk(
            VipTimeAgoFormatter(
                createUKDateTimeFormatter(
                    VIP_POSTED_SINCE_DATE_FORMAT
                )
            )
        )
        private lateinit var rawAd: RawCapiAd

        fun stubRawAdTitle(title: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(title = title)
        }

        fun stubRawAdPrice(price: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(price = price)
        }

        fun stubRawAdLocation(
            city: String?,
            latitude: String?,
            longitude: String?,
            radius: String?
        ) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAddress(city, latitude, longitude, radius)
        }

        fun stubRawAdDescription(description: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(description = description)
        }

        fun stubRawAdAge(age: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(age = age)
        }

        fun stubCurrentDate(date: String) {
            every { vipTimeAgoFormatter.getCurrentDate() } returns date
        }

        fun toVipPostedSinceCardDto() {
            actualVipPostedSinceCardDtoResult = rawAd.toVipPostedSinceCardDto(vipTimeAgoFormatter)
        }

        suspend fun toVipMapCardDto(label: String) {
            actualVipMapCardDtoResult = rawAd.toVipMapCardDto(label, locationsApi, staticMapFactory)
        }

        fun checkVipTitleCardDtoText(expected: String) {
            assertEquals(expected, actualVipTitleCardDtoResult.text)
        }

        fun checkVipPriceCardDtoText(expected: String) {
            assertEquals(expected, actualVipPriceCardDtoResult?.text)
        }

        fun checkVipPriceCardDtoIsNull() {
            assertNull(actualVipPriceCardDtoResult)
        }

        fun checkVipLocationCardDtoText(expected: String) {
            assertEquals(expected, actualVipLocationCardDtoResult?.text)
        }

        fun checkVipLocationCardDtoLatitude(expected: String) {
            assertEquals(expected, actualVipLocationCardDtoResult?.latitude)
        }

        fun checkVipLocationCardDtoLongitude(expected: String) {
            assertEquals(expected, actualVipLocationCardDtoResult?.longitude)
        }

        fun checkVipLocationCardDtoRadius(expected: String) {
            assertEquals(expected, actualVipLocationCardDtoResult?.radius)
        }

        fun checkVipLocationCardDtoIsNull() {
            assertNull(actualVipLocationCardDtoResult)
        }

        fun checkVipDescriptionCardDtoLabel(expected: String) {
            assertEquals(expected, actualVipDescriptionCardDtoResult.label)
        }

        fun checkVipDescriptionCardDtoText(expected: String) {
            assertEquals(expected, actualVipDescriptionCardDtoResult.text)
        }

        fun checkVipPostedSinceCardDtoText(expected: String) {
            assertEquals(expected, actualVipPostedSinceCardDtoResult?.text)
        }

        fun checkVipPostedSinceCardIsNull() {
            assertNull(actualVipPostedSinceCardDtoResult)
        }

        fun checkVipMapCardDtoIsInteractive(expected: Boolean) {
            assertEquals(expected, actualVipMapCardDtoResult?.isInteractive)
        }

        fun checkVipMapCardDtoLocationLabel(expected: String) {
            assertEquals(expected, actualVipMapCardDtoResult?.locationLabel)
        }

        fun checkVipMapCardDtoLocationTypeLabel(expected: String) {
            assertEquals(expected, actualVipMapCardDtoResult?.locationTypeLabel)
        }

        fun checkVipMapCardDtoLatitude(expected: String) {
            assertEquals(expected, actualVipMapCardDtoResult?.latitude)
        }

        fun checkVipMapCardDtoLongitude(expected: String) {
            assertEquals(expected, actualVipMapCardDtoResult?.longitude)
        }

        fun checkVipMapCardDtoRadius(expected: String) {
            assertEquals(expected, actualVipMapCardDtoResult?.radius)
        }

        fun checkVipMapCardDtoIsNull() {
            assertNull(actualVipMapCardDtoResult)
        }
    }
}
