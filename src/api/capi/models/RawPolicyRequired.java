package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.List;

@Root(name = "policy-required", strict = false)
@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
public class RawPolicyRequired {
    @Attribute(name = "title", required = false)
    @Namespace(reference = Namespaces.AD)
    public String title;

    @Attribute(name = "description", required = false)
    @Namespace(reference = Namespaces.AD)
    public String description;

    @ElementList(name = "policy-texts", required = false)
    @Namespace(reference = Namespaces.AD)
    public List<RawPolicyText> policyTexts;
}
