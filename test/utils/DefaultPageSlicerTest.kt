package utils

import com.gumtree.mobile.utils.DefaultPageSlicer
import com.gumtree.mobile.utils.PageIndexes
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class DefaultPageSlicerTest {

    private val robot = Robot()

    @Test
    fun `should return pageStarIndex equals to NULL and pageEndIndex equals to NULL`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(0) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }

            Given { stubPage(2) }
            Given { stubSize(24) }
            Given { stubTotalListSize(2) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }

            Given { stubPage(1) }
            Given { stubSize(24) }
            Given { stubTotalListSize(24) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }

            Given { stubPage(10) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }
        }
    }

    @Test
    fun `should return pageStarIndex equals to 0 and pageEndIndex equals to totalListSize`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(10) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(9) }

            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(2) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(1) }

            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(19) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(18) }
        }
    }

    @Test
    fun `should return pageStarIndex equals to 0 and pageEndIndex equals to size`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(15) }
            Given { stubTotalListSize(20) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(14) }

            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(30) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(23) }

            Given { stubPage(0) }
            Given { stubSize(30) }
            Given { stubTotalListSize(45) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(29) }
        }
    }

    @Test
    fun `should return pageStarIndex equals to 0 and pageEndIndex equals to size and totalListSize`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(20) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(19) }

            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(24) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(23) }

            Given { stubPage(0) }
            Given { stubSize(30) }
            Given { stubTotalListSize(30) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(29) }
        }
    }

    @Test
    fun `should return correct pageStarIndex and pageEndIndex equals to totalListSize`() {
        runUnitTest(robot) {
            Given { stubPage(1) }
            Given { stubSize(24) }
            Given { stubTotalListSize(29) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(24) }
            Then { checkPageEndIndex(28) }

            Given { stubPage(2) }
            Given { stubSize(24) }
            Given { stubTotalListSize(50) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(48) }
            Then { checkPageEndIndex(49) }

            Given { stubPage(3) }
            Given { stubSize(24) }
            Given { stubTotalListSize(81) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(72) }
            Then { checkPageEndIndex(80) }
        }
    }

    @Test
    fun `should return correct pageStarIndex and pageEndIndex for N pages in a row`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(23) }

            Given { stubPage(1) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(24 * 1) }
            Then { checkPageEndIndex((24 * 2) - 1) }

            Given { stubPage(2) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(24 * 2) }
            Then { checkPageEndIndex((24 * 3) - 1) }

            Given { stubPage(3) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(24 * 3) }
            Then { checkPageEndIndex((24 * 4) - 1) }

            Given { stubPage(4) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(24 * 4) }
            Then { checkPageEndIndex(99) }

            Given { stubPage(5) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }
        }
    }

    private class Robot: BaseRobot {

        private var page: Int = 0
        private var size: Int = 0
        private var totalListSize: Int = 0

        private lateinit var actualPageIndexes: PageIndexes

        private lateinit var testSubject: DefaultPageSlicer

        fun stubPage(page: Int) {
            this.page = page
        }

        fun stubSize(size: Int) {
            this.size = size
        }

        fun stubTotalListSize(totalListSize: Int) {
            this.totalListSize = totalListSize
        }

        fun getPageIndexes() {
            testSubject = DefaultPageSlicer(page, size, totalListSize)
            actualPageIndexes = testSubject.getPageIndexes()
        }

        fun checkPageStartIndex(expected: Int?) {
            assertEquals(expected, actualPageIndexes.first)
        }

        fun checkPageEndIndex(expected: Int?) {
            assertEquals(expected, actualPageIndexes.second)
        }
    }

}