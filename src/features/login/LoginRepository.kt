package com.gumtree.mobile.features.login

import com.gumtree.mobile.api.userService.bodies.UserServiceLoginRequestBody
import com.gumtree.mobile.api.userService.bodies.UserServiceLogoutRequestBody
import com.gumtree.mobile.api.userService.bodies.UserServiceSocialRegistrationBody
import com.gumtree.mobile.api.userService.models.RawUserServiceAuthProvider
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.PushNotificationsService
import com.gumtree.mobile.responses.ConflictException
import com.gumtree.mobile.utils.Metric
import com.gumtree.mobile.utils.extensions.handleNoContentResponse
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.handleRequestOrNull
import io.ktor.http.Headers
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.withContext
import retrofit2.HttpException

/**
 * Handles all CRUD operations about the User's Login feature
 */
interface LoginRepository {
    /**
     * Post new User Login
     * @param userName - the username
     * @param userSecret - the user secret
     * @param threatMetrixSessionId - the threat metrix session ID
     * @param authenticationType - the type of the authentication,
     * in the cases of social login and registration the value should NOT be null,
     * in the case of GT login the value should be NULL
     * @return - the mapped UserLogin data ready to be returned to the client
     */
    suspend fun create(
        userName: String,
        userSecret: String,
        threatMetrixSessionId: String,
        authenticationType: AuthenticationType,
        socialRegistration: Boolean,
        optInMarketing: Boolean,
    ): UserLoginDto

    /**
     * Post User Logout
     * @param callHeaders - the request headers
     * @param userToken - The user token
     * @param logoutRequest - The logout request
     */
    suspend fun delete(
        callHeaders: Headers,
        userToken: String,
        logoutRequest: LogoutRequest,
    )

    /**
     * Get the user email and user token exchanged to fresh user login response data including the profile JWT value
     * @param callHeaders - the request headers
     * @param userName - The user email
     * @param userToken - The user token
     * @return - the mapped UserLogin data ready to be returned to the client
     */
    suspend fun exchange(
        callHeaders: Headers,
        userName: String,
        userToken: String,
    ): UserLoginDto
}

class DefaultLoginRepository(
    private val loginService: LoginService,
    private val pushNotificationsService: PushNotificationsService,
    private val loginMapper: LoginMapper,
    private val dispatcherProvider: DispatcherProvider,
) : LoginRepository {

    override suspend fun create(
        userName: String,
        userSecret: String,
        threatMetrixSessionId: String,
        authenticationType: AuthenticationType,
        socialRegistration: Boolean,
        optInMarketing: Boolean,
    ): UserLoginDto {
        return if (socialRegistration) {
            socialRegistration(userName, userSecret, threatMetrixSessionId, authenticationType, optInMarketing)
        } else {
            login(userName, userSecret, threatMetrixSessionId, authenticationType, true, optInMarketing)
        }
    }

    override suspend fun delete(
        callHeaders: Headers,
        userToken: String,
        logoutRequest: LogoutRequest,
    ) {
        if (logoutRequest.pushToken.isNotNullOrEmpty()) {
            handleRequestOrNull {
                withContext(dispatcherProvider.io) {
                    val authorization = pushNotificationsService.createAuthorization(callHeaders, logoutRequest.userId)
                    val hasExistingSubscription = pushNotificationsService.hasExistingPushNotificationSubscription(
                        authorization,
                        logoutRequest.pushToken,
                        logoutRequest.userId,
                    )
                    if (hasExistingSubscription) {
                        pushNotificationsService.unsubscribe(
                            authorization,
                            logoutRequest.pushToken,
                            logoutRequest.userId.toLong(),
                        )
                    }
                }
            }
        }
        withContext(dispatcherProvider.io) {
            loginService.unAuthenticateUser(UserServiceLogoutRequestBody(userToken)).handleNoContentResponse()
        }
    }

    override suspend fun exchange(
        callHeaders: Headers,
        userName: String,
        userToken: String,
    ): UserLoginDto {
        val userDetails = withContext(dispatcherProvider.io) {
            handleRequestOrNull(errorLogPrefix = "LoginExchange error: ") {
                loginService.getUserDetails(userName)
            }
        }
        return when {
            userDetails.isNotNull() -> {
                Metric.LOGIN_EXCHANGE.record()
                loginMapper.map(userDetails, userName, userToken)
            }
            userToken.isEmpty() -> throw ConflictException("LoginExchange error: user token is empty")
            else -> throw ConflictException("LoginExchange error: userDetails=null, userName lth=${userName.length}")
        }
    }

    private suspend fun login(
        userName: String,
        userSecret: String,
        threatMetrixSessionId: String,
        authenticationType: AuthenticationType,
        initialLogin: Boolean = false,
        optInMarketing: Boolean,
    ): UserLoginDto {
        val loginRequest = UserServiceLoginRequestBody(
            authProvider = RawUserServiceAuthProvider.fromString(authenticationType.name),
            password = userSecret,
            username = userName,
            threatMetrixSessionId = threatMetrixSessionId,
        )

        val capiToken = try {
            withContext(dispatcherProvider.io) { loginService.authenticateUser(loginRequest) }
        } catch (e: HttpException) {
            LoginLogger.logSocialLoginAuthenticateUserError(authenticationType, e)
            if (authenticationType != AuthenticationType.GUMTREE && initialLogin && e.code() == HttpStatusCode.Forbidden.value) {
                return socialRegistration(
                    userName,
                    userSecret,
                    threatMetrixSessionId,
                    authenticationType,
                    optInMarketing,
                )
            } else {
                throw e
            }
        }
        val userDetails = withContext(dispatcherProvider.io) { loginService.getUserDetails(userName) }
        LoginLogger.logSocialLoginUserDetailsCompletion(authenticationType)
        Metric.LOGIN.record(
            Metric.Tag.TYPE to authenticationType.name,
            Metric.Tag.SOCIAL to false.toString(),
            Metric.Tag.MARKETING to optInMarketing.toString(),
        )
        return loginMapper.map(userDetails, userName, capiToken.value)
    }

    private suspend fun socialRegistration(
        userName: String,
        userSecret: String,
        threatMetrixSessionId: String,
        authenticationType: AuthenticationType,
        optInMarketing: Boolean,
    ): UserLoginDto {
        val requestBody = UserServiceSocialRegistrationBody(
            authProvider = RawUserServiceAuthProvider.fromString(authenticationType.name),
            accessToken = userSecret,
            username = userName,
            threatMetrixSessionId = threatMetrixSessionId,
            optInMarketing = optInMarketing,
        )
        LoginLogger.logSocialLoginBodyData(requestBody)
        val response = handleRequestOrNull(errorLogPrefix = "SocialLogin: error ") {
            withContext(dispatcherProvider.io) {
                loginService.register(requestBody)
            }
        }
        LoginLogger.logSocialRegistrationBEResponse(response)

        Metric.REGISTRATION.record(
            Metric.Tag.TYPE to authenticationType.name,
            Metric.Tag.SOCIAL to true.toString(),
            Metric.Tag.MARKETING to optInMarketing.toString(),
        )

        return login(userName, userSecret, threatMetrixSessionId, authenticationType, optInMarketing = optInMarketing)
    }
}
