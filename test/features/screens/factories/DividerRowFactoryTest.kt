package features.screens.factories

import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.factories.DividerRowFactory
import com.gumtree.mobile.features.screens.layoutsData.DividerCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class DividerRowFactoryTest {

    private val robot = Robot()

    @Test
    fun `should create divider row with default parameters`() {
        runUnitTest(robot) {
            When { createDividerRowWithDefaults() }
            Then { checkDividerRowWithDefaults() }
        }
    }

    @Test
    fun `should create divider row with custom thickness and margins`() {
        runUnitTest(robot) {
            Given { stubThickness(DividerCardDto.Thickness.HEAVY) }
            Given { stubTopMargin(Space.LARGE) }
            Given { stubLeftMargin(Space.MEDIUM) }
            Given { stubRightMargin(Space.X_LARGE) }
            Given { stubBottomMargin(Space.XX_LARGE) }
            When { createDividerRowWithCustomParams() }
            Then { checkDividerRowWithCustomParams() }
        }
    }

    @ParameterizedTest
    @EnumSource(DividerCardDto.Thickness::class)
    fun `should create divider row with all thickness values`(thickness: DividerCardDto.Thickness) {
        runUnitTest(robot) {
            Given { stubThickness(thickness) }
            When { createDividerRowWithThickness() }
            Then { checkDividerRowThickness(thickness) }
        }
    }

    @ParameterizedTest
    @EnumSource(Space::class)
    fun `should create divider row with all space values for top margin`(space: Space) {
        runUnitTest(robot) {
            Given { stubTopMargin(space) }
            When { createDividerRowWithTopMargin() }
            Then { checkDividerRowTopMargin(space) }
        }
    }

    @ParameterizedTest
    @EnumSource(Space::class)
    fun `should create divider row with all space values for left margin`(space: Space) {
        runUnitTest(robot) {
            Given { stubLeftMargin(space) }
            When { createDividerRowWithLeftMargin() }
            Then { checkDividerRowLeftMargin(space) }
        }
    }

    @ParameterizedTest
    @EnumSource(Space::class)
    fun `should create divider row with all space values for right margin`(space: Space) {
        runUnitTest(robot) {
            Given { stubRightMargin(space) }
            When { createDividerRowWithRightMargin() }
            Then { checkDividerRowRightMargin(space) }
        }
    }

    @ParameterizedTest
    @EnumSource(Space::class)
    fun `should create divider row with all space values for bottom margin`(space: Space) {
        runUnitTest(robot) {
            Given { stubBottomMargin(space) }
            When { createDividerRowWithBottomMargin() }
            Then { checkDividerRowBottomMargin(space) }
        }
    }

    @Test
    fun `should create divider row with extreme margin combinations`() {
        runUnitTest(robot) {
            Given { stubThickness(DividerCardDto.Thickness.MEDIUM) }
            Given { stubTopMargin(Space.XXXX_SMALL) }
            Given { stubLeftMargin(Space.XXX_LARGE) }
            Given { stubRightMargin(Space.XXXX_SMALL) }
            Given { stubBottomMargin(Space.XXX_LARGE) }
            When { createDividerRowWithExtremeMargins() }
            Then { checkDividerRowWithExtremeMargins() }
        }
    }

    @Test
    fun `should create divider row with all same margins`() {
        runUnitTest(robot) {
            Given { stubThickness(DividerCardDto.Thickness.LIGHT) }
            Given { stubAllMargins(Space.X_LARGE) }
            When { createDividerRowWithSameMargins() }
            Then { checkDividerRowWithSameMargins() }
        }
    }

    inner class Robot : BaseRobot {

        private var thickness: DividerCardDto.Thickness = DividerCardDto.Thickness.LIGHT
        private var topMargin: Space = Space.SMALL
        private var leftMargin: Space = Space.SMALL
        private var rightMargin: Space = Space.SMALL
        private var bottomMargin: Space = Space.SMALL

        private lateinit var actualDividerRow: RowLayout<UiItem>

        private val testSubject = DividerRowFactory()

        fun stubThickness(thickness: DividerCardDto.Thickness) {
            this.thickness = thickness
        }

        fun stubTopMargin(topMargin: Space) {
            this.topMargin = topMargin
        }

        fun stubLeftMargin(leftMargin: Space) {
            this.leftMargin = leftMargin
        }

        fun stubRightMargin(rightMargin: Space) {
            this.rightMargin = rightMargin
        }

        fun stubBottomMargin(bottomMargin: Space) {
            this.bottomMargin = bottomMargin
        }

        fun stubAllMargins(margin: Space) {
            this.topMargin = margin
            this.leftMargin = margin
            this.rightMargin = margin
            this.bottomMargin = margin
        }

        fun createDividerRowWithDefaults() {
            actualDividerRow = testSubject.createDividerRow()
        }

        fun createDividerRowWithCustomParams() {
            actualDividerRow = testSubject.createDividerRow(
                thickness = thickness,
                topMargin = topMargin,
                leftMargin = leftMargin,
                rightMargin = rightMargin,
                bottomMargin = bottomMargin
            )
        }

        fun createDividerRowWithThickness() {
            actualDividerRow = testSubject.createDividerRow(thickness = thickness)
        }

        fun createDividerRowWithTopMargin() {
            actualDividerRow = testSubject.createDividerRow(topMargin = topMargin)
        }

        fun createDividerRowWithLeftMargin() {
            actualDividerRow = testSubject.createDividerRow(leftMargin = leftMargin)
        }

        fun createDividerRowWithRightMargin() {
            actualDividerRow = testSubject.createDividerRow(rightMargin = rightMargin)
        }

        fun createDividerRowWithBottomMargin() {
            actualDividerRow = testSubject.createDividerRow(bottomMargin = bottomMargin)
        }

        fun createDividerRowWithExtremeMargins() {
            actualDividerRow = testSubject.createDividerRow(
                thickness = thickness,
                topMargin = topMargin,
                leftMargin = leftMargin,
                rightMargin = rightMargin,
                bottomMargin = bottomMargin
            )
        }

        fun createDividerRowWithSameMargins() {
            actualDividerRow = testSubject.createDividerRow(
                thickness = thickness,
                topMargin = topMargin,
                leftMargin = leftMargin,
                rightMargin = rightMargin,
                bottomMargin = bottomMargin
            )
        }

        fun checkDividerRowWithDefaults() {
            val expected = RowLayout(
                type = RowLayoutType.DIVIDER_ROW,
                data = listOf(DividerCardDto(data = DividerCardDto.DividerData(thickness = DividerCardDto.Thickness.LIGHT))),
                style = Style(
                    spacing = Style.Spacing(
                        margins = Style.Spacing.Dimension(
                            top = Space.SMALL,
                            left = Space.SMALL,
                            right = Space.SMALL,
                            bottom = Space.SMALL
                        )
                    )
                )
            )
            assertEquals(expected, actualDividerRow)
            assertEquals(RowLayoutType.DIVIDER_ROW, actualDividerRow.type)
            assertEquals(1, actualDividerRow.data.size)
            assertNotNull(actualDividerRow.style)
        }

        fun checkDividerRowWithCustomParams() {
            assertEquals(RowLayoutType.DIVIDER_ROW, actualDividerRow.type)
            assertEquals(1, actualDividerRow.data.size)
            val dividerCard = actualDividerRow.data.first() as DividerCardDto
            assertEquals(thickness, dividerCard.data.thickness)
            assertEquals(topMargin, actualDividerRow.style?.spacing?.margins?.top)
            assertEquals(leftMargin, actualDividerRow.style?.spacing?.margins?.left)
            assertEquals(rightMargin, actualDividerRow.style?.spacing?.margins?.right)
            assertEquals(bottomMargin, actualDividerRow.style?.spacing?.margins?.bottom)
        }

        fun checkDividerRowThickness(expectedThickness: DividerCardDto.Thickness) {
            assertEquals(RowLayoutType.DIVIDER_ROW, actualDividerRow.type)
            val dividerCard = actualDividerRow.data.first() as DividerCardDto
            assertEquals(expectedThickness, dividerCard.data.thickness)
        }

        fun checkDividerRowTopMargin(expectedSpace: Space) {
            assertEquals(expectedSpace, actualDividerRow.style?.spacing?.margins?.top)
        }

        fun checkDividerRowLeftMargin(expectedSpace: Space) {
            assertEquals(expectedSpace, actualDividerRow.style?.spacing?.margins?.left)
        }

        fun checkDividerRowRightMargin(expectedSpace: Space) {
            assertEquals(expectedSpace, actualDividerRow.style?.spacing?.margins?.right)
        }

        fun checkDividerRowBottomMargin(expectedSpace: Space) {
            assertEquals(expectedSpace, actualDividerRow.style?.spacing?.margins?.bottom)
        }

        fun checkDividerRowWithExtremeMargins() {
            assertEquals(RowLayoutType.DIVIDER_ROW, actualDividerRow.type)
            val dividerCard = actualDividerRow.data.first() as DividerCardDto
            assertEquals(DividerCardDto.Thickness.MEDIUM, dividerCard.data.thickness)
            assertEquals(Space.XXXX_SMALL, actualDividerRow.style?.spacing?.margins?.top)
            assertEquals(Space.XXX_LARGE, actualDividerRow.style?.spacing?.margins?.left)
            assertEquals(Space.XXXX_SMALL, actualDividerRow.style?.spacing?.margins?.right)
            assertEquals(Space.XXX_LARGE, actualDividerRow.style?.spacing?.margins?.bottom)
        }

        fun checkDividerRowWithSameMargins() {
            assertEquals(RowLayoutType.DIVIDER_ROW, actualDividerRow.type)
            val dividerCard = actualDividerRow.data.first() as DividerCardDto
            assertEquals(DividerCardDto.Thickness.LIGHT, dividerCard.data.thickness)
            assertEquals(Space.X_LARGE, actualDividerRow.style?.spacing?.margins?.top)
            assertEquals(Space.X_LARGE, actualDividerRow.style?.spacing?.margins?.left)
            assertEquals(Space.X_LARGE, actualDividerRow.style?.spacing?.margins?.right)
            assertEquals(Space.X_LARGE, actualDividerRow.style?.spacing?.margins?.bottom)
        }
    }
} 