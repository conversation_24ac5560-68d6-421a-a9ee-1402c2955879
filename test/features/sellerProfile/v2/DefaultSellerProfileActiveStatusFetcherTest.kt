package features.sellerProfile.v2

import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.conversations.models.RawUserInfo
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenResponse
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.features.sellerProfile.v2.DefaultSellerProfileActiveStatusFetcher
import com.gumtree.mobile.responses.UnauthorisedException
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*

import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.UserServiceDataFactory

class DefaultSellerProfileActiveStatusFetcherTest {

    private val robot = Robot()

    @ParameterizedTest
    @CsvSource(
        DataFactory.SOME_SELLER_ACTIVE_STATUS_DATE,
        DataFactory.ANOTHER_SELLER_ACTIVE_STATUS_DATE,
    )
    fun `should return seller user info with last active status date`(lastActiveDate: String) = runTest {
        runUnitTest(robot) {
            Given { stubFetchUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubFetchCoreChatAuthorization(DataFactory.SOME_STREAM_TOKEN) }
            Given { stubFetchUserInfo(lastActiveDate) }
            When { fetchUserActiveStatus(DataFactory.SOME_USER_ID) }
            Then { checkSellerUserInfo(RawUserInfo(lastActive = lastActiveDate)) }
        }
    }

    @Test
    fun `should NOT return seller user info when user email is NULL`() = runTest {
        runUnitTest(robot) {
            When { fetchUserActiveStatus(null) }
            Then { checkSellerUserInfo(null) }
        }
    }

    @Test
    fun `should NOT return seller user info when user id fetch fail`() = runTest {
        runUnitTest(robot) {
            Given { stubFetchUserEmailError() }
            When { fetchUserActiveStatus(DataFactory.SOME_USER_EMAIL) }
            Then { checkSellerUserInfo(null) }
        }
    }

    @Test
    fun `should NOT return seller user info when user core chat authorization fetch fail`() = runTest {
        runUnitTest(robot) {
            Given { stubFetchUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubFetchCoreChatAuthorizationError() }
            When { fetchUserActiveStatus(DataFactory.SOME_USER_ID) }
            Then { checkSellerUserInfo(null) }
        }
    }

    @Test
    fun `should NOT return seller user info when raw user info fetch fail`() = runTest {
        runUnitTest(robot) {
            Given { stubFetchUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubFetchCoreChatAuthorization(DataFactory.SOME_STREAM_TOKEN) }
            Given { stubFetchUserInfoError() }
            When { fetchUserActiveStatus(DataFactory.SOME_USER_ID) }
            Then { checkSellerUserInfo(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualRawUserInfoResult: RawUserInfo? = null

        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val coreChatAuthApi: CoreChatAuthApi = mockk(relaxed = true)
        private val conversationsApi: ConversationsApi = mockk(relaxed = true)

        private val testSubject = DefaultSellerProfileActiveStatusFetcher(
            userServiceApi,
            coreChatAuthApi,
            conversationsApi,
        )

        fun stubFetchUserEmail(email: String) {
            coEvery { userServiceApi.getUserDetailsByUserId(any()) } returns UserServiceDataFactory.createRawUserDetails(userEmail = email)
        }

        fun stubFetchUserEmailError() {
            coEvery { userServiceApi.getUserDetailsByUserId(any()) } throws UnauthorisedException()
        }

        fun stubFetchCoreChatAuthorization(authorization: String) {
            coEvery { coreChatAuthApi.generateCoreChatJwt(any()) } returns CoreChatStreamTokenResponse(token = authorization)
        }

        fun stubFetchCoreChatAuthorizationError() {
            coEvery { coreChatAuthApi.generateCoreChatJwt(any()) } throws UnauthorisedException()
        }

        fun stubFetchUserInfo(lastActiveDate: String) {
            coEvery { conversationsApi.getUserInfo(any(), any()) } returns RawUserInfo(lastActive = lastActiveDate)
        }

        fun stubFetchUserInfoError() {
            coEvery { conversationsApi.getUserInfo(any(), any()) } throws UnauthorisedException()
        }

        suspend fun fetchUserActiveStatus(userId: String?) {
            actualRawUserInfoResult = testSubject.fetchRawUserInfo(userId)
        }

        fun checkSellerUserInfo(expected: RawUserInfo?) {
            assertEquals(expected, actualRawUserInfoResult)
        }
    }
}