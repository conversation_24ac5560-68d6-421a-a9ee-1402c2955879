package com.gumtree.mobile.features.favourites

import com.gumtree.mobile.di.CAPI_HEADERS_PROVIDER
import com.gumtree.mobile.di.FAVOURITES_PAGE_CALCULATOR
import com.gumtree.mobile.features.screens.LimitedPageCalculator
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.core.qualifier.named
import org.koin.dsl.module

/**
 * Provides all dependencies for Favourites feature
 */
val favouritesModule = module {
    single<PageCalculator>(named(FAVOURITES_PAGE_CALCULATOR)) { LimitedPageCalculator() }
    single { FavouritesMapper(commonAnalyticsProvider = getFromKoin()) }
    single<FavouritesRepository> {
        DefaultFavouritesRepository(
            favouritesService = getFromKoin(),
            favouritesMapper = getFromKoin(),
            favouritesPageCalculator = getFromKoin(FAVOURITES_PAGE_CALCULATOR),
            headersProvider = getFromKoin(CAPI_HEADERS_PROVIDER),
            dispatcherProvider = getFromKoin(),
        )
    }
    single {
        FavouritesService(
            favouritesApi = getFromKoin(),
            favouriteAdvertsApi = getFromKoin(),
        )
    }
}
