package com.gumtree.mobile.features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.utils.extensions.data.toVipImageCardDto
import com.gumtree.mobile.utils.extensions.isNotNull

/**
 * Mapping the RawCapiAd data into List with VipImageCardDto mobile application data
 */
class VipImageGalleryMapper {

    fun map(
        rawData: RawCapiAd,
        gamAdvertImageCard: VipImageCardDto?,
        analyticsEventData: AnalyticsEventData? = null,
    ): RowLayout<UiItem> {
        val vipImageCardDtoList = rawData.pictures?.mapNotNull {
            it.toVipImageCardDto(analyticsEventData)
        } ?: emptyList()

        return RowLayout(
            type = RowLayoutType.VIP_IMAGE_GALLERY_ROW,
            data = appendGamAdvert(vipImageCardDtoList, gamAdvertImageCard),
            style = Style(
                spacing = createSpacing(
                    bottomMargin = Space.SMALL,
                ),
            ),
        )
    }

    private fun appendGamAdvert(
        imageCardDtoList: List<VipImageCardDto>,
        gamAdvertImageCard: VipImageCardDto?,
    ): List<VipImageCardDto> {
        return when {
            gamAdvertImageCard.isNotNull() && imageCardDtoList.size >= VipScreenUiConfiguration.IMAGE_GALLERY_GAM_ADVERT_ATTACH_LIMIT -> {
                imageCardDtoList
                    .toMutableList()
                    .apply {
                        add(gamAdvertImageCard)
                    }
            }
            else -> imageCardDtoList
        }
    }
}
