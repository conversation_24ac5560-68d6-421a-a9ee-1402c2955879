package com.gumtree.mobile.features.reviews

import com.gumtree.mobile.requests.RequestBody
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CreateConversationReviewRequest(
    @SerialName("rating")
    val rating: Int,
    @SerialName("review")
    val review: Review?,
) : RequestBody {
    @Serializable
    data class Review(
        @SerialName("positiveTags")
        val positiveTags: List<String>?,

        @SerialName("negativeTags")
        val negativeTags: List<String>?,
    )

    /**
     * In order fo the Review payload to be valid we need rating numbers between MIN_REVIEW_RATING and REVIEW_RATING_STARS.size
     * If the rating value is not between MIN_REVIEW_RATING and REVIEW_RATING_STARS.size we throw a BadRequestException error
     */
    override fun isValid(): Boolean {
        return rating in ReviewsScreenUiConfiguration.MIN_REVIEW_RATING..ReviewsScreenUiConfiguration.REVIEW_RATING_STARS.size
    }
}
