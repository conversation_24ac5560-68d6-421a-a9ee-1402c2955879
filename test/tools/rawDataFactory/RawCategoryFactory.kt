package tools.rawDataFactory

import com.gumtree.mobile.api.categories.models.RawCategory
import tools.DataFactory


object RawCategoryFactory {

    /**
     * Generate RawCategory data ready to be used in Categories feature context
     * @param categoryId - the category id
     * @param categoryText - the category display text
     * @param categoryChildren - the category children
     * @param enabled - the category enabled status
     * @param readOnly - the category readOnly status
     * @param hidden - the category hidden status
     * @return - an instance of RawCategory
     */
    fun createRawCategory(
        categoryId: String,
        categoryText: String,
        categoryChildren: List<RawCategory>? = emptyList(),
        enabled: Boolean = true,
        readOnly: Boolean = true,
        hidden: Boolean? = null,
    ): RawCategory {
        return RawCategory(
            id = categoryId.toInt(),
            name = categoryText,
            seoName = categoryText.replace(" ", "-").lowercase(),
            enabled = enabled,
            readOnly = readOnly,
            hidden = hidden,
            seoDisplayName = categoryText,
            children = categoryChildren
        )
    }

    /**
     * Generate RawCategory tree data ready to be used in Categories feature context
     * @param number - the number of random RawCategory to be generated/stubbed
     * @return - an instance of top RawCategory which is the category tree
     */
    fun createRawCategoryTree(
        number: Int
    ): RawCategory {
        val childCategoriesList = mutableListOf<RawCategory>()
        repeat(number) {
            val rawChildCategory = createRawCategory(
                DataFactory.anyInt().toString(),
                DataFactory.anyString(),
            )
            childCategoriesList.add(it, rawChildCategory)
        }
        val rootCategory = RawCategory(
            id = DataFactory.anyInt(),
            name = DataFactory.anyString(),
            seoName = DataFactory.anyString(),
            enabled = true,
            readOnly = true,
            hidden = false,
            seoDisplayName = DataFactory.anyString(),
            children = childCategoriesList
        )

        return rootCategory
    }
}
