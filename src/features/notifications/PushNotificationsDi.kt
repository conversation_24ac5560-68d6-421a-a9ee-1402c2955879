package com.gumtree.mobile.features.notifications

import com.gumtree.mobile.common.PushNotificationsService
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Provides all dependencies about Push Notifications feature
 */
val pushNotificationsModule = module {
    single { NotificationsBodyCreator() }
    single {
        PushNotificationsService(
            coreChatAuthApi = getFromKoin(),
            conversationsApi = getFromKoin(),
            userServiceApi = getFromKoin(),
            notificationsBodyCreator = getFromKoin(),
        )
    }
    single<PushNotificationsRepository> {
        DefaultPushNotificationsRepository(
            pushNotificationsService = getFromKoin(),
            notificationsBodyCreator = getFromKoin(),
            dispatcherProvider = getFromKoin(),
        )
    }
}
