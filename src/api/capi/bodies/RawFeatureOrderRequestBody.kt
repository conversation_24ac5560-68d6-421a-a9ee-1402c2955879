package com.gumtree.mobile.api.capi.bodies

import api.capi.models.RawBillingAddress
import api.capi.models.RawFeatureOption
import api.capi.models.RawFeaturePrice
import api.capi.models.RawPromotionCode
import com.gumtree.mobile.api.capi.Namespaces
import org.simpleframework.xml.Attribute
import org.simpleframework.xml.Element
import org.simpleframework.xml.ElementList
import org.simpleframework.xml.Namespace
import org.simpleframework.xml.NamespaceList
import org.simpleframework.xml.Root

@Root(name = "order-request", strict = false)
@NamespaceList(
    Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT),
    Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER),
    Namespace(reference = Namespaces.PAYMENT, prefix = Namespaces.Prefix.PAYMENT),
    Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES),
)
@Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
data class RawFeatureOrderRequestBody(

    @field:Attribute(name = "selling-point")
    @param:Attribute(name = "selling-point")
    val sellingPoint: String? = null,

    @Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
    @ElementList(name = "order-items", required = false)
    val orderItems: ArrayList<RawOrderItem>? = null,

    @Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
    @field:Element(name = "payment-method", required = false)
    @param:Element(name = "payment-method")
    val paymentMethod: RawPaymentMethod? = null,

    @Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
    @ElementList(name = "promotion-codes", required = false)
    val promotionCodes: ArrayList<RawPromotionCode>? = null,

    @Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
    @field:Element(name = "billing-address", required = false)
    @param:Element(name = "billing-address")
    val billingAddress: RawBillingAddress? = null,

    @Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
    @ElementList(name = "taxes", required = false)
    val taxes: ArrayList<RawTax>? = null,

)

@Root(name = "order-item", strict = false)
@NamespaceList(
    Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT),
    Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER),
    Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES),
)
@Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
data class RawOrderItem(

    @field:Attribute(name = "target")
    @param:Attribute(name = "target")
    val target: String? = null,

    @field:Attribute(name = "target-id")
    @param:Attribute(name = "target-id")
    val targetId: String? = null,

    @field:Attribute(name = "type")
    @param:Attribute(name = "type")
    val type: String? = null,

    @field:Element(name = "feature-booked")
    @param:Element(name = "feature-booked")
    val featuresBooked: FeatureBooked? = null,

)

@Root(name = "tax", strict = false)
@Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
data class RawTax(

    @field:Attribute(name = "tax-type", required = false)
    @param:Attribute(name = "tax-type")
    val taxType: String? = null,

    @field:Attribute(name = "localized-name", required = false)
    @param:Attribute(name = "localized-name")
    val localizedName: String? = null,

    @Namespace(reference = Namespaces.ORDER)
    @field:Element(name = "amount")
    @param:Element(name = "amount")
    val amount: String? = null,

    @Namespace(reference = Namespaces.ORDER)
    @field:Element(name = "currency-iso-code", required = false)
    @param:Element(name = "currency-iso-code")
    val currencyIsoCode: RawFeaturePrice.CurrencyIsoCode? = null,

)

@Root(name = "feature-booked", strict = false)
@NamespaceList(
    Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT),
    Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER),
    Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES),
)
@Namespace(reference = Namespaces.ORDER, prefix = Namespaces.Prefix.ORDER)
data class FeatureBooked(

    @field:Attribute(name = "group")
    @param:Attribute(name = "group")
    val group: String? = null,

    @field:Attribute(name = "name")
    @param:Attribute(name = "name")
    val name: String? = null,

    @field:Element(name = "option", required = false)
    @param:Element(name = "option")
    val featureOption: RawFeatureOption? = null,
)

@Root(name = "payment-method", strict = false)
@Namespace(reference = Namespaces.PAYMENT)
data class RawPaymentMethod(

    @field:Attribute(name = "id")
    @param:Attribute(name = "id")
    val id: Int = 0,

    @field:Attribute(name = "type")
    @param:Attribute(name = "type")
    val type: String? = null,

    @field:Attribute(name = "name")
    @param:Attribute(name = "name")
    val name: String? = null,

    @field:Element(name = "credit-card", required = false)
    @param:Element(name = "credit-card")
    val creditCard: RawCreditCard? = null,

    @Namespace(reference = Namespaces.PAYMENT, prefix = Namespaces.Prefix.PAYMENT)
    @field:Element(name = "redirect-url", required = false)
    @param:Element(name = "redirect-url")
    val redirectUrl: String? = null,

    @field:Element(name = "set-paypal-express-checkout", required = false)
    @param:Element(name = "set-paypal-express-checkout")
    val setPayPalExpressCheckout: RawPayPalExpressCheckout? = null,

    @field:Element(name = "payflow-response", required = false)
    @param:Element(name = "payflow-response")
    val payFlowResponse: RawPayFlowResponse? = null,

)

@Root(name = "credit-card", strict = false)
@Namespace(reference = Namespaces.PAYMENT)
data class RawCreditCard(

    @field:Attribute(name = "save-for-future")
    @param:Attribute(name = "save-for-future")
    val saveForFuture: Boolean = false,

    @field:Element(name = "name")
    @param:Element(name = "name")
    val name: String? = null,

    @field:Element(name = "number")
    @param:Element(name = "number")
    val number: String? = null,

    @field:Element(name = "expired")
    @param:Element(name = "expired")
    val expired: String? = null,

    @field:Element(name = "verification-number")
    @param:Element(name = "verification-number")
    val verificationNumber: String? = null,

)

@Root(name = "set-paypal-express-checkout")
@Namespace(reference = Namespaces.PAYMENT, prefix = Namespaces.Prefix.PAYMENT)
data class RawPayPalExpressCheckout(

    @Namespace(reference = Namespaces.PAYMENT, prefix = Namespaces.Prefix.PAYMENT)
    @field:Element(name = "confirm-url")
    @param:Element(name = "confirm-url")
    val confirmUrl: String? = null,

    @Namespace(reference = Namespaces.PAYMENT, prefix = Namespaces.Prefix.PAYMENT)
    @field:Element(name = "cancel-url")
    @param:Element(name = "cancel-url")
    val cancelUrl: String? = null,

)

@Root(name = "payflow-response")
data class RawPayFlowResponse(

    @field:Element(name = "result")
    @param:Element(name = "result")
    val result: Int = 0,

    @field:Element(name = "pnref", required = false)
    @param:Element(name = "pnref")
    val pnRef: String? = null,

    @field:Element(name = "ppref", required = false)
    @param:Element(name = "ppref")
    val ppRef: String? = null,

    @field:Element(name = "ponum", required = false)
    @param:Element(name = "ponum")
    val poNum: String? = null,

    @field:Element(name = "last-four-card-digits", required = false)
    @param:Element(name = "last-four-card-digits")
    val lastFourCardDigits: String? = null,

    @field:Element(name = "save-credit-card", required = false)
    @param:Element(name = "save-credit-card")
    val saveCreditCard: Boolean? = null,

    @field:Element(name = "redirect-url", required = false)
    @param:Element(name = "redirect-url")
    val redirectUrl: String? = null,

)
