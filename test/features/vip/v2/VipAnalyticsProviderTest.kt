package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.conversations.models.RawUserInfo
import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsListingPostedTimeAgoFormatter
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFormatter
import com.gumtree.mobile.features.vip.v2.ANALYTICS_CALL_SELLER_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ANALYTICS_CONTACT_SELLER_EXTERNAL_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ANALYTICS_CONTACT_SELLER_LINK_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ANALYTICS_IMAGE_CLICK_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ANALYTICS_LISTING_PARTNER
import com.gumtree.mobile.features.vip.v2.ANALYTICS_PARTNER_ADS_BANNER_TYPE_VALUE
import com.gumtree.mobile.features.vip.v2.ANALYTICS_PARTNER_ADS_CLICK_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ANALYTICS_SELLER_ID
import com.gumtree.mobile.features.vip.v2.ANALYTICS_START_CHAT_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ANALYTICS_VIEW_LISTING_EVENT_NAME
import com.gumtree.mobile.features.vip.v2.ListingPartner
import com.gumtree.mobile.features.vip.v2.VipAnalyticsProvider
import io.ktor.http.Headers
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CallHeadersFactory
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.layoutsDataFactory.CategoryDtoFactory
import tools.rawDataFactory.PartnershipDetailsResponseFactory
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.runUnitTest

class VipAnalyticsProviderTest {

    private val robot = Robot()

    init {
        // for some reason adding this in the @Before doesn't work. Appears to be a race condition when running the full test suite
        CategoriesTreeCache.data = CategoryDtoFactory.createCategoriesTree()
    }

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return correct screen event`() = runTest {
        runUnitTest(robot) {
            When { getScreenViewEvent() }
            Then { checkScreenViewEvent(AnalyticsEventData(ANALYTICS_VIEW_LISTING_EVENT_NAME)) }
        }
    }

    @Test
    fun `should return correct chat event`() = runTest {
        runUnitTest(robot) {
            When { getStartChatEvent() }
            Then { checkScreenViewEvent(AnalyticsEventData(ANALYTICS_START_CHAT_EVENT_NAME)) }
        }
    }

    @Test
    fun `should return correct call event`() = runTest {
        runUnitTest(robot) {
            When { getCallSellerEvent() }
            Then { checkScreenViewEvent(AnalyticsEventData(ANALYTICS_CALL_SELLER_EVENT_NAME)) }
        }
    }

    @Test
    fun `should return correct link event`() = runTest {
        runUnitTest(robot) {
            When { getLinkSellerEvent() }
            Then { checkScreenViewEvent(AnalyticsEventData(ANALYTICS_CONTACT_SELLER_LINK_EVENT_NAME)) }
        }
    }

    @Test
    fun `should return image click event`() = runTest {
        runUnitTest(robot) {
            When { getImgClickEvent() }
            Then { checkScreenViewEvent(AnalyticsEventData(ANALYTICS_IMAGE_CLICK_EVENT_NAME)) }
        }
    }



    @Test
    fun `should return correct screen params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubTimeAgo("2 days")
                stubRawCapiAd()
                stubRawPapiUserProfile()
                stubRawUserInfo()
                stubCallHeaders(CallHeadersFactory.createAuthHeaders())
            }
            When { getVipScreenParams() }
            Then {
                checkScreenParamValue("listing_id", DataFactory.SOME_AD_ID)
                checkScreenParamValue("listing_contact_number", "false")
                checkScreenParamValue("listing_age", "2 days")
                checkScreenParamValue("listing_contact_email", "false")
                checkScreenParamValue("listing_location", DataFactory.SOME_AD_LOCATION)
                checkScreenParamValue("listing_location_id", DataFactory.SOME_LOCATION_ID)
                checkScreenParamValue("listing_name", DataFactory.SOME_AD_TITLE)
                checkScreenParamValue("listing_category", DataFactory.SOME_AD_CATEGORY_NAME)
                checkScreenParamValue("listing_category_id", DataFactory.SOME_AD_CATEGORY_ID)
                checkScreenParamValue("listing_number_of_images", "1")
                checkScreenParamValue("listing_number_of_words_in_description", "4")
                checkScreenParamValue("listing_price", "23")
                checkScreenParamValue("listing_promotions", "")
                checkScreenParamValue("listing_location_show_on_map", "true")
                checkScreenParamValue("listing_is_trade", "false")
                checkScreenParamValue("listing_website_link", "false")
                checkScreenParamValue(AnalyticsParams.User.RATING_AVERAGE, "0.0")
                checkScreenParamValue(AnalyticsParams.User.RATING_COUNT, "0")
                checkScreenParamValue(AnalyticsParams.User.LAST_ACTIVE, EMPTY_STRING)
                checkScreenParamValue(ANALYTICS_SELLER_ID, DataFactory.SOME_USER_ID)
                checkScreenParamValue(ANALYTICS_LISTING_PARTNER, "")
                checkScreenParamValue("page_referrer", "vip")
                checkGetListingPartnerCalled()

                Then { checkScreenParamsSize(26) }

                checkGetTimeAgoCalled()
            }
        }
    }

    @Test
    fun `should return correct partner ads click event`() = runTest {
        runUnitTest(robot) {
            Given { stubPartnership() }
            When { getPartnerAdsClickEvent() }
            Then {
                checkAnalyticsEventName(ANALYTICS_PARTNER_ADS_CLICK_EVENT_NAME)
                checkAnalyticsEventParamValue("advertising_partner_name", DataFactory.SOME_PARTNERSHIP_ID)
                checkAnalyticsEventParamValue("advertising_banner_type", ANALYTICS_PARTNER_ADS_BANNER_TYPE_VALUE)
                checkAnalyticsEventParamValue("link_domain", DataFactory.SOME_EXTERNAL_URL_DOMAIN)
                checkAnalyticsEventParamValue("link_url", DataFactory.SOME_EXTERNAL_URL)
            }
        }
    }

    @Test
    fun `should return correct seller external click event`() = runTest {
        runUnitTest(robot) {
            Given { stubLinkUrl() }
            Given { stubLinkText() }
            When {
                getSellerContactExternalEvent(
                    mapOf(
                        "advertising_partner_name" to DataFactory.SOME_PARTNERSHIP_ID,
                        "advertising_banner_type" to ANALYTICS_PARTNER_ADS_BANNER_TYPE_VALUE,
                    )
                )
            }
            Then {
                checkAnalyticsEventName(ANALYTICS_CONTACT_SELLER_EXTERNAL_EVENT_NAME)
                checkAnalyticsEventParamValue("advertising_partner_name", DataFactory.SOME_PARTNERSHIP_ID)
                checkAnalyticsEventParamValue("advertising_banner_type", ANALYTICS_PARTNER_ADS_BANNER_TYPE_VALUE)
                checkAnalyticsEventParamValue("link_url", DataFactory.SOME_EXTERNAL_URL)
                checkAnalyticsEventParamValue("link_domain", DataFactory.SOME_EXTERNAL_URL_DOMAIN)
                checkAnalyticsEventParamValue("link_text", DataFactory.SOME_AD_TITLE)
                checkAnalyticsEventParamValue(AnalyticsParams.VIP.URL_PARTNER, "emg")
            }
        }
    }

    @Test
    fun `should return correct seller external click event with no link url`() = runTest {
        runUnitTest(robot) {
            Given { stubLinkUrl(linkUrl = null) }
            Given { stubLinkText() }
            When { getSellerContactExternalEvent(emptyMap()) }
            Then {
                checkAnalyticsEventName(ANALYTICS_CONTACT_SELLER_EXTERNAL_EVENT_NAME)
                checkAnalyticsEventParamValue("link_url", EMPTY_STRING)
                checkAnalyticsEventParamValue("link_domain", EMPTY_STRING)
                checkAnalyticsEventParamValue(AnalyticsParams.VIP.URL_PARTNER, "emg")
            }
        }
    }

    @Test
    fun `should handle null user id`() {
        runUnitTest(robot) {
            Given { stubRawCapiAd(RawCapiAdsFactory.createRawCapiAd(userId = null)) }
            When { getVipScreenParams() }
            Then { checkScreenParamValue(ANALYTICS_LISTING_PARTNER, "") }
            Then { checkScreenParamValue(ANALYTICS_SELLER_ID, "") }
        }
    }

    inner class Robot : BaseRobot {
        private var actualEventDataResult: AnalyticsEventData? = null
        private var actualScreenParamsResult: Map<String, String>? = null

        private val timeAgoFormatter: AnalyticsListingPostedTimeAgoFormatter = mockk(relaxed = true)
        private val activeStatusFormatter: SellerProfileActiveStatusFormatter = mockk(relaxed = true)
        private val commonAnalyticsParamsProvider =
            CommonAnalyticsProviderFactory.createInstance(timeAgoFormatter = timeAgoFormatter)

        private lateinit var partnershipDetailsResponse: PartnershipDetailsResponse
        private lateinit var rawCapiAd: RawCapiAd
        private lateinit var linkText: String
        private var rawPapiUserProfile: RawPapiUserProfile? = null
        private var rawUserInfo: RawUserInfo? = null
        private var linkUrl: String? = null
        private var requestId: String? = null
        private lateinit var callHeaders: Headers
        private val testSubject = VipAnalyticsProvider(commonAnalyticsParamsProvider, activeStatusFormatter)

        override fun setup() {
            mockkObject(ListingPartner.Companion)
        }

        override fun tearsDown() {
            unmockkObject(ListingPartner.Companion)
        }

        fun stubRawCapiAd(rawCapiAd: RawCapiAd = RawCapiAdsFactory.createRawCapiAd()) {
            this.rawCapiAd = rawCapiAd
        }

        fun stubRawPapiUserProfile(rawPapiUserProfile: RawPapiUserProfile? = RawPapiUserProfileFactory.createRawUserProfileWithRating()) {
            this.rawPapiUserProfile = rawPapiUserProfile
        }

        fun stubRawUserInfo(lastActive: String = DataFactory.SOME_SELLER_ACTIVE_STATUS_TEXT) {
            this.rawUserInfo = RawUserInfo(lastActive = lastActive)
        }

        fun stubTimeAgo(value: String) {
            every { timeAgoFormatter.getTimeAgoLabel(olderDate = any()) } returns value
        }

        fun stubLinkUrl(linkUrl: String? = DataFactory.SOME_EXTERNAL_URL) {
            this.linkUrl = linkUrl
        }

        fun stubLinkText(text: String = DataFactory.SOME_AD_TITLE) {
            linkText = text
        }

        fun stubCallHeaders(headers: Headers) {
            callHeaders = headers
        }

        fun stubPartnership(partnershipDetailsResponse: PartnershipDetailsResponse = PartnershipDetailsResponseFactory.createPartnershipDetails()) {
            this.partnershipDetailsResponse = partnershipDetailsResponse
        }

        fun getScreenViewEvent() {
            actualEventDataResult = testSubject.getScreenViewEvent()
        }

        fun getStartChatEvent() {
            actualEventDataResult = testSubject.getStartChatEvent()
        }

        fun getCallSellerEvent() {
            actualEventDataResult = testSubject.getCallSellerEvent()
        }

        fun getLinkSellerEvent() {
            actualEventDataResult = testSubject.getContactLinkEvent()
        }

        fun getImgClickEvent() {
            actualEventDataResult = testSubject.getImageClickEvent()
        }

        fun getPartnerAdsClickEvent() {
            actualEventDataResult = testSubject.getPartnerAdsClickEvent(partnershipDetailsResponse)
        }

        fun getSellerContactExternalEvent(commonAnalyticsScreenParams: Map<String, String>) {
            actualEventDataResult = testSubject.getSellerContactExternalEvent(
                analyticsScreenParams = commonAnalyticsScreenParams,
                linkUrl = linkUrl,
                linkText = linkText
            )
        }

        fun getVipScreenParams() {
            actualScreenParamsResult = testSubject.getScreenParams(
                rawAdDetails = rawCapiAd,
                rawUserProfileData = rawPapiUserProfile,
                rawUserInfoData = rawUserInfo,
                requestId = requestId,
            )
        }

        fun checkScreenViewEvent(expected: AnalyticsEventData) {
            assertEquals(expected, actualEventDataResult)
        }

        fun checkAnalyticsEventName(expected: String) {
            assertEquals(expected, actualEventDataResult?.eventName)
        }

        fun checkAnalyticsEventParamValue(
            key: String,
            expected: String?
        ) {
            assertEquals(expected, actualEventDataResult?.parameters?.get(key))
        }

        fun checkGetListingPartnerCalled() {
            verify { ListingPartner.getListingPartnerValue(any()) }
        }

        fun checkScreenParamValue(
            key: String,
            expected: String?,
        ) {
            assertEquals(expected, actualScreenParamsResult?.get(key))
        }

        fun checkGetTimeAgoCalled() {
            verify { timeAgoFormatter.getTimeAgoLabel(olderDate = any()) }
        }

        fun checkScreenParamsSize(expected: Int) {
            assertEquals(expected, actualScreenParamsResult?.size)
        }
    }
}
