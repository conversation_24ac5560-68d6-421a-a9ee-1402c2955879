package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("DIVIDER_CARD")
data class DividerCardDto(
    @SerialName("data")
    val data: DividerData = DividerData(),
) : UiItem {
    @Serializable
    data class DividerData(
        @SerialName("thickness")
        val thickness: Thickness = Thickness.LIGHT,
    )

    @Serializable
    enum class Thickness {
        @SerialName("LIGHT")
        LIGHT,

        @SerialName("MEDIUM")
        MEDIUM,

        @SerialName("HEAVY")
        HEAVY,
    }
}
