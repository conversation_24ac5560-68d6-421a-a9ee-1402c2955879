package com.gumtree.mobile.features.srp

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.utils.AGO
import com.gumtree.mobile.utils.DAY
import com.gumtree.mobile.utils.HOUR
import com.gumtree.mobile.utils.MINUTE
import com.gumtree.mobile.utils.NOW
import com.gumtree.mobile.utils.TimeAgoFormatter
import com.gumtree.mobile.utils.WEEK
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.extensions.optionalPluralSuffix
import com.gumtree.mobile.utils.runOrNull
import java.time.format.DateTimeFormatter

class SrpTimeAgoFormatter(
    override val dateFormat: DateTimeFormatter,
) : TimeAgoFormatter {

    override fun getTimeAgoLabel(
        olderDate: String?,
        newerDate: String,
    ): String? {
        return runOrNull(olderDate.isNotNullOrEmpty()) {
            val (weeks, days, hours, minutes, seconds) = calculateWeeksDaysHoursMinutesSeconds(olderDate, newerDate)
            when {
                weeks > ZERO -> getWeekText(weeks)
                days > ZERO -> getDayText(days)
                hours > ZERO -> getHourText(hours)
                minutes > ZERO -> getMinuteText(minutes)
                seconds > ZERO -> getSecondText()
                else -> null
            }
        }
    }

    private fun getWeekText(timeAgo: Long) = "$timeAgo ${WEEK.optionalPluralSuffix(timeAgo)} $AGO"

    private fun getDayText(timeAgo: Long) = "$timeAgo ${DAY.optionalPluralSuffix(timeAgo)} $AGO"

    private fun getHourText(timeAgo: Long) = "$timeAgo ${HOUR.optionalPluralSuffix(timeAgo)} $AGO"

    private fun getMinuteText(timeAgo: Long) = "$timeAgo ${MINUTE.optionalPluralSuffix(timeAgo)} $AGO"

    private fun getSecondText() = NOW
}
