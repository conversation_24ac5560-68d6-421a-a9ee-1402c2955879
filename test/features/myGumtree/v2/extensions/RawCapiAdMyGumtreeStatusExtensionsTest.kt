package features.myGumtree.v2.extensions

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.capi.models.RawCapiAdStatus
import com.gumtree.mobile.features.myGumtree.v2.extensions.getMyGumtreeStatus
import com.gumtree.mobile.features.screens.layoutsData.HyperlinkMeta
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeStatus
import com.gumtree.mobile.features.screens.layoutsData.StatusLabel
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest
import kotlin.test.assertEquals
import tools.annotations.ParallelTest

@ParallelTest
class RawCapiAdMyGumtreeStatusExtensionsTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }


    @Test
    fun `should return correct status values when when AdStatus is PENDING`() {
        runUnitTest(robot) {
            Given { stubAdStatus(AdStatus.PENDING) }
            When { getMyGumtreeListingButton() }
            Then {
                checkStatus(
                    MyGumtreeStatus(
                        text = "Processing...",
                        toolTip = "We moderate listings in order to follow safety practices, which can sometimes take a few hours"
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct status values when when AdStatus is EXPIRED`() {
        runUnitTest(robot) {
            Given { stubAdStatus(AdStatus.EXPIRED) }
            When { getMyGumtreeListingButton() }
            Then {
                checkStatus(
                    MyGumtreeStatus(
                        label = StatusLabel.EXPIRED,
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct status values when when AdStatus is DELETED`() {
        runUnitTest(robot) {
            Given { stubAdStatus(AdStatus.DELETED) }
            When { getMyGumtreeListingButton() }
            Then {
                checkStatus(
                    MyGumtreeStatus(
                        label = StatusLabel.DELETED,
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct status values when when AdStatus is REVIEW`() {
        runUnitTest(robot) {
            Given { stubAdStatus(AdStatus.DELAYED) } // is this the correct status?
            When { getMyGumtreeListingButton() }
            Then {
                checkStatus(
                    MyGumtreeStatus(
                        text = "Under review...",
                        toolTip = "We moderate listings in order to follow safety practices, which can sometimes take a few hours"
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct status values when when AdStatus is DELETED_CS`() {
        runUnitTest(robot) {
            Given { stubAdStatus(AdStatus.DELETED_CS) }
            When { getMyGumtreeListingButton() }
            Then {
                checkStatus(
                    MyGumtreeStatus(
                        text = "Ad has been removed",
                        toolTip = "Your ad has been removed from the site because it breaks the posting rules. We have emailed you to explain why",
                        toolTipHyperlink = HyperlinkMeta("posting rules", "gumtree.com/rules"),
                        label = StatusLabel.REMOVED
                    )
                )
            }
        }
    }

    private inner class Robot : BaseRobot {

        private lateinit var testSubject: RawCapiAd
        private var actualStatus: MyGumtreeStatus? = null

        override fun setup() {
            testSubject = RawCapiAdsFactory.createRawCapiAd()
        }

        fun stubAdStatus(status: AdStatus) {
            testSubject.status = RawCapiAdStatus(value = status.toString())
        }

        fun getMyGumtreeListingButton() {
            actualStatus = testSubject.getMyGumtreeStatus()
        }

        fun checkStatus(expected: MyGumtreeStatus) {
            assertEquals(expected, actualStatus)
        }

    }
}