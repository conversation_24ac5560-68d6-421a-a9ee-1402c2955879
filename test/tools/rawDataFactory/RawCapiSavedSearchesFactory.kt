package tools.rawDataFactory


import api.capi.models.RawCapiPaging
import api.capi.models.RawCapiSavedSearchList
import com.gumtree.mobile.api.capi.bodies.RawCapiAlertDestinationBody
import com.gumtree.mobile.api.capi.bodies.RawCapiSavedSearchBody
import com.gumtree.mobile.api.capi.bodies.RawCapiSearchLinkBody
import com.gumtree.mobile.api.capi.bodies.RawCapiStatusTypeBody
import com.gumtree.mobile.api.capi.models.RawCapiAlertDestination
import com.gumtree.mobile.api.capi.models.RawCapiSavedSearch
import com.gumtree.mobile.api.capi.models.RawCapiSearchLink
import com.gumtree.mobile.api.capi.models.RawCapiStatusType
import com.gumtree.mobile.api.capi.models.SavedSearchStatus
import tools.DataFactory


object RawCapiSavedSearchesFactory {

    /**
     * Generate RawCapiSavedSearch data ready to be used in SavedSearches feature context
     * @param status - the status of the saved search
     * @param searchDescription - the saved search description
     * @param searchLink - the href value of the saved search link
     * @param id - the saved search id
     */
    fun createRawCapiSavedSearch(
        status: SavedSearchStatus,
        searchDescription: String? = null,
        searchLink: String? = null,
        id: String = DataFactory.SOME_SAVED_SEARCH_ID
    ): RawCapiSavedSearch {
        val rawCapiStatusType = RawCapiStatusType(status)
        val rawCapiAlertDestination = RawCapiAlertDestination(statusType = rawCapiStatusType)
        val rawCapiSearchLink = RawCapiSearchLink(href = searchLink)
        return RawCapiSavedSearch(
            id = id,
            searchLink = rawCapiSearchLink,
            searchDescription = searchDescription,
            alertDestinations = arrayListOf(rawCapiAlertDestination)
        )
    }

    /**
     * Generate RawCapiSavedSearch data ready to be used in SavedSearches feature context
     * @param status - the status of the saved search
     * @param searchDescription - the saved search description
     * @param rawSearchLink - the href value of the saved search link
     * @param id - the saved search id
     */
    fun createRawCapiSavedSearch(
        status: SavedSearchStatus,
        searchDescription: String? = null,
        rawSearchLink: RawCapiSearchLink,
        id: String = DataFactory.SOME_SAVED_SEARCH_ID
    ): RawCapiSavedSearch {
        val rawCapiStatusType = RawCapiStatusType(status)
        val rawCapiAlertDestination = RawCapiAlertDestination(statusType = rawCapiStatusType)
        return RawCapiSavedSearch(
            id = id,
            searchLink = rawSearchLink,
            searchDescription = searchDescription,
            alertDestinations = arrayListOf(rawCapiAlertDestination)
        )
    }

    /**
     * Generate RawCapiSavedSearchBody payload data ready to be used in Create SavedSearch feature
     * @param status - the status of the saved search
     * @param searchDescription - the saved search description
     * @param searchLink - the href value of the saved search link
     */
    fun createRawCapiSavedSearchBody(
        status: SavedSearchStatus,
        searchDescription: String?,
        searchLink: String? = null
    ): RawCapiSavedSearchBody {
        val rawCapiStatusType = RawCapiStatusTypeBody(status)
        val rawCapiAlertDestination = RawCapiAlertDestinationBody(statusType = rawCapiStatusType)
        val rawCapiSearchLink = RawCapiSearchLinkBody(href = searchLink)
        return RawCapiSavedSearchBody(
            searchLink = rawCapiSearchLink,
            searchDescription = searchDescription,
            alertDestinations = arrayListOf(rawCapiAlertDestination)
        )
    }

    /**
     * Generate RawCapiSavedSearchList data ready to be used in SavedSearches feature tests
     * @param number - the number of random RawCapiSavedSearches to be generated/stubbed
     * @param savedSearchStatuses - the list with random saved search statuses we should have in the list
     */
    fun createRawSavedSearchList(
        number: Int,
        savedSearchStatuses: List<SavedSearchStatus> = emptyList()
    ): RawCapiSavedSearchList {
        val rawSavedSearchesList = RawCapiSavedSearchList().apply {
            rawCapiSavedSearches = mutableListOf()
            rawPaging = RawCapiPaging().apply {
                numFound = number
            }
        }
        repeat(number) {
            val savedSearchStatus = when (savedSearchStatuses.size) {
                0 -> DataFactory.anyEnumValue(SavedSearchStatus::class.java)
                else -> DataFactory.mixedEnumValueFromRange(it, savedSearchStatuses)
            }
            val rawSavedSearch = createRawCapiSavedSearch(
                savedSearchStatus,
                DataFactory.anyString()
            )
            rawSavedSearchesList.rawCapiSavedSearches.add(rawSavedSearch)
        }
        return rawSavedSearchesList
    }
}
