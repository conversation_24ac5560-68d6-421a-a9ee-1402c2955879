package adverts.gam

import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.adverts.gam.GAM_BRP_PAGE_TYPE
import com.gumtree.mobile.adverts.gam.GAM_SRP_PAGE_TYPE
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.cache.CATEGORIES_TREE_CACHE_RESOURCE_PATH
import com.gumtree.mobile.cache.createFileInputStream
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.features.srp.GAM_SRP_MIDDLE_1_SLOT
import com.gumtree.mobile.features.srp.GAM_SRP_MIDDLE_2_SLOT
import com.gumtree.mobile.features.srp.GAM_SRP_MIDDLE_3_SLOT
import com.gumtree.mobile.responses.ScreenType
import com.gumtree.mobile.utils.CategoryDefaults
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.rawDataFactory.RawLocationFactory
import tools.runUnitTest

@ParallelTest
class GAMAdvertUtilsTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return advert slot name with category`() {
        runUnitTest(robot) {
            When { createAdvertSlotName(GAM_SRP_MIDDLE_1_SLOT, CategoryDefaults.JOBS.id) }
            Then { checkSlotName("$GAM_SRP_MIDDLE_1_SLOT/${CategoryDefaults.JOBS.idName}") }

            When { createAdvertSlotName(GAM_SRP_MIDDLE_2_SLOT, CategoryDefaults.SERVICES.id) }
            Then { checkSlotName("$GAM_SRP_MIDDLE_2_SLOT/${CategoryDefaults.SERVICES.idName}") }

            When { createAdvertSlotName(GAM_SRP_MIDDLE_3_SLOT, CategoryDefaults.PETS.id) }
            Then { checkSlotName("$GAM_SRP_MIDDLE_3_SLOT/${CategoryDefaults.PETS.idName}") }
        }
    }

    @Test
    fun `should return category levels`() {
        runUnitTest(robot) {
            When { getCategoryLevels(CategoryDefaults.JOBS.id) }
            Then { checkCategoryLevelIdName(1, CategoryDefaults.ALL_CATEGORIES.idName) }
            Then { checkCategoryLevelIdName(2, CategoryDefaults.JOBS.idName) }

            When { getCategoryLevels(CategoryDefaults.SERVICES.id) }
            Then { checkCategoryLevelIdName(1, CategoryDefaults.ALL_CATEGORIES.idName) }
            Then { checkCategoryLevelIdName(2, CategoryDefaults.SERVICES.idName) }

            When { getCategoryLevels(CategoryDefaults.HOME_AND_GARDEN.id) }
            Then { checkCategoryLevelIdName(1, CategoryDefaults.ALL_CATEGORIES.idName) }
            Then { checkCategoryLevelIdName(2, CategoryDefaults.FOR_SALE.idName) }
            Then { checkCategoryLevelIdName(3, CategoryDefaults.HOME_AND_GARDEN.idName) }
        }
    }

    @Test
    fun `should return location value`() {
        runUnitTest(robot) {
            When { getLocationValue(RawLocationFactory.createRawLocation()) }
            Then { checkLocationValue(DataFactory.SOME_LOCATION_NAME) }
        }
    }

    @Test
    fun `should return location post code value`() {
        runUnitTest(robot) {
            When {
                getPostCodeValue(
                    RawLocationFactory.createRawLocation(
                        name = DataFactory.SOME_ZIP_CODE,
                        type = RawLocation.Type.postcode,
                    )
                )
            }
            Then { checkPostCodeValue(DataFactory.SOME_ZIP_CODE) }
        }
    }

    @Test
    fun `should NOT return location post code value`() {
        runUnitTest(robot) {
            When {
                getPostCodeValue(
                    RawLocationFactory.createRawLocation(
                        name = DataFactory.SOME_LOCATION_NAME,
                        type = RawLocation.Type.location,
                    )
                )
            }
            Then { checkPostCodeValue(null) }
        }
    }

    @Test
    fun `should return location out code value`() {
        runUnitTest(robot) {
            When {
                getOutCodeValue(
                    RawLocationFactory.createRawLocation(
                        name = DataFactory.SOME_OUTCODE,
                        type = RawLocation.Type.outcode,
                    )
                )
            }
            Then { checkOutCodeValue(DataFactory.SOME_OUTCODE) }
        }
    }

    @Test
    fun `should NOT return location out code value`() {
        runUnitTest(robot) {
            When {
                getOutCodeValue(
                    RawLocationFactory.createRawLocation(
                        name = DataFactory.SOME_LOCATION_NAME,
                        type = RawLocation.Type.location,
                    )
                )
            }
            Then { checkOutCodeValue(null) }
        }
    }

    @Test
    fun `should return 1 as logged in status`() {
        runUnitTest(robot) {
            When { getLoggedInStatus(true) }
            Then { checkLoggedInStatus("1") }
        }
    }

    @Test
    fun `should return 0 as logged in status`() {
        runUnitTest(robot) {
            When { getLoggedInStatus(false) }
            Then { checkLoggedInStatus("0") }
        }
    }

    @Test
    fun `should return BRP page type`() {
        runUnitTest(robot) {
            When { getSrpPageType(ScreenType.BRP) }
            Then { checkSrpPageTypeResult(GAM_BRP_PAGE_TYPE) }
        }
    }

    @Test
    fun `should return SRP page type`() {
        runUnitTest(robot) {
            When { getSrpPageType(ScreenType.SRP) }
            Then { checkSrpPageTypeResult(GAM_SRP_PAGE_TYPE) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualSlotNameResult: String
        private lateinit var actualCategoryLevelsResult: Map<Int, CategoryDto>
        private lateinit var actualLocationValueResult: String
        private var actualPostCodeValueResult: String? = null
        private var actualOutCodeValueResult: String? = null
        private lateinit var actualLoggedInStatusResult: String
        private lateinit var actualSrpPageTypeResult: String

        private val json = Json { ignoreUnknownKeys = true }
        private val categoriesTreeCache = CategoriesTreeCache

        private val testSubject: GAMAdvertUtils = GAMAdvertUtils(categoriesTreeCache)

        override fun setup() {
            createFileInputStream(CATEGORIES_TREE_CACHE_RESOURCE_PATH)
                ?.runCatching { json.decodeFromStream<CategoryDto>(this).also { categoriesTreeCache.data = it } }
        }

        fun createAdvertSlotName(
            slotName: String,
            categoryId: String,
        ) {
            actualSlotNameResult = testSubject.createAdvertSlotName(slotName, categoryId)
        }

        fun getCategoryLevels(categoryId: String) {
            actualCategoryLevelsResult = testSubject.getCategoryLevels(categoryId)
        }

        fun getLocationValue(rawLocation: RawLocation) {
            actualLocationValueResult = testSubject.getLocationValue(rawLocation)
        }

        fun getPostCodeValue(rawLocation: RawLocation) {
            actualPostCodeValueResult = testSubject.getPostCodeValue(rawLocation)
        }

        fun getOutCodeValue(rawLocation: RawLocation) {
            actualOutCodeValueResult = testSubject.getOutCodeValue(rawLocation)
        }

        fun getLoggedInStatus(isAuthenticated: Boolean) {
            actualLoggedInStatusResult = testSubject.getLoggedInStatus(isAuthenticated)
        }

        fun getSrpPageType(screenType: ScreenType) {
            actualSrpPageTypeResult = testSubject.getSrpPageType(screenType)
        }

        fun checkSlotName(expected: String) {
            assertEquals(expected, actualSlotNameResult)
        }

        fun checkCategoryLevelIdName(
            level: Int,
            expected: String?,
        ) {
            assertEquals(expected, actualCategoryLevelsResult[level]?.idName)
        }

        fun checkLocationValue(expected: String) {
            assertEquals(expected, actualLocationValueResult)
        }

        fun checkPostCodeValue(expected: String?) {
            assertEquals(expected, actualPostCodeValueResult)
        }

        fun checkOutCodeValue(expected: String?) {
            assertEquals(expected, actualOutCodeValueResult)
        }

        fun checkLoggedInStatus(expected: String) {
            assertEquals(expected, actualLoggedInStatusResult)
        }

        fun checkSrpPageTypeResult(expected: String) {
            assertEquals(expected, actualSrpPageTypeResult)
        }
    }
}