package com.gumtree.mobile.features.savedSearches

import com.gumtree.mobile.api.capi.bodies.RawCapiAlertDestinationBody
import com.gumtree.mobile.api.capi.bodies.RawCapiAlertTypeBody
import com.gumtree.mobile.api.capi.bodies.RawCapiDestinationTypeBody
import com.gumtree.mobile.api.capi.bodies.RawCapiSavedSearchBody
import com.gumtree.mobile.api.capi.bodies.RawCapiSearchLinkBody
import com.gumtree.mobile.api.capi.bodies.RawCapiStatusTypeBody
import com.gumtree.mobile.api.capi.models.SavedSearchAlert
import com.gumtree.mobile.api.capi.models.SavedSearchDestination
import com.gumtree.mobile.api.capi.models.SavedSearchStatus
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_LOCATION_ID
import com.gumtree.mobile.utils.extensions.appendKeysAttributePrefix
import com.gumtree.mobile.utils.extensions.request.toQueryParams
import com.gumtree.mobile.utils.extensions.runCatchingWithLog
import com.gumtree.mobile.utils.extensions.urlEncodeValues
import org.jetbrains.annotations.VisibleForTesting

/**
 * Creator of RawCapiSavedSearch data instances when creating new saved searches
 */
class SavedSearchBodyCreator(private val rawLocationFetcher: RawLocationFetcher) {

    /**
     * Create RawCapiSavedSearch data instance to be passed as a body to the POST new saved search request
     * @param savedSearchRequest - saved search body payload
     * @return - the instance of RawCapiSavedSearch data
     */
    suspend fun create(savedSearchRequest: SavedSearchRequest): RawCapiSavedSearchBody {
        val params = swapLocationIdForZipcodeIfRequired(savedSearchRequest.params.toMutableMap())

        val capiParams = params.appendKeysAttributePrefix().urlEncodeValues()
        val savedSearchQueryParams = SavedSearchRequest(capiParams).toQueryParams()
        val rawCapiDestinationType = RawCapiDestinationTypeBody(SavedSearchDestination.EMAIL)
        val rawCapiStatusType = RawCapiStatusTypeBody(SavedSearchStatus.ACTIVE)
        val rawCapiAlertDestination = RawCapiAlertDestinationBody(
            destinationType = rawCapiDestinationType,
            statusType = rawCapiStatusType,
        )
        val rawCapiAlertType = RawCapiAlertTypeBody(SavedSearchAlert.SAVED_SEARCH)
        val url = "https://capi.gt.ecg.so:8080/api/ads?$savedSearchQueryParams" // Todo check the base url later if it matters
        val rawCapiSearchLink = RawCapiSearchLinkBody(
            rel = "saved-search",
            href = url,
        )
        return RawCapiSavedSearchBody(
            alertDestinations = arrayListOf(rawCapiAlertDestination),
            alertType = rawCapiAlertType,
            searchLink = rawCapiSearchLink,
            searchDescription = EMPTY_STRING,
        )
    }

    /**
     *  get locationId and type, convert into zipcode if needed and remove locationId
     */
    @VisibleForTesting
    suspend fun swapLocationIdForZipcodeIfRequired(params: MutableMap<String, String?>): Map<String, String?> {
        val locationId = params[ApiQueryParams.LOCATION_ID] ?: DEFAULT_LOCATION_ID
        val locationType = LocationType.fromString(params[ApiQueryParams.LOCATION_TYPE])
        if (locationType != LocationType.LOCATION) {
            val zipcode = runCatchingWithLog(
                errorPrefix = "SavedSearchBodyCreator swapLocationIdForZipcodeIfRequired locationId = $locationId, locationType = $locationType",
            ) {
                rawLocationFetcher.fetchByLocationIdAndType(locationId, locationType).name
            }.getOrNull()
            zipcode?.let {
                params.remove(ApiQueryParams.LOCATION_ID)
                params[ApiQueryParams.ZIPCODE] = it
            }
        }
        return params
    }
}
