package features.categoryLanding

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.features.categoryLanding.CarsCategoryLandingAnalyticsProvider
import com.gumtree.mobile.features.categoryLanding.CategoryLandingSimilarItemsMapper
import com.gumtree.mobile.features.categoryLanding.CategoryLandingSimilarListingGridSizes
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.ListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawLocationFactory
import tools.runUnitTest

class CategoryLandingSimilarItemsMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return category similar listing cards rows array with correct size`() {
        runUnitTest(robot) {
            Given { stubRawAds(6) }
            When { map() }
            Then { checkSimilarListingRowsArray(3) }
        }
    }

    @Test
    fun `should return only 3 rows with similar listings when data is more`() {
        runUnitTest(robot) {
            Given { stubRawAds(24) }
            When { map() }
            Then { checkSimilarListingRowsArray(3) }
        }
    }

    @Test
    fun `should return empty category similar listing rows array if raw data is empty`() {
        runUnitTest(robot) {
            Given { stubRawAds(0) }
            When { map() }
            Then { checkSimilarListingRowsArray(0) }
        }
    }

    @Test
    fun `should return similar listing cards array with correct row types at positions`() {
        runUnitTest(robot) {
            Given { stubRawAds(6) }
            When { map() }
            Then { checkSimilarListingRowTypeAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkSimilarListingRowTypeAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkSimilarListingRowTypeAtPosition(2, RowLayoutType.LISTING_ROW) }
        }
    }

    @Test
    fun `should return similar listing cards data`() {
        runUnitTest(robot) {
            Given { stubRawAds(6) }
            When { map() }
            Then { checkSimilarListingRowDataCountAtPosition(0, 2) }
            Then { checkSimilarListingRowDataCountAtPosition(1, 2) }
            Then { checkSimilarListingRowDataCountAtPosition(2, 2) }
            Then { checkSimilarListingRowDataTypeAtPosition(0, ListingCardDto::class.java) }
            Then { checkSimilarListingRowDataTypeAtPosition(1, ListingCardDto::class.java) }
            Then { checkSimilarListingRowDataTypeAtPosition(2, ListingCardDto::class.java) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualSimilarListingRowsResult: Array<RowLayout<UiItem>?>
        private lateinit var rawAds: RawCapiAdList

        private lateinit var testSubject: CategoryLandingSimilarItemsMapper

        override fun setup() {
            testSubject = CategoryLandingSimilarItemsMapper(
                CarsCategoryLandingAnalyticsProvider(
                    CommonAnalyticsProviderFactory.createInstance(),
                    RawLocationFactory.createRawLocationFetcher(),
                )
            )
        }

        fun stubRawAds(number: Int) {
            rawAds = RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
        }

        fun map() {
            actualSimilarListingRowsResult = testSubject.map(rawAds, CategoryLandingSimilarListingGridSizes())
        }

        fun checkSimilarListingRowsArray(expectedSize: Int) {
            assertEquals(expectedSize, actualSimilarListingRowsResult.size)
        }

        fun checkSimilarListingRowTypeAtPosition(
            rowPosition: Int,
            expected: RowLayoutType
        ) {
            assertEquals(expected, actualSimilarListingRowsResult[rowPosition]?.type)
        }

        fun checkSimilarListingRowDataCountAtPosition(
            rowPosition: Int,
            expected: Int
        ) {
            assertEquals(expected, actualSimilarListingRowsResult[rowPosition]?.data?.size)
        }

        fun checkSimilarListingRowDataTypeAtPosition(
            rowPosition: Int,
            expected: Class<ListingCardDto>
        ) {
            assertInstanceOf(expected, actualSimilarListingRowsResult[rowPosition]?.data?.first())
        }
    }
}