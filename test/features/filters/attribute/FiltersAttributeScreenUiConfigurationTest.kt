package features.filters.attribute

import com.gumtree.mobile.features.filters.attribute.FiltersAttributeScreenUiConfiguration
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.layoutsDataFactory.FiltersCategoryAttributesFactory

class FiltersAttributeScreenUiConfigurationTest {

    private val robot = Robot()

    @Test
    fun `should return filter attribute values screen title`() = runTest {
        runUnitTest(robot) {
            When {
                getFilterAttributeValuesScreenTitle(
                    FiltersCategoryAttributesFactory.createFilterAttribute(attributeLabel = "Seller type")
                )
            }
            Then { checkFilterAttributeValuesScreenTitle("Seller type") }
        }
    }

    @Test
    fun `should NOT return filter attribute values screen title`() = runTest {
        runUnitTest(robot) {
            When { getFilterAttributeValuesScreenTitle(null) }
            Then { checkFilterAttributeValuesScreenTitle(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualFilterAttributeValuesScreenTitleResult: String? = null
        private val testSubject = FiltersAttributeScreenUiConfiguration

        fun getFilterAttributeValuesScreenTitle(filterAttribute: FiltersCategoryAttributeDto?) {
            actualFilterAttributeValuesScreenTitleResult = testSubject.getFilterAttributeValuesScreenTitle(
                filterAttribute
            )
        }

        fun checkFilterAttributeValuesScreenTitle(expected: String?) {
            assertEquals(expected, actualFilterAttributeValuesScreenTitleResult)
        }
    }

}