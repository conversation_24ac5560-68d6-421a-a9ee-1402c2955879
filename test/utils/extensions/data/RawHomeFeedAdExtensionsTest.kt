package utils.extensions.data

import com.gumtree.mobile.api.homeFeed.models.RawHomeFeedAd
import com.gumtree.mobile.common.analytics.ANALYTICS_CLICK_LISTING_EVENT_NAME
import com.gumtree.mobile.common.analytics.ANALYTICS_FAVOURITE_LISTING_EVENT_NAME
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.screens.layoutsData.ListingCardDto
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.data.toListingCardDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.AnalyticsEventFactory
import tools.rawDataFactory.RawHomeFeedAdsFactory
import tools.runUnitTest

class RawHomeFeedAdExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should map raw home feed ad into listing card with expected data`() {
        runUnitTest(robot) {
            Given {
                stubRawHomeFeedAd(
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_AD_TITLE,
                    DataFactory.SOME_PRICE_NUMBER,
                    DataFactory.SOME_AD_IMAGE_URL,
                )
            }
            When { toListingCardDto() }
            Then { checkListingCardAdId(DataFactory.SOME_AD_ID) }
            Then { checkListingCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkListingCardDestinationRoute("${DestinationRoute.VIP.screenName}?${ApiQueryParams.AD_ID}=${DataFactory.SOME_AD_ID}") }
            Then { checkListingCardPrice(DataFactory.SOME_PRICE_FORMATTED_TEXT) }
            Then { checkListCardImages(DataFactory.SOME_BASE_IMAGE_URL) }
        }
    }

    @Test
    fun `should map raw home feed ad without price into listing card without price`() {
        runUnitTest(robot) {
            Given {
                stubRawHomeFeedAd(
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_AD_TITLE,
                    null,
                    DataFactory.ANOTHER_AD_IMAGE_URL,
                )
            }
            When { toListingCardDto() }
            Then { checkListingCardAdId(DataFactory.SOME_AD_ID) }
            Then { checkListingCardTitle(DataFactory.SOME_AD_TITLE) }
            Then { checkListingCardPrice(null) }
            Then { checkListCardImages(DataFactory.ANOTHER_AD_IMAGE_BASE_URL) }
        }
    }

    @Test
    fun `should map raw home feed ad into listing card with analytics data`() {
        runUnitTest(robot) {
            Given {
                stubRawHomeFeedAd(
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_AD_TITLE,
                    null,
                    DataFactory.ANOTHER_AD_IMAGE_URL,
                )
            }
            When {
                toListingCardDto(
                    analyticsEventData = AnalyticsEventFactory.createAnalyticsEvent(
                        eventName = ANALYTICS_CLICK_LISTING_EVENT_NAME,
                        eventParams = AnalyticsEventFactory.createAnalyticsEventParams(),
                    )
                )
            }
            Then {
                checkListingCardDestinationAnalyticsEvent(
                    expected = AnalyticsEventFactory.createAnalyticsEvent(
                        eventName = ANALYTICS_CLICK_LISTING_EVENT_NAME,
                        eventParams = AnalyticsEventFactory.createAnalyticsEventParams(),
                    )
                )
            }
            Then {
                checkListingCardFavouriteAnalyticsEvent(
                    expected = AnalyticsEventFactory.createAnalyticsEvent(
                        eventName = ANALYTICS_FAVOURITE_LISTING_EVENT_NAME,
                        eventParams = AnalyticsEventFactory.createAnalyticsEventParams(),
                    )
                )
            }
        }
    }

    private class Robot: BaseRobot {

        private lateinit var actualListingCardDtoResult: ListingCardDto
        private lateinit var rawHomeFeedAd: RawHomeFeedAd

        fun stubRawHomeFeedAd(
            adId: String,
            userId: String,
            title: String,
            price: String?,
            imageUrl: String,
        ) {
            rawHomeFeedAd = RawHomeFeedAdsFactory.createHomeFeedRawAd(adId, userId, title, price, imageUrl)
        }

        fun toListingCardDto(analyticsEventData: AnalyticsEventData? = null) {
            actualListingCardDtoResult = rawHomeFeedAd.toListingCardDto(analyticsEventData)
        }

        fun checkListingCardAdId(expected: String) {
            assertEquals(expected, actualListingCardDtoResult.adId)
        }

        fun checkListingCardTitle(expected: String) {
            assertEquals(expected, actualListingCardDtoResult.title)
        }

        fun checkListingCardDestinationRoute(expected: String) {
            assertEquals(expected, actualListingCardDtoResult.destination?.route)
        }

        fun checkListingCardDestinationAnalyticsEvent(expected: AnalyticsEventData?) {
            assertEquals(expected, actualListingCardDtoResult.destination?.analyticsEventData)
        }

        fun checkListingCardPrice(expected: String?) {
            assertEquals(expected, actualListingCardDtoResult.price)
        }

        fun checkListCardImages(expectedBaseUrl: String) {
            assertEquals(DataFactory.landscapeThumbnails(expectedBaseUrl), actualListingCardDtoResult.images)
        }

        fun checkListingCardFavouriteAnalyticsEvent(expected: AnalyticsEventData?) {
            assertEquals(expected, actualListingCardDtoResult.favouriteAnalyticsEventData)
        }
    }
}