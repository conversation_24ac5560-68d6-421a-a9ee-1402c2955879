package api.capi;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Type;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Converter;
import retrofit2.Retrofit;
import retrofit2.converter.simplexml.SimpleXmlConverterFactory;

import javax.annotation.Nullable;

/**
 * The ConverterFactory of the CAPI responses.
 * You can debug (add break points) here , if a CAPI request is failing to understand the actual reason
 */
public class CapiSimpleXmlConverterFactory extends Converter.Factory {

    private static final SimpleXmlConverterFactory sSimpleXmlConverterFactory = SimpleXmlConverterFactory.createNonStrict();

    @Nullable
    @Override
    public Converter<ResponseBody, ?> responseBodyConverter(
        Type type,
        Annotation[] annotations,
        Retrofit retrofit
    ) {
        if (!(type instanceof Class)) {
            return null;
        }
        return new ExceptionSuppressingSimpleXmlResponseConverter(
            sSimpleXmlConverterFactory.responseBodyConverter(type, annotations, retrofit)
        );
    }

    @Nullable
    @Override
    public Converter<?, RequestBody> requestBodyConverter(
        Type type,
        Annotation[] parameterAnnotations,
        Annotation[] methodAnnotations,
        Retrofit retrofit
    ) {
        if (!(type instanceof Class)) {
            return null;
        }
        return new ExceptionSuppressingSimpleXmlRequestConverter(
            sSimpleXmlConverterFactory.requestBodyConverter(type, parameterAnnotations, methodAnnotations, retrofit)
        );
    }

    class ExceptionSuppressingSimpleXmlRequestConverter<T> implements Converter<T, RequestBody> {
        private final Converter<T, RequestBody> innerConverter;

        public ExceptionSuppressingSimpleXmlRequestConverter(Converter<T, RequestBody> converter) {
            innerConverter = converter;
        }

        @Nullable
        @Override
        public RequestBody convert(T value) throws IOException {
            try {
                return innerConverter.convert(value);
            } catch (RuntimeException e) {
                return null;
            }
        }
    }

    class ExceptionSuppressingSimpleXmlResponseConverter<T> implements Converter<ResponseBody, T> {
        private final Converter<ResponseBody, T> innerConverter;

        public ExceptionSuppressingSimpleXmlResponseConverter(Converter<ResponseBody, T> converter) {
            innerConverter = converter;
        }

        @Nullable
        @Override
        public T convert(ResponseBody value) throws IOException {
            try {
                return innerConverter.convert(value);
            } catch (RuntimeException e) {
                return null;
            }
        }
    }
}
