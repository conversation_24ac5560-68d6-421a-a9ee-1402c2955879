openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-filters
  license:
    name: MIT
tags:
  - name: BFF-FILTERS
    description: Mobile Apps BFF Filters API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  parameters:
    AppVersionHeader:
      in: header
      name: App-Version
      description: The installed app version on the device
      required: false
      schema:
        type: string
        default: '10.1.20'
    OSVersionHeader:
      in: header
      name: OS-Version
      description: The device OS version
      required: false
      schema:
        type: string
        default: '14'
    AppDebugModeHeader:
      in: header
      name: App-Debug-Mode
      description: The app debug mode (true or false)
      required: false
      schema:
        type: string
        default: 'true'
    PlatformHeader:
      in: header
      name: Platform
      description: The device platform
      required: false
      schema:
        type: string
        default: 'ANDROID'
    DeviceTypeHeader:
      in: header
      name: Device
      description: The device type (phone vs tablet)
      required: false
      schema:
        type: string
        default: 'phone'
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    CategoryIdQuery:
      in: query
      name: categoryId
      description: Category ID
      required: false
      schema:
        type: integer
        format: int64
    LocationIdQuery:
      in: query
      name: locationId
      description: Location ID
      required: false
      schema:
        type: integer
        format: int64
    DistanceQuery:
      in: query
      name: distance
      description: distance
      required: false
      schema:
        type: string
        enum:
          - ZERO
          - ONE
          - THREE
          - FIVE
          - TEN
          - FIFTEEN
          - THIRTY
          - FIFTY
          - SEVENTY_FIVE
          - HUNDRED
          - HUNDRED_FIFTY
          - TWO_HUNDRED_FIFTY
          - NATIONWIDE
    MinPriceQuery:
      in: query
      name: minPrice
      description: minimum price for different verticals
      required: false
      schema:
        type: integer
        format: int64
    MaxPriceQuery:
      in: query
      name: maxPrice
      description: maximum price for different verticals
      required: false
      schema:
        type: integer
        format: int64
  schemas:
    ScreenResponse:
      type: object
      properties:
        title:
          type: string
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        landscapeData:
          $ref: '#/components/schemas/RowLayoutItems'
        searchParam:
          type: string
        analyticsParameters:
          type: object
          additionalProperties:
            type: string
      required:
        - portraitData
    RowLayoutItems:
      type: array
      items:
        $ref: '#/components/schemas/Row'
    Row:
      type: object
      properties:
        type:
          type: string
          enum:
            - TITLE_ROW
            - FILTER_LOCATION_ROW
            - DOUBLE_INPUT_ROW
            - DROPDOWN_ROW
            - DOUBLE_DROPDOWN_ROW
            - LINK_ROW
        bottomDivider:
          type: boolean
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/TitleCardDto'
              - $ref: '#/components/schemas/FilterLocationCardDto'
              - $ref: '#/components/schemas/DoubleInputCardDto'
              - $ref: '#/components/schemas/DropdownCardDto'
              - $ref: '#/components/schemas/DoubleDropdownCardDto'
              - $ref: '#/components/schemas/FilterLinkCardDto'
      required:
        - type
        - data
    TitleCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - TITLE_CARD
        text:
          type: string
        size:
          type: string
          enum:
            - LARGE
            - MEDIUM
            - SMALL
            - X_SMALL
            - XX_SMALL
        colour:
          type: string
          enum:
            - FOREGROUND_SUBDUED
            - FOREGROUND_DEFAULT
      required:
        - type
        - text
        - size
    FilterLocationCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - FILTER_LOCATION_CARD
        param:
          type: string
        value:
          type: string
        text:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - type
        - param
        - value
        - text
        - destination
    DoubleInputCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - DOUBLE_INPUT_CARD
        param1:
          type: string
        text1:
          type: string
        title1:
          type: string
        hint1:
          type: string
        inputType1:
          type: string
          enum:
            - NUMBER
            - CURRENCY
        param2:
          type: string
        text2:
          type: string
        title2:
          type: string
        hint2:
          type: string
        inputType2:
          type: string
          enum:
            - NUMBER
            - CURRENCY
        separatorText:
          type: string
      required:
        - type
        - param1
        - param2
    DropdownCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - DROPDOWN_CARD
        title:
          type: string
        param:
          type: string
        selectedOption:
          type: string
        options:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/HashMapDTO'
        changeAction:
          type: string
          enum:
            - REFRESH
      required:
        - type
        - title
        - param
        - selectedOption
        - options
    DoubleDropdownCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - DOUBLE_DROPDOWN_CARD
        title1:
          type: string
        param1:
          type: string
        selectedOption1:
          type: string
        options1:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/HashMapDTO'
        changeAction1:
          type: string
          enum:
            - REFRESH
        title2:
          type: string
        param2:
          type: string
        selectedOption2:
          type: string
        options2:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/HashMapDTO'
        changeAction2:
          type: string
          enum:
            - REFRESH
        separatorText:
          type: string
        resetToggle:
          type: object
          properties:
            text:
              type: string
          required:
            - text
      required:
        - type
        - title1
        - param1
        - selectedOption1
        - options1
        - title2
        - param2
        - selectedOption2
        - options2
    FilterLinkCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - FILTER_LINK_CARD
        text:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        params:
          type: array
          items:
            type: string
          example: ["vehicle_make", "vehicle_model"]
        subTitle:
          type: string
        changeAction:
          type: string
          enum:
            - REFRESH
            - RESET
        selectedValue:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/HashMapDTO'
      required:
        - type
        - text
        - destination
        - params
    HashMapDTO:
      type: object
      additionalProperties:
        type: string
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
    Error:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
      required:
        - code
        - message
    FilterAttributesItems:
      type: array
      items:
        $ref: '#/components/schemas/FilterAttribute'
    FilterAttribute:
      type: object
      properties:
        type:
          type: string
          enum:
            - FILTER_CATEGORY_ATTRIBUTE
        name:
          type: string
        label:
          type: string
        dataType:
          type: string
          enum:
            - BOOL
            - INTEGER
            - NUMBER
            - STRING
            - DATE
            - ENUM
            - CURRENCY
            - YEAR
        presentationType:
          type: string
          enum:
            - DOUBLE_INPUT
            - DOUBLE_DROPDOWN
            - LINK
        values:
          type: array
          items:
            $ref: '#/components/schemas/FilterAttributeValue'
        categoryIds:
          type: array
          items:
            type: integer
      required:
        - type
        - name
        - label
        - dataType
        - presentationType
        - values
        - categoryIds
    FilterAttributeValue:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
        dependentValue:
          type: string
      required:
        - label
        - value
paths:
  /filters/screen:
    get:
      summary: Get the Mobile apps filters screen (contains all UI items about the filters screen)
      parameters:
        - $ref: "#/components/parameters/AppVersionHeader"
        - $ref: "#/components/parameters/OSVersionHeader"
        - $ref: "#/components/parameters/AppDebugModeHeader"
        - $ref: "#/components/parameters/PlatformHeader"
        - $ref: "#/components/parameters/DeviceTypeHeader"
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/LocationIdQuery"
        - $ref: "#/components/parameters/CategoryIdQuery"
        - $ref: "#/components/parameters/DistanceQuery"
        - $ref: "#/components/parameters/MinPriceQuery"
        - $ref: "#/components/parameters/MaxPriceQuery"
      responses:
        '200':
          description: The mobile apps Filters screen UI ready to be rendered on the users device
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /filters/attributes:
    get:
      summary: Get the Mobile apps filters screen (contains all UI items about the filters screen)
      parameters:
        - $ref: "#/components/parameters/AppVersionHeader"
        - $ref: "#/components/parameters/OSVersionHeader"
        - $ref: "#/components/parameters/AppDebugModeHeader"
        - $ref: "#/components/parameters/PlatformHeader"
        - $ref: "#/components/parameters/DeviceTypeHeader"
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      responses:
        '200':
          description: The mobile apps Filters category attributes data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FilterAttributesItems"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"