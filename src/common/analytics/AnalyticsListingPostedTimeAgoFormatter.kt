package com.gumtree.mobile.common.analytics

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.utils.TimeAgoFormatter
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import java.time.format.DateTimeFormatter

private const val DAYS_SUFFIX = " days"

class AnalyticsListingPostedTimeAgoFormatter(override val dateFormat: DateTimeFormatter) : TimeAgoFormatter {

    override fun getTimeAgoLabel(
        olderDate: String?,
        newerDate: String,
    ): String {
        return when {
            olderDate.isNotNullOrEmpty() -> {
                val (_, days, _, _, _) = calculateWeeksDaysHoursMinutesSeconds(olderDate, newerDate)
                "$days$DAYS_SUFFIX"
            }
            else -> EMPTY_STRING
        }
    }

    override fun getTimeAgoLabel(
        olderTimestamp: Long?,
        newerTimestamp: Long,
    ): String {
        return when {
            olderTimestamp.isNotNull() -> {
                val (_, days, _, _, _) = calculateWeeksDaysHoursMinutesSeconds(olderTimestamp, newerTimestamp)
                "$days$DAYS_SUFFIX"
            }
            else -> EMPTY_STRING
        }
    }
}
