package com.gumtree.mobile.routes

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.settings.SettingsScreenUiConfiguration
import com.gumtree.mobile.features.vip.VipScreenUiConfiguration
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.utils.extensions.filterNotNullValues
import com.gumtree.mobile.utils.extensions.toStringQuery
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DestinationDto(
    @SerialName("route")
    val route: String,
    @SerialName("type")
    val type: Type = Type.SCREEN,
    @SerialName("analyticsEventData")
    val analyticsEventData: AnalyticsEventData? = null,
) {
    /**
     * The Destination types
     */
    enum class Type {
        /**
         * In app screen deeplink destination (open app screen)
         * the default Destination type
         */
        SCREEN,

        /**
         * Internal web link destination (open webview within the app)
         */
        INTERNAL_LINK,

        /**
         * External web link destination (open web browser/tab)
         */
        EXTERNAL_LINK,

        /**
         * App dialog + external web link destination (open web browser/tab)
         */
        EXTERNAL_LINK_DIALOG,

        /**
         * App should do some action (Http request etc.), it's up to the app of how to handle the action
         */
        ACTION,
    }

    companion object {
        fun buildFromUrl(
            url: String,
            type: Type = Type.EXTERNAL_LINK,
            analyticsEvent: AnalyticsEventData? = null,
        ): DestinationDto {
            return DestinationDto(
                route = createDestinationRouteValue(
                    url,
                    ZERO,
                    EMPTY_STRING,
                ),
                type = type,
                analyticsEventData = analyticsEvent,
            )
        }
    }
}

enum class DestinationRoute(val screenName: String) {
    FAVOURITES("favourites"),
    SAVED_SEARCHES(
        "favourites?tab=saved",
    ), // SavedSearches screen is within the FAVOURITES screen (just another tab there)
    VIP("vip"),
    SRP("srp"),
    FILTER("filter"),
    SET_FILTER("setFilter"),
    SET_CATEGORY("setCategory"),
    PRIVACY_SETTINGS("privacySettings"),
    WEBVIEW("webview"),
    TERMS_OF_USE(SettingsScreenUiConfiguration.TERMS_OF_USE_URL),
    PRIVACY_NOTICE(SettingsScreenUiConfiguration.PRIVACY_NOTICE_URL),
    LEGAL_INFORMATION(SettingsScreenUiConfiguration.LEGAL_INFORMATION_URL),
    HELP_AND_CONTACT(SettingsScreenUiConfiguration.HELP_AND_CONTACT_URL),
    HPI_GET_FULL_HISTORY(VipScreenUiConfiguration.HPI_GET_FULL_HISTORY_URL),
    SYI("syi"),
    SETTINGS("settings"),
    AUTHORISATION("authorisation"),
    LOGOUT("LOGOUT"),
    PROMOTE("promote"),
    CONVERSATION("conversation"),
    CATEGORY_LANDING("categoryLanding"),
    LOCATION_OVERVIEW("locationOverview"),
    LOCATION_SEARCH("locationSearch"),
    DELETE_ACCOUNT_CONFIRMATION("deleteAccountConfirmation"),
    ENQUIRE("enquire"),
    PHONE_VERIFY("phoneVerification"),
    SELLER_PROFILE("sellerProfile"),
    ;

    /**
     * Build a SCREEN DestinationDto instance from pairs of key:value params
     * @param params - the pairs of key:value
     */
    fun build(vararg params: Pair<String, String?>): DestinationDto {
        return DestinationDto(
            route = createDestinationRouteValue(
                screenName,
                params.size,
                params.toMap().filterNotNullValues().toStringQuery(),
            ),
        )
    }

    /**
     * Build a SCREEN DestinationDto instance from query params
     * @param queryParams - the query pairs hashmap
     * @param analyticsEvent - the analytics event when the UIItem is clicked/selected
     */
    fun build(
        queryParams: QueryParams,
        analyticsEvent: AnalyticsEventData? = null,
    ): DestinationDto {
        return DestinationDto(
            route = createDestinationRouteValue(
                screenName,
                queryParams.size,
                queryParams.filterNotNullValues().toStringQuery(),
            ),
            analyticsEventData = analyticsEvent,
        )
    }

    /**
     * Build EXTERNAL_LINK and EXTERNAL_LINK_DIALOG DestinationDto instance
     * @param displayDialog - if true the DestinationDto type will be EXTERNAL_LINK_DIALOG otherwise will be EXTERNAL_LINK
     * @param analyticsData - the destination analytics data
     */
    fun buildExternal(
        displayDialog: Boolean,
        analyticsData: AnalyticsEventData? = null,
    ): DestinationDto {
        return DestinationDto(
            route = createDestinationRouteValue(
                screenName,
                ZERO,
                EMPTY_STRING,
            ),
            type = if (displayDialog) DestinationDto.Type.EXTERNAL_LINK_DIALOG else DestinationDto.Type.EXTERNAL_LINK,
            analyticsEventData = analyticsData,
        )
    }

    /**
     * Build an ACTION DestinationDto instance
     */
    fun buildAction(analyticsEventData: AnalyticsEventData? = null): DestinationDto {
        return DestinationDto(
            route = screenName,
            type = DestinationDto.Type.ACTION,
            analyticsEventData = analyticsEventData,
        )
    }
}

private fun createDestinationRouteValue(
    screenName: String,
    paramsSize: Int,
    query: String,
): String {
    return when {
        paramsSize == ZERO -> screenName
        screenName.contains("?") -> "$screenName&$query"
        else -> "$screenName?$query"
    }
}
