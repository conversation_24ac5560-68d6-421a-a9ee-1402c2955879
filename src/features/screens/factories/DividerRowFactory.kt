package com.gumtree.mobile.features.screens.factories

import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.layoutsData.DividerCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem

class DividerRowFactory {

    fun createDividerRow(
        thickness: DividerCardDto.Thickness = DividerCardDto.Thickness.LIGHT,
        topMargin: Space = Space.SMALL,
        leftMargin: Space = Space.SMALL,
        rightMargin: Space = Space.SMALL,
        bottomMargin: Space = Space.SMALL,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.DIVIDER_ROW,
            data = listOf(
                DividerCardDto(data = DividerCardDto.DividerData(thickness = thickness)),
            ),
            style = Style(
                spacing = Style.Spacing(
                    margins = Style.Spacing.Dimension(
                        top = topMargin,
                        left = leftMargin,
                        right = rightMargin,
                        bottom = bottomMargin,
                    ),
                ),
            ),
        )
    }
}
