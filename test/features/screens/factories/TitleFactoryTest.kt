package features.screens.factories

import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleImageCardDto
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class TitleFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return left title card with expected data`() {
        runUnitTest(robot) {
            When { buildLeftTitleCard("Some text", TitleCardDto.Size.LARGE) }
            Then { checkTitleCardText("Some text") }
            Then { checkTitleCardSize(TitleCardDto.Size.LARGE) }
            Then { checkTitleCardIconLeft(null) }
            Then { checkTitleCardColour(null) }

            When { buildLeftTitleCard("Some text", TitleCardDto.Size.X_SMALL, TitleCardDto.Icon.POSTING_TIME, TitleCardDto.Colour.FOREGROUND_SUBDUED) }
            Then { checkTitleCardText("Some text") }
            Then { checkTitleCardSize(TitleCardDto.Size.X_SMALL) }
            Then { checkTitleCardIconLeft(TitleCardDto.Icon.POSTING_TIME) }
            Then { checkTitleCardColour(TitleCardDto.Colour.FOREGROUND_SUBDUED) }
        }
    }

    @Test
    fun `should return center title card with expected data`() {
        runUnitTest(robot) {
            When { buildCenterTitleCard("More text", TitleCardDto.Size.MEDIUM) }
            Then { checkTitleCardText("More text") }
            Then { checkTitleCardSize(TitleCardDto.Size.MEDIUM) }
            Then { checkTitleCardIconLeft(null) }
            Then { checkTitleCardColour(null) }

            When { buildCenterTitleCard("More text", TitleCardDto.Size.LARGE, TitleCardDto.Icon.EMAIL_VERIFIED, TitleCardDto.Colour.FOREGROUND_DEFAULT) }
            Then { checkTitleCardText("More text") }
            Then { checkTitleCardSize(TitleCardDto.Size.LARGE) }
            Then { checkTitleCardIconLeft(TitleCardDto.Icon.EMAIL_VERIFIED) }
            Then { checkTitleCardColour(TitleCardDto.Colour.FOREGROUND_DEFAULT) }
        }
    }

    @Test
    fun `should return right title card with expected data`() {
        runUnitTest(robot) {
            When { buildRightTitleCard("Title 123", TitleCardDto.Size.XX_SMALL) }
            Then { checkTitleCardText("Title 123") }
            Then { checkTitleCardSize(TitleCardDto.Size.XX_SMALL) }
            Then { checkTitleCardIconLeft(null) }
            Then { checkTitleCardColour(null) }

            When { buildRightTitleCard("Title", TitleCardDto.Size.X_SMALL, TitleCardDto.Icon.POSTING_TIME, TitleCardDto.Colour.FOREGROUND_SUBDUED) }
            Then { checkTitleCardText("Title") }
            Then { checkTitleCardSize(TitleCardDto.Size.X_SMALL) }
            Then { checkTitleCardIconLeft(TitleCardDto.Icon.POSTING_TIME) }
            Then { checkTitleCardColour(TitleCardDto.Colour.FOREGROUND_SUBDUED) }
        }
    }

    @Test
    fun `should return title image card with expected data`() {
        runUnitTest(robot) {
            When { buildTitleImageCard("Image Title text", TitleImageCardDto.Icon.HPI) }
            Then { checkTitleImageCardText("Image Title text") }
            Then { checkTitleImageCardIcon(TitleImageCardDto.Icon.HPI) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualTitleCardResult: TitleCardDto
        private lateinit var actualTitleImageCardResult: TitleImageCardDto

        private val testSubject = TitleFactory()

        fun buildLeftTitleCard(
            text: String,
            size: TitleCardDto.Size,
            iconLeft: TitleCardDto.Icon? = null,
            colour: TitleCardDto.Colour? = null,
        ) {
            actualTitleCardResult = testSubject.buildLeftTitleCard(text, size, iconLeft, colour) as TitleCardDto
        }

        fun buildCenterTitleCard(
            text: String,
            size: TitleCardDto.Size,
            iconLeft: TitleCardDto.Icon? = null,
            colour: TitleCardDto.Colour? = null,
        ) {
            actualTitleCardResult = testSubject.buildCenterTitleCard(text, size, iconLeft, colour) as TitleCardDto
        }

        fun buildRightTitleCard(
            text: String,
            size: TitleCardDto.Size,
            iconLeft: TitleCardDto.Icon? = null,
            colour: TitleCardDto.Colour? = null,
        ) {
            actualTitleCardResult = testSubject.buildRightTitleCard(text, size, iconLeft, colour) as TitleCardDto
        }

        fun buildTitleImageCard(
            text: String,
            icon: TitleImageCardDto.Icon
        ) {
            actualTitleImageCardResult = testSubject.buildTitleImageCard(text, icon) as TitleImageCardDto
        }

        fun checkTitleCardText(expected: String) {
            assertEquals(expected, actualTitleCardResult.text)
        }

        fun checkTitleCardSize(expected: TitleCardDto.Size) {
            assertEquals(expected, actualTitleCardResult.size)
        }

        fun checkTitleCardIconLeft(expected: TitleCardDto.Icon?) {
            assertEquals(expected, actualTitleCardResult.iconLeft)
        }

        fun checkTitleCardColour(expected: TitleCardDto.Colour?) {
            assertEquals(expected, actualTitleCardResult.colour)
        }

        fun checkTitleImageCardText(expected: String) {
            assertEquals(expected, actualTitleImageCardResult.text)
        }

        fun checkTitleImageCardIcon(expected: TitleImageCardDto.Icon) {
            assertEquals(expected, actualTitleImageCardResult.icon)
        }
    }
}