package com.gumtree.mobile.common

import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenRequestBody
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.features.notifications.NotificationsBodyCreator
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.extensions.getClientSemantics
import com.gumtree.mobile.utils.extensions.getOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.isAndroidAppVersionOrGreater
import com.gumtree.mobile.utils.extensions.isIOSAppVersionOrGreater
import com.gumtree.mobile.utils.extensions.isNotNull
import io.ktor.http.Headers

class PushNotificationsService(
    private val coreChatAuthApi: CoreChatAuthApi,
    private val conversationsApi: ConversationsApi,
    private val userServiceApi: UserService<PERSON>pi,
    private val notificationsBodyCreator: NotificationsBodyCreator = NotificationsBodyCreator(),
) : CoreChatAuthApi by coreChatAuthApi,
    ConversationsApi by conversationsApi,
    UserServiceApi by userServiceApi {

    suspend fun subscribeToPushNotifications(
        authorization: String,
        pushToken: String,
        userId: String,
    ) {
        subscribe(
            authorization,
            notificationsBodyCreator.create(pushToken, userId),
        )
    }

    suspend fun unsubscribeFromPushNotifications(
        authorization: String,
        pushToken: String,
        userId: String,
    ) {
        unsubscribe(
            authorization,
            pushToken,
            userId.toLong(),
        )
    }

    suspend fun hasExistingPushNotificationSubscription(
        authorization: String,
        pushToken: String,
        userId: String,
    ): Boolean {
        val userPushNotificationSubscriptions = getSubscriptions(authorization, userId.toLong())
        return userPushNotificationSubscriptions.firstOrNull { it.id == pushToken }.isNotNull()
    }

    suspend fun createAuthorization(
        callHeaders: Headers,
        userId: String,
    ): String {
        val userEmail = callHeaders.getOrThrowBadRequest(ApiHeaderParams.AUTHORISATION_USER_EMAIL)
        return generateCoreChatJwt(CoreChatStreamTokenRequestBody(userEmail, userId)).token
    }

    /**
     * Unfortunately the older app versions send accountId instead of the userId,
     * but the Raw push notifications subscribe and unsubscribe requests in Conversations API required userId,
     * so in the cases of older app clients, we need to user the accountId they send and fetch the userId
     * @param callHeaders - the original BFF call headers
     * @return - true if the app version is older and false if the app version is latest and sends real userId
     */
    fun shouldFetchRealUserId(callHeaders: Headers): Boolean {
        return with(callHeaders.getClientSemantics()) {
            when (platform) {
                ClientPlatform.ANDROID -> !isAndroidAppVersionOrGreater("10.1.23")
                else -> !isIOSAppVersionOrGreater("18.1.19")
            }
        }.apply {
            if (this) {
                println("Push notifications feature: fetching real userId by accountId")
            }
        }
    }
}
