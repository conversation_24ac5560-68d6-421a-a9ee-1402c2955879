package com.gumtree.mobile.features.srp

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.appendKeysAttributePrefix
import com.gumtree.mobile.utils.extensions.handleCapiMinAndMaxAttribute

/**
 * Provides/appends the common CAPI SRP request query params
 */
const val SORT_TYPE_DATE_DESCENDING = "DATE_DESCENDING"

/**
 * Only these CAPI SRP query params don't need "attr[]" wrapping,
 * every other CAPI SRP query param will be wrapped before sending
 */
val exclusionSearchParams = hashSetOf(
    CapiApiParams.AD_STATUS,
    CapiApiParams.SORT_TYPE,
    CapiApiParams.INCLUDE_TOP_ADS,
    CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH,
    CapiApiParams.PICTURE_REQUIRED,
    CapiApiParams.Q,
    CapiApiParams.CATEGORY_ID,
    CapiApiParams.LOCATION_ID,
    CapiApiParams.ZIPCODE,
    CapiApiParams.DISTANCE,
    CapiApiParams.PAGE,
    CapiApiParams.SIZE,

    ApiQueryParams.LOCATION_TYPE,
    CapiApiParams.IS_HIT_RECALL_ALL_CATE,
    CapiApiParams.IS_HIT_RANK_RELEVANT,
    CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE,

    RawCapiAd.ATTRIBUTE_JOBS_RECRUITER_TYPE,
    RawCapiAd.ATTRIBUTE_JOBS_HOURS,
    RawCapiAd.ATTRIBUTE_JOBS_LEVEL,
    RawCapiAd.ATTRIBUTE_JOBS_LANGUAGE,

)

class SrpCapiQueryParamsFactory {

    /**
     * CAPI API requires some specific query parameters, which aren't coming (shouldn't come) from the mobile apps,
     * that's why we need to append them before the actual CAPI request on the BFF level (these are AD_STATUS, INCLUDE_TOP_ADS, SEARCH_OPTIONS_EXACT_MATCH, PICTURE_REQUIRED).
     * Also we wrap some attributes with attr[] prefix due to CAPI specifics (see handleMinAndMaxAttribute() and appendKeysAttributePrefix() functions)
     * @param originalRequestQueryParams - the original request query params,
     * which we'll keep and append more params before we send the CAPI search request
     * @return - all CAPI QueryParams including the original QueryParams + the common CAPI SRP QueryParams
     */
    fun appendRequiredCapiSearchOptions(originalRequestQueryParams: QueryParams): QueryParams {
        return originalRequestQueryParams
            .handleCapiMinAndMaxAttribute(RawCapiAd.ATTRIBUTE_PROPERTY_NUMBER_BEDS)
            .appendKeysAttributePrefix()
            .toMutableMap()
            .also {
                it[CapiApiParams.AD_STATUS] = AdStatus.ACTIVE.toString()
                it[CapiApiParams.INCLUDE_TOP_ADS] = true.toString()
                it[CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH] = true.toString()
                it[CapiApiParams.PICTURE_REQUIRED] = false.toString()
                it.appendDefaultSortTypeIfNecessary()
            }
    }

    /**
     * If the original SRP(apps) request does NOT have sort type parameter,
     * we should append SORT_TYPE_DATE_DESCENDING as default value before the CAPI BE request
     */
    private fun MutableMap<String, String?>.appendDefaultSortTypeIfNecessary() {
        if (this[CapiApiParams.SORT_TYPE].isNullOrEmpty()) {
            if (this[CapiApiParams.IS_HIT_RANK_RELEVANT]?.toBoolean() == true) {
                this[CapiApiParams.SORT_TYPE] = SrpScreenUiConfiguration.RELEVANCE_DESCENDING_KEY
            } else {
                this[CapiApiParams.SORT_TYPE] = SORT_TYPE_DATE_DESCENDING
            }
        }
    }
}
