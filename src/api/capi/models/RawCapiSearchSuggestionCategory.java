package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;


@Root(name = "category", strict = false)
@Namespace(reference = Namespaces.CATEGORY, prefix = Namespaces.Prefix.CATEGORY)
public class RawCapiSearchSuggestionCategory {

    @Attribute(name = "id")
    public String categoryId;

    @Element(name = "localized-name")
    @Namespace(reference = Namespaces.CATEGORY)
    public String localizedName;
}
