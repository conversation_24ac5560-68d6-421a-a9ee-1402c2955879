package features.homeFeed

import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.categories.CategoryAnalyticsProvider
import com.gumtree.mobile.features.homeFeed.HomeFeedCarouselProvider
import com.gumtree.mobile.features.homeFeed.HomeFeedChipsFactory
import com.gumtree.mobile.features.homeFeed.HomeFeedTopBarProvider
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.factories.CategoryCardFactory
import com.gumtree.mobile.responses.TopBar
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class HomeFeedTopBarProviderTest {

    private val robot = Robot()

    @Test
    fun `should return non sticky top bar with chips for Variant A`() {
        runUnitTest(robot) {
            runUnitTest(robot) {
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.HUNDRED) }
                Given { stubCategoryExperimentVariant(Variant.A) }
                When { createHomeFeedTopBar() }
                Then { checkScrollingBehaviour(null) }
                Then { checkRowsSize(1) }
                Then { checkTopBarRowTypeAtPostion(0, RowLayoutType.CHIPS_ROW) }
            }
        }
    }

    @Test
    fun `should return non sticky top bar with carousel for Variant B`() {
        runUnitTest(robot) {
            runUnitTest(robot) {
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.HUNDRED) }
                Given { stubCategoryExperimentVariant(Variant.B) }
                When { createHomeFeedTopBar() }
                Then { checkScrollingBehaviour(null) }
                Then { checkRowsSize(1) }
                Then { checkTopBarRowTypeAtPostion(0, RowLayoutType.CAROUSEL_ROW) }
            }
        }
    }

    @Test
    fun `should return sticky top bar with carousel for Variant C`() {
        runUnitTest(robot) {
            runUnitTest(robot) {
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.HUNDRED) }
                Given { stubCategoryExperimentVariant(Variant.C) }
                When { createHomeFeedTopBar() }
                Then { checkScrollingBehaviour(ScrollingCollapseBehaviour.STICK_TO_TOP) }
                Then { checkRowsSize(1) }
                Then { checkTopBarRowTypeAtPostion(0, RowLayoutType.CAROUSEL_ROW) }
            }
        }
    }

    @Test
    fun `should return sticky top bar with carousel for Variant D`() {
        runUnitTest(robot) {
            runUnitTest(robot) {
                Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
                Given { stubLocationType(LocationType.LOCATION) }
                Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
                Given { stubDistance(Distance.HUNDRED) }
                Given { stubCategoryExperimentVariant(Variant.D) }
                When { createHomeFeedTopBar() }
                Then { checkScrollingBehaviour(ScrollingCollapseBehaviour.STICK_TO_TOP) }
                Then { checkRowsSize(1) }
                Then { checkTopBarRowTypeAtPostion(0, RowLayoutType.CHIPS_ROW) }
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualTopBar: TopBar
        private lateinit var locationId: String
        private lateinit var locationType: LocationType
        private lateinit var locationName: String
        private lateinit var distance: Distance
        private var categoryExperimentVariant: Variant? = null

        private val categoryAnalyticsProvider = CategoryAnalyticsProvider()
        private var testSubject = HomeFeedTopBarProvider(
            homeFeedChipsFactory = HomeFeedChipsFactory(analyticsProvider = categoryAnalyticsProvider),
            homeFeedCarouselProvider = HomeFeedCarouselProvider(factory = CategoryCardFactory(analyticsProvider = categoryAnalyticsProvider)),
        )

        fun stubLocationId(locationId: String) {
            this.locationId = locationId
        }

        fun stubLocationType(locationType: LocationType) {
            this.locationType = locationType
        }

        fun stubLocationName(locationName: String) {
            this.locationName = locationName
        }

        fun stubDistance(distance: Distance) {
            this.distance = distance
        }

        fun stubCategoryExperimentVariant(categoryExperimentVariant: Variant?) {
            this.categoryExperimentVariant = categoryExperimentVariant
        }

        fun createHomeFeedTopBar() {
            actualTopBar = testSubject.createHomeFeedTopBar(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
                categoriesExperimentVariant = categoryExperimentVariant,
            )
        }

        fun checkTopBarRowTypeAtPostion(position: Int, expected: RowLayoutType) {
            assertEquals(expected, actualTopBar.rows[position].type)
        }

        fun checkRowsSize(expected: Int) {
            assertEquals(expected, actualTopBar.rows.size)
        }

        fun checkScrollingBehaviour(expected: ScrollingCollapseBehaviour?) {
            assertEquals(expected, actualTopBar.scrollingCollapseBehaviour)
        }
    }

}