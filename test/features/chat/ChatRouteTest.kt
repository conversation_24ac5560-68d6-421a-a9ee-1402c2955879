package features.chat

import com.gumtree.mobile.features.chat.CHAT_SCREEN_PATH
import com.gumtree.mobile.features.chat.ChatRepository
import com.gumtree.mobile.features.chat.GENERATE_STREAM_TOKEN_PATH
import com.gumtree.mobile.features.chat.StreamTokenResponse
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.ID_PATH
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class ChatRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET chat screen request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getChatScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID, 1, 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { getChatScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID, 2, 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { getChatScreen(DataFactory.ANOTHER_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID, 11, 20) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET chat screen request with success when no page and size provided`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreenPortraitData() }
            And { stubScreenLandscapeData() }
            And { stubRepositoryReadScreenResponse() }
            When { getChatScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID) }
            Then { checkResponseStatus(HttpStatusCode.OK) }

            When { getChatScreen(DataFactory.ANOTHER_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID) }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET chat screen request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryReadScreenResponseError(UnauthorisedException()) }
            When { getChatScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID, 1, 20) }
        }
    }

    @Test
    fun `should complete GET chat screen request with error when conversation id path value is NOT in expected format`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            When { getChatScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID, 1, 20) }
        }
    }

    @Test
    fun `should complete POST generate stream token request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubRepositoryGenerateStreamTokenResponse(DataFactory.SOME_TOKEN) }
            When { generateStreamToken(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            Then { checkTokenResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete POST generate stream token request with error`() =
        runRouteTestForException(module, UnauthorisedException::class) {
            runUnitTest(robot, defaultHttpClient()) {
                Given { stubRepositoryGenerateStreamTokenResponse(UnauthorisedException()) }
                When { generateStreamToken(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_TOKEN) }
            }
        }


    private class Robot : BaseRouteRobot() {
        val repository: ChatRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse
        private lateinit var actualTokenResponse: HttpResponse
        private lateinit var screenPortraitData: List<RowLayout<UiItem>>
        private var screenLandscapeData: List<RowLayout<UiItem>>? = null

        fun stubScreenPortraitData() {
            screenPortraitData = emptyList()
        }

        fun stubScreenLandscapeData() {
            screenLandscapeData = null
        }

        fun stubRepositoryReadScreenResponse() {
            coEvery {
                repository.readScreen(any(), any(), any())
            } returns ScreenResponse(
                portraitData = screenPortraitData,
                landscapeData = screenLandscapeData,
            )
        }

        fun stubRepositoryReadScreenResponseError(error: Throwable) {
            coEvery { repository.readScreen(any(), any(), any()) } throws error
        }

        fun stubRepositoryGenerateStreamTokenResponse(token: String) {
            coEvery { repository.generateToken(any(), any(), any()) } returns StreamTokenResponse(token)
        }

        fun stubRepositoryGenerateStreamTokenResponse(error: Throwable) {
            coEvery { repository.generateToken(any(), any(), any()) } throws error
        }

        suspend fun getChatScreen(
            userEmail: String,
            conversationId: String,
            page: Int? = null,
            size: Int? = null,
        ) {
            actualResponse = client.get(CHAT_SCREEN_PATH.replace("{$ID_PATH}", conversationId)) {
                headers { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, userEmail) }
                parameter(ApiQueryParams.PAGE, page)
                parameter(ApiQueryParams.SIZE, size)
            }
        }

        suspend fun generateStreamToken(
            email: String,
            token: String,
        ) {
            actualTokenResponse = client.post(GENERATE_STREAM_TOKEN_PATH) {
                headers {
                    append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, email)
                    append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, token)
                }
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

        fun checkTokenResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualTokenResponse.status)
        }
    }
}