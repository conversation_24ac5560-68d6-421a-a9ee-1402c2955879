package features.srp

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.LargeListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.ListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.SrpNoResultsCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.srp.SrpAnalyticsProvider
import com.gumtree.mobile.features.srp.SrpListingMapper
import com.gumtree.mobile.features.srp.SrpListingsProvider
import com.gumtree.mobile.features.srp.SrpScreenUiConfiguration
import com.gumtree.mobile.features.srp.SrpTimeAgoFormatter
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.CategoryDtoFactory
import tools.rawDataFactory.RawCapiAdsFactory

class SrpListingsProviderTest {

    private val robot = Robot()

    @Test
    fun `should return no results row on page 0 with 1 item in the row layout data`() {
        tools.runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawAds(0) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            When { createNoResultsRow() }
            Then { checkNoResultsRowLayoutDataSize(1) }
            Then { checkNoResultsRowLayoutType(RowLayoutType.SEARCH_NO_RESULTS_ROW) }
            Then { checkNoResultsCardDtoTitleText(SrpScreenUiConfiguration.NO_RESULTS_TITLE_FOR_CATEGORY) }
            Then { checkNoResultsCardDtoTitleQuery(CategoryDefaults.FOR_SALE.text) }
            Then { checkNoResultsCardDtoSubtitle(SrpScreenUiConfiguration.NO_RESULTS_SUBTITLE) }
            Then { checkNoResultsCardDtoButtonTitle(SrpScreenUiConfiguration.NO_RESULTS_BUTTON_TITLE) }
        }
    }

    @Test
    fun `should NOT return no results row on page greater than 0`() {
        tools.runUnitTest(robot) {
            Given { stubPage(1) }
            Given { stubRawAds(5) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            When { createNoResultsRow() }
            Then { checkNoResultsRowIsNull() }

            Given { stubPage(2) }
            Given { stubRawAds(0) }
            When { createNoResultsRow() }
            Then { checkNoResultsRowIsNull() }
        }
    }

    @Test
    fun `should return listing rows on any page when there are listings for FOR_SALE category`() {
        tools.runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawAds(24) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(12) }
            Then { checkListingsLandscapeDataSize(null) }
            Then { checkAllListingsTypeInPortraitDataForDoubleGrid(ListingCardDto::class.java) }

            Given { stubPage(1) }
            Given { stubRawAds(24) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(12) }
            Then { checkListingsLandscapeDataSize(null) }
            Then { checkAllListingsTypeInPortraitDataForDoubleGrid(ListingCardDto::class.java) }

            Given { stubPage(2) }
            Given { stubRawAds(10) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(5) }
            Then { checkListingsLandscapeDataSize(null) }
            Then { checkAllListingsTypeInPortraitDataForDoubleGrid(ListingCardDto::class.java) }
        }
    }

    @Test
    fun `should return listing rows on any page when there are listings for CARS category`() {
        tools.runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawAds(24) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(24) }
            Then { checkListingsLandscapeDataSize(null) }
            Then { checkAllListingsTypeInPortraitDataForSingleGrid(LargeListingCardDto::class.java) }

            Given { stubPage(1) }
            Given { stubRawAds(24) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(24) }
            Then { checkListingsLandscapeDataSize(null) }
            Then { checkAllListingsTypeInPortraitDataForSingleGrid(LargeListingCardDto::class.java) }

            Given { stubPage(2) }
            Given { stubRawAds(10) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(10) }
            Then { checkListingsLandscapeDataSize(null) }
            Then { checkAllListingsTypeInPortraitDataForSingleGrid(LargeListingCardDto::class.java) }
        }
    }

    @Test
    fun `should return empty listing rows on any page when there are NO listings for any category`() {
        tools.runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawAds(0) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(0) }
            Then { checkListingsLandscapeDataSize(null) }

            Given { stubPage(1) }
            Given { stubRawAds(0) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(0) }
            Then { checkListingsLandscapeDataSize(null) }

            Given { stubPage(2) }
            Given { stubRawAds(0) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.JOBS.id)) }
            When { createSrpListingsRows() }
            Then { checkListingsPortraitDataSize(0) }
            Then { checkListingsLandscapeDataSize(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualNoResultsRowResult: RowLayout<UiItem>? = null
        private lateinit var actualListingRowResult: Pair<PortraitData, LandscapeData?>
        private lateinit var page: String
        private lateinit var rawAds: RawCapiAdList
        private lateinit var searchParams: QueryParams

        private val srpAnalyticsProvider: SrpAnalyticsProvider = mockk(relaxed = true)
        private val srpTimeAgoFormatter: SrpTimeAgoFormatter = mockk(relaxed = true)

        private val testSubject = SrpListingsProvider(SrpListingMapper(srpAnalyticsProvider, srpTimeAgoFormatter))

        fun stubPage(page: Int) {
            this.page = page.toString()
        }

        fun stubRawAds(number: Int) {
            rawAds = RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
        }

        fun stubSearchParams(params: QueryParams) {
            searchParams = params
        }

        fun createNoResultsRow() {
            actualNoResultsRowResult = testSubject.createNoResultsRow(
                page,
                rawAds.rawPaging.numFound,
                searchParams,
                CategoryDtoFactory.createCategoriesTree()
            )
        }

        fun createSrpListingsRows() {
            actualListingRowResult = testSubject.createSrpListingsRows(
                rawAds,
                searchParams,
            )
        }

        fun checkNoResultsRowLayoutType(
            expected: RowLayoutType
        ) {
            assertEquals(expected, actualNoResultsRowResult?.type)
        }

        fun checkNoResultsRowLayoutDataSize(expected: Int) {
            assertEquals(expected, actualNoResultsRowResult?.data?.size)
        }

        fun checkNoResultsCardDtoTitleText(expected: String) {
            assertEquals(expected, (actualNoResultsRowResult?.data?.get(0) as SrpNoResultsCardDto?)?.title?.text)
        }

        fun checkNoResultsCardDtoTitleQuery(expected: String) {
            assertEquals(expected, (actualNoResultsRowResult?.data?.get(0) as SrpNoResultsCardDto?)?.title?.query)
        }

        fun checkNoResultsCardDtoSubtitle(expected: String) {
            assertEquals(expected, (actualNoResultsRowResult?.data?.get(0) as SrpNoResultsCardDto?)?.subtitle)
        }

        fun checkNoResultsCardDtoButtonTitle(expected: String) {
            assertEquals(expected, (actualNoResultsRowResult?.data?.get(0) as SrpNoResultsCardDto?)?.buttonTitle)
        }

        fun checkNoResultsRowIsNull() {
            assertNull(actualNoResultsRowResult)
        }

        fun checkListingsPortraitDataSize(expected: Int) {
            assertEquals(expected, actualListingRowResult.first.size)
        }

        fun checkListingsLandscapeDataSize(expected: Int?) {
            assertEquals(expected, actualListingRowResult.second?.size)
        }

        fun <T>checkAllListingsTypeInPortraitDataForDoubleGrid(expected: Class<T>) {
            actualListingRowResult.first.forEach {
                assertEquals(expected, it.data[0].javaClass)
                assertEquals(expected, it.data[1].javaClass)
            }
        }

        fun <T>checkAllListingsTypeInPortraitDataForSingleGrid(expected: Class<T>) {
            actualListingRowResult.first.forEach {
                assertEquals(expected, it.data[0].javaClass)
            }
        }
    }

}