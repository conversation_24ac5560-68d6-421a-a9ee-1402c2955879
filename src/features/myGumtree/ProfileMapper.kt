package com.gumtree.mobile.features.myGumtree

import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.layoutsData.ProfileCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.utils.extensions.data.getUserNameAbbreviation

class ProfileMapper {

    fun map(rawPapiUserProfile: RawPapiUserProfile?): RowLayout<UiItem>? {
        return rawPapiUserProfile?.let {
            RowLayout(
                type = RowLayoutType.PROFILE_ROW,
                data = listOf(
                    ProfileCardDto(
                        sellerName = it.userData.displayName,
                        sellerAbbreviation = it.getUserNameAbbreviation(),
                        sellerAverageRating = it.rating?.averageRating,
                        sellerTotalRatings = it.rating?.ratingCounts?.total,
                    ),
                ),
                scrollingBehaviour = ScrollingCollapseBehaviour.COLLAPSE_AT_TOP,
            )
        }
    }
}
