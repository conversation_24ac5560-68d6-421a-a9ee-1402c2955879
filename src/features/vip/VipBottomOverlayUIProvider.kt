package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiContactMethod
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.ContactMethodCard
import com.gumtree.mobile.features.screens.layoutsData.DestinationActionDto
import com.gumtree.mobile.features.screens.layoutsData.PhoneActionDto
import com.gumtree.mobile.features.screens.layoutsData.StartConversationActionDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipButtonCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipButtonCardDto.IconType
import com.gumtree.mobile.responses.BottomOverlay
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.handleAuthRequestOrNull
import common.AdjustTrackingData
import io.ktor.http.Headers
import kotlinx.coroutines.withContext

class VipBottomOverlayUIProvider(
    private val capiConversationApi: CapiConversationApi,
    private val headersProvider: ApiHeadersProvider,
    private val vipAnalyticsProvider: VipAnalyticsProvider,
    private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider,
    private val dispatcherProvider: DispatcherProvider,
) {

    suspend fun createContactMethod(
        rawAdDetailsData: RawCapiAd,
        callHeaders: Headers,
        isUserOwnAd: Boolean,
    ): BottomOverlay? {
        val adStatus = AdStatus.fromString(rawAdDetailsData.status.value)
        return when {
            isUserOwnAd -> null
            adStatus == AdStatus.EXPIRED || adStatus == AdStatus.DELETED -> null
            else -> contactMethodOverlay(rawAdDetailsData, callHeaders)
        }
    }

    private suspend fun contactMethodOverlay(
        rawAdDetailsData: RawCapiAd,
        callHeaders: Headers,
    ): BottomOverlay? {
        val selectedContactMethods = rawAdDetailsData.contactMethods.filter { it.enabled.toBoolean() }
        val contactMethodUI = when (selectedContactMethods.size) {
            1 -> createSingleContactMethod(rawAdDetailsData, selectedContactMethods.first(), callHeaders)
            else -> createTwoContactMethods(
                rawAdDetailsData,
                callHeaders,
                selectedContactMethods,
            )
        }

        contactMethodUI?.let {
            return listOf(
                RowLayout(
                    type = RowLayoutType.CONTACT_METHOD_ROW,
                    data = contactMethodUI,
                ),
            )
        }

        return null
    }

    private suspend fun createSingleContactMethod(
        rawAdDetailsData: RawCapiAd,
        contactMethod: RawCapiContactMethod,
        callHeaders: Headers,
    ): List<UiItem>? {

        return when (contactMethod.name) {
            RawCapiContactMethod.PHONE -> listOf(createPhoneContactMethod(rawAdDetailsData, contactMethod.label))
            RawCapiContactMethod.EMAIL -> listOf(
                createEmailContactMethod(
                    rawAdDetailsData,
                    contactMethod.label,
                ),
            )
            RawCapiContactMethod.WEBLINK -> listOf(createLinkContactMethod(rawAdDetailsData, contactMethod.label))
            RawCapiContactMethod.CHAT -> listOf(createChatContactMethod(rawAdDetailsData, callHeaders))
            else -> null
        }
    }

    private suspend fun createTwoContactMethods(
        rawAdDetailsData: RawCapiAd,
        callHeaders: Headers,
        contactMethods: List<RawCapiContactMethod>,
    ): List<UiItem>? {
        val phoneContactMethod = contactMethods.firstOrNull { it.name == RawCapiContactMethod.PHONE }
        val emailContactMethod = contactMethods.firstOrNull { it.name == RawCapiContactMethod.EMAIL }
        val chatContactMethod = contactMethods.firstOrNull { it.name == RawCapiContactMethod.CHAT }
        val webLinkContactMethod = contactMethods.firstOrNull { it.name == RawCapiContactMethod.WEBLINK }

        return when {
            phoneContactMethod.isNotNull() && chatContactMethod.isNotNull() -> createPhoneAndChatContactMethod(
                rawAdDetailsData = rawAdDetailsData,
                callHeaders = callHeaders,
            )

            phoneContactMethod.isNotNull() && emailContactMethod.isNotNull() -> createPhoneAndEmailContactMethod(
                rawAdDetailsData = rawAdDetailsData,
                phoneContactMethod = phoneContactMethod,
                emailContactMethod = emailContactMethod,
            )

            phoneContactMethod.isNotNull() && webLinkContactMethod.isNotNull() -> createPhoneAndWebLinkContactMethod(
                rawAdDetailsData = rawAdDetailsData,
                phoneContactMethod = phoneContactMethod,
                webLinkContactMethod = webLinkContactMethod,
            )

            else -> null
        }
    }

    private fun createPhoneContactMethod(
        rawAdDetailsData: RawCapiAd,
        label: String,
        buttonType: VipButtonCardDto.Type = VipButtonCardDto.Type.PRIMARY,
        iconType: IconType? = VipButtonCardDto.IconType.PHONE,
    ): UiItem {
        return VipButtonCardDto(
            text = label,
            size = VipButtonCardDto.Size.LARGE,
            buttonType = buttonType,
            iconType = iconType,
            action = PhoneActionDto(
                number = null,
                analyticsEventData = vipAnalyticsProvider.getCallSellerEvent(),
                adjustTrackingData = vipAdjustTrackingDataProvider.getCallSellerEvent(rawAdDetailsData),
            ),
        )
    }

    private fun createEmailContactMethod(
        rawAdDetailsData: RawCapiAd,
        label: String,
        buttonType: VipButtonCardDto.Type = VipButtonCardDto.Type.PRIMARY,
    ): UiItem {
        val buttonLabel = labelForEmailCTA(label, rawAdDetailsData.category.id)
        return VipButtonCardDto(
            text = buttonLabel,
            size = VipButtonCardDto.Size.LARGE,
            buttonType = buttonType,
            iconType = null,
            action = DestinationActionDto(
                destination = DestinationRoute.ENQUIRE.build(
                    ApiQueryParams.ID to rawAdDetailsData.id,
                ),
            ),
        )
    }

    private fun createLinkContactMethod(
        rawAdDetailsData: RawCapiAd,
        label: String,
    ): UiItem {
        return VipButtonCardDto(
            text = label,
            size = VipButtonCardDto.Size.LARGE,
            buttonType = VipButtonCardDto.Type.PRIMARY,
            iconType = null,
            action = DestinationActionDto(
                destination = DestinationDto(
                    route = (rawAdDetailsData.links.firstOrNull { it.rel == "reply" }?.href) ?: EMPTY_STRING,
                    type = DestinationDto.Type.EXTERNAL_LINK,
                ),
                analyticsEventData = vipAnalyticsProvider.getContactLinkEvent(),
                adjustTrackingData = vipAdjustTrackingDataProvider.getContactLinkEvent(rawAdDetailsData),
            ),
        )
    }

    private fun createMessageContactMethod(
        rawAdDetailsData: RawCapiAd,
        isPhoneAvailable: Boolean?,
    ): UiItem {
        return startConversationContactMethod(
            rawAdDetailsData = rawAdDetailsData,
            cannedMessage = VipScreenUiConfiguration.I_AM_INTERESTED_CANNED_MESSAGE,
            phoneAvailable = isPhoneAvailable,
            analyticsEventData = vipAnalyticsProvider.getStartChatEvent(),
            adjustTrackingData = vipAdjustTrackingDataProvider.getStartChatEvent(rawAdDetailsData),
        )
    }

    private suspend fun createChatContactMethod(
        rawAdDetailsData: RawCapiAd,
        callHeaders: Headers,
    ): UiItem {
        val existingConversationWithSeller = existingConversationWithSeller(rawAdDetailsData, callHeaders)
        val isAdRelatedToServiceOrCommunity = CategoriesTreeCache.isServiceOrCommunity(rawAdDetailsData.category.id)
        return when {
            isAdRelatedToServiceOrCommunity -> createMessageContactMethod(rawAdDetailsData, null)

            existingConversationWithSeller.isNotNull() -> continueConversationContactMethod(
                rawAdDetailsData = rawAdDetailsData,
                conversationId = existingConversationWithSeller,
                phoneAvailable = null,
            )

            else -> startConversationContactMethod(
                rawAdDetailsData = rawAdDetailsData,
                cannedMessage = VipScreenUiConfiguration.START_CONVERSATION_CANNED_MESSAGE,
                phoneAvailable = null,
                analyticsEventData = vipAnalyticsProvider.getStartChatEvent(),
                adjustTrackingData = vipAdjustTrackingDataProvider.getStartChatEvent(rawAdDetailsData),
            )
        }
    }

    private fun createPhoneAndEmailContactMethod(
        rawAdDetailsData: RawCapiAd,
        phoneContactMethod: RawCapiContactMethod,
        emailContactMethod: RawCapiContactMethod,
    ): List<UiItem> {
        val isMotorAd = rawAdDetailsData.isMotorsAdvert

        return when {
            isMotorAd -> listOf(
                createPhoneContactMethod(
                    rawAdDetailsData,
                    phoneContactMethod.label,
                    VipButtonCardDto.Type.SECONDARY,
                    null,
                ),
                createEmailContactMethod(
                    rawAdDetailsData,
                    VipScreenUiConfiguration.CTA_MESSAGE,
                    VipButtonCardDto.Type.PRIMARY,
                ),
            )
            else -> listOf(
                createPhoneContactMethod(rawAdDetailsData, phoneContactMethod.label, VipButtonCardDto.Type.SECONDARY),
                createEmailContactMethod(rawAdDetailsData, emailContactMethod.label),
            )
        }
    }

    private fun createPhoneAndWebLinkContactMethod(
        rawAdDetailsData: RawCapiAd,
        phoneContactMethod: RawCapiContactMethod,
        webLinkContactMethod: RawCapiContactMethod,
    ): List<UiItem> {
        return listOf(
            createPhoneContactMethod(
                rawAdDetailsData = rawAdDetailsData,
                label = phoneContactMethod.label,
                buttonType = VipButtonCardDto.Type.SECONDARY,
            ),
            createLinkContactMethod(rawAdDetailsData, webLinkContactMethod.label),
        )
    }

    private suspend fun createPhoneAndChatContactMethod(
        rawAdDetailsData: RawCapiAd,
        callHeaders: Headers,
    ): List<UiItem> {
        val existingConversationWithSeller = existingConversationWithSeller(rawAdDetailsData, callHeaders)
        val isAdRelatedToServiceOrCommunity = CategoriesTreeCache.isServiceOrCommunity(rawAdDetailsData.category.id)
        val isPhoneAvailable = true
        return when {
            isAdRelatedToServiceOrCommunity -> listOf(
                createMessageContactMethod(rawAdDetailsData, isPhoneAvailable),
            )

            existingConversationWithSeller.isNotNull() -> listOf(
                continueConversationContactMethod(rawAdDetailsData, existingConversationWithSeller, isPhoneAvailable),
            )

            else -> listOf(
                startConversationContactMethod(
                    rawAdDetailsData = rawAdDetailsData,
                    cannedMessage = VipScreenUiConfiguration.START_CONVERSATION_CANNED_MESSAGE,
                    phoneAvailable = isPhoneAvailable,
                    analyticsEventData = vipAnalyticsProvider.getStartChatEvent(),
                    adjustTrackingData = vipAdjustTrackingDataProvider.getStartChatEvent(rawAdDetailsData),
                ),
            )
        }
    }

    private fun startConversationContactMethod(
        rawAdDetailsData: RawCapiAd,
        cannedMessage: String,
        phoneAvailable: Boolean?,
        analyticsEventData: AnalyticsEventData,
        adjustTrackingData: AdjustTrackingData,
    ): UiItem {
        return ContactMethodCard.StartConversationCardDto(
            cannedMessage = cannedMessage,
            buttonText = VipScreenUiConfiguration.START_CONVERSATION_BUTTON_TITLE,
            phoneAvailable = phoneAvailable,
            action = StartConversationActionDto(
                rawAdDetailsData.id,
                rawAdDetailsData.userId,
                analyticsEventData,
                adjustTrackingData,
            ),
            actionLeft = when (phoneAvailable) {
                true -> PhoneActionDto(
                    number = null,
                    analyticsEventData = vipAnalyticsProvider.getCallSellerEvent(),
                    adjustTrackingData = vipAdjustTrackingDataProvider.getCallSellerEvent(rawAdDetailsData),
                )
                else -> null
            },
        )
    }

    private fun continueConversationContactMethod(
        rawAdDetailsData: RawCapiAd,
        conversationId: String,
        phoneAvailable: Boolean?,
    ): UiItem {
        return ContactMethodCard.ContinueConversationCardDto(
            buttonText = VipScreenUiConfiguration.CONTINUE_CONVERSATION_MESSAGE,
            phoneAvailable = phoneAvailable,
            action = DestinationActionDto(
                destination = DestinationRoute.CONVERSATION.build(
                    CapiApiParams.CONVERSATION_ID to conversationId,
                ),
            ),
            actionLeft = when (phoneAvailable) {
                true -> PhoneActionDto(
                    number = null,
                    analyticsEventData = vipAnalyticsProvider.getCallSellerEvent(),
                    adjustTrackingData = vipAdjustTrackingDataProvider.getCallSellerEvent(rawAdDetailsData),
                )
                else -> null
            },
        )
    }

    private suspend fun existingConversationWithSeller(
        rawAdDetailsData: RawCapiAd,
        callHeaders: Headers,
    ): String? {
        return callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL]?.let { userEmail ->
            withContext(dispatcherProvider.io) {
                handleAuthRequestOrNull(callHeaders) {
                    capiConversationApi
                        .getAllConversations(headersProvider.createAuthorisedHeaders(callHeaders), userEmail, "0")
                        .rawConversations
                        .firstOrNull { it.adId == rawAdDetailsData.id && it.adReplierEmail == userEmail }
                        ?.conversationId
                }
            }
        }
    }

    private fun labelForEmailCTA(
        defaultLabel: String,
        categoryId: String,
    ): String {
        val isAdRelatedToService = CategoriesTreeCache.isServices(categoryId)
        return if (isAdRelatedToService) VipScreenUiConfiguration.CTA_REQUEST_QUOTE else defaultLabel
    }
}
