package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.OtherInfoCardDto
import com.gumtree.mobile.features.vip.v2.OtherInfoMapper
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class OtherInfoMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return Vip ad id row`() {
        runUnitTest(robot) {
            Given { stubRawAd(adId = DataFactory.SOME_AD_ID) }
            When { mapAdId() }
            Then { checkVipOtherInfoRowType(RowLayoutType.OTHER_INFO_ROW) }
            Then {
                checkVipOtherInfoRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.XX_SMALL,
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return Vip ad id row data`() {
        runUnitTest(robot) {
            Given { stubRawAd(adId = DataFactory.SOME_AD_ID) }
            When { mapAdId() }
            Then { checkVipOtherInfoRowDataCount(1) }
            Then { checkVipOtherInfoRowDataType(OtherInfoCardDto::class.java) }
            Then { checkVipOtherInfoCardText("${VipScreenUiConfiguration.AD_ID_PREXIF_TEXT} ${DataFactory.SOME_AD_ID}") }
        }
    }

    @Test
    fun `should return Vip vat row`() {
        runUnitTest(robot) {
            Given { stubRawAd(vat = DataFactory.SOME_VAT) }
            When { mapVAT() }
            Then { checkVipOtherInfoRowType(RowLayoutType.OTHER_INFO_ROW) }
            Then {
                checkVipOtherInfoRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.XX_SMALL,
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return Vip vat row data`() {
        runUnitTest(robot) {
            Given { stubRawAd(vat = DataFactory.SOME_VAT) }
            When { mapVAT() }
            Then { checkVipOtherInfoRowDataCount(1) }
            Then { checkVipOtherInfoRowDataType(OtherInfoCardDto::class.java) }
            Then { checkVipOtherInfoCardText("${VipScreenUiConfiguration.VAT_PREXIF_TEXT} ${DataFactory.SOME_VAT}") }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualVipOtherInfoCardDtoResult: RowLayout<UiItem>
        private lateinit var rawAd: RawCapiAd

        private lateinit var testSubject: OtherInfoMapper

        override fun setup() {
            testSubject = OtherInfoMapper()
        }

        fun stubRawAd(adId: String = "", vat: String = "") {
            rawAd = RawCapiAdsFactory.createRawCapiAd(adId = adId, vat = vat)
        }

        fun mapAdId() {
            actualVipOtherInfoCardDtoResult = testSubject.mapAdId(rawAd)
        }

        fun mapVAT() {
            actualVipOtherInfoCardDtoResult = testSubject.mapVAT(rawAd)!!
        }

        fun checkVipOtherInfoRowType(expected: RowLayoutType) {
            assertEquals(expected, actualVipOtherInfoCardDtoResult.type)
        }

        fun checkVipOtherInfoRowDataCount(expected: Int) {
            assertEquals(expected, actualVipOtherInfoCardDtoResult.data.size)
        }

        fun checkVipOtherInfoRowDataType(expected: Class<OtherInfoCardDto>) {
            assertInstanceOf(expected, actualVipOtherInfoCardDtoResult.data.first())
        }

        fun checkVipOtherInfoCardText(expected: String) {
            assertEquals(expected, (actualVipOtherInfoCardDtoResult.data.first() as OtherInfoCardDto).text)
        }

        fun checkVipOtherInfoRowStyle(expected: Style) {
            assertEquals(expected, actualVipOtherInfoCardDtoResult.style)
        }

    }
}
