package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("INPUT_CARD")
data class InputCardDto(
    @SerialName("param")
    val param: String,
    @SerialName("text")
    val text: String?,
    @SerialName("title")
    val title: String?,
    @SerialName("hint")
    val hint: String?,
    @SerialName("iconType")
    val iconType: IconType?,
    @SerialName("inputType")
    val inputType: InputType?,
) : UiItem {
    enum class IconType {
        SEARCH,
    }

    enum class InputType {
        NUMBER,
        CURRENCY,
    }
}

@Serializable
@SerialName("DOUBLE_INPUT_CARD")
data class DoubleInputCardDto(
    @SerialName("param1")
    val param1: String,
    @SerialName("text1")
    val text1: String?,
    @SerialName("title1")
    val title1: String?,
    @SerialName("hint1")
    val hint1: String?,
    @SerialName("inputType1")
    val inputType1: InputCardDto.InputType?,
    @SerialName("param2")
    val param2: String,
    @SerialName("text2")
    val text2: String?,
    @SerialName("title2")
    val title2: String?,
    @SerialName("hint2")
    val hint2: String?,
    @SerialName("inputType2")
    val inputType2: InputCardDto.InputType?,
    @SerialName("separatorText")
    val separatorText: String?,
    @SerialName("resetToggle")
    val resetToggle: ResetToggle?,
) : UiItem

@Serializable
data class ResetToggle(val text: String)
