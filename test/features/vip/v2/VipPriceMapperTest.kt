package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipPriceCardDto
import com.gumtree.mobile.features.vip.v2.VipPriceMapper
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class VipPriceMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return Vip price row`() {
        runUnitTest(robot) {
            Given { stubRawAd("12000") }
            When { map() }
            Then { checkVipPriceRowIsNotNull() }
            Then { checkVipPriceRowType(RowLayoutType.VIP_PRICE_ROW) }
            Then {
                checkVipPriceRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.XX_SMALL,
                        ),
                    )
                )
            }
        }
    }

    @Test
    fun `should NOT return Vip price row`() {
        runUnitTest(robot) {
            Given { stubRawAd("12bg45") }
            When { map() }
            Then { checkVipPriceRowIsNull() }
        }
    }

    @Test
    fun `should return Vip price data`() {
        runUnitTest(robot) {
            Given { stubRawAd("12000") }
            When { map() }
            Then { checkVipPriceRowDataCount(1) }
            Then { checkVipPriceRowDataType(VipPriceCardDto::class.java) }
            Then { checkVipPriceCardDtoText("£12,000") }
        }
    }

    private class Robot: BaseRobot {
        private var actualVipPriceCardDtoResult: RowLayout<UiItem>? = null
        private lateinit var rawAd: RawCapiAd

        private lateinit var tested: VipPriceMapper

        override fun setup() {
            tested = VipPriceMapper()
        }

        fun stubRawAd(price: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(price = price)
        }

        fun map() {
            actualVipPriceCardDtoResult = tested.map(rawAd)
        }

        fun checkVipPriceRowIsNotNull() {
            assertNotNull(actualVipPriceCardDtoResult)
        }

        fun checkVipPriceRowIsNull() {
            assertNull(actualVipPriceCardDtoResult)
        }

        fun checkVipPriceRowType(expected: RowLayoutType) {
            assertEquals(expected, actualVipPriceCardDtoResult?.type)
        }

        fun checkVipPriceRowDataCount(expected: Int) {
            assertEquals(expected, actualVipPriceCardDtoResult?.data?.size)
        }

        fun checkVipPriceRowDataType(expected: Class<VipPriceCardDto>) {
            assertInstanceOf(expected, actualVipPriceCardDtoResult?.data?.first())
        }

        fun checkVipPriceCardDtoText(expected: String) {
            assertEquals(expected, (actualVipPriceCardDtoResult?.data?.first() as VipPriceCardDto?)?.text)
        }

        fun checkVipPriceRowStyle(expected: Style) {
            assertEquals(expected, actualVipPriceCardDtoResult?.style)
        }

    }
}