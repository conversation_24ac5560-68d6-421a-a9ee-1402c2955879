package tools.layoutsDataFactory

import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.adverts.gam.GAMAdvertSize
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import tools.DataFactory


object GAMAdvertDtoFactory {
    /**
     * Generate a stubbed GAM Advert
     * @param androidUnitId - the GAM advert android unit id
     * @param iosUnitId - the GAM advert iOS unit id
     * @param slotName - the GAM advert slot name
     * @param displaySizes - the GAM advert sizes
     * @param attributes - the GAM advert custom attributes
     * @return - an instance of GAMAdvertDto ready to be used in tests
     */
    fun createGAMAdvert(
        androidUnitId: String = DataFactory.SOME_GAM_ADVERT_ANDROID_UNIT_ID,
        iosUnitId: String = DataFactory.SOME_GAM_ADVERT_IOS_UNIT_ID,
        slotName: String = DataFactory.SOME_GAM_ADVERT_SLOT_NAME,
        displaySizes: List<GAMAdvertSize> = listOf(GAMAdvertSize.Ad320x50),
        attributes: GAMAdvertAttributes = emptyMap(),
    ): GAMAdvertDto {
        return GAMAdvertDto(
            androidUnitId = androidUnitId,
            iosUnitId = iosUnitId,
            slotName = slotName,
            displaySize = displaySizes,
            attributes = attributes
        )
    }
}