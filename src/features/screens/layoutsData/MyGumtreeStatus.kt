package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.Serializable

@Serializable
data class MyGumtreeStatus(
    val text: String? = null,
    val toolTip: String? = null,
    val toolTipHyperlink: HyperlinkMeta? = null,
    val label: StatusLabel? = null,
)

@Serializable
data class WarningText(
    val text: String,
    val warningHyperlink: HyperlinkMeta? = null,
)

@Serializable
data class HyperlinkMeta(val substring: String, val hyperlink: String)

enum class StatusLabel { DELETED, REMOVED, EXPIRED }
