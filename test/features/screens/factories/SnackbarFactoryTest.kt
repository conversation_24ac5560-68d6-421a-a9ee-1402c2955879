package features.screens.factories

import com.gumtree.mobile.features.screens.factories.SnackbarFactory
import com.gumtree.mobile.features.screens.layoutsData.SnackbarActionData
import com.gumtree.mobile.features.screens.layoutsData.SnackbarCardDto
import com.gumtree.mobile.routes.ApiQueryParams
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Then
import tools.When
import tools.runUnitTest

class SnackbarFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return snackbar with message only`() {
        runUnitTest(robot) {
            When { buildSnackbarCardWithMessageOnly(DataFactory.SOME_MESSAGE_TEXT) }
            Then { checkSnackbarMessage(DataFactory.SOME_MESSAGE_TEXT) }
            Then { checkSnackbarAction(null) }
            Then { checkSnackbarActionText(null) }
            Then { checkSnackbarActionDataIsNull() }
        }
    }

    @Test
    fun `should return snackbar with message and action`() {
        runUnitTest(robot) {
            When {
                buildSnackbarCard(
                    DataFactory.SOME_MESSAGE_TEXT,
                    SnackbarCardDto.Action.UNBLOCK,
                    null
                )
            }
            Then { checkSnackbarMessage(DataFactory.SOME_MESSAGE_TEXT) }
            Then { checkSnackbarAction(SnackbarCardDto.Action.UNBLOCK) }
            Then { checkSnackbarActionText(SnackbarCardDto.Action.UNBLOCK.actionText) }
            Then { checkSnackbarActionDataIsNull() }
        }
    }

    @Test
    fun `should return snackbar with message, action and action data`() {
        runUnitTest(robot) {
            When {
                buildSnackbarCard(
                    DataFactory.SOME_MESSAGE_TEXT,
                    SnackbarCardDto.Action.UNBLOCK,
                    mapOf(ApiQueryParams.USER_ID to DataFactory.SOME_USER_ID)
                )
            }
            Then { checkSnackbarMessage(DataFactory.SOME_MESSAGE_TEXT) }
            Then { checkSnackbarAction(SnackbarCardDto.Action.UNBLOCK) }
            Then { checkSnackbarActionText(SnackbarCardDto.Action.UNBLOCK.actionText) }
            Then { checkSnackbarActionData(ApiQueryParams.USER_ID, DataFactory.SOME_USER_ID) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualSnackbarCardResult: SnackbarCardDto

        private val testSubject = SnackbarFactory()

        fun buildSnackbarCard(
            message: String,
            action: SnackbarCardDto.Action?,
            actionData: SnackbarActionData?
        ) {
            actualSnackbarCardResult = testSubject.buildSnackbarCard(message, action, actionData) as SnackbarCardDto
        }

        fun buildSnackbarCardWithMessageOnly(message: String) {
            actualSnackbarCardResult = testSubject.buildSnackbarCard(message) as SnackbarCardDto
        }

        fun checkSnackbarMessage(expected: String) {
            assertEquals(expected, actualSnackbarCardResult.message)
        }

        fun checkSnackbarAction(expected: SnackbarCardDto.Action?) {
            assertEquals(expected, actualSnackbarCardResult.action)
        }

        fun checkSnackbarActionText(expected: String?) {
            assertEquals(expected, actualSnackbarCardResult.actionText)
        }

        fun checkSnackbarActionData(
            key: String,
            expected: String?
        ) {
            assertEquals(expected, actualSnackbarCardResult.actionData?.get(key))
        }

        fun checkSnackbarActionDataIsNull() {
            assertNull(actualSnackbarCardResult.actionData)
        }
    }
}