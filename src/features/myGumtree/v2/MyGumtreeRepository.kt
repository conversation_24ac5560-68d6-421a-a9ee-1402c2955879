package com.gumtree.mobile.features.myGumtree.v2

import api.capi.models.RawConversationList
import com.gumtree.mobile.abTests.ClientExperiments.Companion.shouldUseCoreChatRepo
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.conversations.models.RawAllConversationsResponse
import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenRequestBody
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.api.reviews.bodies.RawCanCreateReviewRequestBody
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import com.gumtree.mobile.api.userService.models.RawUserServiceUserType
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.chat.ChatScreenUiConfiguration
import com.gumtree.mobile.features.chat.ChatService
import com.gumtree.mobile.features.myGumtree.MyGumtreeScreenUiConfiguration.SETTINGS_TITLE
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeScreenUiConfiguration.LESS_MESSAGE_COUNT
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeScreenUiConfiguration.SUM_LESS_MESSAGE_COUNT
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeScreenUiConfiguration.TWENTY_EIGHT_DAYS_MILLIS
import com.gumtree.mobile.features.myGumtree.v2.extensions.PENDING_VERIFY
import com.gumtree.mobile.features.reviews.ReviewsAccountsFetcher
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.layoutsData.ConversationData
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeFilter
import com.gumtree.mobile.features.screens.layoutsData.PotentialBuyerDto
import com.gumtree.mobile.features.screens.layoutsData.SettingsToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.getClientSemantics
import com.gumtree.mobile.utils.extensions.getSizeOrZero
import com.gumtree.mobile.utils.extensions.handleNoContentResponse
import com.gumtree.mobile.utils.extensions.isAndroidAppVersionOrGreater
import com.gumtree.mobile.utils.extensions.isIOSAppVersionOrGreater
import com.gumtree.mobile.utils.handleRequestOrNull
import io.ktor.http.Headers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import api.capi.models.RawConversation as CapiConversation
import com.gumtree.mobile.api.conversations.models.RawConversation as CoreConversation

const val MINIMUM_PHONE_VERIFY_VERSION_ANDROID = "10.1.26"
const val MINIMUM_PHONE_VERIFY_VERSION_IOS = "18.1.28"

interface MyGumtreeRepository {

    /**
     * Read MyGumtree screen
     * @param callHeaders - The request headers
     * @param filterType - The filter type (ALL or ACTIVE)
     * @return - the mapped data ready to be returned to the client
     */
    suspend fun readScreen(
        callHeaders: Headers,
        filterType: String?,
        email: String,
        page: String,
        size: String,
    ): ScreenResponse

    /**
     * Delete a user's Ad
     * @param callHeaders - The request headers
     * @param userName - the username (user email)
     * @param adId - The id of the Ad to be deleted
     */
    suspend fun delete(
        callHeaders: Headers,
        userName: String,
        adId: String,
    )

    suspend fun getPotentialBuyer(
        callHeaders: Headers,
        userName: String,
        adId: String,
    ): PotentialBuyerDto

    suspend fun repostFreeAd(
        callHeaders: Headers,
        adId: String,
    )

    suspend fun getAdPostedSuccessAnalyticsEvent(
        callHeaders: Headers,
        adId: String,
        postType: PostType,
        isPaidAd: Boolean,
    ): AnalyticsEventData
}

data class Mappers(
    val myGumtreeAdsMapper: MyGumtreeAdsMapper,
    val profileMapper: ProfileMapper,
    val myGumtreeTitleRowMapper: MyGumtreeTitleRowMapper,
    val potentialBuyerMapper: PotentialBuyerMapper,
)

class DefaultMyGumtreeRepository(
    private val papiHeadersProvider: ApiHeadersProvider,
    private val capiHeadersProvider: ApiHeadersProvider,
    private val myGumtreeService: MyGumtreeService,
    private val pageCalculator: PageCalculator,
    private val dispatcherProvider: DispatcherProvider,
    private val analyticsProvider: MyGumtreeAnalyticsProvider,
    private val mapper: Mappers,
    private val chatService: ChatService,
    private val reviewsAccountsFetcher: ReviewsAccountsFetcher,
) : MyGumtreeRepository {
    override suspend fun readScreen(
        callHeaders: Headers,
        filterType: String?,
        email: String,
        page: String,
        size: String,
    ): ScreenResponse {
        val capiAuthHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)
        val papiAuthHeaders = papiHeadersProvider.createAuthorisedHeaders(callHeaders)
        val clientSemantics = callHeaders.getClientSemantics()

        val myGumtreeFilter = MyGumtreeFilter.fromString(filterType)
        val rawCapiAds = handleRequestOrNull {
            withContext(dispatcherProvider.io) {
                myGumtreeService.getUserAds(
                    capiAuthHeaders,
                    email,
                    page,
                    size,
                    myGumtreeFilter.queryParams,
                )
            }
        }

        // for phone verification, Adverts need to be controlled to prevent old version from returning to the new state
        val isPhoneVerifySupported = isPhoneVerifyVersion(callHeaders)
        if (!isPhoneVerifySupported && rawCapiAds != null) {
            val filteredAds = rawCapiAds.rawAds?.filter { ad ->
                ad.status?.value != PENDING_VERIFY
            }
            rawCapiAds.rawAds = filteredAds
        }

        val adIds = rawCapiAds?.rawAds?.map { it.id }?.joinToString(",").orEmpty()
        val accountId = withContext(
            dispatcherProvider.io,
        ) { myGumtreeService.getUserDetails(email).accountIds.firstOrNull() }
        val rawAdStatListFuture = withContext(dispatcherProvider.io) {
            async {
                handleRequestOrNull {
                    myGumtreeService.getAdsStats(
                        capiAuthHeaders,
                        email,
                        adIds,
                    )
                }
            }
        }

        val rawUserProfileFuture = withContext(dispatcherProvider.io) {
            async {
                accountId?.let {
                    handleRequestOrNull { myGumtreeService.getBaseUserProfileByAccountId(papiAuthHeaders, it) }
                }
            }
        }

        val myGumtreeTitleRow = mapper.myGumtreeTitleRowMapper.map(myGumtreeFilter.text)

        val nextPage = pageCalculator.calculateNextPage(
            page = page,
            size = size,
            dataSize = rawCapiAds?.rawAds.getSizeOrZero(),
        )

        val rawAdStatList = rawAdStatListFuture.await()
        val rawUserProfile = rawUserProfileFuture.await()

        return ScreenResponse(
            portraitData = mapper.myGumtreeAdsMapper.map(rawCapiAds, rawAdStatList, clientSemantics),
            toolbar = ToolbarDto(
                actions = listOf(
                    SettingsToolbarActionDto(
                        SETTINGS_TITLE,
                        false,
                        DestinationRoute.SETTINGS.build(),
                    ),
                ),
                behavior = "inline",
            ),
            stickyBar = listOfNotNull(
                mapper.profileMapper.map(rawUserProfile),
                myGumtreeTitleRow,
            ),
            nextPage = nextPage,
        )
    }

    override suspend fun delete(
        callHeaders: Headers,
        userName: String,
        adId: String,
    ) {
        val authHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)

        withContext(dispatcherProvider.io) {
            myGumtreeService.deleteUserAd(authHeaders, userName, adId).handleNoContentResponse()
        }
    }

    override suspend fun getPotentialBuyer(
        callHeaders: Headers,
        userName: String,
        adId: String,
    ): PotentialBuyerDto {
        val conversationDatas: List<ConversationData>?
        val rawCapiAd: RawFlatAd? = withContext(dispatcherProvider.io) {
            handleRequestOrNull {
                chatService.getAdDetailsById(adId)
            }
        }

        val userDetails = myGumtreeService.getUserDetails(userName)
        if (!ChatScreenUiConfiguration.isPostingReviewAllowed(rawCapiAd?.categories) ||
            filterUserPro(userDetails)
        ) {
            conversationDatas = emptyList()
            return PotentialBuyerDto(conversationData = conversationDatas)
        }

        if (shouldUseCoreChatRepo(callHeaders)) {
            conversationDatas = getConversationsFromCoreChat(userName)
                .conversations
                .mapNotNull { mapCoreChatConversation(it, adId) }
        } else {
            conversationDatas = getConversationsFromCapi(callHeaders, userName)
                .rawConversations
                .mapNotNull { mapCapiConversation(it, callHeaders, userName, adId) }
        }

        val sortConversationData = conversationDatas.sortedByDescending { it.lastBuyerMessageTime }
        return PotentialBuyerDto(conversationData = sortConversationData)
    }

    override suspend fun repostFreeAd(
        callHeaders: Headers,
        adId: String,
    ) {
        val authHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)

        withContext(dispatcherProvider.io) {
            myGumtreeService.repostForFree(authHeaders, adId)
        }
    }

    override suspend fun getAdPostedSuccessAnalyticsEvent(
        callHeaders: Headers,
        adId: String,
        postType: PostType,
        isPaidAd: Boolean,
    ): AnalyticsEventData {
        val clientSemantics = callHeaders.getClientSemantics()
        val rawAdDetails = withContext(dispatcherProvider.io) {
            myGumtreeService.getAdDetailsById(adId)
        }
        return analyticsProvider.getAdPostedAnalyticsEvent(rawAdDetails, clientSemantics, postType, isPaidAd)
    }

    private fun isPhoneVerifyVersion(callHeaders: Headers): Boolean {
        return with(callHeaders.getClientSemantics()) {
            when (platform) {
                ClientPlatform.ANDROID -> isAndroidAppVersionOrGreater(MINIMUM_PHONE_VERIFY_VERSION_ANDROID)
                else -> isIOSAppVersionOrGreater(MINIMUM_PHONE_VERIFY_VERSION_IOS)
            }
        }
    }

    private suspend fun mapCoreChatConversation(
        conversation: CoreConversation,
        adId: String,
    ): ConversationData? {
        if (conversation.adId != adId) {
            return null
        }

        val conversationData = mapper.potentialBuyerMapper.mapWithoutBuyerName(conversation)
        val buyerDetail = withContext(dispatcherProvider.io) {
            myGumtreeService.getUserDetailsByUserId(conversation.buyerId)
        }

        if (filter(conversationData) || filterUserPro(buyerDetail) ||
            !checkConversationIfCanPostReview(conversation.sellerId, conversation.buyerId, conversation.adId)
        ) {
            return null
        }

        return conversationData.copy(buyerName = buyerDetail.firstName, buyerEmail = buyerDetail.userEmail)
    }

    private suspend fun mapCapiConversation(
        conversation: CapiConversation,
        callHeaders: Headers,
        userName: String,
        adId: String,
    ): ConversationData? {
        if (conversation.adId != adId) {
            return null
        }

        val authHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)
        val conversationDetails = myGumtreeService.getConversationDetails(authHeaders, userName, conversation.id)
        val conversationData = mapper.potentialBuyerMapper.map(conversationDetails)
        val buyerDetail = withContext(dispatcherProvider.io) {
            myGumtreeService.getUserDetails(conversationData.buyerEmail)
        }

        if (filter(conversationData) || filterUserPro(buyerDetail) ||
            !checkConversationIfCanPostReview(conversation.adOwnerId, conversation.adReplierId, conversation.adId)
        ) {
            return null
        }

        return conversationData
    }

    private suspend fun getConversationsFromCapi(
        callHeaders: Headers,
        userName: String,
    ): RawConversationList {

        val authHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)
        var pageOffset = 0
        val finalResult = RawConversationList().apply {
            rawConversations = mutableListOf()
        }

        var shouldContinue = true

        while (shouldContinue) {
            val pageResult = withContext(dispatcherProvider.io) {
                myGumtreeService.getAllConversations(
                    authHeaders,
                    userName,
                    pageOffset.toString(),
                )
            }

            pageResult.rawConversations?.let { conversations ->
                if (conversations.isNotEmpty()) {
                    (finalResult.rawConversations as MutableList).addAll(conversations)
                    pageOffset = finalResult.rawConversations.size
                }
            }

            shouldContinue = pageResult.rawConversations?.isNotEmpty() == true
        }

        return finalResult
    }

    private suspend fun getConversationsFromCoreChat(userName: String): RawAllConversationsResponse {
        val userDetails = withContext(dispatcherProvider.io) {
            myGumtreeService.getUserDetails(userName)
        }
        val jwtResponse = withContext(dispatcherProvider.io) {
            myGumtreeService.generateCoreChatJwt(
                CoreChatStreamTokenRequestBody(
                    userName,
                    userDetails.userId,
                ),
            )
        }
        val rawConversations = withContext(dispatcherProvider.io) {
            myGumtreeService.getAllConversations(
                jwt = jwtResponse.token,
                userId = userDetails.userId,
                offset = "0",
            )
        }
        return rawConversations
    }

    private suspend fun checkConversationIfCanPostReview(
        reviewerUserId: String,
        revieweeUserId: String,
        advertId: String,
    ): Boolean {
        val (reviewerId, revieweeId) = reviewsAccountsFetcher.fetchAccountIdByUserId(reviewerUserId, revieweeUserId)
        val canPostReview: Boolean? =
            withContext(dispatcherProvider.io) {
                handleRequestOrNull {
                    chatService.canPostReview(
                        RawCanCreateReviewRequestBody(
                            reviewer = RawCanCreateReviewRequestBody.RawReviewer(reviewerId = reviewerId),
                            revieweeId = revieweeId,
                            itemId = advertId.toInt(),
                        ),
                    ).value
                }
            }
        if (canPostReview == null) {
            return false
        }
        return canPostReview
    }

    private fun filter(conversationData: ConversationData): Boolean {
        return (
            conversationData.buyerSentMessageCnt < LESS_MESSAGE_COUNT ||
                conversationData.sellerSentMessageCnt < LESS_MESSAGE_COUNT ||
                conversationData.buyerSentMessageCnt + conversationData.sellerSentMessageCnt < SUM_LESS_MESSAGE_COUNT
            ) || (System.currentTimeMillis() - conversationData.lastBuyerMessageTime > TWENTY_EIGHT_DAYS_MILLIS)
    }

    private fun filterUserPro(userDetails: RawUserServiceUserDetails) = RawUserServiceUserType.PRO == userDetails.userType
}
