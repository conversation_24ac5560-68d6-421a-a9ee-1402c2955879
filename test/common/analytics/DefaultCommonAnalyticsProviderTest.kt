package common.analytics

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.api.similarItems.models.RawPapiSimilarAd
import com.gumtree.mobile.common.analytics.ANALYTICS_CLICK_LISTING_EVENT_NAME
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsListingPostedTimeAgoFormatter
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.common.analytics.AnalyticsPostingDurationFormatter
import com.gumtree.mobile.common.analytics.DefaultCommonAnalyticsProvider
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.utils.CategoryDefaults
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawCapiAttributeFactory
import tools.rawDataFactory.RawFlatAdsFactory
import tools.rawDataFactory.RawPapiSimilarAdFactory
import tools.runUnitTest

class DefaultCommonAnalyticsProviderTest {

    private val robot = Robot()

    @Test
    fun `should create correct sub category map`() {
        runUnitTest(robot) {
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            Given { stubCategoriesTree() }
            Given { stubCapiAdWithCategory("4") }
            When { getSubCategoryParams() }
            Then {
                checkParams(
                    mapOf(
                        "${DataFactory.SOME_PREFIX}_subcategory1" to "2",
                        "${DataFactory.SOME_PREFIX}_subcategory2" to "3",
                        "${DataFactory.SOME_PREFIX}_subcategory3" to "4",
                    ),
                )
            }
        }
    }

    @Test
    fun `should create correct sub category map with Flat ad`() {
        runUnitTest(robot) {
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            Given {
                stubRawAd(
                    RawFlatAdsFactory.createRawFlatAdWithCategories(
                        categories = listOf(
                            RawFlatAd.AdCategory(
                                id = CategoryDefaults.ALL_CATEGORIES.id,
                                name = CategoryDefaults.ALL_CATEGORIES.idName,
                                displayName = CategoryDefaults.ALL_CATEGORIES.seoDisplayName,
                                primary = false,
                            ),
                            RawFlatAd.AdCategory(
                                id = CategoryDefaults.MOTORS.id,
                                name = CategoryDefaults.MOTORS.idName,
                                displayName = CategoryDefaults.MOTORS.seoDisplayName,
                                primary = false,
                            ),
                            RawFlatAd.AdCategory(
                                id = CategoryDefaults.CARS.id,
                                name = CategoryDefaults.CARS.idName,
                                displayName = CategoryDefaults.CARS.seoDisplayName,
                                primary = false,
                            ),
                            RawFlatAd.AdCategory(
                                id = CategoryDefaults.DIY_AND_TRADE.id,
                                name = CategoryDefaults.DIY_AND_TRADE.idName,
                                displayName = CategoryDefaults.DIY_AND_TRADE.seoDisplayName,
                                primary = true,
                            ),
                        )
                    )
                )
            }
            When { getSubCategoryParamsWithFlatAd() }
            Then {
                checkParams(
                    mapOf(
                        "${DataFactory.SOME_PREFIX}_subcategory1" to CategoryDefaults.MOTORS.id,
                        "${DataFactory.SOME_PREFIX}_subcategory2" to CategoryDefaults.CARS.id,
                        "${DataFactory.SOME_PREFIX}_subcategory3" to CategoryDefaults.DIY_AND_TRADE.id,
                    ),
                )
            }
        }
    }

    @Test
    fun `should handle empty categories list with Flat ad`() {
        runUnitTest(robot) {
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            Given { stubRawAd(RawFlatAdsFactory.createRawFlatAdWithCategories(categories = emptyList())) }
            When { getSubCategoryParamsWithFlatAd() }
            Then { checkParams(emptyMap()) }
        }
    }

    @Test
    fun `should handle empty Category Tree with`() {
        runUnitTest(robot) {
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            Given { stubEmptyCategoriesTree() }
            Given { stubCapiAdWithCategory("2") }
            When { getSubCategoryParams() }
            Then { checkParams(emptyMap()) }
        }
    }

    @Test
    fun `should return empty map when using root category`() {
        runUnitTest(robot) {
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            Given { stubCategoriesTree() }
            Given { stubCapiAdWithCategory("1") }
            When { getSubCategoryParams() }
            Then { checkParams(emptyMap()) }
        }
    }

    @Test
    fun `should return empty map when using root category with Flat ad`() {
        runUnitTest(robot) {
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            Given {
                stubRawAd(
                    RawFlatAdsFactory.createRawFlatAdWithCategories(
                        categories = listOf(
                            RawFlatAd.AdCategory(
                                id = CategoryDefaults.ALL_CATEGORIES.id,
                                name = CategoryDefaults.ALL_CATEGORIES.idName,
                                displayName = CategoryDefaults.ALL_CATEGORIES.seoDisplayName,
                                primary = false,
                            ),
                        )
                    )
                )
            }
            When { getSubCategoryParamsWithFlatAd() }
            Then { checkParams(emptyMap()) }
        }
    }

    @Test
    fun `should return correct listing params for capi ad`() = runTest {
        runUnitTest(robot) {
            Given {
                stubTimeAgo("2 days")
                stubPostingForTimeAgo("1 year")
                stubRawCapiAd()
                stubPrefix(DataFactory.SOME_PREFIX)
            }
            When { getCommonListingParams() }
            Then {
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_id", DataFactory.SOME_AD_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_contact_number", "false")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_age", "2 days")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_contact_email", "false")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location", DataFactory.SOME_AD_LOCATION)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location_id", DataFactory.SOME_LOCATION_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_name", DataFactory.SOME_AD_TITLE)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_category", DataFactory.SOME_AD_CATEGORY_NAME)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_category_id", DataFactory.SOME_AD_CATEGORY_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_number_of_images", "1")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_number_of_words_in_description", "4")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_price", "23")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_promotions", EMPTY_STRING)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location_show_on_map", "true")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location_hierarchy", "L1")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_is_trade", "false")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_website_link", "false")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_breed", EMPTY_STRING)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_${AnalyticsParams.User.POSTING_FOR}", "1 year")

                checkScreenParamsSize(19)

                checkGetTimeAgoCalled()
            }
        }
    }

    @Test
    fun `should return correct listing params for Flat ad`() = runTest {
        runUnitTest(robot) {
            Given {
                stubTimeAgo("2 days")
                stubPostingForTimeAgo("10 years")
                stubRawAd()
                stubPrefix(DataFactory.SOME_PREFIX)
            }
            When { getCommonListingParamsWithFlatAd() }
            Then {
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_id", DataFactory.SOME_AD_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_contact_number", "false")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_age", "2 days")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_contact_email", "true")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location", DataFactory.SOME_AD_LOCATION)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location_id", DataFactory.SOME_LOCATION_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location_hierarchy", "L1")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_location_show_on_map", "true")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_name", DataFactory.SOME_AD_TITLE)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_category", DataFactory.SOME_AD_CATEGORY_NAME)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_category_id", DataFactory.SOME_AD_CATEGORY_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_subcategory1", DataFactory.SOME_AD_CATEGORY_ID)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_number_of_images", "2")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_number_of_words_in_description", "4")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_price", "£23")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_promotions", EMPTY_STRING)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_is_trade", "false")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_website_link", "true")
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_breed", EMPTY_STRING)
                checkScreenParamValue("${DataFactory.SOME_PREFIX}_${AnalyticsParams.User.POSTING_FOR}", "10 years")

                checkScreenParamsSize(20)
            }
        }
    }

    @Test
    fun `should return correct click listing analytics event for capi ad`() = runTest {
        runUnitTest(robot) {
            Given { stubTimeAgo("2 days") }
            Given { stubRawCapiAdWithPetBreed() }
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            When { getClickListingEventFromCapiAd() }
            Then {
                checkAnalyticsEvent(
                    AnalyticsEventData(
                        eventName = ANALYTICS_CLICK_LISTING_EVENT_NAME,
                        parameters = mapOf(
                            "${DataFactory.SOME_PREFIX}_id" to DataFactory.SOME_AD_ID,
                            "${DataFactory.SOME_PREFIX}_contact_number" to "false",
                            "${DataFactory.SOME_PREFIX}_age" to "2 days",
                            "${DataFactory.SOME_PREFIX}_contact_email" to "false",
                            "${DataFactory.SOME_PREFIX}_location" to DataFactory.SOME_AD_LOCATION,
                            "${DataFactory.SOME_PREFIX}_location_id" to DataFactory.SOME_LOCATION_ID,
                            "${DataFactory.SOME_PREFIX}_name" to DataFactory.SOME_AD_TITLE,
                            "${DataFactory.SOME_PREFIX}_category" to DataFactory.SOME_AD_CATEGORY_NAME,
                            "${DataFactory.SOME_PREFIX}_category_id" to DataFactory.SOME_AD_CATEGORY_ID,
                            "${DataFactory.SOME_PREFIX}_number_of_images" to "1",
                            "${DataFactory.SOME_PREFIX}_number_of_words_in_description" to "4",
                            "${DataFactory.SOME_PREFIX}_price" to DataFactory.SOME_AD_PRICE,
                            "${DataFactory.SOME_PREFIX}_promotions" to EMPTY_STRING,
                            "${DataFactory.SOME_PREFIX}_location_show_on_map" to "true",
                            "${DataFactory.SOME_PREFIX}_location_hierarchy" to "L1",
                            "${DataFactory.SOME_PREFIX}_is_trade" to "false",
                            "${DataFactory.SOME_PREFIX}_website_link" to "false",
                            "${DataFactory.SOME_PREFIX}_breed" to DataFactory.SOME_AD_PET_BREED,
                            "${DataFactory.SOME_PREFIX}_${AnalyticsParams.User.POSTING_FOR}" to EMPTY_STRING,
                        )
                    )
                )
            }
        }
    }

    @Test
    fun `should return correct click listing analytics event for papi similar ad`() = runTest {
        runUnitTest(robot) {
            Given { stubRawPapiSimilarAd() }
            Given { stubPrefix(DataFactory.SOME_PREFIX) }
            When { getClickListingEventFromPapiSimilarAd() }
            Then {
                checkAnalyticsEvent(
                    AnalyticsEventData(
                        eventName = ANALYTICS_CLICK_LISTING_EVENT_NAME,
                        parameters = mapOf(
                            "${DataFactory.SOME_PREFIX}_id" to DataFactory.SOME_AD_ID,
                            "${DataFactory.SOME_PREFIX}_name" to DataFactory.SOME_AD_TITLE,
                            "${DataFactory.SOME_PREFIX}_number_of_images" to ZERO.toString(),
                            "${DataFactory.SOME_PREFIX}_price" to DataFactory.SOME_AD_PRICE,
                        )
                    )
                )
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualAnalyticsParams: Map<String, String>
        private lateinit var actualAnalyticsEventData: AnalyticsEventData
        private lateinit var capiAd: RawCapiAd
        private lateinit var rawAd: RawFlatAd
        private lateinit var papiSimilarAd: RawPapiSimilarAd
        private lateinit var prefix: String

        private val timeAgoFormatter: AnalyticsListingPostedTimeAgoFormatter = mockk(relaxed = true)
        private val postingDurationFormatter: AnalyticsPostingDurationFormatter = mockk(relaxed = true)

        private val testSubject = CommonAnalyticsProviderFactory.createInstance(
            timeAgoFormatter,
            postingDurationFormatter,
        ) as DefaultCommonAnalyticsProvider

        fun stubPrefix(prefix: String) {
            this.prefix = prefix
        }

        fun stubEmptyCategoriesTree() {
            CategoriesTreeCache.data = null
        }

        fun stubCategoriesTree() {
            CategoriesTreeCache.data = CategoryDto(
                id = "1",
                text = "1",
                idName = "1",
                seoDisplayName = "1",
                parentId = null,
                children = listOf(
                    CategoryDto(
                        id = "2", text = "2", idName = "2", seoDisplayName = "2", parentId = "1", children = listOf(
                            CategoryDto(
                                id = "3",
                                text = "3",
                                idName = "3",
                                seoDisplayName = "3",
                                parentId = "2",
                                children = listOf(
                                    CategoryDto(
                                        id = "4",
                                        text = "4",
                                        idName = "4",
                                        seoDisplayName = "4",
                                        parentId = "3",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            )
        }

        fun stubCapiAdWithCategory(catId: String) {
            capiAd = RawCapiAdsFactory.createRawCapiAd(categoryId = catId)
        }

        fun stubRawCapiAd(rawCapiAd: RawCapiAd = RawCapiAdsFactory.createRawCapiAd()) {
            this.capiAd = rawCapiAd
        }

        fun stubRawAd(rawAdData: RawFlatAd = RawFlatAdsFactory.createRawFlatAd()) {
            rawAd = rawAdData
        }

        fun stubRawCapiAdWithPetBreed() {
            this.capiAd = RawCapiAdsFactory.createRawCapiAd(attributes = listOf(RawCapiAttributeFactory.createRawCapiAttribute(DataFactory.SOME_AD_PET_BREED, DataFactory.SOME_AD_PET_BREED)))
        }

        fun stubRawPapiSimilarAd(papiSimilarAd: RawPapiSimilarAd = RawPapiSimilarAdFactory.createRawSimilarListing()) {
            this.papiSimilarAd = papiSimilarAd
        }

        fun stubTimeAgo(value: String) {
            every { timeAgoFormatter.getTimeAgoLabel(olderDate = any()) } returns value
            every { timeAgoFormatter.getTimeAgoLabel(olderTimestamp = any()) } returns value
        }

        fun stubPostingForTimeAgo(value: String) {
            every { postingDurationFormatter.getTimeAgoLabel(olderDate = any()) } returns value
            every { postingDurationFormatter.getTimeAgoLabel(olderTimestamp = any()) } returns value
        }

        fun getCommonListingParams() {
            actualAnalyticsParams = testSubject.getCommonListingParams(prefix, capiAd)
        }

        fun getCommonListingParamsWithFlatAd() {
            actualAnalyticsParams = testSubject.getCommonListingParams(prefix, rawAd)
        }

        fun getClickListingEventFromCapiAd() {
            actualAnalyticsEventData = testSubject.getClickListingEvent(prefix, capiAd)
        }

        fun getClickListingEventFromPapiSimilarAd() {
            actualAnalyticsEventData = testSubject.getClickListingEvent(prefix, papiSimilarAd)
        }

        fun getSubCategoryParams() {
            actualAnalyticsParams = testSubject.getSubCategoryParams(prefix, capiAd)
        }

        fun getSubCategoryParamsWithFlatAd() {
            actualAnalyticsParams = testSubject.getSubCategoryParams(prefix, rawAd)
        }

        fun checkParams(expected: Map<String, String>) {
            assertEquals(expected, actualAnalyticsParams)
        }

        fun checkScreenParamValue(
            key: String,
            expected: String?,
        ) {
            assertEquals(expected, actualAnalyticsParams[key])
        }

        fun checkScreenParamsSize(expected: Int) {
            assertEquals(expected, actualAnalyticsParams.size)
        }

        fun checkGetTimeAgoCalled() {
            verify { timeAgoFormatter.getTimeAgoLabel(olderDate = any()) }
        }

        fun checkAnalyticsEvent(expected: AnalyticsEventData) {
            assertEquals(expected, actualAnalyticsEventData)
        }
    }
}
