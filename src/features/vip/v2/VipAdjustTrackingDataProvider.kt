package com.gumtree.mobile.features.vip.v2

import api.capi.models.RawCapiAd
import common.AdjustTrackingData
import org.jetbrains.annotations.VisibleForTesting

class VipAdjustTrackingDataProvider {

    fun getScreenTrackingData(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_SCREEN_EVENT,
        getDefaultParameters(rawAdDetails),
    )

    fun getShareTrackingData(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_SHARE_EVENT,
        getDefaultParameters(rawAdDetails),
    )

    fun getCallSellerEvent(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_PHONE_EVENT,
        getDefaultParameters(rawAdDetails),
    )

    fun getContactLinkEvent(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_CONTACT_LINK_EVENT,
        getDefaultParameters(rawAdDetails),
    )

    private fun getDefaultParameters(rawAdDetails: RawCapiAd) = mapOf(
        LISTING_ID_KEY to rawAdDetails.id.orEmpty(),
    )

    fun getStartChatEvent(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_START_CHAT,
        getDefaultParameters(rawAdDetails),
    )

    companion object {
        @VisibleForTesting
        const val TOKEN_SCREEN_EVENT = "bajcxm"

        @VisibleForTesting
        const val TOKEN_SHARE_EVENT = "xekkic"

        @VisibleForTesting
        const val TOKEN_PHONE_EVENT = "oo16qw"

        @VisibleForTesting
        const val TOKEN_CONTACT_LINK_EVENT = "jk22e9"

        @VisibleForTesting
        const val TOKEN_START_CHAT = "ir1bia"

        // keys
        @VisibleForTesting
        const val LISTING_ID_KEY = "product"
    }
}
