package com.gumtree.mobile.features.sellerProfile.v2

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.reviews.models.RawReview
import com.gumtree.mobile.utils.extensions.optionalPluralSuffix

/**
 * Defines the Seller profile screen UI components configurations
 */

object SellerProfileScreenUiConfiguration {
    const val SELLER_PROFILE_ACTIVE_STATUS_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss[.SSSSSS][.SSSSS][.SSSS]'Z'" // 2024-10-27T09:55:24.81711Z , 2024-11-11T11:35:35.704299Z
    const val SELLER_PROFILE_ACTIVE_STATUS_CURRENT_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSS'Z'" // this format is to get current date for active status comparison
    const val SELLER_PROFILE_REVIEW_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" // 2024-12-04T15:18:25.816Z
    const val SELLER_PROFILE_POSTING_SINCE_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'" // 2020-11-12T20:18:21Z
    const val FOR_SALE_TEXT = "for sale"
    const val POSTING_SINCE_TEXT = "Posting for"
    const val EMAIL_ADDRESS_VERIFIED_TEXT = "Email address verified"
    const val NO_LISTING_RESULTS_TITLE_TEXT = "Sorry, nothing here today"
    const val NO_REVIEWS_RESULTS_TITLE_TEXT = "No reviews yet"
    const val NO_RESULTS_SUBTITLE_TEXT = "Check back again soon"
    const val SELLER_HISTORY_TITLE = "Selling history"
    const val SELLER_HISTORY_TOTAL_ITEMS_TEXT = "Total items"
    const val SELLER_HISTORY_CATEGORIES_TEXT = "Categories"
    const val SELLER_TEXT = "Seller"
    const val BUYER_TEXT = "Buyer"
    const val ACTIVE_TEXT = "Active"
    const val TODAY_TEXT = "today"
    const val YESTERDAY_TEXT = "yesterday"
    const val AGO = "ago"
    const val TOP_FEEDBACK = "Top feedback"
    const val LOCATION_MIN_LISTINGS_NUMBER = 2 // to display a seller location we need last 2 listings with same location

    fun getScreenTitle(userDisplayName: String) = userDisplayName

    fun getSellerOtherAdsTitle(totalNumber: Int) = "$totalNumber ${"item".optionalPluralSuffix(
        totalNumber.toLong(),
    )} $FOR_SALE_TEXT"

    fun getYearText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"year".optionalPluralSuffix(timeAgo)}"

    fun getMonthText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"month".optionalPluralSuffix(timeAgo)}"

    fun getWeekText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"week".optionalPluralSuffix(timeAgo)}"

    fun getDayText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"day".optionalPluralSuffix(timeAgo)}"

    fun getHourText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"hour".optionalPluralSuffix(timeAgo)}"

    fun getReviewYearText(timeAgo: Long) = "$timeAgo ${"year".optionalPluralSuffix(timeAgo)} $AGO"

    fun getReviewMonthText(timeAgo: Long) = "$timeAgo ${"month".optionalPluralSuffix(timeAgo)} $AGO"

    fun getReviewWeekText(timeAgo: Long) = "$timeAgo ${"week".optionalPluralSuffix(timeAgo)} $AGO"

    fun getReviewDayText(timeAgo: Long) = "$timeAgo ${"day".optionalPluralSuffix(timeAgo)} $AGO"

    fun getReviewHourText(timeAgo: Long) = "$timeAgo ${"hour".optionalPluralSuffix(timeAgo)} $AGO"

    fun getActiveStatusWeekText(timeAgo: Long) = "$ACTIVE_TEXT $timeAgo ${"week".optionalPluralSuffix(timeAgo)} $AGO"

    fun getActiveStatusDayText(timeAgo: Long) = "$ACTIVE_TEXT $timeAgo ${"day".optionalPluralSuffix(timeAgo)} $AGO"

    fun getActiveStatusYesterdayText() = "$ACTIVE_TEXT $YESTERDAY_TEXT"

    fun getActiveStatusTodayText() = "$ACTIVE_TEXT $TODAY_TEXT"

    fun getReviewTitlePrefix(direction: String): String {
        return when (RawReview.Direction.fromString(direction)) {
            RawReview.Direction.B2S -> "$BUYER_TEXT:"
            RawReview.Direction.S2B -> "$SELLER_TEXT:"
            else -> EMPTY_STRING
        }
    }
}
