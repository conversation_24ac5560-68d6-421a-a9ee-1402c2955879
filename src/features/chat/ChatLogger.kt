package com.gumtree.mobile.features.chat

import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object ChatLogger {

    private val logger: Logger = LoggerFactory.getLogger("ChatLogger")

    fun logAuthenticateCapiToken(
        userEmail: String,
        token: String,
    ) {
        logger.info("authenticateCapiToken: usr lth: ${userEmail.length}, token lth: ${token.length}")
    }

    fun logEmptyCounterPartyDisplayName(
        counterPartyDisplayName: String,
        conversationId: String,
        counterPartyUserId: String,
        isConversationAboutMyPosting: Boolean,
        rawAdDetails: RawFlatAd?,
    ) {
        if (counterPartyDisplayName.isBlank()) {
            logger.warn(
                "counterPartyName is empty: conversationId: $conversationId - " +
                    "counterPartyUserId: $counterPartyUserId - " +
                    "isMyAd $isConversationAboutMyPosting - " +
                    "ad userId: ${rawAdDetails?.createdBy?.id?.toString().orEmpty()} - " +
                    "adId: ${rawAdDetails?.id?.toString().orEmpty()} - ",
            )
        }
    }
}
