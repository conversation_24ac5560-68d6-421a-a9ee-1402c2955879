package com.gumtree.mobile.api.conversations.models

import com.google.gson.annotations.SerializedName

data class RawAllConversationsResponse(
    @SerializedName("conversations")
    val conversations: List<RawConversation>,
)

data class RawConversation(
    @SerializedName("id")
    val id: String,
    @SerializedName("adId")
    val adId: String,
    @SerializedName("sellerId")
    val sellerId: String,
    @SerializedName("buyerId")
    val buyerId: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String,
    @SerializedName("unreadMessagesCount")
    val unreadMessagesCount: Int,
    @SerializedName("messages")
    val messages: List<RawMessage>,
)

data class RawMessage(
    @SerializedName("id")
    val id: String,
    @SerializedName("text")
    val text: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String,
    @SerializedName("userRole")
    val userRole: SendMessageUserRole,
    @SerializedName("userId")
    val userId: Long,
    @SerializedName("attachments")
    val attachments: List<RawAttachment>?,
)

enum class SendMessageUserRole {
    @SerializedName("BUYER")
    BUYER,

    @SerializedName("SELLER")
    SELLER,
}

data class RawAttachment(
    @SerializedName("url")
    val url: String,
    @SerializedName("type")
    val type: String,
)
