package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.SpecificationDoubleColumnItem
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipKeyInfoCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipSpecificationCardDto
import com.gumtree.mobile.features.vip.v2.VipScreenUiConfiguration
import com.gumtree.mobile.features.vip.v2.VipSpecificationFactory
import com.gumtree.mobile.utils.CategoryDefaults
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class VipSpecificationFactoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return VIP cars chips row`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsChipsRow() }
            Then { checkVipCarsChipsRowType(RowLayoutType.CHIPS_ROW) }
            Then { checkVipCarsChipsRowDataSize(5) }
            Then {
                checkVipCarsChipsRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.LARGE,
                        ),
                    )
                )
            }
        }
    }

    @Test
    fun `should return VIP cars overview section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(0, VipScreenUiConfiguration.SPECIFICATIONS_OVERVIEW_TITLE) }
            Then { checkSpecificationCardDtoIconType(0, VipSpecificationCardDto.IconType.OVERVIEW) }
            Then { checkSpecificationCardDtoDropdownItemsSize(0, 8) }
        }
    }

    @Test
    fun `should return VIP cars performance section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(1, VipScreenUiConfiguration.SPECIFICATIONS_PERFORMANCE_TITLE) }
            Then { checkSpecificationCardDtoIconType(1, VipSpecificationCardDto.IconType.PERFORMANCE) }
            Then { checkSpecificationCardDtoDropdownItemsSize(1, 6) }
        }
    }

    @Test
    fun `should return VIP cars running costs section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(2, VipScreenUiConfiguration.SPECIFICATIONS_RUNNING_COST_TITLE) }
            Then { checkSpecificationCardDtoIconType(2, VipSpecificationCardDto.IconType.RUNNING_COST) }
            Then { checkSpecificationCardDtoDropdownItemsSize(2, 7) }
        }
    }

    @Test
    fun `should return VIP cars safety and security section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(3, VipScreenUiConfiguration.SPECIFICATIONS_SAFETY_SECURITY_TITLE) }
            Then { checkSpecificationCardDtoIconType(3, VipSpecificationCardDto.IconType.SAFETY_AND_SECURITY) }
            Then { checkSpecificationCardDtoDropdownItemsSize(3, 12) }
        }
    }

    @Test
    fun `should return VIP cars driving convenience section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(4, VipScreenUiConfiguration.SPECIFICATIONS_DRIVING_CONVENIENCE_TITLE) }
            Then { checkSpecificationCardDtoIconType(4, VipSpecificationCardDto.IconType.DRIVING_CONVENIENCE) }
            Then { checkSpecificationCardDtoDropdownItemsSize(4, 14) }
        }
    }

    @Test
    fun `should return VIP cars interior section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(5, VipScreenUiConfiguration.SPECIFICATIONS_INTERIOR_TITLE) }
            Then { checkSpecificationCardDtoIconType(5, VipSpecificationCardDto.IconType.INTERIOR) }
            Then { checkSpecificationCardDtoDropdownItemsSize(5, 9) }
        }
    }

    @Test
    fun `should return VIP cars exterior section`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkSpecificationCardDtoLabel(6, VipScreenUiConfiguration.SPECIFICATIONS_EXTERIOR_TITLE) }
            Then { checkSpecificationCardDtoIconType(6, VipSpecificationCardDto.IconType.EXTERIOR) }
            Then { checkSpecificationCardDtoDropdownItemsSize(6, 9) }
        }
    }

    @Test
    fun `should return VIP cars specification row`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.CARS.id) }
            When { buildVipCarsSpecificationsRow() }
            Then { checkVipCarsSpecificationsRowType(RowLayoutType.VIP_SPECIFICATION_ROW) }
            Then { checkSpecificationsRowDataSize(7) }
        }
    }

    @Test
    fun `should return VIP Pets keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.PETS.id) }
            When { buildVipPetsKeyInfoRow() }
            Then { checkPetsKeyInfoItemsSize(2) }
            Then { checkVipPetsKeyInfoRowType(RowLayoutType.KEY_INFO_ROW) }
            Then { checkPetsKeyInfoLabelAtPosition(0, DataFactory.SOME_PET_LABEL) }
            Then { checkPetsKeyInfoValueAtPosition(0, DataFactory.SOME_PET_VALUE) }
            Then {
                checkPetsKeyInfoRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.LARGE,
                        ),
                    )
                )
            }
        }
    }

    @Test
    fun `should return VIP Mobile Phones keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAd(CategoryDefaults.MOBILE_PHONES.id) }
            When { buildVipMobilePhonesKeyInfoRow() }
            Then { checkMobilePhonesKeyInfoItemsSize(3) }
            Then {
                checkVipMobilePhonesKeyInfoRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.LARGE,
                        ),
                    )
                )
            }
            Then { checkVipMobilePhonesKeyInfoRowType(RowLayoutType.KEY_INFO_ROW) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(0, DataFactory.SOME_MOBILE_CONDITION_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(0, DataFactory.SOME_MOBILE_CONDITION_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(1, DataFactory.SOME_MOBILE_STORAGE_CAPACITY_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(1, DataFactory.SOME_MOBILE_STORAGE_CAPACITY_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(2, DataFactory.SOME_MOBILE_MOBILE_COLOUR_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(2, DataFactory.SOME_MOBILE_MOBILE_COLOUR_VALUE) }
        }
    }

    @Test
    fun `should return VIP Mobile Model SAMSUNG keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAdWithSamSungAttributes("4663") }
            When { buildVipMobilePhonesKeyInfoRow() }
            Then { checkMobilePhonesKeyInfoItemsSize(4) }
            Then { checkVipMobilePhonesKeyInfoRowType(RowLayoutType.KEY_INFO_ROW) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(0, DataFactory.SOME_MOBILE_MODEL_SAMSUNG_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(0, DataFactory.SOME_MOBILE_MODEL_SAMSUNG_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(1, DataFactory.SOME_MOBILE_CONDITION_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(1, DataFactory.SOME_MOBILE_CONDITION_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(2, DataFactory.SOME_MOBILE_STORAGE_CAPACITY_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(2, DataFactory.SOME_MOBILE_STORAGE_CAPACITY_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(3, DataFactory.SOME_MOBILE_MOBILE_COLOUR_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(3, DataFactory.SOME_MOBILE_MOBILE_COLOUR_VALUE) }
        }
    }

    @Test
    fun `should return VIP Mobile Model APPLE keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAdWithAppleAttributes("10205") }
            When { buildVipMobilePhonesKeyInfoRow() }
            Then { checkMobilePhonesKeyInfoItemsSize(4) }
            Then { checkVipMobilePhonesKeyInfoRowType(RowLayoutType.KEY_INFO_ROW) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(0, DataFactory.SOME_MOBILE_MODEL_APPLE_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(0, DataFactory.SOME_MOBILE_MODEL_APPLE_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(1, DataFactory.SOME_MOBILE_CONDITION_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(1, DataFactory.SOME_MOBILE_CONDITION_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(2, DataFactory.SOME_MOBILE_STORAGE_CAPACITY_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(2, DataFactory.SOME_MOBILE_STORAGE_CAPACITY_VALUE) }
            Then { checkMobilePhonesKeyInfoLabelAtPosition(3, DataFactory.SOME_MOBILE_MOBILE_COLOUR_LABEL) }
            Then { checkMobilePhonesKeyInfoValueAtPosition(3, DataFactory.SOME_MOBILE_MOBILE_COLOUR_VALUE) }
        }
    }

    @Test
    fun `should NOT return VIP Mobile Phones keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAdWithPropertyAttributes(CategoryDefaults.MOBILE_PHONES.id) }
            When { buildVipMobilePhonesKeyInfoRow() }
            Then { checkVipMapRowIsNull() }
        }
    }

    @Test
    fun `should return VIP Properties keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAdWithPropertyAttributes(CategoryDefaults.PROPERTIES.id) }
            When { buildVipPropertiesKeyInfoRow() }
            Then {
                checkPropertiesKeyInfoRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.LARGE,
                        ),
                    )
                )
            }
            Then { checkPropertiesKeyInfoItemsSize(4) }
            Then { checkVipPropertiesKeyInfoRowType(RowLayoutType.KEY_INFO_ROW) }
            Then { checkPropertiesKeyInfoLabelAtPosition(0, DataFactory.SOME_PROPERTY_LABEL) }
        }
    }

    @Test
    fun `should return VIP Jobs keyInfo row`() {
        runUnitTest(robot) {
            Given { stubRawAdWithJobsAttributes(CategoryDefaults.JOBS.id) }
            When { buildVipJobsKeyInfoRow() }
            Then {
                checkJobsKeyInfoRowStyle(
                    Style(
                        spacing = createSpacing(
                            leftMargin = Space.SMALL,
                            rightMargin = Space.SMALL,
                            bottomMargin = Space.LARGE,
                        ),
                    )
                )
            }
            Then { checkJobsKeyInfoItemsSize(2) }
            Then { checkVipJobsKeyInfoRowType(RowLayoutType.KEY_INFO_ROW) }
            Then { checkJobsKeyInfoLabelAtPosition(0, DataFactory.SOME_JOB_LABEL) }
            Then { checkJobsKeyInfoValueAtPosition(0, DataFactory.SOME_JOB_VALUE) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualCarChipsRowResult: RowLayout<UiItem>
        private lateinit var actualSpecificationsRowResult: RowLayout<UiItem>
        private lateinit var actualPetsKeyInfoRowResult: RowLayout<UiItem>
        private lateinit var actualPropertiesKeyInfoRowResult: RowLayout<UiItem>
        private lateinit var actualJobsKeyInfoRowResult: RowLayout<UiItem>
        private lateinit var rawAd: RawCapiAd
        private var actualMobilePhonesKeyInfoRowResult: RowLayout<UiItem>? = null

        private lateinit var testSubject: VipSpecificationFactory

        override fun setup() {
            testSubject = VipSpecificationFactory()
        }

        fun stubRawAd(categoryId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(categoryId, RawCapiAdsFactory.createRawCapiAttributesNamesArray())
        }

        fun stubRawAdWithPropertyAttributes(categoryId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(categoryId, RawCapiAdsFactory.createRawCapiAttributesNamesArrayForProperties())
        }

        fun stubRawAdWithJobsAttributes(categoryId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(categoryId, RawCapiAdsFactory.createRawCapiAttributesNamesArrayForJobs())
        }

        fun stubRawAdWithSamSungAttributes(categoryId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(categoryId, RawCapiAdsFactory.createRawCapiAttributesNamesArrayForSamSungProperties())
        }

        fun stubRawAdWithAppleAttributes(categoryId: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(categoryId, RawCapiAdsFactory.createRawCapiAttributesNamesArrayForAppleProperties())
        }

        fun buildVipCarsChipsRow() {
            actualCarChipsRowResult = testSubject.buildVipCarsChipsRow(rawAd)
        }

        fun buildVipCarsSpecificationsRow() {
            actualSpecificationsRowResult = testSubject.buildVipCarsSpecificationsRow(rawAd)
        }

        fun buildVipPetsKeyInfoRow() {
            actualPetsKeyInfoRowResult = testSubject.buildVipPetsKeyInfoRow(rawAd)
        }

        fun buildVipMobilePhonesKeyInfoRow() {
            actualMobilePhonesKeyInfoRowResult = testSubject.buildVipMobilePhonesKeyInfoRow(rawAd)
        }

        fun checkVipMapRowIsNull() {
            assertNull(actualMobilePhonesKeyInfoRowResult)
        }

        fun buildVipPropertiesKeyInfoRow() {
            actualPropertiesKeyInfoRowResult = testSubject.buildVipPropertiesKeyInfoRow(rawAd)
        }

        fun buildVipJobsKeyInfoRow() {
            actualJobsKeyInfoRowResult = testSubject.buildVipJobsKeyInfoRow(rawAd)
        }

        fun checkJobsKeyInfoRowStyle(expected: Style) {
            assertEquals(expected, actualJobsKeyInfoRowResult.style)
        }

        fun checkPropertiesKeyInfoRowStyle(expected: Style) {
            assertEquals(expected, actualPropertiesKeyInfoRowResult.style)
        }

        fun checkVipCarsSpecificationsRowType(expected: RowLayoutType) {
            assertEquals(expected, actualSpecificationsRowResult.type)
        }

        fun checkVipCarsChipsRowType(expected: RowLayoutType) {
            assertEquals(expected, actualCarChipsRowResult.type)
        }

        fun checkVipCarsChipsRowDataSize(expected: Int) {
            assertEquals(expected, actualCarChipsRowResult.data.size)
        }

        fun checkVipCarsChipsRowStyle(expected: Style) {
            assertEquals(expected, actualCarChipsRowResult.style)
        }

        fun checkSpecificationCardDtoLabel(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualSpecificationsRowResult.data[position] as VipSpecificationCardDto).label)
        }

        fun checkSpecificationCardDtoIconType(
            position: Int,
            expected: VipSpecificationCardDto.IconType
        ) {
            assertEquals(expected, (actualSpecificationsRowResult.data[position] as VipSpecificationCardDto).iconType)
        }

        fun checkSpecificationCardDtoDropdownItemsSize(
            position: Int,
            expected: Int
        ) {
            assertEquals(expected, (actualSpecificationsRowResult.data[position] as VipSpecificationCardDto).dropdownItems.size)
        }

        fun checkSpecificationsRowDataSize(expected: Int) {
            assertEquals(expected, actualSpecificationsRowResult.data.size)
        }

        fun checkVipPetsKeyInfoRowType(expected: RowLayoutType) {
            assertEquals(expected, actualPetsKeyInfoRowResult.type)
        }

        fun checkVipMobilePhonesKeyInfoRowType(expected: RowLayoutType) {
            assertEquals(expected, actualMobilePhonesKeyInfoRowResult?.type)
        }

        fun checkVipMobilePhonesKeyInfoRowStyle(expected: Style) {
            assertEquals(expected, actualMobilePhonesKeyInfoRowResult?.style)
        }

        fun checkVipPropertiesKeyInfoRowType(expected: RowLayoutType) {
            assertEquals(expected, actualPropertiesKeyInfoRowResult.type)
        }

        fun checkVipJobsKeyInfoRowType(expected: RowLayoutType) {
            assertEquals(expected, actualJobsKeyInfoRowResult.type)
        }

        fun checkPetsKeyInfoItemsSize(expected: Int) {
            assertEquals(expected, (actualPetsKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.size)
        }

        fun checkPetsKeyInfoRowStyle(expected: Style) {
            assertEquals(expected, actualPetsKeyInfoRowResult.style)
        }

        fun checkMobilePhonesKeyInfoItemsSize(expected: Int) {
            assertEquals(expected, (actualMobilePhonesKeyInfoRowResult?.data?.first() as VipKeyInfoCardDto?)?.infoItems?.size)
        }

        fun checkPropertiesKeyInfoItemsSize(expected: Int) {
            assertEquals(expected, (actualPropertiesKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.size)
        }

        fun checkJobsKeyInfoItemsSize(expected: Int) {
            assertEquals(expected, (actualJobsKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.size)
        }

        fun checkPetsKeyInfoLabelAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualPetsKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.get(position)?.label)
        }

        fun checkPetsKeyInfoValueAtPosition(
            position: Int,
            expected: String
        ) {
            val specItem: SpecificationDoubleColumnItem = (actualPetsKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.get(position) as SpecificationDoubleColumnItem
            assertEquals(expected, specItem.value)
        }

        fun checkMobilePhonesKeyInfoLabelAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualMobilePhonesKeyInfoRowResult?.data?.first() as VipKeyInfoCardDto?)?.infoItems?.get(position)?.label)
        }

        fun checkMobilePhonesKeyInfoValueAtPosition(
            position: Int,
            expected: String
        ) {
            val specItem: SpecificationDoubleColumnItem = (actualMobilePhonesKeyInfoRowResult?.data?.first() as VipKeyInfoCardDto?)?.infoItems?.get(position) as SpecificationDoubleColumnItem
            assertEquals(expected, specItem.value)
        }

        fun checkPropertiesKeyInfoLabelAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualPropertiesKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.get(position)?.label)
        }

        fun checkJobsKeyInfoLabelAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, (actualJobsKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.get(position)?.label)
        }

        fun checkJobsKeyInfoValueAtPosition(
            position: Int,
            expected: String
        ) {
            val specItem = (actualJobsKeyInfoRowResult.data.first() as VipKeyInfoCardDto?)?.infoItems?.get(position) as SpecificationDoubleColumnItem
            assertEquals(expected, specItem.value)
        }
    }
}