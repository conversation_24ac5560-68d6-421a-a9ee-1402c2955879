package com.gumtree.mobile.utils.extensions

import com.gumtree.mobile.features.locations.SimpleLocationDto
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_UK_LATITUDE
import com.gumtree.mobile.routes.DEFAULT_UK_LONGITUDE

/**
 * Convert SimpleLocationDto to map of query params related to Location
 * @param distance - the distance
 * @param includeAll - if set to true all location related query params shall be included in the map
 * @param useDefaults - if set to true latitude and longitude will fall back to default values if not provided
 * if set to false only locationId and locationType will be included in the map
 * @return - an instance of QueryParams with all Location related params
 */
fun SimpleLocationDto.toQueryParams(
    distance: String,
    includeAll: Boolean,
    useDefaults: Boolean,
): QueryParams {
    return when {
        includeAll -> mapOf(
            ApiQueryParams.LOCATION_ID to id,
            ApiQueryParams.LOCATION_TYPE to type.name,
            ApiQueryParams.LOCATION_NAME to name,
            ApiQueryParams.LATITUDE to if (useDefaults) getLatitudeOrDefault() else latitude?.toString(),
            ApiQueryParams.LONGITUDE to if (useDefaults) getLongitudeOrDefault() else longitude?.toString(),
            ApiQueryParams.DISTANCE to distance,
        )
        else -> {
            mapOf(
                ApiQueryParams.LOCATION_ID to id,
                ApiQueryParams.LOCATION_TYPE to type.name,
                ApiQueryParams.DISTANCE to distance,
            )
        }
    }
}

/**
 * Get SimpleLocationDto latitude value or default (DEFAULT_UK_LATITUDE) if latitude is NULL
 */
fun SimpleLocationDto.getLatitudeOrDefault(): String {
    return latitude?.toString() ?: DEFAULT_UK_LATITUDE
}

/**
 * Get SimpleLocationDto longitude value or default (DEFAULT_UK_LONGITUDE) if longitude is NULL
 */
fun SimpleLocationDto.getLongitudeOrDefault(): String {
    return longitude?.toString() ?: DEFAULT_UK_LONGITUDE
}
