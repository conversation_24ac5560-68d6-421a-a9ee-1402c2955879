package com.gumtree.mobile.routes

import com.gumtree.mobile.utils.annotations.NotCovered

/**
 * The application's headers parameters (the BFF headers)
 * The BFF uses also the standard "Authorization" header with Bearer tokens, which is not explicitly used in the code
 * The Authorization header is automatically read from the client call JWTPrincipal value (ApplicationCallExtensions)
 */

@NotCovered
object ApiHeaderParams {
    const val AUTHORISATION_USER_EMAIL = "Authorisation-User-Email"
    const val AUTHORISATION_USER_TOKEN = "Authorisation-User-Token"
    const val APP_VERSION = "App-Version"
    const val APP_DEBUG_MODE = "App-Debug-Mode"
    const val OS_VERSION = "OS-Version"
    const val PLATFORM = "Platform"
    const val EXPERIMENTS = "Experiments"
    const val DEVICE = "Device"
    const val UDID = "UDID"
    const val THREATMETRIX_SESSION = "Threat-Metrix-Session"
    const val CONSENT_STRING = "Authorisation-User-Consent"
    const val AUTHORISATION = "Authorization"
}
