package com.gumtree.mobile.features.settings

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider

const val ANALYTICS_USER_LOGOUT_EVENT_NAME = "user_logout"

class SettingsAnalyticsProvider(
    commonAnalyticsProvider: CommonAnalyticsProvider,
) : CommonAnalyticsProvider by commonAnalyticsProvider {

    fun getUserLogoutEvent(): AnalyticsEventData = AnalyticsEventData(
        eventName = ANALYTICS_USER_LOGOUT_EVENT_NAME,
    )
}
