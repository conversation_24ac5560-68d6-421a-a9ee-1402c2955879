package api.capi.models;


import java.util.ArrayList;
import java.util.List;


public class ExtendedInfo {

    private String mName;
    private String mPath;
    private boolean mPropertyValue;
    private boolean mPropertyDisplay;
    private boolean mPropertiesAreLoaded = false;
    private List<RawAdPropertyGroup> mRawAdPropertyGroups = new ArrayList<>();

    public ExtendedInfo(String name, String path) {
        mName = name;
        mPath = path;
    }


    public String getName() {
        return mName;
    }

    public String getPath() {
        return mPath;
    }

    public List<RawAdPropertyGroup> getRawAdPropertyGroups() {
        return mRawAdPropertyGroups;
    }

    public boolean arePropertiesLoaded() {
        return mPropertiesAreLoaded;
    }

    public void setPropertiesLoaded() {
        mPropertiesAreLoaded = true;
    }

    public boolean isPropertyValue() {
        return mPropertyValue;
    }

    public void setPropertyValue(boolean propertyValue) {
        mPropertyValue = propertyValue;
    }

    public boolean isPropertyDisplay() {
        return mPropertyDisplay;
    }

    public void setPropertyDisplay(boolean propertyDisplay) {
        mPropertyDisplay = propertyDisplay;
    }

}
