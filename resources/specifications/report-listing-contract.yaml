openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-report-listing
  license:
    name: MIT
tags:
  - name: BFF-Report-Listing
    description: Mobile Apps BFF Report Listing API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  securitySchemes:
    ApiKeyAuthUserEmail:
      type: apiKey
      description: Security API key to authorize requests with an user email
      in: header
      name: Authorisation-User-Email
    ApiKeyAuthUserToken:
      type: apiKey
      description: Security API key to authorize requests with an user token
      in: header
      name: Authorisation-User-Token
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
  schemas:
    Error:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
      required:
        - code
        - message
    ReportListingRequest:
      type: object
      properties:
        adId:
          type: string
        reason:
          type: string
          enum:
            - FRAUD
            - SPAM
            - DUPLICATE
            - WRONG_CATEGORY
            - POLICY
        comment:
          type: string
      required:
        - adId
        - reason
        - comment
security:
  - ApiKeyAuthUserEmail: []
    ApiKeyAuthUserToken: []
paths:
  /reportlisting:
    post:
      summary: Post report listing info
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      requestBody:
        description: reportListingRequest
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ReportListingRequest"
      responses:
        '200':
          description: The listing was reported successfully
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"