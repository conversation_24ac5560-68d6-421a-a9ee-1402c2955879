openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-push-notifications
  license:
    name: MIT
tags:
  - name: BFF-Push-notifications
    description: Mobile Apps BFF Push Notifications API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  securitySchemes:
    ApiKeyAuthUserEmail:
      type: apiKey
      description: Security API key to authorize requests with an user email
      in: header
      name: Authorisation-User-Email
    ApiKeyAuthUserToken:
      type: apiKey
      description: Security API key to authorize requests with an user token
      in: header
      name: Authorisation-User-Token
    ApiKeyAuthorization:
      type: apiKey
      description: Security API key to authorize requests with an Authorization (Bearer token)
      in: header
      name: Authorization
  parameters:
    AppVersionHeader:
      in: header
      name: App-Version
      description: The installed app version on the device
      required: false
      schema:
        type: string
        default: '10.1.20'
    OSVersionHeader:
      in: header
      name: OS-Version
      description: The device OS version
      required: false
      schema:
        type: string
        default: '14'
    AppDebugModeHeader:
      in: header
      name: App-Debug-Mode
      description: The app debug mode (true or false)
      required: false
      schema:
        type: string
        default: 'true'
    PlatformHeader:
      in: header
      name: Platform
      description: The device platform
      required: false
      schema:
        type: string
        default: 'ANDROID'
    DeviceTypeHeader:
      in: header
      name: Device
      description: The device type (phone vs tablet)
      required: false
      schema:
        type: string
        default: 'phone'
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
  schemas:
    PushRequest:
      type: object
      properties:
        userId:
          type: string
        pushToken:
          type: string
        topics:
          type: array
          items:
            type: string
            enum:
              - CHATMESSAGE
              - SEARCHALERTS
      required:
        - userId
        - pushToken
        - topics
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
security:
  - ApiKeyAuthUserEmail: [ ]
    ApiKeyAuthUserToken: [ ]
    ApiKeyAuthorization: [ ]
paths:
  /push-notifications:
    post:
      summary: Subscribe OR UnSubscribe the user to or from some push notification types (DEPRECATED use /subscribe && /unsubscribe)
      deprecated: true
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PushRequest"
      responses:
        '204':
          description: Successful subscribe to push notification types
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '404':
          description: Not found error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /push-notifications/subscribe:
    post:
      summary: Subscribe the user to push notifications
      parameters:
        - $ref: '#/components/parameters/AppVersionHeader'
        - $ref: '#/components/parameters/OSVersionHeader'
        - $ref: '#/components/parameters/AppDebugModeHeader'
        - $ref: '#/components/parameters/PlatformHeader'
        - $ref: '#/components/parameters/DeviceTypeHeader'
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PushRequest"
      responses:
        '204':
          description: Successful subscribe to push notifications
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '404':
          description: Not found error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /push-notifications/unsubscribe:
    post:
      summary: Unsubscribe the user from push notification
      parameters:
        - $ref: '#/components/parameters/AppVersionHeader'
        - $ref: '#/components/parameters/OSVersionHeader'
        - $ref: '#/components/parameters/AppDebugModeHeader'
        - $ref: '#/components/parameters/PlatformHeader'
        - $ref: '#/components/parameters/DeviceTypeHeader'
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PushRequest"
      responses:
        '204':
          description: Successful unsubscribe from push notifications
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '404':
          description: Not found error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
