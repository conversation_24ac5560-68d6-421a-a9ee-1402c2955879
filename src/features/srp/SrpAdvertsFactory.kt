package com.gumtree.mobile.features.srp

import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.adverts.gam.GAMAdvertSize
import com.gumtree.mobile.features.screens.layoutsData.BingAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.responses.ScreenType

/*******************************
 * GAM SRP ADVERT UNIT IDS
 ******************************/
const val GAM_ANDROID_SRP_UNIT_ID = "/5144/android.m/srp/"
const val GAM_IOS_SRP_UNIT_ID = "/5144/ios.m/srp/"

/*********************************
 * GAM SRP ADVERT SLOT NAMES
 ********************************/
const val GAM_SRP_MIDDLE_1_SLOT = "middle1"
const val GAM_SRP_MIDDLE_2_SLOT = "middle2"
const val GAM_SRP_MIDDLE_3_SLOT = "middle3"

/*********************************
 * GAM SRP ATT APPTR ADVERT PLACEMENT IDS
 ********************************/
const val GAM_SRP_ATTAPPTR_MIDDLE_1_PLACEMENT_ID = "srp_middle1"
const val GAM_SRP_ATTAPPTR_MIDDLE_2_PLACEMENT_ID = "srp_middle2"
const val GAM_SRP_ATTAPPTR_MIDDLE_3_PLACEMENT_ID = "srp_middle3"

/**
 * Factory about the SRP adverts
 */
class SrpAdvertsFactory(private val bingAdUrlProvider: BingAdUrlProvider) {

    fun buildSrpMiddle1Advert(
        slotName: String,
        attributes: GAMAdvertAttributes,
        pageUrl: String?,
    ): UiItem {
        return GAMAdvertDto(
            androidUnitId = GAM_ANDROID_SRP_UNIT_ID + slotName,
            iosUnitId = GAM_IOS_SRP_UNIT_ID + slotName,
            slotName = slotName,
            displaySize = listOf(GAMAdvertSize.Ad300x250),
            attributes = attributes,
            pageUrl = pageUrl,
            addApptrPlacementId = GAM_SRP_ATTAPPTR_MIDDLE_1_PLACEMENT_ID,
        )
    }

    fun buildSrpMiddle2Advert(
        slotName: String,
        attributes: GAMAdvertAttributes,
        pageUrl: String?,
    ): UiItem {
        return GAMAdvertDto(
            androidUnitId = GAM_ANDROID_SRP_UNIT_ID + slotName,
            iosUnitId = GAM_IOS_SRP_UNIT_ID + slotName,
            slotName = slotName,
            displaySize = listOf(GAMAdvertSize.Ad300x250),
            attributes = attributes,
            pageUrl = pageUrl,
            addApptrPlacementId = GAM_SRP_ATTAPPTR_MIDDLE_2_PLACEMENT_ID,
        )
    }

    fun buildSrpMiddle3Advert(
        slotName: String,
        attributes: GAMAdvertAttributes,
        pageUrl: String?,
        key: String,
    ): UiItem {
        return GAMAdvertDto(
            androidUnitId = GAM_ANDROID_SRP_UNIT_ID + slotName,
            iosUnitId = GAM_IOS_SRP_UNIT_ID + slotName,
            slotName = slotName,
            displaySize = listOf(GAMAdvertSize.Ad300x250),
            attributes = attributes,
            pageUrl = pageUrl,
            addApptrPlacementId = GAM_SRP_ATTAPPTR_MIDDLE_3_PLACEMENT_ID,
            key = key,
        )
    }

    fun buildBingAdvert(
        categoryId: String,
        screenType: ScreenType,
        searchTerm: String?,
        consentString: String,
        clientSemantics: ClientSemantics,
        experiments: ClientExperiments?,
    ): UiItem {
        return BingAdvertDto(
            bingAdUrlProvider.getUrl(categoryId, screenType, searchTerm, consentString, clientSemantics, experiments),
        )
    }
}
