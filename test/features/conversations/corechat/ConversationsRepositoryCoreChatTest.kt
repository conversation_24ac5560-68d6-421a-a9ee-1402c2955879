package features.conversations.corechat

import com.gumtree.mobile.api.conversations.models.SendMessageRequest
import com.gumtree.mobile.api.conversations.models.SendMessageUserRole
import com.gumtree.mobile.api.conversations.models.UnreadMessageCountResponse
import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenResponse
import com.gumtree.mobile.features.conversations.ConversationsGridSizes
import com.gumtree.mobile.features.conversations.CreateConversationRequest
import com.gumtree.mobile.features.conversations.UnreadMessageCountResponseDto
import com.gumtree.mobile.features.conversations.corechat.ConversationsMapperCoreChat
import com.gumtree.mobile.features.conversations.corechat.ConversationsRepositoryCoreChat
import com.gumtree.mobile.features.screens.LimitedPageCalculator
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.createGridSizes
import io.ktor.http.Headers
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import kotlin.test.assertEquals
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.GRID_SIZES_FILE_PATH
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCoreChatFactory
import tools.rawDataFactory.UserServiceDataFactory
import utils.TestDispatcherProvider

class ConversationsRepositoryCoreChatTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should use service to get users conversations`() = runTest {
        runUnitTest(robot) {
            Given { stubUserDetails() }
            Given { stubJwtToken() }
            Given { stubRawConversations(15) }
            Given { stubMapper() }
            When { readScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkServiceGetUserConversations(DataFactory.SOME_TOKEN, DataFactory.SOME_USER_ID, DataFactory.SOME_OFFSET) }
        }
    }

    @Test
    fun `should use mapper to map the raw conversations`() = runTest {
        runUnitTest(robot) {
            Given { stubGridSizes() }
            Given { stubUserDetails() }
            Given { stubJwtToken() }
            Given { stubRawConversations(25) }
            Given { stubRecipientDetails() }
            Given { stubAdDetails() }
            Given { stubMapper() }
            When { readScreen(DataFactory.SOME_USER_EMAIL, "0", DataFactory.SOME_SIZE) }
            Then { checkMapperMapsRawConversations() }
        }
    }

    @Test
    fun `should create JWT token and fetch user details when reading conversations`() = runTest {
        runUnitTest(robot) {
            Given { stubGridSizes() }
            Given { stubUserDetails() }
            Given { stubJwtToken() }
            Given { stubRawConversations(20) }
            Given { stubMapper() }
            When { readScreen(DataFactory.SOME_USER_EMAIL, "0", DataFactory.SOME_SIZE) }
            Then { checkGetConversationsActionsInOrder(DataFactory.SOME_USER_EMAIL) }
        }
    }

    @Test
    fun `should use service to delete conversation`() = runTest {
        runUnitTest(robot) {
            Given { stubUserDetails() }
            Given { stubJwtToken() }
            Given { stubConversationById() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID) }
            Then { checkDeleteConversationActionsInOrder(DataFactory.SOME_TOKEN, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should create conversation with correct sender role and message`() = runTest {
        runUnitTest(robot) {
            Given { stubUserDetails() }
            Given { stubJwtToken() }
            Given { stubAdDetails() }
            Given { stubCreateConversationRequest(DataFactory.SOME_COMMENT, null) }
            When { createConversation(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkCreateConversationWithCorrectDetails(DataFactory.SOME_TOKEN, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should return the upstream unread message count`() = runTest {
        runUnitTest(robot) {
            Given { stubJwtToken() }
            Given { stubUnreadMessageCount(DataFactory.SOME_UNREAD_MESSAGE_COUNT) }
            When { getUnreadMessageCount() }
            Then { checkUnreadMessageCount(DataFactory.SOME_UNREAD_MESSAGE_COUNT) }
        }
    }

    private class Robot : BaseRobot {

        private val conversationsService: ConversationsServiceCoreChat = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)
        private val conversationsMapper: ConversationsMapperCoreChat = mockk(relaxed = true)
        private val listingGridSizes = spyk(ConversationsGridSizes())
        private val savedSearchesPageCalculator: PageCalculator = LimitedPageCalculator()
        private lateinit var actualUnreadMessageCountResponse: UnreadMessageCountResponseDto
        private lateinit var createConversationRequest: CreateConversationRequest

        private lateinit var testSubject: ConversationsRepositoryCoreChat

        override fun setup() {
            testSubject = ConversationsRepositoryCoreChat(
                conversationsService,
                conversationsMapper,
                savedSearchesPageCalculator,
                TestDispatcherProvider(),
            )
            mockkStatic(GRID_SIZES_FILE_PATH)
        }

        override fun tearsDown() {
            unmockkStatic(GRID_SIZES_FILE_PATH)
        }

        fun stubMapper() {
            every {
                conversationsMapper.mapScreen(any(), any())
            } returns Pair(emptyList(), emptyList())
        }

        fun stubGridSizes() {
            every { createGridSizes<ConversationsGridSizes>(any()) } returns listingGridSizes
        }

        fun stubUserDetails() {
            coEvery {
                conversationsService.getUserDetails(DataFactory.SOME_USER_EMAIL)
            } returns UserServiceDataFactory.createRawUserDetails()
        }

        fun stubJwtToken() {
            coEvery {
                conversationsService.generateCoreChatJwt(any())
            } returns CoreChatStreamTokenResponse(DataFactory.SOME_TOKEN)
        }

        fun stubRawConversations(number: Int) {
            coEvery {
                conversationsService.getAllConversations(
                    jwt = any(),
                    userId = any(),
                    offset = any()
                )
            } returns RawCoreChatFactory.createRawAllConversationsResponse(number)
        }

        fun stubRecipientDetails() {
            coEvery {
                conversationsService.getUserDetailsByUserId(any())
            } returns mockk()
        }

        fun stubAdDetails() {
            coEvery {
                conversationsService.getAdDetailsById(any())
            } returns mockk(relaxed = true) {
                every { createdBy } returns mockk {
                    every { id } returns DataFactory.SOME_SELLER_ID.toInt()
                }
                every { primaryImageUrl } returns DataFactory.SOME_AD_IMAGE_URL
            }
        }

        fun stubConversationById() {
            coEvery {
                conversationsService.getConversationById(
                    jwt = any(),
                    channelId = any(),
                    userId = any()
                )
            } returns mockk {
                every { adId } returns DataFactory.SOME_AD_ID
                every { buyerId } returns DataFactory.SOME_BUYER_ID
                every { sellerId } returns DataFactory.SOME_SELLER_ID
            }
        }

        fun stubUnreadMessageCount(count: Int) {
            coEvery {
                conversationsService.getUnreadMessageCount(any(), any())
            } returns UnreadMessageCountResponse(count)
        }

        fun stubCreateConversationRequest(
            text: String?,
            imageURL: String?
        ) {
            createConversationRequest = CreateConversationRequest(
                message = CreateConversationRequest.Message(
                    text = text,
                    imageURL = imageURL
                )
            )
        }

        suspend fun createConversation(
            userName: String,
            adId: String
        ) {
            testSubject.create(callHeaders, userName, adId, createConversationRequest)
        }

        suspend fun readScreen(
            userName: String,
            page: String,
            size: String
        ) {
            testSubject.readScreen(callHeaders, userName, page, size)
        }

        suspend fun delete(
            userName: String,
            conversationId: String
        ) {
            testSubject.delete(callHeaders, userName, conversationId)
        }

        suspend fun getUnreadMessageCount() {
            actualUnreadMessageCountResponse = testSubject.getUnreadMessageCount(
                DataFactory.SOME_USER_ID,
                DataFactory.SOME_USER_EMAIL
            )
        }

        fun checkUnreadMessageCount(expected: Int) {
            assertEquals(expected, actualUnreadMessageCountResponse.unreadMessageCount)
        }

        fun checkServiceGetUserConversations(
            expectedJwt: String,
            expectedUserId: String,
            expectedOffset: String
        ) {
            coVerify {
                conversationsService.getAllConversations(
                    jwt = expectedJwt,
                    userId = expectedUserId,
                    offset = expectedOffset
                )
            }
        }

        fun checkMapperMapsRawConversations() {
            coVerify { conversationsMapper.mapScreen(any(), any()) }
        }

        fun checkGetConversationsActionsInOrder(expectedUserEmail: String) {
            coVerifyOrder {
                conversationsService.getUserDetails(expectedUserEmail)
                conversationsService.generateCoreChatJwt(any())
                conversationsService.getAllConversations(any(), any(), any())
            }
        }

        fun checkDeleteConversationActionsInOrder(
            expectedJwt: String,
            expectedAdId: String
        ) {
            coVerifyOrder {
                conversationsService.getUserDetails(any())
                conversationsService.generateCoreChatJwt(any())
                conversationsService.getConversationById(any(), any(), any())
                conversationsService.deleteConversation(
                    jwt = expectedJwt,
                    adId = expectedAdId,
                    buyerId = any(),
                    sellerId = any()
                )
            }
        }

        fun checkCreateConversationWithCorrectDetails(
            expectedJwt: String,
            expectedAdId: String
        ) {
            coVerify {
                conversationsService.sendMessage(
                    jwt = expectedJwt,
                    request = match<SendMessageRequest> {
                        it.adId == expectedAdId &&
                            it.sender == SendMessageUserRole.BUYER &&
                            it.messageBody == DataFactory.SOME_COMMENT
                    }
                )
            }
        }
    }
}