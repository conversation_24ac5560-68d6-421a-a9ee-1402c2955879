openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-phone-verification
  license:
    name: MIT
tags:
  - name: BFF-PHONE-VERIFICATION
    description: Mobile Apps BFF Phone Verification API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  securitySchemes:
    ApiKeyAuthUserEmail:
      type: apiKey
      description: Security API key to authorize requests with an user email
      in: header
      name: Authorisation-User-Email
    ApiKeyAuthUserToken:
      type: apiKey
      description: Security API key to authorize requests with an user token
      in: header
      name: Authorisation-User-Token
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
    AdIdQuery:
      in: query
      name: adId
      description: the id of the ad
      required: false
      schema:
        type: string
  schemas:
    PhoneVerifyConfigResponse:
      type: object
      properties:
        code:
          type: string
        data:
          type: object
          properties:
            title:
              type: string
            subTitle:
              type: string
            codeTips:
              type: string
            phone:
              $ref: '#/components/schemas/Phone'
            button:
              $ref: '#/components/schemas/Button'
            help:
              $ref: '#/components/schemas/Help'
            resend:
              $ref: '#/components/schemas/Resend'
            analyticsParameters:
              $ref: '#/components/schemas/AnalyticsEventData'
    Phone:
      type: object
      properties:
        label:
          type: string
        areaIcon:
          type: string
        areaCode:
          type: string
        errorTips:
          type: string
    Button:
      type: object
      properties:
        text:
          type: string
        url:
          type: string
        errorTips:
          type: string
    Resend:
      type: object
      properties:
        text:
          type: string
        buttonText:
          type: string
        url:
          type: string
    Help:
      type: object
      properties:
        text:
          type: string
        helpText:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
    PhoneVerifyResponse:
      type: object
      properties:
        code:
          type: integer
          required: true
        msg:
          type: string
          required: true
        bizCode:
          type: string
          required: true
    AnalyticsEventData:
      type: object
      properties:
        eventName:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
security:
  - ApiKeyAuthUserEmail: []
    ApiKeyAuthUserToken: []
paths:
  /phone-authentication/config:
    get:
      summary: Get the phone verification config
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/AdIdQuery"
      responses:
        '200':
          description: Response with phone verification config data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneVerifyConfigResponse"
  /phone-authentication/verification/config:
    get:
      summary: Get the phone verification code config
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/AdIdQuery"
      responses:
        '200':
          description: Response with phone verification code config data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneVerifyConfigResponse"
  /phone-authentication/send:
    post:
      summary: Send verification code to phone number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phoneNumber:
                  type: string
              required:
                - phoneNumber
      responses:
        '200':
          description: Verification code sent successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneVerifyResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /phone-authentication/code-verify:
    post:
      summary: Verify the SMS code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                smsCode:
                  type: string
              required:
                - smsCode
      responses:
        '200':
          description: SMS code verified successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhoneVerifyResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"