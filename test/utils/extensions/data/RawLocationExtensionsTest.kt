package utils.extensions.data

import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.locations.SimpleLocationDto
import com.gumtree.mobile.utils.extensions.data.toSimpleLocationDto
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.rawDataFactory.RawLocationFactory
import tools.runUnitTest

@ParallelTest
class RawLocationExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should map RawLocation with to SimpleLocationDto`() {
        runUnitTest(robot) {
            Given {
                stubRawLocation(
                    DataFactory.SOME_LOCATION_ID.toInt(),
                    DataFactory.SOME_LOCATION_NAME,
                    RawLocation.Type.postcode,
                    DataFactory.SOME_LOCATION_ID.toInt(),
                    DataFactory.SOME_LOCATION_LAT_DOUBLE,
                    DataFactory.SOME_LOCATION_LON_DOUBLE,
                    null
                )
            }
            When { toSimpleLocationDto() }
            Then {
                checkSimpleLocationDto(
                    SimpleLocationDto(
                        DataFactory.SOME_LOCATION_ID,
                        DataFactory.SOME_LOCATION_NAME,
                        LocationType.POSTCODE,
                        DataFactory.SOME_LOCATION_LAT_DOUBLE,
                        DataFactory.SOME_LOCATION_LON_DOUBLE
                    )
                )
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualSimpleLocationDtoResult: SimpleLocationDto

        private lateinit var testSubject: RawLocation

        fun stubRawLocation(
            id: Int,
            name: String,
            type: RawLocation.Type,
            locationId: Int?,
            latitude: Double?,
            longitude: Double?,
            postcode: String?,
        ) {
            testSubject =
                RawLocationFactory.createRawLocation(id, name, type, locationId, latitude, longitude, postcode)
        }

        fun toSimpleLocationDto() {
            actualSimpleLocationDtoResult = testSubject.toSimpleLocationDto()
        }

        fun checkSimpleLocationDto(expected: SimpleLocationDto) {
            assertEquals(expected, actualSimpleLocationDtoResult)
        }
    }
}