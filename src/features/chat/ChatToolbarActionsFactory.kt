package com.gumtree.mobile.features.chat

import api.capi.models.RawConversation
import com.gumtree.mobile.api.userService.models.RawUserServiceAccountDetails
import com.gumtree.mobile.features.screens.layoutsData.BlockUserToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.DestinationToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ReportConversationToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarActionDto
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.data.findConversationCounterPartyDisplayName
import com.gumtree.mobile.utils.extensions.data.findConversationCounterPartyUserId
import com.gumtree.mobile.utils.runOrNull

/**
 * Factory about the Chat Toolbar actions
 */
class ChatToolbarActionsFactory {

    fun createBlockUserAction(
        username: String,
        rawConversation: RawConversation,
        isCounterPartyBlocked: <PERSON><PERSON><PERSON>,
    ): ToolbarActionDto? {
        return runOrNull(!isCounterPartyBlocked) {
            val blockUserId = rawConversation.findConversationCounterPartyUserId(username)
            val blockUserDisplayName = rawConversation.findConversationCounterPartyDisplayName(username, null)
            BlockUserToolbarActionDto(
                title = "${ChatScreenUiConfiguration.BLOCK_USER_TEXT} $blockUserDisplayName",
                isHidden = ChatScreenUiConfiguration.IS_TOOLBAR_BLOCK_USER_HIDDEN,
                userId = blockUserId,
            )
        }
    }

    fun createReportConversationAction(
        username: String,
        rawConversation: RawConversation,
        isConversationAlreadyReported: Boolean,
    ): ToolbarActionDto? {
        return runOrNull(!isConversationAlreadyReported) {
            val counterPartyUserId = rawConversation.findConversationCounterPartyUserId(username)
            val counterPartyName = rawConversation.findConversationCounterPartyDisplayName(username, null)

            ReportConversationToolbarActionDto(
                title = "${ChatScreenUiConfiguration.REPORT_CONVERSATION_TEXT} $counterPartyName",
                isHidden = ChatScreenUiConfiguration.IS_TOOLBAR_REPORT_CONVERSATION_HIDDEN,
                conversationId = rawConversation.id,
                counterPartyUserId = counterPartyUserId,
                counterPartyName = counterPartyName,
                adId = rawConversation.adId,
            )
        }
    }

    fun createViewProfileAction(
        accountDetails: RawUserServiceAccountDetails,
    ): ToolbarActionDto {
        return DestinationToolbarActionDto(
            title = ChatScreenUiConfiguration.VIEW_PROFILE_TEXT,
            isHidden = true,
            destination = DestinationRoute.SELLER_PROFILE.build(
                ApiQueryParams.USER_ID to accountDetails.userIds.firstOrNull(),
                ApiQueryParams.PUBLIC_USER_ID to accountDetails.publicId,
            ),
        )
    }
}
