package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.routes.DestinationDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("POST_PUBLISHER_INFO_CARD")
data class PostPublisherInfoCardDto(
    @SerialName("data")
    val data: PostPublisherInfoCardData,
) : UiItem

@Serializable
data class PostPublisherInfoCardData(
    @SerialName("sellerLink")
    val sellerLink: NewSellerLink?,
    @SerialName("destination")
    val destination: DestinationDto?,
    @SerialName("avatar")
    val avatar: List<ImageDto>,
    @SerialName("rating")
    val rating: RatingCardDto?,
    @SerialName("poweredBy")
    val poweredBy: PoweredByCardDto?,
)

@Serializable
@SerialName("RATING_CARD")
data class RatingCardDto(
    @SerialName("type")
    val type: String = "RATING_CARD",
    @SerialName("data")
    val data: RatingData,
) : UiItem {
    @Serializable
    data class RatingData(
        @SerialName("score")
        val score: Double,
        @SerialName("reviewCount")
        val reviewCount: Int,
    )
}

@Serializable
@SerialName("POWERED_BY_CARD")
data class PoweredByCardDto(
    @SerialName("type")
    val type: String = "POWERED_BY_CARD",
    @SerialName("data")
    val data: PoweredByData,
) : UiItem {
    @Serializable
    data class PoweredByData(
        @SerialName("isHidden")
        val isHidden: Boolean,
        @SerialName("prefix")
        val prefix: String,
        @SerialName("by")
        val by: String,
        @SerialName("infoIconVisible")
        val infoIconVisible: Boolean,
        @SerialName("displayType")
        val displayType: String = "Icon",
        @SerialName("infoIconAction")
        val infoIconAction: BubbleTipAction? = null,
    )
}

@Serializable
data class ImageDto(
    @SerialName("url")
    val url: String,
    @SerialName("width")
    val width: Int,
    @SerialName("height")
    val height: Int,
)

@Serializable
@SerialName("BUBBLE_TIP_Action")
data class BubbleTipAction(
    val label: String,
    val analyticsEventData: AnalyticsEventData? = null,
)
