package com.gumtree.mobile.features.sellerProfile.v2

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.utils.TimeAgoFormatter
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.runOrNull
import java.time.format.DateTimeFormatter

class SellerProfileReviewDateFormatter(
    override val dateFormat: DateTimeFormatter,
    private val sellerProfileScreenUiConfiguration: SellerProfileScreenUiConfiguration = SellerProfileScreenUiConfiguration,
) : TimeAgoFormatter {

    override fun getTimeAgoLabel(
        olderDate: String?,
        newerDate: String,
    ): String? {
        return runOrNull(olderDate.isNotNullOrEmpty()) {
            val (years, months, weeks, days, hours) = calculateYearsMonthsWeeksDaysHours(olderDate, newerDate)
            when {
                years > ZERO -> sellerProfileScreenUiConfiguration.getReviewYearText(years)
                months > ZERO -> sellerProfileScreenUiConfiguration.getReviewMonthText(months)
                weeks > ZERO -> sellerProfileScreenUiConfiguration.getReviewWeekText(weeks)
                days > ZERO -> sellerProfileScreenUiConfiguration.getReviewDayText(days)
                hours > ZERO -> sellerProfileScreenUiConfiguration.getReviewHourText(hours)
                else -> null
            }
        }
    }
}
