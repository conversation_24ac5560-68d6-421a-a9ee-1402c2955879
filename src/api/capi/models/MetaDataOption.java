package api.capi.models;


import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Root;

import static com.gumtree.mobile.api.common.ConstantsKt.*;

@Root(strict = false)
public class MetaDataOption {
    @Attribute(required = false, name = "localized-label")
    public String localizedLabel;

    @Attribute(required = false)
    public String type;

    @Attribute(name = "search-param", required = false)
    public String searchParam;

    @Attribute(name = "write", required = false)
    public String write;

    @Attribute(name = "size-max", required = false)
    public int maxSize = -1;

    @Attribute(name = "size-min", required = false)
    public int minSize = -1;

    @Attribute(name = "digits-fraction", required = false)
    public int digitsFraction = -1;

    @Attribute(name = "digits-integer", required = false)
    public int digitsInteger = -1;

    // Filled-in by the code handling reply forms before the
    // final reply is submitted to the API.
    //
    // For now, only handle STRING types
    public String stringValue;

    public MetaDataOption() {

    }

    public MetaDataOption(
            @Attribute(name = "localized-label", required = false) String displayString,
            @Attribute(name = "type") String type,
            @Attribute(name = "write") String write
    ) {
        this.localizedLabel = displayString;
        this.type = type;
        this.write = write;
    }

    public boolean disabledForSearch(){
        return UNSUPPORTED.equals(searchParam);
    }

    public boolean isSupported() {
        return OPTIONAL.equals(write) || REQUIRED.equals(write);
    }

    public boolean isRequired() {
        return REQUIRED.equals(write);
    }
}
