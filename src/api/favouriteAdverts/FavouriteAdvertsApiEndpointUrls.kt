package com.gumtree.mobile.api.favouriteAdverts

import com.gumtree.mobile.api.favouriteAdverts.FavouriteAdvertsApiParams.ID
import com.gumtree.mobile.api.favouriteAdverts.FavouriteAdvertsApiParams.USER_ID
import com.gumtree.mobile.utils.annotations.NotCovered

@NotCovered
internal object FavouriteAdvertsApiEndpointUrls {

    const val GET_ALL_FAVOURITE_ADVERTS_ENDPOINT = "/favourite-adverts/user/{$USER_ID}"
    const val POST_FAVOURITE_ADVERT_ENDPOINT = "/favourite-adverts/user/{$USER_ID}/advert/{$ID}"
}
