package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import static com.gumtree.mobile.api.common.ConstantsKt.EMPTY_STRING;

@Root(strict = false, name = "evaluation-response")
@Namespace(reference = Namespaces.ECHELON, prefix = Namespaces.Prefix.ECHELON)
public class RawCapiEchelon {
    public enum RateLimitPermission {
        ALLOW,
        BLOCK
    }

    public boolean isAllowed() {
        return RateLimitPermission.ALLOW.toString().equalsIgnoreCase(action);
    }

    @Element(name="action", required = false)
    @Namespace(reference = Namespaces.ECHELON)
    public String action = EMPTY_STRING;

    @Element(name="message", required = false)
    @Namespace(reference = Namespaces.ECHELON)
    public String message = EMPTY_STRING;

    @Element(name="value", required = false)
    @Namespace(reference = Namespaces.ECHELON)
    public String value = EMPTY_STRING;

}
