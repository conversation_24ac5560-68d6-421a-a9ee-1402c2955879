package com.gumtree.mobile.features.myGumtree

import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.features.myGumtree.MyGumtreeScreenUiConfiguration.SETTINGS_TITLE
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.layoutsData.MyGumtreeFilter
import com.gumtree.mobile.features.screens.layoutsData.SettingsToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.getSizeOrZero
import com.gumtree.mobile.utils.extensions.handleNoContentResponse
import com.gumtree.mobile.utils.handleRequestOrNull
import io.ktor.http.Headers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

interface MyGumtreeRepository {

    /**
     * Read MyGumtree screen
     * @param callHeaders - The request headers
     * @param adId - the Ad id
     * @param filter - The filter type (ALL or ACTIVE)
     * @return - the mapped data ready to be returned to the client
     */
    suspend fun readScreen(
        callHeaders: Headers,
        filterType: String?,
        email: String,
        page: String,
        size: String,
    ): ScreenResponse

    /**
     * Delete a user's Ad
     * @param callHeaders - The request headers
     * @param userName - the username (user email)
     * @param adId - The id of the Ad to be deleted
     */
    suspend fun delete(
        callHeaders: Headers,
        userName: String,
        adId: String,
    )

    suspend fun repostFreeAd(
        callHeaders: Headers,
        adId: String,
    )
}

class DefaultMyGumtreeRepository(
    private val papiHeadersProvider: ApiHeadersProvider,
    private val capiHeadersProvider: ApiHeadersProvider,
    private val myGumtreeService: MyGumtreeService,
    private val myGumtreeAdsMapper: MyGumtreeAdsMapper,
    private val profileMapper: ProfileMapper,
    private val myGumtreeTitleRowMapper: MyGumtreeTitleRowMapper,
    private val pageCalculator: PageCalculator,
    private val dispatcherProvider: DispatcherProvider,
) : MyGumtreeRepository {

    override suspend fun readScreen(
        callHeaders: Headers,
        filterType: String?,
        email: String,
        page: String,
        size: String,
    ): ScreenResponse {
        val capiAuthHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)
        val papiAuthHeaders = papiHeadersProvider.createAuthorisedHeaders(callHeaders)

        val myGumtreeFilter = MyGumtreeFilter.fromString(filterType)
        val rawCapiAds = handleRequestOrNull {
            withContext(dispatcherProvider.io) {
                myGumtreeService.getUserAds(
                    capiAuthHeaders,
                    email,
                    page,
                    size,
                    myGumtreeFilter.queryParams,
                )
            }
        }

        val adIds = rawCapiAds?.rawAds?.map { it.id }?.joinToString(",").orEmpty()
        val accountId =
            withContext(dispatcherProvider.io) { myGumtreeService.getUserDetails(email).accountIds.firstOrNull() }
        val rawAdStatListFuture = withContext(dispatcherProvider.io) {
            async {
                handleRequestOrNull {
                    myGumtreeService.getAdsStats(
                        capiAuthHeaders,
                        email,
                        adIds,
                    )
                }
            }
        }

        val rawUserProfileFuture = withContext(dispatcherProvider.io) {
            async {
                accountId?.let {
                    handleRequestOrNull { myGumtreeService.getBaseUserProfileByAccountId(papiAuthHeaders, it) }
                }
            }
        }

        val myGumtreeTitleRow = myGumtreeTitleRowMapper.map(myGumtreeFilter.text)

        val nextPage = pageCalculator.calculateNextPage(
            page = page,
            size = size,
            dataSize = rawCapiAds?.rawAds.getSizeOrZero(),
        )

        val rawAdStatList = rawAdStatListFuture.await()
        val rawUserProfile = rawUserProfileFuture.await()

        return ScreenResponse(
            portraitData = myGumtreeAdsMapper.map(rawCapiAds, rawAdStatList),
            toolbar = ToolbarDto(
                actions = listOf(
                    SettingsToolbarActionDto(
                        SETTINGS_TITLE,
                        false,
                        DestinationRoute.SETTINGS.build(),
                    ),
                ),
                behavior = "inline",
            ),
            stickyBar = listOfNotNull(
                profileMapper.map(rawUserProfile),
                myGumtreeTitleRow,
            ),
            nextPage = nextPage,
        )
    }

    override suspend fun delete(
        callHeaders: Headers,
        userName: String,
        adId: String,
    ) {
        val authHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)

        withContext(dispatcherProvider.io) {
            myGumtreeService.deleteUserAd(authHeaders, userName, adId).handleNoContentResponse()
        }
    }

    override suspend fun repostFreeAd(
        callHeaders: Headers,
        adId: String,
    ) {
        val authHeaders = capiHeadersProvider.createAuthorisedHeaders(callHeaders)

        withContext(dispatcherProvider.io) {
            myGumtreeService.repostForFree(authHeaders, adId)
        }
    }
}
