name: Sonar

# Re-usable workflow for running Sonar checks

# Optional Inputs:
#  - sonar_properties
#       These properties will be added to the end of the gradle task so need to be
#       provided as shown in the docs for providing properties through the command line
#       e.g. "-Dsonar.host.url=http://sonar.mycompany.com -Dsonar.verbose=true"
#       https://docs.sonarqube.org/9.6/analyzing-source-code/scanners/sonarscanner-for-gradle/

on:
  workflow_call:
    inputs:
      sonar_properties:
        required: false
        type: string
    secrets:
      sonar_token:
        required: true

jobs:
  build:
    name: Build and analyze
    runs-on: [gumtree-mobile]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - uses: ./.github/actions/setup
      - name: Cache SonarQube packages
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar
      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle') }}
          restore-keys: ${{ runner.os }}-gradle
      - name: Run all tests with coverage report
        run: |
          run: |
            ./gradlew check --info
#          This will complete all sub-tasks from the "check" task
      - name: Sonar analysis
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          run: |
          ./gradlew sonar ${{ inputs.sonar_properties }} --info
#          When the "check" task finish, sonar task shall start (the Kover report should be done on the previous step)
      - name: Archive coverage report
        uses: actions/upload-artifact@v4
        with:
          name: code-coverage-report
          path: build/reports/kover/html
