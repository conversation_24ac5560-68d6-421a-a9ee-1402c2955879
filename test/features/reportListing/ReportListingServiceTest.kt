package features.reportListing

import api.capi.bodies.RawFlagAdBody
import com.gumtree.mobile.api.capi.apis.CapiUserApi
import com.gumtree.mobile.features.reportListing.ReportListingService
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class ReportListingServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use CAPI user API to POST report listing`() = runTest {
        runUnitTest(robot) {
            Given { stubAdIdandBody(
                DataFactory.SOME_AD_ID,
                RawFlagAdBody(DataFactory.SOME_COMMENT, DataFactory.SOME_REASON, DataFactory.SOME_USER_EMAIL)
            ) }
            When { sendReportListing() }
            Then { checkApiSendReportListing() }
        }
    }

    private class Robot: BaseRobot {
        private val capiUserApi: CapiUserApi = mockk(relaxed = true)
        private lateinit var testSubject: ReportListingService
        private lateinit var body: RawFlagAdBody
        private lateinit var adId: String

        override fun setup() {
            testSubject = ReportListingService(capiUserApi)
        }

        fun stubAdIdandBody(adId: String, body: RawFlagAdBody) {
            this.adId = adId
            this.body = body
        }

        suspend fun sendReportListing() {
            testSubject.reportListing(adId, body)
        }

        fun checkApiSendReportListing() {
            coVerify { capiUserApi.reportListing(adId, body) }
        }
    }
}