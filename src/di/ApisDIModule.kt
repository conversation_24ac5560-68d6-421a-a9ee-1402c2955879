package com.gumtree.mobile.di

import api.phoneNumber.api.PhoneNumberApi
import api.phoneNumberAuthenticator.api.PhoneNumberAuthenticatorApi
import api.similarItems.api.SimilarItemsApi
import com.gumtree.mobile.api.blockUser.api.BlockUserApi
import com.gumtree.mobile.api.capi.apis.CapiAdsApi
import com.gumtree.mobile.api.capi.apis.CapiCategoryApi
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.capi.apis.CapiFavoritesApi
import com.gumtree.mobile.api.capi.apis.CapiOtherUserApi
import com.gumtree.mobile.api.capi.apis.CapiPostApi
import com.gumtree.mobile.api.capi.apis.CapiReplyApi
import com.gumtree.mobile.api.capi.apis.CapiSavedSearchesApi
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.capi.apis.CapiUserApi
import com.gumtree.mobile.api.categories.api.CategoriesApi
import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.crm.api.CrmApi
import com.gumtree.mobile.api.favouriteAdverts.api.FavouriteAdvertsApi
import com.gumtree.mobile.api.fullAdsSearch.api.FullAdsSearchApi
import com.gumtree.mobile.api.homeFeed.api.HomeFeedApi
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.partnerships.api.PartnershipAdsApi
import com.gumtree.mobile.api.reportChat.api.ReportChatApi
import com.gumtree.mobile.api.reviews.api.ReviewsApi
import com.gumtree.mobile.api.savedSearch.api.SavedSearchApi
import com.gumtree.mobile.api.seller.api.SellerApi
import com.gumtree.mobile.api.userProfile.api.UserProfileApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.utils.extensions.createRetrofitApiFromKoin
import org.koin.dsl.module

/**
 * Create and inject instances of all APIs used in this application to obtain data (Retrofit APIs)
 */
val apisDIModule = module {

    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiUserApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiOtherUserApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiFavoritesApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiSavedSearchesApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiConversationApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiAdsApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiPostApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiReplyApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiCategoryApi::class.java) }
    single { createRetrofitApiFromKoin(CAPI_API_RETROFIT_CLIENT, CapiSearchApi::class.java) }
    single { createRetrofitApiFromKoin(LOCATIONS_API_RETROFIT_CLIENT, LocationsApi::class.java) }
    single { createRetrofitApiFromKoin(CATEGORIES_API_RETROFIT_CLIENT, CategoriesApi::class.java) }
    single { createRetrofitApiFromKoin(USER_SERVICE_API_RETROFIT_CLIENT, UserServiceApi::class.java) }
    single { createRetrofitApiFromKoin(CORE_CHAT_API_RETROFIT_CLIENT, CoreChatAuthApi::class.java) }
    single { createRetrofitApiFromKoin(HOME_FEED_API_RETROFIT_CLIENT, HomeFeedApi::class.java) }
    single { createRetrofitApiFromKoin(CONVERSATIONS_API_RETROFIT_CLIENT, ConversationsApi::class.java) }
    single { createRetrofitApiFromKoin(USER_PROFILE_API_RETROFIT_CLIENT, UserProfileApi::class.java) }
    single { createRetrofitApiFromKoin(SIMILAR_ITEMS_RETROFIT_CLIENT, SimilarItemsApi::class.java) }
    single { createRetrofitApiFromKoin(BLOCK_USER_RETROFIT_CLIENT, BlockUserApi::class.java) }
    single { createRetrofitApiFromKoin(PHONE_NUMBER_RETROFIT_CLIENT, PhoneNumberApi::class.java) }
    single { createRetrofitApiFromKoin(CRM_RETROFIT_CLIENT, CrmApi::class.java) }
    single { createRetrofitApiFromKoin(PARTNERSHIP_ADS_RETROFIT_CLIENT, PartnershipAdsApi::class.java) }
    single { createRetrofitApiFromKoin(REVIEWS_API_RETROFIT_CLIENT, ReviewsApi::class.java) }
    single { createRetrofitApiFromKoin(FULL_ADS_SEARCH_API_RETROFIT_CLIENT, FullAdsSearchApi::class.java) }
    single { createRetrofitApiFromKoin(PHONE_VERIFY_API_RETROFIT_CLIENT, PhoneNumberAuthenticatorApi::class.java) }
    single { createRetrofitApiFromKoin(SELLER_API_RETROFIT_CLIENT, SellerApi::class.java) }
    single { createRetrofitApiFromKoin(FAVOURITE_ADVERTS_API_RETROFIT_CLIENT, FavouriteAdvertsApi::class.java) }
    single { createRetrofitApiFromKoin(SAVED_SEARCH_API_RETROFIT_CLIENT, SavedSearchApi::class.java) }
    single { createRetrofitApiFromKoin(REPORT_CHAT_API_RETROFIT_CLIENT, ReportChatApi::class.java) }
}
