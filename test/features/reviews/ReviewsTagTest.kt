package features.reviews

import com.gumtree.mobile.features.reviews.ReviewsTag
import org.junit.jupiter.api.Assertions.*

import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.runUnitTest
import tools.When

class ReviewsTagTest {

    private val robot = Robot()

    @Test
    fun `should return key value as the ReviewTag name`() {
        runUnitTest(robot) {
            Given { stubReviewsTag(ReviewsTag.FRIENDLY) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.FRIENDLY.name) }

            Given { stubReviewsTag(ReviewsTag.POLITE) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.POLITE.name) }

            Given { stubReviewsTag(ReviewsTag.HELPFUL) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.HELPFUL.name) }

            Given { stubReviewsTag(ReviewsTag.SPEEDY_RESPONDER) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.SPEEDY_RESPONDER.name) }

            Given { stubReviewsTag(ReviewsTag.ITEM_AS_DESCRIBED) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.ITEM_AS_DESCRIBED.name) }

            Given { stubReviewsTag(ReviewsTag.QUICK_TRANSACTION) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.QUICK_TRANSACTION.name) }

            Given { stubReviewsTag(ReviewsTag.SHOWED_UP_ON_TIME) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.SHOWED_UP_ON_TIME.name) }

            Given { stubReviewsTag(ReviewsTag.FAIR_NEGOTIATION) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.FAIR_NEGOTIATION.name) }

            Given { stubReviewsTag(ReviewsTag.RUDE) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.RUDE.name) }

            Given { stubReviewsTag(ReviewsTag.UNHELPFUL) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.UNHELPFUL.name) }

            Given { stubReviewsTag(ReviewsTag.UNRESPONSIVE) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.UNRESPONSIVE.name) }

            Given { stubReviewsTag(ReviewsTag.ITEM_NOT_AS_DESCRIBED) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.ITEM_NOT_AS_DESCRIBED.name) }

            Given { stubReviewsTag(ReviewsTag.CANCELLED_OFFER) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.CANCELLED_OFFER.name) }

            Given { stubReviewsTag(ReviewsTag.DIDN_T_SHOW_UP) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.DIDN_T_SHOW_UP.name) }

            Given { stubReviewsTag(ReviewsTag.TOO_MUCH_HAGGLING) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(ReviewsTag.TOO_MUCH_HAGGLING.name) }
        }
    }

    @Test
    fun `should return key value as NULL`() {
        runUnitTest(robot) {
            Given { stubReviewsTag(ReviewsTag.NONE_OF_THESE) }
            When { getKeyValue() }
            Then { checkReviewsTagKeyValue(null) }
        }
    }

    private class Robot: BaseRobot {
        private var actualReviewsTagKeyValueResult: String? = null

        private lateinit var testSubject: ReviewsTag

        fun stubReviewsTag(tag: ReviewsTag) {
            testSubject = tag
        }

        fun getKeyValue() {
            actualReviewsTagKeyValueResult = testSubject.getKeyValue()
        }

        fun checkReviewsTagKeyValue(expected: String?) {
            assertEquals(expected, actualReviewsTagKeyValueResult)
        }
    }
}