package features.srp

import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.adverts.gam.GAMAdvertAttribute
import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.adverts.gam.GAM_SRP_PAGE_TYPE
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.cache.CATEGORIES_TREE_CACHE_RESOURCE_PATH
import com.gumtree.mobile.cache.createFileInputStream
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.BingAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.srp.BingAdUrlProvider
import com.gumtree.mobile.features.srp.SrpAdvertsFactory
import com.gumtree.mobile.features.srp.SrpAdvertsProvider
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.ScreenType
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.extensions.DEBUG_APP
import com.gumtree.mobile.utils.extensions.LIVE_APP
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.ClientExperimentsFactory
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.RowLayoutsFactory
import tools.rawDataFactory.RawLocationFactory
import tools.runUnitTest

class SrpAdvertsProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return SRP advertising row layout type with one GAM advert when building middle1 advert`() {
        runUnitTest(robot) {
            When { createSrpMiddle1AdvertRow() }
            Then { checkSrpGAMAdvertisingRowLayoutType(RowLayoutType.ADVERTISING_ROW) }
            Then { checkSrpGAMAdvertisingRowLayoutDataTypeAtPosition(0, GAMAdvertDto::class.java) }
            Then { checkSrpGAMAdvertisingRowLayoutDataSize(1) }
            Then { checkGAMAdvertKeyIsNull() }
        }
    }

    @Test
    fun `should return SRP advertising row layout type with one GAM advert when building middle2 advert`() {
        runUnitTest(robot) {
            When { createSrpMiddle2AdvertRow() }
            Then { checkSrpGAMAdvertisingRowLayoutType(RowLayoutType.ADVERTISING_ROW) }
            Then { checkSrpGAMAdvertisingRowLayoutDataTypeAtPosition(0, GAMAdvertDto::class.java) }
            Then { checkSrpGAMAdvertisingRowLayoutDataSize(1) }
            Then { checkGAMAdvertKeyIsNull() }
        }
    }

    @Test
    fun `should return SRP advertising row layout type with one GAM advert when building middle3 advert`() {
        runUnitTest(robot) {
            When { createSrpMiddle3AdvertRow() }
            Then { checkSrpGAMAdvertisingRowLayoutType(RowLayoutType.ADVERTISING_ROW) }
            Then { checkSrpGAMAdvertisingRowLayoutDataTypeAtPosition(0, GAMAdvertDto::class.java) }
            Then { checkSrpGAMAdvertisingRowLayoutDataSize(1) }
            Then { checkGAMAdvertKeyIsNotNull() }
        }
    }

    @Test
    fun `should return Bing advert row`() {
        runUnitTest(robot) {
            val experiments = ClientExperimentsFactory.createExperiments(Experiment.GTNA_1 to Variant.B)
            Given { stubBingAdProviderGetUrl(DataFactory.SOME_BING_URL) }
            Given { stubExperiments(experiments) }
            When {
                createBingAdvertRow(
                    DataFactory.SOME_AD_CATEGORY_ID,
                    ScreenType.SRP,
                    DataFactory.SOME_SEARCH_TERM,
                    DataFactory.SOME_CONSENT_STRING,
                    ClientSemantics(
                        appVersion = DataFactory.SOME_APP_VERSION,
                        device = DataFactory.SOME_DEVICE,
                        platform = ClientPlatform.ANDROID,
                    ),
                )
            }
            Then { checkSrpGAMAdvertisingRowLayoutType(RowLayoutType.ADVERTISING_ROW) }
            Then { checkSrpGAMAdvertisingRowLayoutDataTypeAtPosition(0, BingAdvertDto::class.java) }
            Then { checkSrpGAMAdvertisingRowLayoutDataSize(1) }
            Then { checkAdvertisementRowData(BingAdvertDto(DataFactory.SOME_BING_URL)) }
            Then {
                checkBingAdProviderCalled(
                    DataFactory.SOME_AD_CATEGORY_ID,
                    ScreenType.SRP,
                    DataFactory.SOME_SEARCH_TERM,
                    DataFactory.SOME_CONSENT_STRING,
                    ClientSemantics(
                        appVersion = DataFactory.SOME_APP_VERSION,
                        device = DataFactory.SOME_DEVICE,
                        platform = ClientPlatform.ANDROID,
                    ),
                    experiments,
                )
            }
        }
    }

    @Test
    fun `should return SRP GAM advert common attributes`() {
        runUnitTest(robot) {
            When {
                getSrpGAMCommonAttributes(
                    screenType = ScreenType.SRP,
                    categoryId = CategoryDefaults.CARS.id,
                    searchParams = mapOf(
                        ApiQueryParams.Q to DataFactory.SOME_KEYWORD,
                        ApiQueryParams.SELLER_TYPE to DataFactory.SOME_SELLER_TYPE,
                        ApiQueryParams.VEHICLE_MAKE to DataFactory.SOME_VEHICLE_MAKE,
                        ApiQueryParams.VEHICLE_MODEL to DataFactory.SOME_VEHICLE_MODEL,
                        ApiQueryParams.VEHICLE_BODY_TYPE to DataFactory.SOME_VEHICLE_BODY_TYPE,
                    ),
                    rawLocation = RawLocationFactory.createRawLocation(),
                    isAuthenticated = true,
                    clientSemantics = ClientSemantics(appVersion = DataFactory.SOME_APP_VERSION),
                    experiments = DataFactory.SOME_EXPERIMENT,
                )
            }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_SRP_PAGE_TYPE,
                        GAMAdvertAttribute.ENVIRONMENT.value to LIVE_APP,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.KEYWORD.value to DataFactory.SOME_KEYWORD,
                        GAMAdvertAttribute.SELLER_TYPE.value to DataFactory.SOME_SELLER_TYPE,
                        GAMAdvertAttribute.VEHICLE_MAKE.value to DataFactory.SOME_VEHICLE_MAKE,
                        GAMAdvertAttribute.VEHICLE_MODEL.value to DataFactory.SOME_VEHICLE_MODEL,
                        GAMAdvertAttribute.VEHICLE_BODY_TYPE.value to DataFactory.SOME_VEHICLE_BODY_TYPE,
                        GAMAdvertAttribute.CATEGORY_L2.value to "cars",
                    )
                )
            }
        }
    }

    @Test
    fun `should return SRP GAM advert common attributes and filter null values`() {
        runUnitTest(robot) {
            When {
                getSrpGAMCommonAttributes(
                    screenType = ScreenType.SRP,
                    categoryId = CategoryDefaults.CARS.id,
                    searchParams = mapOf(
                        ApiQueryParams.VEHICLE_MAKE to DataFactory.SOME_VEHICLE_MAKE,
                    ),
                    rawLocation = RawLocationFactory.createRawLocation(),
                    isAuthenticated = true,
                    clientSemantics = ClientSemantics(isAppDebugMode = true),
                    experiments = null,
                )
            }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.PAGE_TYPE.value to GAM_SRP_PAGE_TYPE,
                        GAMAdvertAttribute.ENVIRONMENT.value to DEBUG_APP,
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.VEHICLE_MAKE.value to DataFactory.SOME_VEHICLE_MAKE,
                        GAMAdvertAttribute.CATEGORY_L2.value to "cars",
                    )
                )
            }
        }
    }

    @Test
    fun `should return all SRP GAM adverts`() {
        runUnitTest(robot) {
            When { onlySrpGAMAdverts(RowLayoutsFactory.createPortraitHomeFeedListingsRowLayoutWithAllAdverts(10)) }
            Then { checkSrpGAMAdvertsSize(5) }
        }
    }

    @Test
    fun `should return NO SRP GAM adverts`() {
        runUnitTest(robot) {
            When { onlySrpGAMAdverts(RowLayoutsFactory.createRowLayouts(10)!!) }
            Then { checkSrpGAMAdvertsSize(null) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualSrpAdvertisingRowResult: RowLayout<UiItem>
        private lateinit var actualGAMAdvertAttributesResult: GAMAdvertAttributes
        private var experiments: ClientExperiments? = null
        private var actualSrpGAMAdvertsResult: List<GAMAdvertDto>? = null

        private val bingAdUrlProvider: BingAdUrlProvider = mockk(relaxed = true)
        private val json = Json { ignoreUnknownKeys = true }
        private val categoriesTreeCache = CategoriesTreeCache

        private var testSubject = SrpAdvertsProvider(SrpAdvertsFactory(bingAdUrlProvider), GAMAdvertUtils(categoriesTreeCache))

        override fun setup() {
            createFileInputStream(CATEGORIES_TREE_CACHE_RESOURCE_PATH)
                ?.runCatching { json.decodeFromStream<CategoryDto>(this).also { categoriesTreeCache.data = it } }
        }

        fun stubBingAdProviderGetUrl(url: String) {
            every { bingAdUrlProvider.getUrl(any(), any(), any(), any(), any(), any()) } returns url
        }

        fun stubExperiments(experiments: ClientExperiments) {
            this.experiments = experiments
        }

        fun createSrpMiddle1AdvertRow() {
            actualSrpAdvertisingRowResult =
                testSubject.createSrpMiddle1AdvertRow(DataFactory.anyString(), emptyMap(), DataFactory.anyString())
        }

        fun createSrpMiddle2AdvertRow() {
            actualSrpAdvertisingRowResult =
                testSubject.createSrpMiddle2AdvertRow(DataFactory.anyString(), emptyMap(), DataFactory.anyString())
        }

        fun createSrpMiddle3AdvertRow() {
            actualSrpAdvertisingRowResult =
                testSubject.createSrpMiddle3AdvertRow(DataFactory.anyString(), emptyMap(), DataFactory.anyString())
        }

        fun createBingAdvertRow(
            categoryId: String,
            screenType: ScreenType,
            searchTerm: String?,
            consentString: String,
            clientSemantics: ClientSemantics,
        ) {
            actualSrpAdvertisingRowResult = testSubject.createBingAdvertRow(
                categoryId, screenType, searchTerm, consentString, clientSemantics, experiments
            )
        }

        fun getSrpGAMCommonAttributes(
            screenType: ScreenType,
            categoryId: String,
            searchParams: QueryParams,
            rawLocation: RawLocation,
            isAuthenticated: Boolean,
            clientSemantics: ClientSemantics,
            experiments: String?,
        ) {
            actualGAMAdvertAttributesResult = testSubject.getSrpGAMCommonAttributes(
                screenType,
                categoryId,
                searchParams,
                rawLocation,
                isAuthenticated,
                clientSemantics,
                experiments,
            )
        }

        fun onlySrpGAMAdverts(rowLayouts: List<RowLayout<UiItem>>) {
            actualSrpGAMAdvertsResult = testSubject.onlySrpGAMAdverts(rowLayouts)
        }

        fun checkSrpGAMAdvertisingRowLayoutType(expected: RowLayoutType) {
            assertEquals(expected, actualSrpAdvertisingRowResult.type)
        }

        fun <T> checkSrpGAMAdvertisingRowLayoutDataTypeAtPosition(
            position: Int,
            expected: T
        ) {
            assertEquals(expected, actualSrpAdvertisingRowResult.data[position].javaClass)
        }

        fun checkSrpGAMAdvertisingRowLayoutDataSize(expected: Int) {
            assertEquals(expected, actualSrpAdvertisingRowResult.data.size)
        }

        fun checkGAMAdvertAttributes(expected: GAMAdvertAttributes) {
            assertEquals(expected, actualGAMAdvertAttributesResult)
        }

        fun checkAdvertisementRowData(expected: UiItem) {
            assertEquals(expected, actualSrpAdvertisingRowResult.data.first())
        }

        fun checkGAMAdvertKeyIsNull() {
            assertNull((actualSrpAdvertisingRowResult.data[0] as GAMAdvertDto).key)
        }

        fun checkGAMAdvertKeyIsNotNull() {
            assertNotNull((actualSrpAdvertisingRowResult.data[0] as GAMAdvertDto).key)
        }

        fun checkSrpGAMAdvertsSize(expected: Int?) {
            assertEquals(expected, actualSrpGAMAdvertsResult?.size)
        }

        fun checkBingAdProviderCalled(
            categoryId: String,
            screenType: ScreenType,
            searchTerm: String?,
            consentString: String,
            clientSemantics: ClientSemantics,
            experiments: ClientExperiments,
        ) {
            verify {
                bingAdUrlProvider.getUrl(
                    categoryId,
                    screenType,
                    searchTerm,
                    consentString,
                    clientSemantics,
                    experiments,
                )
            }
        }
    }
}