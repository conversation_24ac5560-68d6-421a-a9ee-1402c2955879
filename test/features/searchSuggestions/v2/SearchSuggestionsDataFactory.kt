package features.searchSuggestions.v2

import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionCategoryDto
import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionResultDto
import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionsResponse
import com.gumtree.mobile.routes.DestinationDto
import tools.DataFactory

object SearchSuggestionsDataFactory {

    private val category = SearchSuggestionCategoryDto("1", "All Ads")

    fun createSearchSuggestionsRespons(size: Int): SearchSuggestionsResponse {
        return SearchSuggestionsResponse(
            suggestions = createListOfSuggestions(size),
            inputAnalyticsParams = DataFactory.anyAnalyticsEventWithParams().parameters!!
        )
    }

    private fun createListOfSuggestions(size: Int): List<SearchSuggestionResultDto> {
        val list = mutableListOf<SearchSuggestionResultDto>()
        repeat(size) {
            val term = "term:$it"
            list.add(SearchSuggestionResultDto(term, "", "$it", "", category, getDest(term), getParams(term)))
        }
        return list
    }

    private fun getDest(q: String): DestinationDto = DestinationDto("srp?q=$q&categoryId=1")
    private fun getParams(q: String): String = "q=$q&categoryId=1"
}
