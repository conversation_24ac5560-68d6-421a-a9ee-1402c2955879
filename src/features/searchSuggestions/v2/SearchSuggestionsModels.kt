package com.gumtree.mobile.features.searchSuggestions.v2

import com.gumtree.mobile.routes.DestinationDto
import kotlinx.serialization.Serializable

@Serializable
data class SearchSuggestionResultDto(
    val searchTerm: String,
    val prefix: String,
    val boldText: String,
    val suffix: String,
    val category: SearchSuggestionCategoryDto,
    val destination: DestinationDto,
    val params: String,
)

@Serializable
data class SearchSuggestionCategoryDto(val id: String, val name: String)

@Serializable
data class SearchSuggestionsResponse(
    val suggestions: List<SearchSuggestionResultDto>,
    val inputAnalyticsParams: Map<String, String>,
)
