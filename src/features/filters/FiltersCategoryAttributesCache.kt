package com.gumtree.mobile.features.filters

import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.cache.DefaultCategoryAttributesCache

private const val FILTERS_CATEGORY_ATTRIBUTES_CACHE_EXPIRE_TIME = 1 * 60 * 60 * 1000 // 1 hour in mills

/**
 * The filters screen attributes cache
 */
object FiltersCategoryAttributesCache : CategoryAttributesCache<FiltersCategoryAttributeDto> by DefaultCategoryAttributesCache(
    FILTERS_CATEGORY_ATTRIBUTES_CACHE_EXPIRE_TIME,
)
