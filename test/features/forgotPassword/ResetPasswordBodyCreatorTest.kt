package features.forgotPassword

import com.gumtree.mobile.api.userService.bodies.UserServiceChangePasswordBody
import com.gumtree.mobile.features.forgotPassword.ResetPasswordBodyCreator
import com.gumtree.mobile.features.forgotPassword.ResetPasswordRequest
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.http.Headers
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CallHeadersFactory
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class ResetPasswordBodyCreatorTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should create new user forgot password request body payload`() {
        runUnitTest(robot) {
            Given {
                stubCallHeaders(
                    CallHeadersFactory.createUnAuthHeaders(
                        ApiHeaderParams.PLATFORM to ClientPlatform.ANDROID.name,
                    ),
                )
            }
            Given {
                stubResetPasswordRequest(
                    DataFactory.SOME_USER_SECRET,
                    DataFactory.SOME_TOKEN
                )
            }
            When { create() }
            Then {
                checkRawResetPasswordRequest(
                    UserServiceChangePasswordBody(
                        password = DataFactory.SOME_USER_SECRET,
                        confirmedPassword = DataFactory.SOME_USER_SECRET,
                        verificationKey = DataFactory.SOME_TOKEN,
                        verificationKeyType = UserServiceChangePasswordBody.VerificationKeyType.RESET_PASSWORD_KEY,
                        client = ClientPlatform.ANDROID.name,
                    )
                )
            }

        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualRawResetPasswordBody: UserServiceChangePasswordBody
        private lateinit var resetResetPasswordRequest: ResetPasswordRequest
        private lateinit var callHeaders: Headers

        private lateinit var tested: ResetPasswordBodyCreator

        override fun setup() {
            tested = ResetPasswordBodyCreator()
        }

        fun stubCallHeaders(headers: Headers) {
            callHeaders = headers
        }

        fun stubResetPasswordRequest(
            userSecret: String,
            resetToken: String,
        ) {
            resetResetPasswordRequest = ResetPasswordRequest(
                userSecret = userSecret,
                resetToken = resetToken,
            )
        }

        fun create() {
            actualRawResetPasswordBody = tested.create(callHeaders, resetResetPasswordRequest)
        }

        fun checkRawResetPasswordRequest(expected: UserServiceChangePasswordBody) {
            assertEquals(expected, actualRawResetPasswordBody)
        }
    }
}