package features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.features.vip.VipImageGalleryMapper
import com.gumtree.mobile.utils.extensions.isNotNull
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.GAMAdvertDtoFactory
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class VipImageGalleryMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return Vip image gallery row`() {
        runUnitTest(robot) {
            Given { stubRawAd(DataFactory.SOME_AD_IMAGE_URL) }
            Given { stubGAMAdvert(null) }
            When { map() }
            Then { checkVipImageGalleryRowIsNotNull() }
            Then { checkVipImageGalleryRowType(RowLayoutType.VIP_IMAGE_GALLERY_ROW) }
            Then { checkVipImageCardGAMAdvertAdPosition(0,null) }
            Then { checkVipImageGalleryRowDataSize(1) }
        }
    }

    @Test
    fun `should return Vip image gallery row with empty list of images`() {
        runUnitTest(robot) {
            Given { stubGAMAdvert(null) }
            Given { stubRawAdWithoutPictures() }
            When { map() }
            Then { checkVipImageGalleryRowDataSize(0) }
        }
    }

    @Test
    fun `should return Vip picture images gallery data`() {
        runUnitTest(robot) {
            Given { stubRawAd(DataFactory.ANOTHER_AD_IMAGE_URL) }
            Given { stubGAMAdvert(null) }
            When { map() }
            Then { checkVipImageGalleryRowDataCount(1) }
            Then { checkVipImageGalleryRowDataTypeAtPosition(0, VipImageCardDto::class.java) }
            Then {
                checkVipImageCardUrlAtPosition(
                    0,
                    DataFactory.largeSquareImages(DataFactory.ANOTHER_AD_IMAGE_BASE_URL),
                )
            }
            Then { checkVipImageCardGAMAdvertAdPosition(0,null) }
            Then { checkVipImageGalleryRowDataSize(1) }
        }
    }

    @Test
    fun `should NOT return Vip picture images gallery data with GAM advert if ad has 1 picture`() {
        runUnitTest(robot) {
            Given { stubRawAd(DataFactory.ANOTHER_AD_IMAGE_URL) }
            Given { stubGAMAdvert(GAMAdvertDtoFactory.createGAMAdvert()) }
            When { map() }
            Then { checkVipImageGalleryRowDataCount(1) }
            Then { checkVipImageGalleryRowDataTypeAtPosition(0, VipImageCardDto::class.java) }
            Then {
                checkVipImageCardUrlAtPosition(
                    0,
                    DataFactory.largeSquareImages(DataFactory.ANOTHER_AD_IMAGE_BASE_URL),
                )
            }
            Then { checkVipImageCardGAMAdvertAdPosition(0,null) }
            Then { checkVipImageGalleryRowDataSize(1) }
        }
    }

    @Test
    fun `should return Vip picture images gallery data with GAM advert if ad has 2 pictures`() {
        runUnitTest(robot) {
            Given { stubRawAdWithPictures(2, Pair(DataFactory.ANOTHER_AD_IMAGE_URL, RawCapiAd.Size.PREVIEW)) }
            Given { stubGAMAdvert(GAMAdvertDtoFactory.createGAMAdvert()) }
            When { map() }
            Then { checkVipImageGalleryRowDataCount(3) }
            Then { checkVipImageGalleryRowDataTypeAtPosition(0, VipImageCardDto::class.java) }
            Then { checkVipImageGalleryRowDataTypeAtPosition(1, VipImageCardDto::class.java) }
            Then { checkVipImageGalleryRowDataTypeAtPosition(2, VipImageCardDto::class.java) }
            Then {
                checkVipImageCardUrlAtPosition(
                    0,
                    DataFactory.largeSquareImages(DataFactory.ANOTHER_AD_IMAGE_BASE_URL),
                )
            }
            Then {
                checkVipImageCardUrlAtPosition(
                    1,
                    DataFactory.largeSquareImages(DataFactory.ANOTHER_AD_IMAGE_BASE_URL),
                )
            }
            Then { checkVipImageCardGAMAdvertAdPosition(0,null) }
            Then { checkVipImageCardGAMAdvertAdPosition(1,null) }
            Then { checkVipImageCardGAMAdvertAdPosition(2, GAMAdvertDtoFactory.createGAMAdvert()) }
            Then { checkVipImageGalleryRowDataSize(3) }
        }
    }

    private class Robot: BaseRobot {
        private var actualVipImageCardsDtoResult: RowLayout<UiItem>? = null
        private lateinit var rawAd: RawCapiAd
        private var gamAdvertImageCard: VipImageCardDto? = null

        private lateinit var testSubject: VipImageGalleryMapper

        override fun setup() {
            testSubject = VipImageGalleryMapper()
        }

        fun stubRawAd(imageUrl: String) {
            rawAd = RawCapiAdsFactory.createRawCapiAd(imageUrl = imageUrl)
        }

        fun stubRawAdWithPictures(
            numberOfPictures: Int,
            vararg pictureSizes: Pair<String, RawCapiAd.Size>?
        ) {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithPicture(numberOfPictures, *pictureSizes)
        }

        fun stubRawAdWithoutPictures() {
            rawAd = RawCapiAdsFactory.createRawCapiAdWithoutPictures()
        }

        fun stubGAMAdvert(gamAdvertDto: GAMAdvertDto?) {
            gamAdvertImageCard = when {
                gamAdvertDto.isNotNull() -> VipImageCardDto(
                    images = emptyList(),
                    gamAdvert = GAMAdvertDtoFactory.createGAMAdvert()
                )
                else -> null
            }
        }

        fun map() {
            actualVipImageCardsDtoResult = testSubject.map(rawAd, gamAdvertImageCard)
        }

        fun checkVipImageGalleryRowIsNotNull() {
            assertNotNull(actualVipImageCardsDtoResult)
        }

        fun checkVipImageGalleryRowDataSize(expected: Int) {
            assertEquals(expected, actualVipImageCardsDtoResult?.data?.size)
        }

        fun checkVipImageGalleryRowType(expected: RowLayoutType) {
            assertEquals(expected, actualVipImageCardsDtoResult?.type)
        }

        fun checkVipImageGalleryRowDataCount(expected: Int) {
            assertEquals(expected, actualVipImageCardsDtoResult?.data?.size)
        }

        fun <T>checkVipImageGalleryRowDataTypeAtPosition(
            position: Int,
            expected: Class<T>,
        ) {
            assertInstanceOf(expected, actualVipImageCardsDtoResult?.data?.get(position))
        }

        fun checkVipImageCardUrlAtPosition(
            position: Int,
            expected: List<Image>,
        ) {
            assertEquals(expected, (actualVipImageCardsDtoResult?.data?.get(position) as VipImageCardDto?)?.images)
        }

        fun checkVipImageCardGAMAdvertAdPosition(
            position: Int,
            expected: GAMAdvertDto?,
        ) {
            assertEquals(expected, (actualVipImageCardsDtoResult?.data?.get(position) as VipImageCardDto?)?.gamAdvert)
        }

    }
}
