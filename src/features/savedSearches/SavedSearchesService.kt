package com.gumtree.mobile.features.savedSearches

import api.capi.models.RawCapiSavedSearchList
import com.gumtree.mobile.api.capi.apis.CapiSavedSearchesApi
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.savedSearch.api.SavedSearchApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.utils.extensions.slicePageOrEmptyList

class SavedSearchesService(
    private val capiSavedSearchesApi: CapiSavedSearchesApi,
    private val capiSearchApi: CapiSearchApi,
    private val savedSearchApi: SavedSearchApi,
    private val userServiceApi: UserServiceApi,
) : CapiSavedSearchesApi by capiSavedSearchesApi,
    CapiSearchApi by capiSearchApi,
    SavedSearchApi by savedSearchApi,
    UserServiceApi by userServiceApi {

    /**
     * We need to override CapiSavedSearchesApi.getUserSavedSearches(), because CAPI does NOT return the data on pages,
     * but instead it returns all saved search items at once,
     * so that's why we need to manually slice the BFF saved search pages
     */
    override suspend fun getUserSavedSearches(
        authorisationHeaders: ApiHeaders,
        page: String,
        size: String,
    ): RawCapiSavedSearchList {
        return capiSavedSearchesApi
            .getUserSavedSearches(authorisationHeaders, page, size)
            .apply {
                rawCapiSavedSearches = rawCapiSavedSearches.slicePageOrEmptyList(page, size, rawPaging.numFound)
            }
    }
}
