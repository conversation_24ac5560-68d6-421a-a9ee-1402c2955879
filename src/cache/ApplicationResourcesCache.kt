package com.gumtree.mobile.cache

import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import com.gumtree.mobile.utils.extensions.runCatchingWithLog
import io.ktor.server.application.Application
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import java.io.InputStream

/**
 * See the resources folder
 */
const val CATEGORIES_TREE_CACHE_RESOURCE_PATH = "categoriesTreeCache.json"
const val FILTERS_CATEGORY_ATTRIBUTES_CACHE_RESOURCE_PATH = "filtersCategoryAttributesCache.json"

private val json = Json { ignoreUnknownKeys = true }

/**
 * When the BFF starts we populate the CategoriesTreeCache object with local cached data
 * Later on, the CategoriesTreeCache object will be updated from CategoriesRoute.kt GET /categories endpoint
 * @param cache - the CategoriesTreeCache object to be updated
 */
fun Application.initialiseCategoriesTreeCache(cache: IndexedTreeCache<CategoryDto>) {
    createFileInputStream(CATEGORIES_TREE_CACHE_RESOURCE_PATH)
        ?.runCatchingWithLog { json.decodeFromStream<CategoryDto>(this).also { cache.data = it } }
        ?.onSuccess { println("[BFF] Categories tree local cache successfully loaded") }
        ?.onFailure { println("[BFF] Categories tree local cache unsuccessful, ERROR: $it") }
        ?: run { println("[BFF] Categories tree local cache file [$CATEGORIES_TREE_CACHE_RESOURCE_PATH] is missing") }
}

/**
 * When the BFF starts we populate the FiltersCategoryAttributesCache object with local cached data
 * Later on, the FiltersCategoryAttributesCache object will be updated from FiltersRoute.kt GET /filters/screen endpoint
 * @param cache - the CategoryAttributesCache object to be updated
 */
fun Application.initialiseFiltersCategoryAttributesCache(cache: CategoryAttributesCache<FiltersCategoryAttributeDto>) {
    createFileInputStream(FILTERS_CATEGORY_ATTRIBUTES_CACHE_RESOURCE_PATH)
        ?.runCatchingWithLog { json.decodeFromStream<List<FiltersCategoryAttributeDto>>(this).also { cache.data = it } }
        ?.onSuccess { println("[BFF] Filters category attributes local cache successfully loaded") }
        ?.onFailure { println("[BFF] Filters category attributes local cache unsuccessful, ERROR: $it") }
        ?: run {
            println("[BFF] Filters category attributes local cache file [$FILTERS_CATEGORY_ATTRIBUTES_CACHE_RESOURCE_PATH] is missing")
        }
}

/**
 * Open a File from the project resources with InputStream
 * @param - the file name
 * @return - the file input stream instance or null of the file can't be found
 */
fun Any.createFileInputStream(fileName: String): InputStream? {
    return javaClass.classLoader.getResourceAsStream(fileName)
}
