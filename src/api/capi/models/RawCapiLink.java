package api.capi.models;

import org.simpleframework.xml.Attribute;

public class RawCapiLink {
    @Attribute(required = false)
    public String href;
    @Attribute(required = false)
    public String rel;

    public RawCapiLink() {
    }

    public RawCapiLink(String href, String rel) {
        this.href = href;
        this.rel = rel;
    }

    public String getRelSize() {
        return rel;
    }

    public String getUrlHref() {
        return href;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        RawCapiLink rawCapiLink = (RawCapiLink) o;

        if (href != null ? !href.equals(rawCapiLink.href) : rawCapiLink.href != null) {
            return false;
        }
        return !(rel != null ? !rel.equals(rawCapiLink.rel) : rawCapiLink.rel != null);
    }

    @Override
    public int hashCode() {
        int result = href != null ? href.hashCode() : 0;
        result = 31 * result + (rel != null ? rel.hashCode() : 0);
        return result;
    }
}
