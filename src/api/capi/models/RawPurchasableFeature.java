package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.NamespaceList;
import org.simpleframework.xml.Root;

import java.util.List;

@Root(name = "feature", strict = false)
@NamespaceList({
        @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT),
        @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES)
})
public class RawPurchasableFeature {
    @Attribute(name = "name", required = false)
    public String name;

    @Element(name = "description", required = false)
    @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
    public String description;

    @ElementList(name = "options", entry = "option", required = false)
    @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
    public List<RawFeatureOption> options;

    @Element(name = "long-description", required = false)
    @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
    public String longDescription;

    @Element(name = "is-available", required = false)
    @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
    public BooleanWithDefault isAvailable;

    @Element(name = "is-fee", required = false)
    @Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
    public BooleanWithDefault isFee;
}
