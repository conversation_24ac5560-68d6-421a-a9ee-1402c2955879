package features.filters

import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.cache.CategoryAttributesCache
import com.gumtree.mobile.common.ANY_KEY
import com.gumtree.mobile.common.allCarsPriceFilterAttributeOptions
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.filters.FiltersAttributesPriorityOrder
import com.gumtree.mobile.features.filters.FiltersAttributesProvider
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import com.gumtree.mobile.features.filters.FiltersCategoryAttributesCache
import com.gumtree.mobile.features.filters.FiltersScreenUiConfiguration
import com.gumtree.mobile.features.filters.MAX_PREFIX
import com.gumtree.mobile.features.filters.MIN_PREFIX
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.DropdownFactory
import com.gumtree.mobile.features.screens.factories.InputFactory
import com.gumtree.mobile.features.screens.factories.LinkFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.DoubleDropdownCardDto
import com.gumtree.mobile.features.screens.layoutsData.DoubleInputCardDto
import com.gumtree.mobile.features.screens.layoutsData.DropdownCardDto
import com.gumtree.mobile.features.screens.layoutsData.InputCardDto
import com.gumtree.mobile.features.screens.layoutsData.LinkCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.CategoryDefaults
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertInstanceOf
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.layoutsDataFactory.FiltersCategoryAttributesFactory
import tools.runUnitTest

class FiltersAttributesProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return price title row and price double input row for PETS category`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.PETS.id) }
            When { createAttributeRows(CategoryDefaults.PETS.id) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutDataTypeAtPosition(1, DoubleInputCardDto::class.java) }
            Then { checkDoubleInputCardDtoParam1AtPosition(1, ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleInputCardDtoText1AtPosition(1, null) }
            Then { checkDoubleInputCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleInputCardDtoHint1AtPosition(1, FiltersScreenUiConfiguration.MIN_PRICE_HINT_TEXT) }
            Then { checkDoubleInputCardDtoInputType1AtPosition(1, InputCardDto.InputType.CURRENCY) }
            Then { checkDoubleInputCardDtoParam2AtPosition(1, ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleInputCardDtoText2AtPosition(1, null) }
            Then { checkDoubleInputCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleInputCardDtoHint2AtPosition(1, FiltersScreenUiConfiguration.MAX_PRICE_HINT_TEXT) }
            Then { checkDoubleInputCardDtoInputType2AtPosition(1, InputCardDto.InputType.CURRENCY) }
            Then { checkDoubleInputCardDtoSeparatorTextAtPosition(1, FiltersScreenUiConfiguration.TO_TEXT) }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
            Then { checkAttributesRowLayoutsSize(2) }
        }
    }

    @Test
    fun `should return price title row and price double dropdown row for CARS category`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id) }
            When { createAttributeRows(CategoryDefaults.CARS.id) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutDataTypeAtPosition(1, DoubleDropdownCardDto::class.java) }
            Then { checkDoubleDropdownCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleDropdownCardDtoParam1AtPosition(1, ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption1AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions1SizeAtPosition(1, allCarsPriceFilterAttributeOptions.size + 1) }
            Then { checkDoubleDropdownCardDtoChangeAction1AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleDropdownCardDtoParam2AtPosition(1, ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption2AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions2SizeAtPosition(1, allCarsPriceFilterAttributeOptions.size + 1) }
            Then { checkDoubleDropdownCardDtoChangeAction2AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoSeparatorAtPosition(1, FiltersScreenUiConfiguration.TO_TEXT) }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
        }
    }

    @Test
    fun `should always return price, condition, storage capacity and colour attributes whether SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION is enable for Mobile Phones`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForP1(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.MOBILE_PHONES.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION, Variant.B) }

            When { createAttributeRows(CategoryDefaults.MOBILE_PHONES.id) }

            Then { checkAttributesRowLayoutsSize(5) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Condition") }
            Then { checkRowLayoutTypeAtPosition(3, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(3, "Storage Capacity") }
            Then { checkRowLayoutTypeAtPosition(4, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(4, "Colour") }
        }
    }

    @Test
    fun `should always return price, condition, storage capacity and colour attributes whether SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION is disable for Mobile Phones`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForP1(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.MOBILE_PHONES.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION, Variant.A) }

            When { createAttributeRows(CategoryDefaults.MOBILE_PHONES.id) }

            Then { checkAttributesRowLayoutsSize(5) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Condition") }
            Then { checkRowLayoutTypeAtPosition(3, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(3, "Storage Capacity") }
            Then { checkRowLayoutTypeAtPosition(4, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(4, "Colour") }
        }
    }


    @Test
    fun `should return price and model attributes when SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL is enabled for Apple iPhone`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForIphone(),
                )
            }
            Given {
                stubFilterParams(
                    ApiQueryParams.CATEGORY_ID to CategoryDefaults.IPHONE.id,
                )
            }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL, Variant.B) }

            When { createAttributeRows(CategoryDefaults.IPHONE.id) }

            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
        }
    }

    @Test
    fun `should return price and model attributes when SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL is enabled for Samsung`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForSamsung(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.SAMSUNG.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL, Variant.B) }

            When { createAttributeRows(CategoryDefaults.SAMSUNG.id) }

            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
        }
    }

    @Test
    fun `should return price, colour, storage capacity, condition, model for Apple iPhone when both P1 and P2 enabled`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForBothP1AndP2(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.IPHONE.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION, Variant.B) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL, Variant.B) }

            When { createAttributeRows(CategoryDefaults.IPHONE.id) }

            Then { checkAttributesRowLayoutsSize(6) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
            Then { checkRowLayoutTypeAtPosition(3, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(3, "Condition") }
            Then { checkRowLayoutTypeAtPosition(4, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(4, "Storage Capacity") }
            Then { checkRowLayoutTypeAtPosition(5, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(5, "Colour") }
        }
    }

    @Test
    fun `should return price, colour, storage capacity, condition, model for Samsung when both P1 and P2 enabled`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForBothP1AndP2(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.SAMSUNG.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION, Variant.B) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL, Variant.B) }

            When { createAttributeRows(CategoryDefaults.SAMSUNG.id) }

            Then { checkAttributesRowLayoutsSize(6) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
            Then { checkRowLayoutTypeAtPosition(3, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(3, "Condition") }
            Then { checkRowLayoutTypeAtPosition(4, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(4, "Storage Capacity") }
            Then { checkRowLayoutTypeAtPosition(5, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(5, "Colour") }
        }
    }

    @Test
    fun `should return price,condition,storage,colour attribute when both experiments are disabled for Mobile Phones`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForP1(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.MOBILE_PHONES.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION, Variant.A) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL, Variant.A) }

            When { createAttributeRows(CategoryDefaults.MOBILE_PHONES.id) }

            Then { checkAttributesRowLayoutsSize(5) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Condition") }
            Then { checkRowLayoutTypeAtPosition(3, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(3, "Storage Capacity") }
            Then { checkRowLayoutTypeAtPosition(4, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(4, "Colour") }
        }
    }

    @Test
    fun `should return only price attribute when experiment is disable for DIY Tools & Materials`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createBasicDiyToolsAttributes(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.DIY_AND_TRADE.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_DIY_TOOLS_MATERIALS_CONDITION, Variant.A) }

            When { createAttributeRows(CategoryDefaults.DIY_AND_TRADE.id) }

            Then { checkAttributesRowLayoutsSize(2) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
        }
    }

    @Test
    fun `should return price & condition attribute when experiment is enable for DIY Tools & Materials`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createDIYToolMaterialsConditionAttributes(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.DIY_AND_TRADE.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_DIY_TOOLS_MATERIALS_CONDITION, Variant.B) }

            When { createAttributeRows(CategoryDefaults.DIY_AND_TRADE.id) }

            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Condition") }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
        }
    }

    @Test
    fun `should always create link row for non-mobile attributes regardless of experiments`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createNonMobileAttributeWithLinkPresentation(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id) }
            When { createAttributeRows(CategoryDefaults.FOR_SALE.id) }
            Then { checkAttributesRowLayoutsSize(1) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.LINK_ROW) }
        }
    }

    @Test
    fun `should return salary title row and salary double dropdown row for JOBS category`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.JOBS.id) }
            When { createAttributeRows(CategoryDefaults.JOBS.id) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Salary") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutDataTypeAtPosition(1, DoubleDropdownCardDto::class.java) }
            Then { checkDoubleDropdownCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleDropdownCardDtoParam1AtPosition(1, ApiQueryParams.MIN_SALARY) }
            Then { checkDoubleDropdownCardDtoSelectedOption1AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions1SizeAtPosition(1, 5) }
            Then { checkDoubleDropdownCardDtoChangeAction1AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleDropdownCardDtoParam2AtPosition(1, ApiQueryParams.MAX_SALARY) }
            Then { checkDoubleDropdownCardDtoSelectedOption2AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions2SizeAtPosition(1, 5) }
            Then { checkDoubleDropdownCardDtoChangeAction2AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoSeparatorAtPosition(1, FiltersScreenUiConfiguration.TO_TEXT) }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
            Then { checkAttributesRowLayoutsSize(2) }
        }
    }

    @Test
    fun `should return property number beds title row and property number beds double dropdown row for PROPERTY_FOR_SALE category`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to "9342") }
            When { createAttributeRows("9342") }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Number of bedrooms") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutDataTypeAtPosition(1, DoubleDropdownCardDto::class.java) }
            Then { checkDoubleDropdownCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleDropdownCardDtoParam1AtPosition(1, "min_property_number_beds") }
            Then { checkDoubleDropdownCardDtoSelectedOption1AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions1SizeAtPosition(1, 5) }
            Then { checkDoubleDropdownCardDtoChangeAction1AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleDropdownCardDtoParam2AtPosition(1, "max_property_number_beds") }
            Then { checkDoubleDropdownCardDtoSelectedOption2AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions2SizeAtPosition(1, 5) }
            Then { checkDoubleDropdownCardDtoChangeAction2AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoSeparatorAtPosition(1, FiltersScreenUiConfiguration.TO_TEXT) }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
            Then { checkAttributesRowLayoutsSize(2) }
        }
    }

    @Test
    fun `should return price title row, price double dropdown row, vehicle make and vehicle model double link row for CARS category`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given {
                stubFilterParams(
                    ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                    ApiQueryParams.VEHICLE_MAKE to "audi",
                    ApiQueryParams.VEHICLE_MODEL to "A5",
                )
            }
            When { createAttributeRows(CategoryDefaults.CARS.id) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_DROPDOWN_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutDataTypeAtPosition(1, DoubleDropdownCardDto::class.java) }
            Then { checkDoubleDropdownCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleDropdownCardDtoParam1AtPosition(1, ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption1AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions1SizeAtPosition(1, allCarsPriceFilterAttributeOptions.size + 1) }
            Then { checkDoubleDropdownCardDtoChangeAction1AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleDropdownCardDtoParam2AtPosition(1, ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleDropdownCardDtoSelectedOption2AtPosition(1, ANY_KEY) }
            Then { checkDoubleDropdownCardDtoOptions2SizeAtPosition(1, allCarsPriceFilterAttributeOptions.size + 1) }
            Then { checkDoubleDropdownCardDtoChangeAction2AtPosition(1, null) }
            Then { checkDoubleDropdownCardDtoSeparatorAtPosition(1, FiltersScreenUiConfiguration.TO_TEXT) }

            Then { checkRowLayoutDataTypeAtPosition(2, LinkCardDto.Filter::class.java) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Make & Model") }
            Then {
                checkFilterLinkCardDtoDestinationRouteAtPosition(
                    2,
                    "${DestinationRoute.SET_FILTER.screenName}?${ApiQueryParams.NAME}" +
                        "=vehicle_make&${ApiQueryParams.VALUE}" +
                        "=audi&${ApiQueryParams.DEPENDENT_NAME}" +
                        "=vehicle_model&${ApiQueryParams.DEPENDENT_VALUE}=A5",
                )
            }
            Then { checkFilterLinkCardDtoParamsAtPosition(2, "[vehicle_make, vehicle_model]") }
            Then { checkFilterLinkCardDtoSelectedValuesSizeAtPosition(2, 2) }
            Then { checkFilterLinkCardDtoSubTitleAtPosition(2, null) }
            Then { checkFilterLinkCardDtoChangeActionAtPosition(2, null) }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
            Then { checkRowLayoutBottomDividerAtPosition(2, true) }
            Then { checkAttributesRowLayoutsSize(3) }
        }
    }

    @Test
    fun `should return price title row, price double input row and seller type link row for category with seller type attribute`() {
        runUnitTest(robot) {
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given {
                stubFilterParams(
                    ApiQueryParams.CATEGORY_ID to "148",
                    "seller_type" to "trade",
                )
            }
            When { createAttributeRows("148") }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutDataTypeAtPosition(1, DoubleInputCardDto::class.java) }
            Then { checkDoubleInputCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleInputCardDtoParam1AtPosition(1, ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleInputCardDtoHint1AtPosition(1, FiltersScreenUiConfiguration.MIN_PRICE_HINT_TEXT) }
            Then { checkDoubleInputCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleInputCardDtoParam2AtPosition(1, ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleInputCardDtoHint2AtPosition(1, FiltersScreenUiConfiguration.MAX_PRICE_HINT_TEXT) }

            Then { checkRowLayoutDataTypeAtPosition(2, LinkCardDto.Filter::class.java) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Seller type") }
            Then {
                checkFilterLinkCardDtoDestinationRouteAtPosition(
                    2,
                    "${DestinationRoute.SET_FILTER.screenName}?${ApiQueryParams.NAME}=seller_type&${ApiQueryParams.VALUE}=trade&${ApiQueryParams.CATEGORY_ID}=148",
                )
            }
            Then { checkFilterLinkCardDtoParamsAtPosition(2, "[seller_type]") }
            Then { checkFilterLinkCardDtoSelectedValuesSizeAtPosition(2, 1) }
            Then { checkFilterLinkCardDtoSubTitleAtPosition(2, null) }
            Then { checkFilterLinkCardDtoChangeActionAtPosition(2, null) }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
            Then { checkRowLayoutBottomDividerAtPosition(2, true) }
            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkDoubleInputCardDtoResetTextAtPosition(1, null) }
        }
    }

    @Test
    fun `should return generic double input row for category with unknown double input type attribute`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    listOf(
                        FiltersCategoryAttributesFactory.createFilterAttribute(
                            attributeName = "unknown_double_input",
                            attributeLabel = "Some Double Input attribute",
                            attributeDataType = CategoryAttributesCache.Data.Type.NUMBER,
                            attributePresentationType = CategoryAttributesCache.Data.PresentationType.DOUBLE_INPUT,
                            attributeCategoryIds = listOf(148),
                        ),
                    ),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to "148") }
            When { createAttributeRows("148") }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkTitleCardDtoTextAtPosition(0, "Some Double Input attribute") }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.SMALL) }

            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutDataTypeAtPosition(1, DoubleInputCardDto::class.java) }
            Then { checkDoubleInputCardDtoTitle1AtPosition(1, FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleInputCardDtoParam1AtPosition(1, MIN_PREFIX + "unknown_double_input") }
            Then {
                checkDoubleInputCardDtoHint1AtPosition(
                    1,
                    FiltersScreenUiConfiguration.MIN_TEXT + "Some Double Input attribute",
                )
            }
            Then { checkDoubleInputCardDtoTitle2AtPosition(1, FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleInputCardDtoParam2AtPosition(1, MAX_PREFIX + "unknown_double_input") }
            Then {
                checkDoubleInputCardDtoHint2AtPosition(
                    1,
                    FiltersScreenUiConfiguration.MAX_TEXT + "Some Double Input attribute",
                )
            }

            Then { checkRowLayoutBottomDividerAtPosition(0, null) }
            Then { checkRowLayoutBottomDividerAtPosition(1, true) }
            Then { checkAttributesRowLayoutsSize(2) }
            Then { checkDoubleInputCardDtoResetTextAtPosition(1, null) }
        }
    }

    @Test
    fun `should return only price attributes when SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI is disabled`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createBasicMobilePhoneAttributes(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.GOOGLE.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI, Variant.A) }

            When { createAttributeRows(CategoryDefaults.GOOGLE.id) }

            Then { checkAttributesRowLayoutsSize(2) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
        }
    }

    @Test
    fun `should return price and model attributes when SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI is enabled for Google`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForGoogle(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.GOOGLE.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI, Variant.B) }

            When { createAttributeRows(CategoryDefaults.GOOGLE.id) }

            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
        }
    }

    @Test
    fun `should return price and model attributes when SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI is enabled for Xiaomi`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForXiaomi(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.XIAOMI.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI, Variant.B) }

            When { createAttributeRows(CategoryDefaults.XIAOMI.id) }

            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
        }
    }

    @Test
    fun `should return price and model attributes when SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI is enabled for Huawei`() {
        runUnitTest(robot) {
            Given {
                stubFiltersCategoryAttributesCache(
                    FiltersCategoryAttributesFactory.createMobilePhoneAttributesForHuawei(),
                )
            }
            Given { stubFilterParams(ApiQueryParams.CATEGORY_ID to CategoryDefaults.HUAWEI.id) }
            Given { stubExperiment(Experiment.SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI, Variant.B) }

            When { createAttributeRows(CategoryDefaults.HUAWEI.id) }

            Then { checkAttributesRowLayoutsSize(3) }
            Then { checkRowLayoutTypeAtPosition(0, RowLayoutType.TITLE_ROW) }
            Then { checkTitleCardDtoTextAtPosition(0, "Price") }
            Then { checkRowLayoutTypeAtPosition(1, RowLayoutType.DOUBLE_INPUT_ROW) }
            Then { checkRowLayoutTypeAtPosition(2, RowLayoutType.LINK_ROW) }
            Then { checkFilterLinkCardDtoTextAtPosition(2, "Model") }
        }
    }

    @Test
    fun `should return free toggle when in iOS experiment`() {
        runUnitTest(robot) {
            Given { stubExperiment(Experiment.GTB_499, Variant.B) }
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given {
                stubFilterParams(
                    ApiQueryParams.CATEGORY_ID to "148",
                    "seller_type" to "trade",
                )
            }

            When { createAttributeRows("148") }

            Then { checkDoubleInputCardDtoResetTextAtPosition(1, FiltersScreenUiConfiguration.PRICE_RESET_TEXT) }
        }
    }

    @Test
    fun `should return free toggle when in Android experiment`() {
        runUnitTest(robot) {
            Given { stubExperiment(Experiment.GTB_500, Variant.B) }
            Given { stubFiltersCategoryAttributesCache(FiltersCategoryAttributesFactory.createFiltersAttributes()) }
            Given {
                stubFilterParams(
                    ApiQueryParams.CATEGORY_ID to "148",
                    "seller_type" to "trade",
                )
            }

            When { createAttributeRows("148") }

            Then { checkDoubleInputCardDtoResetTextAtPosition(1, FiltersScreenUiConfiguration.PRICE_RESET_TEXT) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualAttributesRowLayoutsResult: Array<RowLayout<UiItem>>
        private val categoriesTreeCache = CategoriesTreeCache
        private val filtersCategoryAttributesCache: FiltersCategoryAttributesCache = FiltersCategoryAttributesCache
        private val filtersParams: HashMap<String, String?> = hashMapOf()
        private var clientExperiments: ClientExperiments = ClientExperiments(emptyMap())
        private val filtersAttributesPriorityOrder = FiltersAttributesPriorityOrder(categoriesTreeCache)

        private val testSubject = FiltersAttributesProvider(
            filtersCategoryAttributesCache,
            filtersAttributesPriorityOrder,
            TitleFactory(),
            InputFactory(),
            DropdownFactory(),
            LinkFactory(),
        )

        override fun setup() {
            super.setup()
            filtersParams.clear()
        }

        fun stubFiltersCategoryAttributesCache(filtersCategoryAttributes: List<FiltersCategoryAttributeDto>) {
            filtersCategoryAttributesCache.data = filtersCategoryAttributes
        }

        fun stubFilterParams(vararg params: Pair<String, String?>) {
            params.forEach { filtersParams[it.first] = it.second }
        }

        fun stubExperiment(experiment: Experiment, variant: Variant) {
            val existingExperiments = clientExperiments.experiments?.toMutableMap()
            existingExperiments?.set(experiment, variant)
            clientExperiments = ClientExperiments(existingExperiments)
        }

        fun createAttributeRows(categoryId: String) {
            actualAttributesRowLayoutsResult =
                testSubject.createAttributeRows(categoryId, filtersParams, clientExperiments)
        }

        fun checkAttributesRowLayoutsSize(expected: Int) {
            assertEquals(expected, actualAttributesRowLayoutsResult.size)
        }

        fun checkRowLayoutTypeAtPosition(
            position: Int,
            expected: RowLayoutType,
        ) {
            assertEquals(expected, actualAttributesRowLayoutsResult[position].type)
        }

        fun <T> checkRowLayoutDataTypeAtPosition(
            position: Int,
            expected: Class<T>,
        ) {
            assertInstanceOf(expected, actualAttributesRowLayoutsResult[position].data[0])
        }

        fun checkRowLayoutBottomDividerAtPosition(
            position: Int,
            expected: Boolean?,
        ) {
            assertEquals(expected, actualAttributesRowLayoutsResult[position].bottomDivider)
        }

        fun checkTitleCardDtoTextAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as TitleCardDto.Left).text)
        }

        fun checkTitleCardDtoSizeAtPosition(
            position: Int,
            expected: TitleCardDto.Size,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as TitleCardDto.Left).size)
        }

        fun checkDoubleInputCardDtoText1AtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).text1)
        }

        fun checkDoubleInputCardDtoParam1AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).param1)
        }

        fun checkDoubleInputCardDtoTitle1AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).title1)
        }

        fun checkDoubleInputCardDtoHint1AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).hint1)
        }

        fun checkDoubleInputCardDtoInputType1AtPosition(
            position: Int,
            expected: InputCardDto.InputType?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).inputType1,
            )
        }

        fun checkDoubleInputCardDtoText2AtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).text2)
        }

        fun checkDoubleInputCardDtoParam2AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).param2)
        }

        fun checkDoubleInputCardDtoTitle2AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).title2)
        }

        fun checkDoubleInputCardDtoHint2AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).hint2)
        }

        fun checkDoubleInputCardDtoInputType2AtPosition(
            position: Int,
            expected: InputCardDto.InputType?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).inputType2,
            )
        }

        fun checkDoubleInputCardDtoSeparatorTextAtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).separatorText,
            )
        }

        fun checkDoubleInputCardDtoResetTextAtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleInputCardDto).resetToggle?.text,
            )
        }

        fun checkDoubleDropdownCardDtoTitle1AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).title1)
        }

        fun checkDoubleDropdownCardDtoParam1AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).param1)
        }

        fun checkDoubleDropdownCardDtoSelectedOption1AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).selectedOption1,
            )
        }

        fun checkDoubleDropdownCardDtoOptions1SizeAtPosition(
            position: Int,
            expected: Int,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).options1.size,
            )
        }

        fun checkDoubleDropdownCardDtoChangeAction1AtPosition(
            position: Int,
            expected: DropdownCardDto.Action?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).changeAction1,
            )
        }

        fun checkDoubleDropdownCardDtoTitle2AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).title2)
        }

        fun checkDoubleDropdownCardDtoParam2AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).param2)
        }

        fun checkDoubleDropdownCardDtoSelectedOption2AtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).selectedOption2,
            )
        }

        fun checkDoubleDropdownCardDtoOptions2SizeAtPosition(
            position: Int,
            expected: Int,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).options2.size,
            )
        }

        fun checkDoubleDropdownCardDtoChangeAction2AtPosition(
            position: Int,
            expected: DropdownCardDto.Action?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).changeAction2,
            )
        }

        fun checkDoubleDropdownCardDtoSeparatorAtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as DoubleDropdownCardDto).separatorText,
            )
        }

        fun checkFilterLinkCardDtoTextAtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as LinkCardDto.Filter).text)
        }

        fun checkFilterLinkCardDtoDestinationRouteAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as LinkCardDto.Filter).destination.route,
            )
        }

        fun checkFilterLinkCardDtoParamsAtPosition(
            position: Int,
            expected: String?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as LinkCardDto.Filter).params.toString(),
            )
        }

        fun checkFilterLinkCardDtoSelectedValuesSizeAtPosition(
            position: Int,
            expected: Int?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as LinkCardDto.Filter).selectedValue?.size,
            )
        }

        fun checkFilterLinkCardDtoSubTitleAtPosition(
            position: Int,
            expected: LinkCardDto.Filter.Action?,
        ) {
            assertEquals(expected, (actualAttributesRowLayoutsResult[position].data[0] as LinkCardDto.Filter).subTitle)
        }

        fun checkFilterLinkCardDtoChangeActionAtPosition(
            position: Int,
            expected: LinkCardDto.Filter.Action?,
        ) {
            assertEquals(
                expected,
                (actualAttributesRowLayoutsResult[position].data[0] as LinkCardDto.Filter).changeAction,
            )
        }
    }
}
