package features.vip

import api.phoneNumber.api.PhoneNumberApi
import api.similarItems.api.SimilarItemsApi
import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.capi.apis.CapiAdsApi
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.capi.apis.CapiReplyApi
import com.gumtree.mobile.api.common.UserProfileService
import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.papi.PapiHeadersProvider
import com.gumtree.mobile.api.partnerships.api.PartnershipAdsApi
import com.gumtree.mobile.api.partnerships.models.PartnershipDetailsResponse
import com.gumtree.mobile.api.userProfile.api.UserProfileApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.OtherInfoCardDto
import com.gumtree.mobile.features.screens.layoutsData.SpecificationDoubleColumnItem
import com.gumtree.mobile.features.screens.layoutsData.VipKeyInfoCardDto
import com.gumtree.mobile.features.sellerProfile.v2.DefaultSellerProfileActiveStatusFetcher
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.features.vip.*
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import common.AdjustTrackingData
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.DataFactory.SOME_COMMON_ANALYTICS_KEY
import tools.DataFactory.SOME_COMMON_ANALYTICS_VALUE
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawLocationFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.rawDataFactory.stringToDate
import utils.TestDispatcherProvider
import kotlin.test.assertNotEquals

class VipRepositoryVerticalsTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should read screen and return cars vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.CARS.id) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubPartnershipAdsResponseFailure() }
            When { readScreen() }
            Then { checkCarsVIPSectionReturned() }
        }
    }

    @Test
    fun `should read screen and return normal for sale vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            When { readScreen() }
            Then { checkNormalVIPSectionReturned() }
        }
    }

    @Test
    fun `should read screen and return properties vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForProperties() }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            When { readScreen() }
            Then { checkPropertiesVIPSectionReturned() }
            Then { checkPropertiesPostedSinceRowReturned() }
        }
    }

    @Test
    fun `should read screen and return pets vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForPets() }
            Given { stubRawBaseUserProfile(DataFactory.ANOTHER_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            When { readScreen() }
            Then { checkPetsKeyInfoRowReturned() }
            Then { checkPetsPostedSinceRowReturned() }
        }
    }

    @Test
    fun `should read screen and return jobs vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForJobs() }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            When { readScreen() }
            Then { checkJobsKeyInfoRowReturned() }
        }
    }

    @Test
    fun `should read screen and return vat info`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdVAT(DataFactory.SOME_VAT) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            When { readScreen() }
            Then { checkVATInfoReturned() }
        }
    }

    @Test
    fun `should return screen view analytics event name`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForPets() }
            Given { stubRawBaseUserProfile(DataFactory.ANOTHER_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubVipViewScreenEvent() }
            When { readScreen() }
            Then { checkScreenViewAnalyticsEvent(AnalyticsEventData(DataFactory.SOME_ANALYTICS_EVENT_NAME)) }
        }
    }

    @Test
    fun `should return screen analytics parameters`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForPets() }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubScreenAnalyticsParams(mapOf(SOME_COMMON_ANALYTICS_KEY to SOME_COMMON_ANALYTICS_VALUE)) }
            When { readScreen() }
            Then { checkScreenAnalyticsParameters(mapOf(SOME_COMMON_ANALYTICS_KEY to SOME_COMMON_ANALYTICS_VALUE)) }
        }
    }

    @Test
    fun `should return screen adverts data for all categories`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.CARS.id) }
            Given { stubRawBaseUserProfile(DataFactory.ANOTHER_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            When { readScreen() }
            Then { checkAdvertDataSize(3) }

            Given { stubRawAdForPets() }
            When { readScreen() }
            Then { checkAdvertDataSize(3) }

            Given { stubRawAdForJobs() }
            When { readScreen() }
            Then { checkAdvertDataSize(3) }

            Given { stubRawAdForProperties() }
            When { readScreen() }
            Then { checkAdvertDataSize(3) }
        }
    }

    @Test
    fun `should return adjust tracking data for cars vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.CARS.id) }
            Given { stubRawBaseUserProfile(DataFactory.ANOTHER_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubAdjustTrackingData() }
            When { readScreen() }
            Then { checkAdjustTrackingData() }
        }
    }

    @Test
    fun `should return adjust tracking data for pets vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForPets() }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubAdjustTrackingData() }
            When { readScreen() }
            Then { checkAdjustTrackingData() }
        }
    }

    @Test
    fun `should return adjust tracking data for properties vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForProperties() }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubAdjustTrackingData() }
            When { readScreen() }
            Then { checkAdjustTrackingData() }
        }
    }

    @Test
    fun `should return adjust tracking data for jobs vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdForJobs() }
            Given { stubRawBaseUserProfile(DataFactory.ANOTHER_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubAdjustTrackingData() }
            When { readScreen() }
            Then { checkAdjustTrackingData() }
        }
    }

    @Test
    fun `should return adjust tracking data for default vertical`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubAdjustTrackingData() }
            When { readScreen() }
            Then { checkAdjustTrackingData() }
        }
    }

    @Test
    fun `should return partnership row for applicable ad in for sale category AND not return GAM advert`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubPartnershipAdsData() }
            When { readScreen() }
            Then { checkExternalPartnershipRowReturned(4) }
            Then { checkAdvertisingRowNotReturned() }
        }
    }

    @Test
    fun `should NOT return partnership row for ad in for sale category AND should return GAM advert`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubPartnershipAdsResponseFailure() }
            When { readScreen() }
            Then { checkExternalPartnershipRowNotReturned() }
            Then { checkAdvertisingRowReturned() }
        }
    }

    @Test
    fun `should return partnership row for ad if partnership service returns a response`() = runTest {
        runUnitTest(robot) {
            Given { stubRawAdWithCategoryId(CategoryDefaults.CARS.id) }
            Given { stubRawBaseUserProfile(DataFactory.SOME_USER_REGISTRATION_DATE_TIME) }
            Given { stubUserProfileData(null) }
            Given { stubPartnershipAdsData() }
            When { readScreen() }
            Then { checkExternalPartnershipRowReturned(5) }
        }
    }

    private class Robot : BaseRobot {

        private lateinit var headersBuilder: HeadersBuilder
        private var userProfileData: UserProfileData? = null
        private val capiAdsApi: CapiAdsApi = mockk(relaxed = true)
        private val capiReplyApi: CapiReplyApi = mockk(relaxed = true)
        private val conversationApi: CapiConversationApi = mockk(relaxed = true)
        private val papiSimilarItemsApi: SimilarItemsApi = mockk(relaxed = true)
        private val userProfileApi: UserProfileApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val phoneNumberApi: PhoneNumberApi = mockk(relaxed = true)
        private val partnershipAdsApi: PartnershipAdsApi = mockk(relaxed = true)
        private val conversationsApi: ConversationsApi = mockk(relaxed = true)
        private val coreChatAuthApi: CoreChatAuthApi = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)
        private val vipTimeAgoFormatter = VipTimeAgoFormatter(createUKDateTimeFormatter(VipScreenUiConfiguration.VIP_POSTED_SINCE_DATE_FORMAT))
        private val sellerProfileActiveStatusFormatter = SellerProfileActiveStatusFormatter(createUKDateTimeFormatter(SellerProfileScreenUiConfiguration.SELLER_PROFILE_ACTIVE_STATUS_DATE_FORMAT))
        private val vipService =
            VipService(
                capiAdsApi,
                capiReplyApi,
                papiSimilarItemsApi,
                userProfileApi,
                phoneNumberApi,
                partnershipAdsApi,
            )
        private val userProfileService = UserProfileService(mockk(relaxed = true))
        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val staticMapFactory: StaticMapFactory = mockk(relaxed = true)
        private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider = mockk(relaxed = true)
        private val vipAnalyticsProvider: VipAnalyticsProvider = mockk(relaxed = true)
        private val vipSpecificationFactory = VipSpecificationFactory()
        private val vipHpiFactory = VipHpiFactory()
        private val titleFactory = TitleFactory()
        private val vipPartnershipAnalyticsProvider = VipPartnershipAnalyticsProvider()
        private val vipCarsUIProvider = VipVerticalsUIProvider(
            vipSpecificationFactory,
            vipHpiFactory,
            titleFactory,
            vipPartnershipAnalyticsProvider
        )
        private val vipPartnershipAdvertsFactory = VipPartnershipAdvertsFactory(VipPartnershipAnalyticsProvider())
        private val vipImageGalleryMapper = VipImageGalleryMapper()
        private val vipTitleMapper = VipTitleMapper()
        private val vipPriceMapper = VipPriceMapper()
        private val vipLocationMapper = VipLocationMapper(locationsApi)
        private val vipDescriptionMapper = VipDescriptionMapper()
        private val vipPostedSinceMapper = VipPostedSinceMapper(vipTimeAgoFormatter)
        private val vipSellerProfileMapper = VipSellerProfileMapper(sellerProfileActiveStatusFormatter)
        private val vipMapMapper = VipMapMapper(locationsApi = locationsApi, staticMapFactory = staticMapFactory)
        private val vipSimilarItemsMapper = VipSimilarItemsMapper(vipAnalyticsProvider = vipAnalyticsProvider)
        private val otherInfoMapper = OtherInfoMapper()
        private val vipToolbarActionsProvider = VipToolbarActionsProvider(vipAdjustTrackingDataProvider)
        private val capiHeadersProvider = CapiHeadersProvider()
        private val papiHeadersProvider = PapiHeadersProvider()
        private val vipAdvertsProvider = VipAdvertsProvider(VipAdvertsFactory(), GAMAdvertUtils(CategoriesTreeCache))
        private val rawLocationFetcher = RawLocationFactory.createRawLocationFetcher()
        private val sellerProfileActiveStatusFetcher = DefaultSellerProfileActiveStatusFetcher(userServiceApi, coreChatAuthApi, conversationsApi)
        private val dispatcherProvider = TestDispatcherProvider()
        private val vipDefaultsUIProvider = VipDefaultsUIProvider(
            vipImageGalleryMapper,
            vipTitleMapper,
            vipPriceMapper,
            vipLocationMapper,
            vipDescriptionMapper,
            vipPostedSinceMapper,
            vipSellerProfileMapper,
            vipMapMapper,
            vipPartnershipAdvertsFactory,
            vipSimilarItemsMapper,
            otherInfoMapper,
            vipToolbarActionsProvider,
            vipAnalyticsProvider,
            titleFactory,
            dispatcherProvider,
        )

        private val bottomOverlayUIProvider = VipBottomOverlayUIProvider(
            conversationApi,
            capiHeadersProvider,
            vipAnalyticsProvider,
            vipAdjustTrackingDataProvider,
            dispatcherProvider,
        )

        private lateinit var testSubject: DefaultVipRepository
        private lateinit var screenResponse: ScreenResponse

        override fun setup() {
            testSubject = DefaultVipRepository(
                vipService,
                userProfileService,
                vipDefaultsUIProvider,
                bottomOverlayUIProvider,
                vipCarsUIProvider,
                vipAdvertsProvider,
                rawLocationFetcher,
                sellerProfileActiveStatusFetcher,
                papiHeadersProvider,
                capiHeadersProvider,
                vipAnalyticsProvider,
                vipAdjustTrackingDataProvider,
                dispatcherProvider,
            )
            headersBuilder = HeadersBuilder()
        }

        fun stubRawAdWithCategoryId(id: String) {
            val rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(
                id,
                RawCapiAdsFactory.createRawCapiAttributesNamesArray()
            )
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawAdForProperties() {
            val rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(
                CategoryDefaults.PROPERTIES.id,
                RawCapiAdsFactory.createRawCapiAttributesNamesArrayForProperties(),
                DataFactory.SOME_AD_START_DATE_TIME
            )
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawAdForPets() {
            val rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(
                CategoryDefaults.PETS.id,
                RawCapiAdsFactory.createRawCapiAttributesNamesArrayForPets(),
                DataFactory.SOME_AD_START_DATE_TIME
            )
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawAdForJobs() {
            val rawAd = RawCapiAdsFactory.createRawCapiAdWithAttributes(
                CategoryDefaults.JOBS.id,
                RawCapiAdsFactory.createRawCapiAttributesNamesArrayForJobs(),
                DataFactory.SOME_AD_START_DATE_TIME
            )
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawAdVAT(vat: String) {
            val rawAd = RawCapiAdsFactory.createRawCapiAd(vat = vat)
            coEvery { capiAdsApi.getAdDetail(any(), any()) } returns rawAd
        }

        fun stubRawBaseUserProfile(registrationDate: String) {
            val rawUserProfile = RawPapiUserProfileFactory.createRawUserProfile(
                registrationDate = stringToDate(registrationDate),
                postingSinceDate = stringToDate(registrationDate),
            )
            coEvery { userProfileApi.getBaseUserProfile(any(), any()) } returns rawUserProfile
        }

        fun stubUserProfileData(userProfile: UserProfileData?) {
            userProfileData = userProfile
        }


        fun stubVipViewScreenEvent() {
            every { vipAnalyticsProvider.getScreenViewEvent() } returns AnalyticsEventData(DataFactory.SOME_ANALYTICS_EVENT_NAME)
        }

        fun stubScreenAnalyticsParams(params: Map<String, String>) {
            coEvery { vipAnalyticsProvider.getScreenParams(any(), any(), any()) } returns params
        }

        fun stubAdjustTrackingData() {
            every { vipAdjustTrackingDataProvider.getScreenTrackingData(any()) } returns AdjustTrackingData(
                DataFactory.SOME_TOKEN,
                mapOf(VipAdjustTrackingDataProvider.LISTING_ID_KEY to DataFactory.SOME_AD_ID)
            )
        }

        fun stubPartnershipAdsResponseFailure() {
            coEvery { partnershipAdsApi.getPartnershipDetails(any(), any(), any(), any(), any(), any(), any(), any()) } throws UnauthorisedException()
        }

        fun stubPartnershipAdsData() {
            coEvery { partnershipAdsApi.getPartnershipDetails(any(), any(), any(), any(), any(), any(), any(), any()) } returns PartnershipDetailsResponse(
                DataFactory.SOME_AD_ID,
                DataFactory.SOME_PARTNERSHIP_ADVERT_ICON_URL,
                DataFactory.SOME_AD_TITLE,
                DataFactory.SOME_EXTERNAL_URL
            )
        }

        suspend fun readScreen() {
            screenResponse = testSubject.readScreen(callHeaders, DataFactory.SOME_AD_ID, userProfileData)
        }

        fun checkScreenViewAnalyticsEvent(expected: AnalyticsEventData) {
            assertEquals(expected, screenResponse.screenViewAnalyticsEvent)
        }

        fun checkScreenAnalyticsParameters(expected: Map<String, String>) {
            assertEquals(expected, screenResponse.analyticsParameters)
        }

        fun checkCarsVIPSectionReturned() {
            assertEquals(RowLayoutType.CHIPS_ROW, screenResponse.portraitData[4].type)
            assertEquals(RowLayoutType.VIP_SPECIFICATION_ROW, screenResponse.portraitData[8].type)
        }

        fun checkNormalVIPSectionReturned() {
            val imageGalleryRow = screenResponse.portraitData[0]
            assertEquals(RowLayoutType.VIP_IMAGE_GALLERY_ROW, imageGalleryRow.type)
            val titleRow = screenResponse.portraitData[1]
            assertEquals(RowLayoutType.VIP_TITLE_ROW, titleRow.type)
        }

        fun checkPropertiesVIPSectionReturned() {
            val keyInfoRow = screenResponse.portraitData[7]
            val keyInfoCard: VipKeyInfoCardDto = keyInfoRow.data.first() as VipKeyInfoCardDto
            val specItem: SpecificationDoubleColumnItem = keyInfoCard.infoItems.first() as SpecificationDoubleColumnItem
            assertEquals(DataFactory.SOME_PROPERTY_LABEL, specItem.label)
        }

        fun checkPropertiesPostedSinceRowReturned() {
            val postedSinceRow = screenResponse.portraitData[5]
            assertEquals(RowLayoutType.VIP_POSTED_SINCE_ROW, postedSinceRow.type)
        }

        fun checkPetsKeyInfoRowReturned() {
            val keyInfoRow = screenResponse.portraitData[7]
            val keyInfoCard: VipKeyInfoCardDto = keyInfoRow.data.first() as VipKeyInfoCardDto
            val specItem: SpecificationDoubleColumnItem = keyInfoCard.infoItems.first() as SpecificationDoubleColumnItem
            assertEquals(DataFactory.SOME_PET_LABEL, specItem.label)
            assertEquals(DataFactory.SOME_PET_VALUE, specItem.value)
        }

        fun checkJobsKeyInfoRowReturned() {
            val keyInfoRow = screenResponse.portraitData[4]
            val keyInfoCard: VipKeyInfoCardDto = keyInfoRow.data.first() as VipKeyInfoCardDto
            val specItem: SpecificationDoubleColumnItem = keyInfoCard.infoItems.first() as SpecificationDoubleColumnItem
            assertEquals(DataFactory.SOME_JOB_LABEL, specItem.label)
            assertEquals(DataFactory.SOME_JOB_VALUE, specItem.value)
        }

        fun checkPetsPostedSinceRowReturned() {
            assertEquals(RowLayoutType.VIP_POSTED_SINCE_ROW, screenResponse.portraitData[5].type)
        }

        fun checkVATInfoReturned() {
            assertEquals(RowLayoutType.OTHER_INFO_ROW, screenResponse.portraitData.last().type)
            assertEquals(
                "${VipScreenUiConfiguration.VAT_PREXIF_TEXT} ${DataFactory.SOME_VAT}",
                (screenResponse.portraitData.last().data[0] as OtherInfoCardDto).text
            )
        }

        fun checkExternalPartnershipRowReturned(position: Int) {
            assertEquals(RowLayoutType.EXTERNAL_PARTNERSHIP_ADVERT_ROW, screenResponse.portraitData[position].type)
        }

        fun checkExternalPartnershipRowNotReturned() {
            assertNotEquals(RowLayoutType.EXTERNAL_PARTNERSHIP_ADVERT_ROW, screenResponse.portraitData[4].type)
        }

        fun checkAdvertisingRowReturned() {
            assertEquals(RowLayoutType.ADVERTISING_ROW, screenResponse.portraitData[5].type)
        }

        fun checkAdvertisingRowNotReturned() {
            assertNotEquals(RowLayoutType.ADVERTISING_ROW, screenResponse.portraitData[6].type)
        }

        fun checkAdvertDataSize(expected: Int) {
            assertEquals(expected, screenResponse.gamAdvertsData?.size)
        }

        fun checkAdjustTrackingData() {
            assertNotNull(screenResponse.adjustTrackingData)
            assertEquals(DataFactory.SOME_TOKEN, screenResponse.adjustTrackingData?.eventToken)
            assertEquals(
                mapOf(VipAdjustTrackingDataProvider.LISTING_ID_KEY to DataFactory.SOME_AD_ID),
                screenResponse.adjustTrackingData?.parameters
            )
        }
    }
}