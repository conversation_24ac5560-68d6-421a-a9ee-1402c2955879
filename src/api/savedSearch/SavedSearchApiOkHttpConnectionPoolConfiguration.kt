package com.gumtree.mobile.api.savedSearch

import com.gumtree.mobile.api.common.OkHttpConnectionPoolConfiguration
import java.util.concurrent.TimeUnit

// https://github.com/Netflix/Hystrix/wiki/Configuration#ThreadPool 100 RPS ×0.80 seconds = 80
const val SAVED_SEARCH_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS = 80
const val SAVED_SEARCH_API_CONNECTION_POOL_KEEP_ALIVE_DURATION = 300L

class SavedSearchApiOkHttpConnectionPoolConfiguration : OkHttpConnectionPoolConfiguration {

    override val maxIdleConnections: Int = SAVED_SEARCH_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS

    override val keepAliveDuration: Long = SAVED_SEARCH_API_CONNECTION_POOL_KEEP_ALIVE_DURATION

    override val timeUnit: TimeUnit = TimeUnit.SECONDS
}
