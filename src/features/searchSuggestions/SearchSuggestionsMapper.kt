package com.gumtree.mobile.features.searchSuggestions

import api.capi.models.RawCapiSearchSuggestions
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.CategoryDefaults.ALL_CATEGORIES
import com.gumtree.mobile.utils.extensions.getPrefixAndSuffix
import com.gumtree.mobile.utils.extensions.toStringQuery

class SearchSuggestionsMapper {

    fun map(
        rawCapiSearchSuggestions: RawCapiSearchSuggestions,
        searchTerm: String,
        commonAnalyticsEventData: AnalyticsEventData,
    ): List<SearchSuggestionResultDto> {
        return rawCapiSearchSuggestions.rawSearchSuggestionList.map {

            val category = it.categories.firstOrNull()?.let { rawCategory ->
                SearchSuggestionCategoryDto(rawCategory.categoryId, rawCategory.localizedName)
            } ?: SearchSuggestionCategoryDto(ALL_CATEGORIES.id, ALL_CATEGORIES.text)

            val suggestionText: String = it.keyword
            val boldText = if (suggestionText.contains(searchTerm, true)) searchTerm else EMPTY_STRING
            val (prefix, suffix) = suggestionText.getPrefixAndSuffix(searchTerm)

            val queryParams = mapOf(
                CapiApiParams.Q to suggestionText,
                CapiApiParams.CATEGORY_ID to category.id,
                AnalyticsParams.Search.SUGGESTION_TYPE to com.gumtree.mobile.features.searchSuggestions.v2.ANALYTICS_SUGGESTED_SUGGESTION_TYPE_VALUE,
            )

            SearchSuggestionResultDto(
                searchTerm = suggestionText,
                prefix = prefix,
                boldText = boldText,
                suffix = suffix,
                category = category,
                destination = DestinationRoute.SRP.build(
                    queryParams = queryParams,
                    analyticsEvent = AnalyticsEventData(
                        eventName = commonAnalyticsEventData.eventName,
                        parameters = commonAnalyticsEventData.parameters?.let { params ->
                            params + SearchSuggestionsAnalyticsProvider.getSearchSuggestionParams(
                                searchSuggestionCategory = category.id,
                                searchSuggestionSearchTermSelected = suggestionText,
                            )
                        },
                    ),
                ),
                params = queryParams.toStringQuery(),
            )
        }
    }
}
