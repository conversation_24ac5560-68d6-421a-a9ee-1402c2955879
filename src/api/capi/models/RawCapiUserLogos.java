package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

@Root(name = "user-logos")
@Namespace(reference = Namespaces.USER, prefix = Namespaces.Prefix.USER)
public class RawCapiUserLogos {

    @Namespace(reference = Namespaces.PICTURE, prefix = Namespaces.Prefix.PICTURE)
    @ElementList(entry = "picture", inline = true, required = false)
    public List<RawUserPicture> rawUserPictures = new ArrayList<>();

    public RawCapiUserLogos() {}

    public RawCapiUserLogos(@ElementList(entry = "picture", inline = true, required = false) List<RawUserPicture> rawUserPictures) {
        this.rawUserPictures = rawUserPictures;
    }

    public void setRawUserPictures(List<RawUserPicture> rawUserPictures) {
        this.rawUserPictures = rawUserPictures;
    }

    @Nullable
    public RawUserPicture getPictureByType(String type) {
        for (RawUserPicture rawUserLogosPicture : rawUserPictures) {
            if (rawUserLogosPicture.getRel().equals(type)) {
                return rawUserLogosPicture;
            }
        }
        return null;
    }
}
