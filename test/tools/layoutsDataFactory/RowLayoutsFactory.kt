package tools.layoutsDataFactory

import com.gumtree.mobile.adverts.gam.GAMAdvertSize
import com.gumtree.mobile.features.homeFeed.GAM_ANDROID_HOME_FEED_UNIT_ID
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_HPTO_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_MIDDLE_1_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_MIDDLE_2_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_MIDDLE_3_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_HOME_FEED_TOP_SLOT
import com.gumtree.mobile.features.homeFeed.GAM_IOS_HOME_FEED_UNIT_ID
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import com.gumtree.mobile.features.screens.layoutsData.ListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.utils.extensions.isNotNull
import okhttp3.internal.toImmutableList
import tools.DataFactory


object RowLayoutsFactory {

    /**
     * Generate a List with number of RowLayouts, useful in tests when the RowLayout type does not matter
     * @param number - the number of RowLayouts to be generated/stubbed
     * @return - a list with N RowLayouts or NULL if the number == NULL
     */
    fun createRowLayouts(number: Int?): List<RowLayout<UiItem>>? {
        return when {
            number.isNotNull() -> mutableListOf<RowLayout<UiItem>>().apply {
                repeat(number) {
                    add(
                        RowLayout(
                            type = RowLayoutType.LISTING_ROW,
                            data = listOf(
                                ListingCardDto(
                                    DataFactory.anyString(),
                                    DataFactory.anyString(),
                                    DataFactory.anyDestination()
                                )
                            )
                        )
                    )
                }
            }
            else -> null
        }
    }

    /**
     * Stub HomeFeed PortraitData for testing
     * @param number - the number of portrait data items to stub
     */
    @Suppress("unused")
    fun createPortraitHomeFeedListingsRowLayout(
        number: Int
    ): PortraitData {
        return with(mutableListOf<RowLayout<UiItem>>()) {
            for (i in 1..number) {
                add(
                    RowLayout(
                        type = RowLayoutType.LISTING_ROW,
                        data = emptyList()
                    )
                )
            }
            this.toImmutableList()
        }
    }

    /**
     * Stub HomeFeed PortraitData with GAM adverts for testing
     * @param number - the number of portrait data items to stub
     */
    fun createPortraitHomeFeedListingsRowLayoutWithAllAdverts(number: Int): PortraitData {
        val advertRowLayouts = with(mutableListOf<RowLayout<UiItem>>()) {
            add(createHomeFeedHptoAdvertRow())
            add(createHomeFeedTopAdvertRow())
            add(createHomeFeedMiddle1AdvertRow())
            add(createHomeFeedMiddle2AdvertRow())
            add(createHomeFeedMiddle3AdvertRow())
            this
        }
        return with(mutableListOf<RowLayout<UiItem>>()) {
            for (i in 1..number) {
                add(
                    RowLayout(
                        type = RowLayoutType.LISTING_ROW,
                        data = emptyList()
                    )
                )
            }
            this.addAll(advertRowLayouts)
            this.toImmutableList()
        }
    }

    /**
     * Stub HomeFeed LandscapeData for testing
     * @param number - the number of landscape data items to stub
     */
    @Suppress("unused")
    fun createLandscapeHomeFeedListingsRowLayout(
        number: Int = 0
    ): LandscapeData? {
        return when (number) {
            0 -> null
            else -> with(mutableListOf<RowLayout<UiItem>>()) {
                for (i in 1..number) {
                    add(
                        RowLayout(
                            type = RowLayoutType.LISTING_ROW,
                            data = emptyList()
                        )
                    )
                }
                this.toImmutableList()
            }
        }
    }

    /**
     * Stub HomeFeed SavedSearchesRowLayout for testing
     */
    @Suppress("Unused")
    fun createHomeFeedSavedSearchesRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.HOME_FEED_SAVED_SEARCHES_ROW,
            data = emptyList()
        )
    }

    /**
     * Stub HomeFeed hpto Advert RowLayout for testing
     */
    fun createHomeFeedHptoAdvertRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.ADVERTISING_ROW,
            data = listOf(
                GAMAdvertDto(
                    androidUnitId = GAM_ANDROID_HOME_FEED_UNIT_ID,
                    iosUnitId = GAM_IOS_HOME_FEED_UNIT_ID,
                    slotName = GAM_HOME_FEED_HPTO_SLOT,
                    displaySize = listOf(GAMAdvertSize.Ad300x250),
                    attributes = emptyMap()
                )
            )
        )
    }

    /**
     * Stub HomeFeed top Advert RowLayout for testing
     */
    fun createHomeFeedTopAdvertRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.ADVERTISING_ROW,
            data = listOf(
                GAMAdvertDto(
                    androidUnitId = GAM_ANDROID_HOME_FEED_UNIT_ID,
                    iosUnitId = GAM_IOS_HOME_FEED_UNIT_ID,
                    slotName = GAM_HOME_FEED_TOP_SLOT,
                    displaySize = listOf(GAMAdvertSize.Ad300x250),
                    attributes = emptyMap()
                )
            )
        )
    }

    /**
     * Stub HomeFeed middle1 Advert RowLayout for testing
     */
    fun createHomeFeedMiddle1AdvertRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.ADVERTISING_ROW,
            data = listOf(
                GAMAdvertDto(
                    androidUnitId = GAM_ANDROID_HOME_FEED_UNIT_ID,
                    iosUnitId = GAM_IOS_HOME_FEED_UNIT_ID,
                    slotName = GAM_HOME_FEED_MIDDLE_1_SLOT,
                    displaySize = listOf(GAMAdvertSize.Ad300x250),
                    attributes = emptyMap()
                )
            )
        )
    }

    /**
     * Stub HomeFeed middle2 Advert RowLayout for testing
     */
    fun createHomeFeedMiddle2AdvertRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.ADVERTISING_ROW,
            data = listOf(
                GAMAdvertDto(
                    androidUnitId = GAM_ANDROID_HOME_FEED_UNIT_ID,
                    iosUnitId = GAM_IOS_HOME_FEED_UNIT_ID,
                    slotName = GAM_HOME_FEED_MIDDLE_2_SLOT,
                    displaySize = listOf(GAMAdvertSize.Ad300x250),
                    attributes = emptyMap()
                )
            )
        )
    }

    /**
     * Stub HomeFeed middle3 Advert RowLayout for testing
     */
    fun createHomeFeedMiddle3AdvertRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.ADVERTISING_ROW,
            data = listOf(
                GAMAdvertDto(
                    androidUnitId = GAM_ANDROID_HOME_FEED_UNIT_ID,
                    iosUnitId = GAM_IOS_HOME_FEED_UNIT_ID,
                    slotName = GAM_HOME_FEED_MIDDLE_3_SLOT,
                    displaySize = listOf(GAMAdvertSize.Ad300x250),
                    attributes = emptyMap()
                )
            )
        )
    }
}
