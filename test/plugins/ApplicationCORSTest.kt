package plugins

import io.ktor.http.*
import io.ktor.server.plugins.cors.*
import io.mockk.coVerifyOrder
import io.mockk.spyk
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class ApplicationCORSTest {

    private val robot = Robot()

    @Test
    fun `should initialize CORS`() {
        runUnitTest(robot) {
            When { initializeCORS() }
            Then { checkInitializeCORSSetupOrder() }
        }
    }

    private class Robot: BaseRobot {

        private val testSubject = spyk(CORSConfig())

        fun initializeCORS() {
            testSubject.initializeCORS()
        }

        fun checkInitializeCORSSetupOrder() {
            coVerifyOrder {
                testSubject.anyHost()
                testSubject.allowCredentials = true
                testSubject.allowNonSimpleContentTypes = true
                testSubject.allowMethod(HttpMethod.Get)
                testSubject.allowMethod(HttpMethod.Post)
                testSubject.allowMethod(HttpMethod.Put)
                testSubject.allowMethod(HttpMethod.Delete)
                testSubject.allowMethod(HttpMethod.Patch)
                testSubject.allowMethod(HttpMethod.Options)
                testSubject.allowHeader(HttpHeaders.ContentType)
            }
        }
    }
}