package features.reviews

import com.gumtree.mobile.api.reviews.models.RawPostReview
import com.gumtree.mobile.features.reviews.CreateReviewResponseDto
import com.gumtree.mobile.features.reviews.ReviewsMapper
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.runUnitTest
import tools.When
import kotlin.test.assertEquals

class ReviewsMapperTest {

    private val robot = Robot()

    @Test
    fun `should return review response with review id`() {
        runUnitTest(robot) {
            When { mapCreateReviewResponse(RawPostReview(reviewId = DataFactory.SOME_REVIEW_ID)) }
            When { checkReviewResponse(CreateReviewResponseDto(reviewId = DataFactory.SOME_REVIEW_ID)) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualReviewResponseDtoResult: CreateReviewResponseDto
        private val testSubject = ReviewsMapper()

        fun mapCreateReviewResponse(rawData: RawPostReview) {
            actualReviewResponseDtoResult = testSubject.mapCreateReviewResponse(rawData)
        }

        fun checkReviewResponse(expected: CreateReviewResponseDto) {
            assertEquals(expected, actualReviewResponseDtoResult)
        }
    }
}