package com.gumtree.mobile.features.reportChat

import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenRequestBody
import com.gumtree.mobile.api.reportChat.ReportChatHeadersProvider
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.UserProfileData
import io.ktor.server.plugins.BadRequestException
import kotlinx.coroutines.withContext

fun interface ReportChatRepository {

    suspend fun createChatReport(
        reportChatRequest: ReportChatRequest,
        userProfile: UserProfileData,
    )
}

class DefaultReportChatRepository(
    private val reportChatService: ReportChatService,
    private val reportChatBodyCreator: ReportChatBodyCreator,
    private val dispatcherProvider: DispatcherProvider,
) : ReportChatRepository {

    override suspend fun createChatReport(
        reportChatRequest: ReportChatRequest,
        userProfile: UserProfileData,
    ) {
        withContext(dispatcherProvider.io) {
            reportChatBodyCreator.createBody(reportChatRequest)?.let {
                val coreChatStreamTokenResponse = withContext(dispatcherProvider.io) {
                    reportChatService.generateCoreChatJwt(
                        CoreChatStreamTokenRequestBody(userProfile.userEmail, userProfile.userId),
                    )
                }

                withContext(dispatcherProvider.io) {
                    reportChatService.reportChat(
                        it,
                        ReportChatHeadersProvider.createAuthHeader(coreChatStreamTokenResponse.token),
                    )
                }
            } ?: throw BadRequestException("Invalid counterPartyId or adId")
        }
    }
}
