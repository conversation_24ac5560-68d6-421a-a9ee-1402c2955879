openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-saved-searches
  license:
    name: MIT
tags:
  - name: BFF-SavedSearches
    description: Mobile Apps BFF SavedSearches API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  securitySchemes:
    ApiKeyAuthUserEmail:
      type: apiKey
      description: Security API key to authorize requests with an user email
      in: header
      name: Authorisation-User-Email
    ApiKeyAuthUserToken:
      type: apiKey
      description: Security API key to authorize requests with an user token
      in: header
      name: Authorisation-User-Token
  parameters:
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    PageQuery:
      in: query
      name: page
      description: Specify which page of the list with items
      required: false
      schema:
        type: integer
        format: int32
        default: 0
    SearchSuggestionsQuery:
      in: query
      name: searchSuggestions
      description: Specify which type of saved search card to return
      required: false
      schema:
        type: boolean
        default: false
    SavedSearchId:
      in: path
      name: id
      description: the saved search id to be deleted
      required: true
      schema:
        type: string
  schemas:
    Error:
      type: object
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
      required:
        - code
        - message
    ScreenResponse:
      type: object
      properties:
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        landscapeData:
          $ref: '#/components/schemas/RowLayoutItems'
        nextPage:
          type: string
      required:
        - portraitData
    RowLayoutItems:
      type: array
      items:
        $ref: '#/components/schemas/RowLayout'
    RowLayout:
      type: object
      properties:
        type:
          type: string
          enum:
            - FAVOURITES_SAVED_SEARCHES_ROW
            - SEARCH_SUGGESTIONS_SAVED_SEARCHES_ROW
        data:
          $ref: '#/components/schemas/SavedSearchItems'
        scrollingBehaviour:
          type: string
          enum:
            - COLLAPSE_AT_TOP
            - COLLAPSE_ANYWHERE
        topDivider:
          type: boolean
        bottomDivider:
          type: boolean
      required:
        - type
        - data
    SavedSearchItems:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/SavedSearchCard'
          - $ref: '#/components/schemas/SearchSuggestionSavedSearchCard'
    SavedSearchCard:
      type: object
      properties:
        type:
          type: string
          enum:
            - SAVED_SEARCH_CARD
        id:
          type: string
        title:
          type: string
        subtitle:
          type: string
        price:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        displayAlert:
          type: boolean
      required:
        - id
        - title
        - destination
    SearchSuggestionSavedSearchCard:
      type: object
      properties:
        type:
          type: string
          enum:
            - SEARCH_SUGGESTIONS_SAVED_SEARCH_CARD
        id:
          type: string
        title:
          type: string
        subtitle:
          type: string
        params:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - id
        - title
        - params
        - destination
    SavedSearch:
      type: object
      properties:
        id:
          type: string
        params:
          type: object
          additionalProperties:
            type: string
      required:
        - id
        - params
    SavedSearches:
      type: array
      items:
        $ref: '#/components/schemas/SavedSearch'
    SavedSearchRequest:
      description: New saved search payload data
      type: object
      properties:
        savedSearchParams:
          type: object
          properties:
            locationId:
              type: string
            q:
              type: string
            categoryId:
              type: string
            distance:
              type: string
              enum:
                - ZERO
                - QUARTER
                - HALF
                - ONE
                - THREE
                - FIVE
                - TEN
                - FIFTEEN
                - THIRTY
                - FIFTY
                - SEVENTY_FIVE
                - HUNDRED
                - HUNDRED_FIFTY
                - TWO_HUNDRED_FIFTY
                - FIVE_HUNDRED
                - NATIONWIDE
            minPrice:
              type: string
            maxPrice:
              type: string
            pictureRequired:
              type: boolean
    SavedSearchResponse:
      type: object
      properties:
        id:
          type: string
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
    SavedSearchesStatusResponse:
      type: object
      properties:
        updatedSearches:
          $ref: '#/components/schemas/UpdatedSearches'
    UpdatedSearches:
      type: array
      items:
        $ref: '#/components/schemas/UpdatedSearch'
    UpdatedSearch:
      type: object
      properties:
        id:
          type: string
      required:
        - id
    SavedSearchIdsWithLastSearchedRequest:
      type: object
      description: Map of saved search IDs to their last searched dates
      additionalProperties:
        type: string
        format: date-time
security:
  - ApiKeyAuthUserEmail: []
    ApiKeyAuthUserToken: []
paths:
  /saved-searches/screen:
    get:
      summary: Get the Mobile apps Saved Searches screen (contains all UI items about the SavedSearches screen/tab)
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/PageQuery"
        - $ref: "#/components/parameters/SearchSuggestionsQuery"
      responses:
        '200':
          description: The mobile apps Saved Searches screen UI ready to be rendered on the users device (in favourites screen and search suggestions screen)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Post a map of saved search ids with their last searched dates
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/PageQuery"
        - $ref: "#/components/parameters/SearchSuggestionsQuery"
      requestBody:
        description: Map of saved search ids with their last searched dates
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SavedSearchIdsWithLastSearchedRequest"
      responses:
        '200':
          description: The mobile apps Saved Searches screen UI ready to be rendered on the users device (in favourites screen and search suggestions screen)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /saved-searches:
    get:
      summary:  Get the list of all active saved searches associated to a user
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      responses:
        '200':
          description: Saved searches
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SavedSearches"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Post new saved search
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      requestBody:
        description: savedSearchRequest
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SavedSearchRequest"
      responses:
        '201':
          description: New saved search created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SavedSearchResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /saved-searches/{id}:
    delete:
      summary: Delete saved search
      parameters:
        - $ref: "#/components/parameters/SavedSearchId"
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      responses:
        '204':
          description: Saved search deleted - NoContent
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /saved-searches/status:
    post:
      summary: GET saved searches statuses (updates about new SS results etc.)
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
      requestBody:
        description: Map of saved search ids with their last searched dates
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SavedSearchIdsWithLastSearchedRequest"
      responses:
        '200':
          description: The mobile apps Saved Searches updates results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SavedSearchesStatusResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '401':
          description: Unauthorized request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
