package com.gumtree.mobile.features.myGumtree.extensions

import api.capi.models.RawCapiAd
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.screens.layoutsData.ActionButtonDto
import com.gumtree.mobile.features.screens.layoutsData.ButtonDto
import com.gumtree.mobile.features.screens.layoutsData.NavigationButtonDto
import com.gumtree.mobile.features.screens.layoutsData.RepostAdAction
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import org.jetbrains.annotations.VisibleForTesting

fun RawCapiAd.getMyGumtreeListingButton(): ButtonDto? {
    val status = status.value
    return when {
        status == ACTIVE && featuresActive.size < 2 -> MyGumtreeButton.PROMOTE.toButtonCard(id)
        status == DELETED -> MyGumtreeButton.RESTORE.toButtonCard(id)
        status == EXPIRED -> MyGumtreeButton.REPOST.toButtonCard(id)
        status == PAYABLE || status == CREATED -> MyGumtreeButton.POST_AD.toButtonCard(id)
        status == PAUSED -> MyGumtreeButton.EDIT_AD.toButtonCard(id)
        else -> null
    }
}

@VisibleForTesting
enum class MyGumtreeButton(val text: String) {
    PROMOTE("Promote") {
        override fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData?): ButtonDto {
            return NavigationButtonDto(
                text,
                ButtonDto.Size.SMALL,
                ButtonDto.Type.PRIMARY,
                DestinationRoute.PROMOTE.build(
                    ApiQueryParams.AD_ID to id,
                ),
            )
        }
    },
    RESTORE("Restore") {
        private val pathValue = "repost"
        override fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData?): ButtonDto {
            return NavigationButtonDto(
                text,
                ButtonDto.Size.SMALL,
                ButtonDto.Type.SECONDARY,
                DestinationRoute.SYI.build(
                    ApiQueryParams.AD_ID to id,
                    ApiQueryParams.PATH_VALUE to pathValue,
                ),
            )
        }
    },
    REPOST("Repost") {
        private val pathValue = "repost"
        override fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData?): ButtonDto {
            return NavigationButtonDto(
                text,
                ButtonDto.Size.SMALL,
                ButtonDto.Type.SECONDARY,
                DestinationRoute.SYI.build(
                    ApiQueryParams.AD_ID to id,
                    ApiQueryParams.PATH_VALUE to pathValue,
                ),
            )
        }
    },
    REPOST_FREE("Repost") {
        override fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData?): ButtonDto {
            return ActionButtonDto(
                text,
                ButtonDto.Size.SMALL,
                ButtonDto.Type.SECONDARY,
                ActionButtonDto.Action.REPOST,
                RepostAdAction(id, analyticsEventData),
            )
        }
    },
    POST_AD("Post ad") {
        private val pathValue = "edit"
        override fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData?): ButtonDto {
            return NavigationButtonDto(
                text,
                ButtonDto.Size.SMALL,
                ButtonDto.Type.SECONDARY,
                DestinationRoute.SYI.build(
                    ApiQueryParams.AD_ID to id,
                    ApiQueryParams.PATH_VALUE to pathValue,
                ),
            )
        }
    },
    EDIT_AD("Edit") {
        private val pathValue = "edit"
        override fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData?): ButtonDto {
            return NavigationButtonDto(
                text,
                ButtonDto.Size.SMALL,
                ButtonDto.Type.SECONDARY,
                DestinationRoute.SYI.build(
                    ApiQueryParams.AD_ID to id,
                    ApiQueryParams.PATH_VALUE to pathValue,
                ),
            )
        }
    }, ;

    abstract fun toButtonCard(id: String, analyticsEventData: AnalyticsEventData? = null): ButtonDto?
}
