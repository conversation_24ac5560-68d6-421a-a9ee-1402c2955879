package com.gumtree.mobile.features.screens

import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.runOrNull
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Style(
    @SerialName("spacing")
    val spacing: Spacing?,
) {
    @Serializable
    data class Spacing(
        @SerialName("margins")
        val margins: Dimension? = null,
        @SerialName("paddings")
        val paddings: Dimension? = null,
    ) {
        @Serializable
        data class Dimension(
            @SerialName("left")
            val left: Space? = null,
            @SerialName("top")
            val top: Space? = null,
            @SerialName("right")
            val right: Space? = null,
            @SerialName("bottom")
            val bottom: Space? = null,
        )
    }
}

enum class Space {
    XXX_LARGE, // 96.00dp
    XX_LARGE, // 64.00dp
    X_LARGE, // 48.00dp
    LARGE, // 32.00dp
    MEDIUM, // 24.00dp
    SMALL, // 16.00dp
    X_SMALL, // 12.00dp
    XX_SMALL, // 8.00dp
    XXX_SMALL, // 4.00dp
    XXXX_SMALL, // 2.00dp
}

fun createSpacing(
    leftMargin: Space? = null,
    topMargin: Space? = null,
    rightMargin: Space? = null,
    bottomMargin: Space? = null,
    leftPadding: Space? = null,
    topPadding: Space? = null,
    rightPadding: Space? = null,
    bottomPadding: Space? = null,
): Style.Spacing {
    return Style.Spacing(
        margins = runOrNull(
            leftMargin.isNotNull() || topMargin.isNotNull() || rightMargin.isNotNull() || bottomMargin.isNotNull(),
        ) {
            Style.Spacing.Dimension(
                left = leftMargin,
                top = topMargin,
                right = rightMargin,
                bottom = bottomMargin,
            )
        },
        paddings = runOrNull(
            leftPadding.isNotNull() || topPadding.isNotNull() || rightPadding.isNotNull() || bottomPadding.isNotNull(),
        ) {
            Style.Spacing.Dimension(
                left = leftPadding,
                top = topPadding,
                right = rightPadding,
                bottom = bottomPadding,
            )
        },
    )
}
