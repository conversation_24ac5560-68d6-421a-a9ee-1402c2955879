package features.searchSuggestions.v2

import api.capi.models.RawCapiSearchSuggestions
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.searchSuggestions.v2.DefaultSearchSuggestionsRepository
import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionCategoryDto
import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionResultDto
import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionsMapper
import com.gumtree.mobile.features.searchSuggestions.v2.SearchSuggestionsResponse
import com.gumtree.mobile.requests.ClientPlatform
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.utils.CurrentDateProvider
import io.ktor.http.headers
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import kotlin.test.assertEquals
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.DataFactory.SOME_AD_KEYWORD
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiSearchSuggestionsFactory.createRawSearchSuggestions
import utils.TestDispatcherProvider

class DefaultSearchSuggestionsRepositoryTest {
    private val expectedCategory = SearchSuggestionCategoryDto("1", "cat")
    private val analyticsEvent = AnalyticsEventData(eventName = DataFactory.SOME_ANALYTICS_EVENT_NAME)
    private val expectedDestination = DestinationDto(route = EMPTY_STRING, analyticsEventData = analyticsEvent)
    private val expectedParams = EMPTY_STRING

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @Test
    fun `should get suggestions from capi and map them to correct type`() = runTest {
        val expectedResult = SearchSuggestionsResponse(
            suggestions = listOf(
                SearchSuggestionResultDto(
                    SOME_AD_KEYWORD,
                    EMPTY_STRING,
                    SOME_AD_KEYWORD,
                    EMPTY_STRING,
                    expectedCategory,
                    expectedDestination,
                    expectedParams
                )
            ),
            inputAnalyticsParams = DataFactory.anyAnalyticsEventWithParams().parameters!!
        )

        runUnitTest(robot) {
            Given { stubCapiSearchSuggestions(createRawSearchSuggestions()) }
            Given { stubMapperResult(expectedResult) }
            Given { stubStartTime() }
            Given { stubEndTime() }
            When { getSearchResults(SOME_AD_KEYWORD) }
            Then { checkResults(expectedResult) }
            Then { checkCapiApiCalled(SOME_AD_KEYWORD) }
            Then { checkMapCalled() }
            Then { checkLatency() }
            Then { verifyNoMoreCallsMade() }
        }
    }

    @Test
    fun `should only get the correct number of results`() = runTest {
        val mapperResults = SearchSuggestionsDataFactory.createSearchSuggestionsRespons(10)
        runUnitTest(robot) {
            Given { stubCapiSearchSuggestions(createRawSearchSuggestions()) }
            Given { stubMapperResult(mapperResults) }
            Given { stubStartTime() }
            Given { stubEndTime() }
            When { getSearchResults(SOME_AD_KEYWORD) }
        }
    }

    private inner class Robot : BaseRobot {
        private lateinit var actualResults: SearchSuggestionsResponse

        private val searchApi: CapiSearchApi = mockk()
        private val mapper: SearchSuggestionsMapper = mockk()
        private val startNanoTime = 1000000000L // 1 second in nanos
        private val endNanoTime = 1002000000L   // 1.002 seconds in nanos
        private val expectedLatencyMs = 2L      // Expected latency in milliseconds

        private val testSubject =
            DefaultSearchSuggestionsRepository(searchApi, mapper, TestDispatcherProvider())

        override fun setup() {
            mockkObject(CurrentDateProvider)
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubStartTime() {
            every { CurrentDateProvider.getStartTime() } returns startNanoTime
        }

        fun stubEndTime() {
            every { CurrentDateProvider.getEndTime() } returns endNanoTime
        }

        fun stubCapiSearchSuggestions(suggestions: RawCapiSearchSuggestions) {
            coEvery {
                searchApi.getSearchSuggestions(
                    searchQuery = SOME_AD_KEYWORD,
                    platform = ClientPlatform.IOS.toString(),
                    useFiveEightFlag = true
                )
            } returns suggestions
        }

        fun stubMapperResult(result: SearchSuggestionsResponse) {
            every { mapper.map(any(), any(), any()) } returns result
        }

        suspend fun getSearchResults(keyword: String) {
            val callHeader = headers {
                append(ApiHeaderParams.PLATFORM, ClientPlatform.IOS.toString())
            }
            actualResults = testSubject.getSearchResults(keyword, callHeader)
        }

        fun checkResults(expected: SearchSuggestionsResponse) {
            assertEquals(expected, actualResults)
        }

        fun checkCapiApiCalled(keyword: String) {
            coVerify {
                searchApi.getSearchSuggestions(
                    searchQuery = keyword,
                    platform = ClientPlatform.IOS.toString(),
                    useFiveEightFlag = true
                )
            }
        }

        fun checkMapCalled() {
            verify { mapper.map(any(), any(), any()) }
        }

        fun checkLatency() {
            verify {
                mapper.map(
                    any(),
                    any(),
                    withArg { latencyMs ->
                        assertEquals(expectedLatencyMs, latencyMs)
                    }
                )
            }
        }

        fun verifyNoMoreCallsMade() {
            confirmVerified(searchApi)
            confirmVerified(mapper)
        }
    }
}
