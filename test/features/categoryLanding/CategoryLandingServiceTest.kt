package features.categoryLanding

import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.features.categoryLanding.CategoryLandingService
import com.gumtree.mobile.features.srp.SrpCapiQueryParamsFactory
import com.gumtree.mobile.responses.QueryParams
import io.mockk.coEvery
import io.mockk.coVerifyOrder
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class CategoryLandingServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use Capi search API to get category similar listings`() = runTest {
        runUnitTest(robot) {
            Given { stubPage(DataFactory.SOME_PAGE_NUMBER) }
            Given { stubSize(DataFactory.SOME_SIZE) }
            Given {
                stubSearchOptions(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            When { searchAds() }
            Then { checkSearchAdsActionsInOrder() }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var searchOptions: QueryParams
        private val capiSearchApi: CapiSearchApi = mockk(relaxed = true)
        private val apiHeaders: ApiHeaders = mockk(relaxed = true)
        private val srpCapiQueryParamsFactory: SrpCapiQueryParamsFactory = mockk(relaxed = true)
        private lateinit var page: String
        private lateinit var size: String

        private lateinit var testSubject: CategoryLandingService

        override fun setup() {
            testSubject = CategoryLandingService(capiSearchApi, srpCapiQueryParamsFactory)
        }

        fun stubPage(page: String) {
            this.page = page
        }

        fun stubSize(size: String) {
            this.size = size
        }

        fun stubSearchOptions(queryParams: QueryParams) {
            searchOptions = queryParams
            coEvery { srpCapiQueryParamsFactory.appendRequiredCapiSearchOptions(any()) } returns searchOptions
        }

        suspend fun searchAds() {
            testSubject.searchAds(apiHeaders, searchOptions, page, size)
        }

        fun checkSearchAdsActionsInOrder() {
            coVerifyOrder {
                srpCapiQueryParamsFactory.appendRequiredCapiSearchOptions(any())
                capiSearchApi.searchAds(apiHeaders, searchOptions, page, size)
            }
        }
    }
}