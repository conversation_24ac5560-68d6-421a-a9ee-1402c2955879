package api.phoneNumber

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val PHONE_NUMBER_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "PHONE_NUMBER_API_BASE_URL"
const val PHONE_NUMBER_API_QA_PORT = 8093

/**
 * The PhoneNumber API base url manager
 */
class PhoneNumberApiBaseUrl : ApiBaseUrl() {
    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(
            PHONE_NUMBER_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY,
        ).toBaseUrl()
    }

    // see helm-chart/values.yaml
    override val localUrl: BaseUrl by lazy {
        LOCAL_HOST_BASE_URL.toBaseUrl()
    }

    override val localPort: Int = PHONE_NUMBER_API_QA_PORT
}
