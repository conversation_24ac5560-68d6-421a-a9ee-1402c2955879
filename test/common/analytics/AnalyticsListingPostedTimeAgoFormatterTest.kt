package common.analytics

import com.gumtree.mobile.common.analytics.AnalyticsListingPostedTimeAgoFormatter
import com.gumtree.mobile.utils.CurrentDateProvider
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.ZonedDateTimeFactory
import tools.runUnitTest

class AnalyticsListingPostedTimeAgoFormatterTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @ParameterizedTest
    @CsvSource(
        "2024-10-10T08:20:02, 2024-10-10T08:20:02",
        "2025-01-13T07:04:22, 2025-01-13T07:04:22",
    )
    fun `should return current date within the expected format`(currentDate: String, expected: String) {
        runUnitTest(robot) {
            runUnitTest(robot) {
                Given { stubCurrentDate(currentDate) }
                When { getCurrentDate() }
                Then { checkCurrentDate(expected) }
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2024-10-10T08:20:02, 1728548402000",
        "2025-01-13T07:04:22, 1736751862000",
    )
    fun `should return current date timestamp`(currentDate: String, expected: Long) {
        runUnitTest(robot) {
            runUnitTest(robot) {
                Given { stubCurrentDate(currentDate) }
                When { getCurrentDateTimestamp() }
                Then { checkCurrentDateTimestamp(expected) }
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2024-01-20T06:00:00, 2024-01-20T18:00:00, 0 days",
        "2024-01-20T18:00:00, 2024-01-21T19:00:00, 1 days",
        "2024-01-20T18:00:00, 2024-02-22T19:00:00, 33 days",
        " , 2024-02-22T19:00:00, ''"
    )
    fun `should return expected days ago label with String dates`(olderDate: String?, currentDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(olderDate) }
            Given { stubCurrentDate(currentDate) }
            When { getTimeAgoLabel() }
            Then { checkTimeAgoLabel(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "1705730400000, 2024-01-20T18:00:00, 0 days",
        "1705773600000, 2024-01-21T19:00:00, 1 days",
        "1705773600000, 2024-02-22T19:00:00, 33 days",
        " , 2024-02-22T19:00:00, ''"
    )
    fun `should return expected days ago label with Timestamp dates`(olderDate: Long?, currentDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(olderDate) }
            Given { stubCurrentDate(currentDate) }
            When { getTimeAgoLabelWithTimestamp() }
            Then { checkTimeAgoLabel(expected) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualTimeAgoLabel: String
        private lateinit var actualCurrentDateResult: String
        private var actualCurrentDateTimestampResult: Long = 0L

        private var olderDateString: String? = null
        private var olderDateTimestamp: Long? = null
        private val testDateFormatter = createUKDateTimeFormatter("yyyy-MM-dd'T'HH:mm:ss")

        private val testSubject = AnalyticsListingPostedTimeAgoFormatter(testDateFormatter)

        override fun setup() {
            mockkObject(CurrentDateProvider)
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubOlderDate(dateString: String?) {
            olderDateString = dateString
        }

        fun stubOlderDate(dateTimestamp: Long?) {
            olderDateTimestamp = dateTimestamp
        }

        fun stubCurrentDate(dateString: String) {
            every { CurrentDateProvider.getCurrentDate() } returns ZonedDateTimeFactory.createZonedDate(dateString)
        }

        fun getTimeAgoLabel() {
            actualTimeAgoLabel = testSubject.getTimeAgoLabel(olderDateString)
        }

        fun getTimeAgoLabelWithTimestamp() {
            actualTimeAgoLabel = testSubject.getTimeAgoLabel(olderDateTimestamp)
        }

        fun getCurrentDate() {
            actualCurrentDateResult = testSubject.getCurrentDate()
        }

        fun getCurrentDateTimestamp() {
            actualCurrentDateTimestampResult = testSubject.getCurrentDateTimestamp()
        }

        fun checkTimeAgoLabel(expected: String) {
            assertEquals(expected, actualTimeAgoLabel)
        }

        fun checkCurrentDate(expected: String) {
            assertEquals(expected, actualCurrentDateResult)
        }

        fun checkCurrentDateTimestamp(expected: Long) {
            assertEquals(expected, actualCurrentDateTimestampResult)
        }
    }
}
