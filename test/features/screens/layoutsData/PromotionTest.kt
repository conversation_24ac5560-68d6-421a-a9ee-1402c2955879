package features.screens.layoutsData

import com.gumtree.mobile.features.screens.layoutsData.Promotion
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class PromotionTest {

    private val robot = Robot()

    @Test
    fun `should return URGENT promotion`() {
        runUnitTest(robot) {
            When { fromFeatureString("AD_URGENT") }
            Then { checkPromotion(Promotion.URGENT) }
        }
    }

    @Test
    fun `should return FEATURED promotion`() {
        runUnitTest(robot) {
            When { fromFeatureString("AD_GP_TOP_AD") }
            Then { checkPromotion(Promotion.FEATURED) }
        }
    }

    @Test
    fun `should NOT return promotion`() {
        runUnitTest(robot) {
            When { fromFeatureString("SOMETHING") }
            Then { checkPromotion(null) }
        }
    }

    private class Robot: BaseRobot {

        private var actualPromotionResult: Promotion? = null

        fun fromFeatureString(string: String) {
            actualPromotionResult = Promotion.fromFeatureString(string)
        }

        fun checkPromotion(expected: Promotion?) {
            assertEquals(expected, actualPromotionResult)
        }

    }
}