package com.gumtree.mobile.utils.extensions.data

import api.capi.models.FEATURE_AD_INSERTION
import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdSlot.RawVipCustomTab
import api.capi.models.RawCapiAttribute
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.common.ImageType
import com.gumtree.mobile.features.screens.layoutsData.Promotion
import com.gumtree.mobile.utils.extensions.isNotNull

/**
 * Check if the seller is private seller or trade
 * @return - true if the seller is private otherwise if the seller is trade it returns false
 */
fun RawCapiAd.isPrivateSeller(): <PERSON><PERSON><PERSON> {
    return getAttributeData(RawCapiAd.ATTRIBUTE_SELLER_TYPE)
        ?.mValueElements
        ?.firstOrNull { it.mValue.equals("private", true) }
        .isNotNull()
}

fun RawCapiAd.isTradeSeller(): <PERSON><PERSON><PERSON> {
    return getAttributeData(RawCapiAd.ATTRIBUTE_SELLER_TYPE)
        ?.mValueElements
        ?.firstOrNull { it.mValue.equals("trade", true) }
        .isNotNull()
}

/**
 * Get the RawCapiAd first picture images to display
 * @return - the Ad first picture list with images or NULL if pictures are empty
 */
fun RawCapiAd.getFirstPictureImages(imageType: ImageType = ImageType.THUMBNAIL_SQUARE): List<Image>? {
    return pictures?.firstOrNull()?.toListWithImages(imageType)
}

/**
 * Get the RawCapiAd location to display (it will get the last location from the locations list)
 * @return - the Ad location or NULL if locations are empty or the first location name is NULL
 */
fun RawCapiAd.getLocationName(): String? {
    return when {
        locations.isNullOrEmpty() -> null
        else -> locations?.last()?.name
    }
}

/**
 * Get a more descriptive location by fetching the last two location names and combining with a comma separator
 * @return - the Ad's location E.g. `Richmond, United Kingdom`
 */
fun RawCapiAd.getDescriptiveLocation(): String {
    return locations?.takeLast(2)?.reversed()?.joinToString(separator = ", ") { it.name.orEmpty() } ?: EMPTY_STRING
}

fun RawCapiAd.getLocationHierarchy(): String {
    return when {
        locations.isNotEmpty() -> "L${locations.size - 1}"
        else -> EMPTY_STRING
    }
}

/**
 * Get the RawCapiAd location ID (it will get the last location from the locations list)
 * @return - the Ad location ID or the method will throw and exception is the Ad does not have locations list is empty
 */
fun RawCapiAd.getLocationId(): String {
    require(locations.isNotEmpty()) { "Missing Ad location data" }
    return locations.last().id
}

/**
 * Check does RawCapiAd object have a specific active feature
 * @param feature - the feature name to be checked (see RawActiveFeature.kt constants about the possible active features)
 * @return - true if the object has the feature in the active features list, otherwise false
 */
fun RawCapiAd.hasActiveFeature(feature: String): Boolean {
    return featuresActive.firstOrNull {
        it.featuresActive?.firstOrNull { activeFeature -> activeFeature.name == feature }.isNotNull()
    }.isNotNull()
}

/**
 * Get an Ad self public website link href value
 *@return - the Ad "self-public-website" link href or NULL if the Ad doesn't have such link rel
 */
fun RawCapiAd.getAdSelfPublicWebsite(): String? {
    return links.firstOrNull { it.rel == "self-public-website" }?.href
}

fun RawCapiAd.getFormattedPrice(): String? = price?.amount?.let { "£$it" }

fun RawCapiAd.isActive(): Boolean = status?.value == "ACTIVE"

fun RawCapiAd.getPromotions(): List<Promotion> {
    return featuresActive.firstOrNull()?.featuresActive?.filter { it.display }?.mapNotNull { rawFeature ->
        Promotion.fromFeatureString(rawFeature.name)
    } ?: emptyList()
}

/**
 * Determine if an ad is from a paid category by using the AD_INSERTION feature
 */
fun RawCapiAd.isFromPaidCategory(): Boolean {
    return featuresActive.firstOrNull()?.featuresActive?.filter {
        it.display
    }?.find { it.name == FEATURE_AD_INSERTION } != null
}

fun RawCapiAd.getAttributeData(name: String): RawCapiAttribute? {
    return attributes.firstOrNull { it.name == name }
}

/**
 * Get an Ad attribute value
 * @param name - the attribute name
 * @return - the attribute value (based on attributeName), or NULL if attributeName does not exist in the raw data
 */
fun RawCapiAd.getAttributeValue(name: String): String? {
    val attribute = getAttributeData(name)?.mValueElements?.firstOrNull()
    return attribute?.localizedLabel ?: attribute?.mValue
}

fun RawCapiAd.vatTooltip(): String? {
    return isMotorsAdvert
        ?.takeIf { it && getAttributeValue(RawCapiAd.ATTRIBUTE_VAT).isNotNull() }
        ?.let { "VAT registered businesses can claim back the VAT of this vehicle" }
}

fun RawCapiAd.hasLocationData(): Boolean {
    return address.city.isNotNull() && address.latitude.isNotNull() && address.longitude.isNotNull()
}

fun RawCapiAd.getVipCustomTabAdSlot(): RawVipCustomTab? {
    return adSlots.find { it.adSlotType.value == "VIP_CUSTOM_TAB" }?.getVipCustomTab()
}

fun RawCapiAd.getBreedAttribute(): String {
    return attributes.firstOrNull { it.name.endsWith("_breed", ignoreCase = true) }?.localizedLabel ?: EMPTY_STRING
}
