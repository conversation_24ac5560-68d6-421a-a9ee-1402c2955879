package api.capi.models

import com.gumtree.mobile.api.capi.Namespaces
import org.simpleframework.xml.ElementList
import org.simpleframework.xml.Namespace

@Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
class RawCapiFeaturesActiveContainer(

    @Namespace(reference = Namespaces.FEATURE)
    @field:ElementList(
        entry = "feature-active",
        required = false,
        name = "feature-active",
        empty = false,
        inline = true,
    )
    @param:ElementList(
        entry = "feature-active",
        required = false,
        name = "feature-active",
        empty = false,
        inline = true,
    )
    val featuresActive: List<RawActiveFeature>? = null,
)
