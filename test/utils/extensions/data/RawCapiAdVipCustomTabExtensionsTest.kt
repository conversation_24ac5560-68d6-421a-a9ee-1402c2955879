package utils.extensions.data

import api.capi.models.RawCapiAdSlot
import com.gumtree.mobile.utils.extensions.data.getEncryptedVrnValue
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import kotlin.test.assertEquals
import tools.annotations.ParallelTest

@ParallelTest
class RawCapiAdVipCustomTabExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should return expected VRN`() {
        runUnitTest(robot) {
            Given { stubTargetUrl(DataFactory.SOME_VIP_TARGET_URL) }
            When { getEncryptedVrnValue() }
            Then { checkVrn(DataFactory.SOME_VIP_VRN) }
        }
    }

    @Test
    fun `should return null if VRN param is not present`() {
        runUnitTest(robot) {
            Given { stubTargetUrl(DataFactory.SOME_VIP_TARGET_URL_WITHOUT_VRN) }
            When { getEncryptedVrnValue() }
            Then { checkVrn(null) }
        }
    }

    @Test
    fun `should return null if target url is empty`() {
        runUnitTest(robot) {
            Given { stubTargetUrl("") }
            When { getEncryptedVrnValue() }
            Then { checkVrn(null) }
        }
    }

    @Test
    fun `should return null if target url is null`() {
        runUnitTest(robot) {
            Given { stubTargetUrl(null) }
            When { getEncryptedVrnValue() }
            Then { checkVrn(null) }
        }
    }


    inner class Robot: BaseRobot {

        private val testSubject = RawCapiAdSlot.RawVipCustomTab()
        private var actualVrn: String? = null

        fun stubTargetUrl(targetUrl: String?) {
            testSubject.targetURL = targetUrl
        }

        fun getEncryptedVrnValue() {
            actualVrn = testSubject.getEncryptedVrnValue()
        }

        fun checkVrn(expected: String?) {
            assertEquals(expected, actualVrn)
        }
    }
}
