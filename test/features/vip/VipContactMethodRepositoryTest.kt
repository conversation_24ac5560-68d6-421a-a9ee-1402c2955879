package features.vip

import api.phoneNumber.models.RawRevealPhoneNumber
import com.gumtree.mobile.api.papi.PapiHeadersProvider
import com.gumtree.mobile.features.vip.DefaultVipContactMethodRepository
import com.gumtree.mobile.features.vip.ReplyToAdBodyCreator
import com.gumtree.mobile.features.vip.ReplyToAdRequest
import com.gumtree.mobile.features.vip.VipService
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.http.*
import io.ktor.server.plugins.BadRequestException
import io.mockk.coEvery
import io.mockk.coVerifyOrder
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import retrofit2.Response
import tools.BaseRobot
import tools.CallHeadersFactory
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.coroutines.runUnitTestForException

class VipContactMethodRepositoryTest {

    private val robot = Robot()

    @Test
    fun `should get VIP phone number`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            Given { stubRawRevealPhoneNumberResponse(DataFactory.SOME_PHONE_NUMBER) }
            When { readPhoneNumber(DataFactory.SOME_AD_ID, DataFactory.PHONE_CONTACT_METHOD) }
            Then { checkPhoneNumber(DataFactory.SOME_PHONE_NUMBER) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "${DataFactory.SOME_USER_EMAIL}, ''",
        "'' ,${DataFactory.SOME_TOKEN}, ",
        "'' , ''",
    )
    fun `should throw UnAuthorized exception if user email AND OR user token is NOT provided`(email: String, token: String) = runTest {
        runUnitTestForException(robot, UnauthorisedException::class) {
            Given {
                stubCallHeaders(
                    CallHeadersFactory.createAuthHeaders(
                        ApiHeaderParams.AUTHORISATION_USER_EMAIL to email,
                        ApiHeaderParams.AUTHORISATION_USER_TOKEN to token,
                    )
                )
            }
            Given { stubRawRevealPhoneNumberResponse(DataFactory.SOME_PHONE_NUMBER) }
            When { readPhoneNumber(DataFactory.SOME_AD_ID, DataFactory.PHONE_CONTACT_METHOD) }
        }
    }

    @Test
    fun `should throw BadRequestException exception if PAPI throw BadRequestException error when reveal phone number`() = runTest {
        runUnitTestForException(robot, BadRequestException::class) {
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            Given { stubRawRevealPhoneNumberError(BadRequestException("error")) }
            When { readPhoneNumber(DataFactory.SOME_AD_ID, DataFactory.PHONE_CONTACT_METHOD) }
        }
    }

    @Test
    fun `should complete with success when BE send email reply is successful`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            Given {
                stubReplyToAdRequest(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_PHONE_NUMBER,
                    DataFactory.SOME_MESSAGE_TEXT,
                    true,
                )
            }
            Given { stubReplyToAdResponse() }
            When { replyToAd(DataFactory.SOME_AD_ID) }
            Then { checkReplyToAdActionsInOrder(DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should throw BadRequestException exception if BE throw BadRequestException error when send email reply`() = runTest {
        runUnitTestForException(robot, BadRequestException::class) {
            Given { stubCallHeaders(CallHeadersFactory.createAuthHeaders()) }
            Given { stubReplyToAdError(BadRequestException("error")) }
            Given {
                stubReplyToAdRequest(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_PHONE_NUMBER,
                    DataFactory.SOME_MESSAGE_TEXT,
                    true,
                )
            }
            When { replyToAd(DataFactory.SOME_AD_ID) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualPhoneNumberResult: String

        private val vipService: VipService = mockk(relaxed = true)
        private val replyToAdBodyCreator = ReplyToAdBodyCreator()
        private val headersProvider = PapiHeadersProvider()
        private lateinit var callHeaders: Headers
        private lateinit var replyToAdRequest: ReplyToAdRequest

        private val testSubject = DefaultVipContactMethodRepository(vipService, headersProvider, replyToAdBodyCreator)

        fun stubCallHeaders(headers: Headers) {
            callHeaders = headers
        }

        fun stubRawRevealPhoneNumberResponse(phoneNumber: String) {
            coEvery { vipService.getPhoneNumber(any(), any(), any()) } returns RawRevealPhoneNumber(phoneNumber)
        }

        fun stubRawRevealPhoneNumberError(error: Throwable) {
            coEvery { vipService.getPhoneNumber(any(), any(), any()) } throws error
        }

        fun stubReplyToAdResponse() {
            coEvery { vipService.sendReplyEmail(any(), any(), null) } returns Response.success(Unit)
        }

        fun stubReplyToAdError(error: Throwable) {
            coEvery { vipService.sendReplyEmail(any(), any(), null) } throws error
        }

        fun stubReplyToAdRequest(
            email: String,
            firstName: String,
            phoneNo: String,
            message: String,
            isMarketingOptIn: Boolean?,
        ) {
            replyToAdRequest = ReplyToAdRequest(
                email = email,
                firstName = firstName,
                phoneNo = phoneNo,
                message = message,
                isMarketingOptIn = isMarketingOptIn,
            )
        }

        suspend fun readPhoneNumber(adId: String, contactMethod: String) {
            actualPhoneNumberResult = testSubject.readPhoneNumber(callHeaders, adId, contactMethod)
        }

        suspend fun replyToAd(adId: String) {
            testSubject.replyToAd(callHeaders, adId, replyToAdRequest)
        }

        fun checkPhoneNumber(expected: String) {
            assertEquals(actualPhoneNumberResult, expected)
        }

        fun checkReplyToAdActionsInOrder(expectedAdId: String) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                replyToAdBodyCreator.create(expectedAdId, replyToAdRequest)
                vipService.sendReplyEmail(any(), any(), any())
            }
        }
    }
}