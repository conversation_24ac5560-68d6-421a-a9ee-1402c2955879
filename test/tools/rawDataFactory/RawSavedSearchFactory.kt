package tools.rawDataFactory

import com.gumtree.mobile.api.savedSearch.bodies.RawAddSavedSearchBody
import com.gumtree.mobile.api.savedSearch.models.RawSavedSearch
import com.gumtree.mobile.api.savedSearch.models.RawSavedSearches
import tools.DataFactory

object RawSavedSearchFactory {

    fun createRawSavedSearch(
        id: String = DataFactory.SOME_SAVED_SEARCH_ID,
        userId: String = DataFactory.SOME_USER_ID,
        eDialogId: String = DataFactory.SOME_AD_ID,
        url: String = DataFactory.SOME_SAVED_SEARCH_URL,
        categoryId: String = DataFactory.SOME_AD_CATEGORY_ID,
        alertType: String = DataFactory.SOME_SAVED_SEARCH_ALERT_TYPE,
        firstName: String = DataFactory.SOME_USER_FIRST_NAME,
        lastName: String = DataFactory.SOME_USER_LAST_NAME,
        email: String = DataFactory.SOME_USER_EMAIL,
        createdAt: String = DataFactory.SOME_REVIEW_DATE,
        emailAlert: Boolean? = true,
    ): RawSavedSearch {
        return RawSavedSearch(
            id = id,
            userId = userId.toInt(),
            eDialogId = eDialogId,
            url = url,
            category = categoryId,
            alertType = alertType,
            firstName = firstName,
            lastName = lastName,
            email = email,
            createdAt = createdAt,
            emailAlert = emailAlert,
        )
    }

    fun createRawSavedSearchBody(
        url: String = DataFactory.SOME_SAVED_SEARCH_URL,
        categoryId: String = DataFactory.SOME_AD_CATEGORY_ID,
        alertType: String = DataFactory.SOME_SAVED_SEARCH_ALERT_TYPE,
        firstName: String = DataFactory.SOME_USER_FIRST_NAME,
        lastName: String = DataFactory.SOME_USER_LAST_NAME,
        email: String = DataFactory.SOME_USER_EMAIL,
        emailAlert: Boolean = true,
    ): RawAddSavedSearchBody {
        return RawAddSavedSearchBody(
            url = url,
            category = categoryId,
            alertType = alertType,
            firstName = firstName,
            lastName = lastName,
            email = email,
            emailAlert = emailAlert,
        )
    }

    fun createRawSavedSearches(number: Int): RawSavedSearches {
        val rawSavedSearchesList = mutableListOf<RawSavedSearch>()
        repeat(number) {
            rawSavedSearchesList.add(
                createRawSavedSearch(
                    id = DataFactory.anyString(),
                    url = DataFactory.anyUrl(),
                )
            )
        }
        return RawSavedSearches(savedSearches = rawSavedSearchesList)
    }
}
