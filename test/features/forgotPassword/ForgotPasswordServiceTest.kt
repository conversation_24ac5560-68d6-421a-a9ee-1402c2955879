package features.forgotPassword

import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.api.userService.bodies.UserServiceChangePasswordBody
import com.gumtree.mobile.api.userService.bodies.UserServiceForgotPasswordBody
import com.gumtree.mobile.features.forgotPassword.ForgotPasswordService
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class ForgotPasswordServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use CAPI user API to POST forgot password`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(DataFactory.SOME_USER_EMAIL) }
            When { sendForgotPassword() }
            Then { checkApiSendForgotPassword() }
        }
    }

    @Test
    fun `should use CAPI user API to reset user password`() = runTest {
        runUnitTest(robot) {
            Given { stubUserSecret(DataFactory.SOME_USER_SECRET) }
            Given { stubResetToken(DataFactory.SOME_TOKEN) }
            When { resetPassword() }
            Then { checkApiSendResetPassword() }
        }
    }

    @Test
    fun `should use user API to reset user password`() = runTest {
        runUnitTest(robot) {
            Given { stubUserSecret(DataFactory.SOME_USER_SECRET) }
            Given { stubResetToken(DataFactory.SOME_TOKEN) }
            When { resetPassword() }
            Then { checkApiSendResetPassword() }
        }
    }

    private class Robot: BaseRobot {
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)

        private lateinit var userName: String
        private lateinit var userSecret: String
        private lateinit var resetToken: String

        private lateinit var rawForgotPasswordBody: UserServiceForgotPasswordBody
        private lateinit var rawPasswordResetBody: UserServiceChangePasswordBody

        private lateinit var testSubject: ForgotPasswordService

        override fun setup() {
            testSubject = ForgotPasswordService(userServiceApi)
        }

        fun stubUserName(email: String) {
            userName = email
            rawForgotPasswordBody = UserServiceForgotPasswordBody(email)
        }

        fun stubUserSecret(secret: String) {
            userSecret = secret
        }

        fun stubResetToken(token: String) {
            resetToken = token
        }

        suspend fun sendForgotPassword() {
            testSubject.sendForgotPassword(rawForgotPasswordBody)
        }

        suspend fun resetPassword() {
            rawPasswordResetBody = UserServiceChangePasswordBody(
                password = userSecret,
                confirmedPassword = userSecret,
                verificationKey = resetToken,
                verificationKeyType = UserServiceChangePasswordBody.VerificationKeyType.RESET_PASSWORD_KEY,
            )
            testSubject.resetPassword(rawPasswordResetBody)
        }

        fun checkApiSendForgotPassword() {
            coVerify { userServiceApi.sendForgotPassword(rawForgotPasswordBody) }
        }

        fun checkApiSendResetPassword() {
            coVerify { userServiceApi.resetPassword(rawPasswordResetBody) }
        }
    }
}