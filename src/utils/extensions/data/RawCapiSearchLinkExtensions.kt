package com.gumtree.mobile.utils.extensions.data

import com.gumtree.mobile.api.capi.models.RawCapiSearchLink
import com.gumtree.mobile.features.homeFeed.HomeFeedScreenUiConfiguration.FILTERS_APPLIED
import com.gumtree.mobile.features.homeFeed.HomeFeedScreenUiConfiguration.FILTER_APPLIED
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.runCatchingWithLog
import org.apache.http.client.utils.URLEncodedUtils
import java.net.URI
import java.nio.charset.Charset

/**
 * Count the filters in Raw CAPI saved search by scanning the query parameters from the link href value
 * @return - String representing the numbers of filters found in the link query parameters,
 * or NULL if the link does not have query parameters or the link href is null
 */
fun RawCapiSearchLink?.getFiltersCount(): String? {
    val queryParams = toQueryParams().filterKeys {
        it != ApiQueryParams.LOCATION_TYPE &&
            it != ApiQueryParams.LATITUDE &&
            it != ApiQueryParams.LONGITUDE
    }
    return when (val size = queryParams.size) {
        0 -> null
        1 -> "$size $FILTER_APPLIED"
        else -> "$size $FILTERS_APPLIED"
    }
}

/**
 * Read the query parameters from the SavedSearchLink href value
 * @return - QueryParams/HashMap with all SavedSearchLink query parameters as key -> value,
 * or empty map if the link does not have query parameters or the link href is null
 */
fun RawCapiSearchLink?.toQueryParams(): QueryParams {
    return runCatchingWithLog {
        this
            ?.href
            ?.let {
                URLEncodedUtils.parse(
                    URI(it),
                    Charset.forName("UTF-8"),
                ).associate { param -> param.name to param.value }
            }
    }.getOrNull() ?: emptyMap()
}
