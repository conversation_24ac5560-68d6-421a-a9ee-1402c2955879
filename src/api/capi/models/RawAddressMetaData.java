package api.capi.models;

import org.simpleframework.xml.Element;
import org.simpleframework.xml.Root;

@Root(strict = false)
public class RawAddressMetaData extends MetaDataOption {
    @Element(name = "full-address", required = false)
    public MetaDataOption fullAddress;
    @Element(name = "street", required = false)
    public MetaDataOption street;
    @Element(name = "city", required = false)
    public MetaDataOption city;
    @Element(name = "state", required = false)
    public MetaDataOption state;
    @Element(name = "zip-code", required = false)
    public MetaDataOption zipCode;
}
