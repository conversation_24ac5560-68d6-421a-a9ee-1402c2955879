package utils.extensions.data

import api.capi.models.RawConversation
import api.capi.models.RawMessage
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.common.Image
import com.gumtree.mobile.features.conversations.ConversationsScreenUiConfiguration.CONVERSATION_LAST_MESSAGE_POST_DATE_FORMAT
import com.gumtree.mobile.features.conversations.ConversationsTimeAgoFormatter
import com.gumtree.mobile.features.screens.layoutsData.ConversationCardDto
import com.gumtree.mobile.features.screens.layoutsData.ConversationMessageDto
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import com.gumtree.mobile.utils.extensions.data.findConversationCounterPartyDisplayName
import com.gumtree.mobile.utils.extensions.data.findConversationCounterPartyEmail
import com.gumtree.mobile.utils.extensions.data.findConversationCounterPartyUserId
import com.gumtree.mobile.utils.extensions.data.findConversationMyUserId
import com.gumtree.mobile.utils.extensions.data.isConversationAboutMyPosting
import com.gumtree.mobile.utils.extensions.data.toConversationCard
import com.gumtree.mobile.utils.extensions.isNotNull
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.rawDataFactory.RawCapiConversationsFactory
import tools.rawDataFactory.RawMessageFactory
import tools.runUnitTest

@ParallelTest
class RawConversationExtensionsTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should map raw conversation into conversation card with conversationId, title, image, unread count and message content`() {
        runUnitTest(robot) {
            Given {
                stubRawMessage(
                    DataFactory.SOME_MESSAGE_ID,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_MESSAGE_TEXT,
                    DataFactory.SOME_MESSAGE_POST_TIMESTAMP,
                    DataFactory.anyString(),
                )
            }
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_AD_IMAGE_URL,
                    2,
                )
            }
            When { toConversationCard(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationId(DataFactory.SOME_CONVERSATION_ID) }
            Then { checkConversationTitle(DataFactory.ANOTHER_USER_FIRST_NAME) }
            Then { checkConversationUnreadMessagesCount(2) }
            Then { checkConversationLastMessageType(ConversationMessageDto.Type.TEXT) }
            Then { checkConversationLastMessageContent(DataFactory.SOME_MESSAGE_TEXT) }
            Then {
                checkDestination(
                    DestinationDto(
                        "${DestinationRoute.CONVERSATION.screenName}?${ApiQueryParams.CONVERSATION_ID}=${DataFactory.SOME_CONVERSATION_ID}",
                        DestinationDto.Type.SCREEN
                    )
                )
            }
            Then { checkConversationImage(DataFactory.squareThumbnails(DataFactory.ANOTHER_AD_IMAGE_BASE_URL)) }
        }
    }

    @Test
    fun `should map raw conversation into conversation card with conversationId, title, image, unread count and NO message content`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.ANOTHER_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { toConversationCard(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationId(DataFactory.SOME_CONVERSATION_ID) }
            Then { checkConversationTitle(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkConversationUnreadMessagesCount(5) }
            Then { checkConversationLastMessageType(ConversationMessageDto.Type.TEXT) }
            Then { checkConversationLastMessageContent(null) }
            Then { checkConversationImage(DataFactory.squareThumbnails(DataFactory.SOME_BASE_IMAGE_URL)) }
        }
    }

    @Test
    fun `should return true when username does not match raw conversation adOwnerEmail`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { isConversationAboutMyPosting(DataFactory.ANOTHER_USER_EMAIL) }
            Then { checkConversationAboutMyPosting(false) }
        }
    }

    @Test
    fun `should return false when username matches raw conversation adOwnerEmail`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { isConversationAboutMyPosting(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationAboutMyPosting(true) }
        }
    }

    @Test
    fun `should return ad owner name when username matches raw conversation adOwnerEmail`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.ANOTHER_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { findConversationUserDisplayName(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationUserDisplayName(DataFactory.SOME_USER_FIRST_NAME) }
        }
    }

    @Test
    fun `should return ad replier name when username does NOT match raw conversation adOwnerEmail`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { findConversationUserDisplayName(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationUserDisplayName(DataFactory.ANOTHER_USER_FIRST_NAME) }
        }
    }

    @Test
    fun `should return ad replier email when username matches raw conversation adOwnerEmail`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    adId = DataFactory.SOME_AD_ID,
                    adOwnerName = DataFactory.SOME_USER_FIRST_NAME,
                    adOwnerId = DataFactory.SOME_USER_ID,
                    adOwnerEmail = DataFactory.SOME_USER_EMAIL,
                    adReplierName = DataFactory.ANOTHER_USER_FIRST_NAME,
                    adReplierId = DataFactory.ANOTHER_USER_ID,
                    adReplierEmail = DataFactory.ANOTHER_USER_EMAIL,
                    imageUrl = DataFactory.SOME_AD_IMAGE_URL,
                    unreadCount = 5,
                )
            }
            When { findConversationCounterPartyEmail(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationCounterPartyEmail(DataFactory.ANOTHER_USER_EMAIL) }
        }
    }

    @Test
    fun `should return ad owner email when username does NOT match raw conversation adOwnerEmail`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    adId = DataFactory.SOME_AD_ID,
                    adOwnerName = DataFactory.SOME_USER_FIRST_NAME,
                    adOwnerId = DataFactory.SOME_USER_ID,
                    adOwnerEmail = DataFactory.SOME_USER_EMAIL,
                    adReplierName = DataFactory.ANOTHER_USER_FIRST_NAME,
                    adReplierId = DataFactory.ANOTHER_USER_ID,
                    adReplierEmail = DataFactory.ANOTHER_USER_EMAIL,
                    imageUrl = DataFactory.SOME_AD_IMAGE_URL,
                    unreadCount = 5,
                )
            }
            When { findConversationCounterPartyEmail(DataFactory.ANOTHER_USER_EMAIL) }
            Then { checkConversationCounterPartyEmail(DataFactory.SOME_USER_EMAIL) }
        }
    }

    @Test
    fun `should return empty string when ad owner name is NULL`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    null,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { findConversationUserDisplayName(DataFactory.ANOTHER_USER_EMAIL) }
            Then { checkConversationUserDisplayName(EMPTY_STRING) }
        }
    }

    @Test
    fun `should return ad replier id when is my posting`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    adId = DataFactory.SOME_AD_ID,
                    adOwnerName = DataFactory.SOME_USER_FIRST_NAME,
                    adOwnerId = DataFactory.SOME_USER_ID,
                    adOwnerEmail = DataFactory.SOME_USER_EMAIL,
                    adReplierName = DataFactory.ANOTHER_USER_FIRST_NAME,
                    adReplierId = DataFactory.ANOTHER_USER_ID,
                    adReplierEmail = DataFactory.ANOTHER_USER_EMAIL,
                    imageUrl = DataFactory.SOME_AD_IMAGE_URL,
                    unreadCount = 5,
                )
            }
            When { findConversationCounterPartyUserId(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationCounterPartyUserId(DataFactory.ANOTHER_USER_ID) }
        }
    }

    @Test
    fun `should return ad owner id when is NOT my posting`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    conversationId = DataFactory.ANOTHER_CONVERSATION_ID,
                    adId = DataFactory.ANOTHER_AD_ID,
                    adOwnerName = DataFactory.ANOTHER_USER_FIRST_NAME,
                    adOwnerId = DataFactory.ANOTHER_USER_ID,
                    adOwnerEmail = DataFactory.ANOTHER_USER_EMAIL,
                    adReplierName = DataFactory.SOME_USER_FIRST_NAME,
                    adReplierId = DataFactory.SOME_USER_ID,
                    adReplierEmail = DataFactory.SOME_USER_EMAIL,
                    imageUrl = DataFactory.SOME_AD_IMAGE_URL,
                    unreadCount = 10,
                )
            }
            When { findConversationCounterPartyUserId(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationCounterPartyUserId(DataFactory.ANOTHER_USER_ID) }
        }
    }

    @Test
    fun `should return ad owner id when is my posting`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    adId = DataFactory.SOME_AD_ID,
                    adOwnerName = DataFactory.SOME_USER_FIRST_NAME,
                    adOwnerId = DataFactory.SOME_USER_ID,
                    adOwnerEmail = DataFactory.SOME_USER_EMAIL,
                    adReplierName = DataFactory.ANOTHER_USER_FIRST_NAME,
                    adReplierId = DataFactory.ANOTHER_USER_ID,
                    adReplierEmail = DataFactory.ANOTHER_USER_EMAIL,
                    imageUrl = DataFactory.SOME_AD_IMAGE_URL,
                    unreadCount = 5,
                )
            }
            When { findConversationMyUserId(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationMyUserId(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should return ad replier id when is NOT my posting`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    conversationId = DataFactory.ANOTHER_CONVERSATION_ID,
                    adId = DataFactory.ANOTHER_AD_ID,
                    adOwnerName = DataFactory.ANOTHER_USER_FIRST_NAME,
                    adOwnerId = DataFactory.ANOTHER_USER_ID,
                    adOwnerEmail = DataFactory.ANOTHER_USER_EMAIL,
                    adReplierName = DataFactory.SOME_USER_FIRST_NAME,
                    adReplierId = DataFactory.SOME_USER_ID,
                    adReplierEmail = DataFactory.SOME_USER_EMAIL,
                    imageUrl = DataFactory.SOME_AD_IMAGE_URL,
                    unreadCount = 10,
                )
            }
            When { findConversationMyUserId(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationMyUserId(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should return empty string when ad replier name is NULL`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    null,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { findConversationUserDisplayName(DataFactory.SOME_USER_EMAIL) }
            Then { checkConversationUserDisplayName(EMPTY_STRING) }
        }
    }

    @Test
    fun `should return sellerDisplayName when sellerDisplayName is not blank but AdOwnerName is`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    null,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { findConversationUserDisplayName(DataFactory.ANOTHER_USER_EMAIL, DataFactory.SOME_USER_DISPLAY_NAME) }
            Then { checkConversationUserDisplayName(DataFactory.SOME_USER_DISPLAY_NAME) }
        }
    }

    @Test
    fun `should return empty string when sellerDisplayName and adOwnerName is blank`() {
        runUnitTest(robot) {
            Given {
                stubRawConversation(
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    null,
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.ANOTHER_USER_FIRST_NAME,
                    DataFactory.ANOTHER_USER_ID,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_AD_IMAGE_URL,
                    5,
                )
            }
            When { findConversationUserDisplayName(DataFactory.ANOTHER_USER_EMAIL, DataFactory.SOME_USER_DISPLAY_NAME) }
            Then { checkConversationUserDisplayName(DataFactory.SOME_USER_DISPLAY_NAME) }
        }
    }

    private class Robot: BaseRobot {
        private var actualConversationAboutMyPostingResult: Boolean? = null
        private var actualConversationCounterPartyEmailResult: String? = null
        private var actualConversationCounterPartyUserIdResult: String? = null
        private var actualConversationMyUserIdResult: String? = null
        private lateinit var actualConversationUserDisplayNameResult: String
        private lateinit var actualConversationCardResult: ConversationCardDto

        private val conversationsTimeAgoFormatter = ConversationsTimeAgoFormatter(
            createUKDateTimeFormatter(CONVERSATION_LAST_MESSAGE_POST_DATE_FORMAT)
        )
        private var rawMessageData: RawMessage? = null

        private lateinit var testSubject: RawConversation

        override fun setup() {
            rawMessageData = null
        }

        fun stubRawMessage(
            messageId: String,
            senderId: String,
            text: String,
            postTimeStamp: String,
            direction: String,
        ) {
            rawMessageData = RawMessageFactory.createRawMessage(
                messageId,
                senderId,
                text,
                postTimeStamp,
                direction,
            )
        }

        fun stubRawConversation(
            conversationId: String,
            adId: String,
            adOwnerName: String?,
            adOwnerId: String,
            adOwnerEmail: String,
            adReplierName: String?,
            adReplierId: String,
            adReplierEmail: String,
            imageUrl: String,
            unreadCount: Int,
        ) {
            testSubject = RawCapiConversationsFactory.createRawConversation(
                conversationId = conversationId,
                conversationAdId = adId,
                conversationAdOwnerName = adOwnerName,
                conversationAdOwnerEmail = adOwnerEmail,
                conversationAdOwnerId = adOwnerId,
                conversationAdReplierName = adReplierName,
                conversationAdReplierId = adReplierId,
                conversationAdReplierEmail = adReplierEmail,
                conversationAdImageUrl = imageUrl,
                conversationUnreadMessagesCount = unreadCount,
                conversationMessages = if (rawMessageData.isNotNull()) listOf(rawMessageData!!) else emptyList(),
            )
        }

        fun toConversationCard(userName: String) {
            actualConversationCardResult = testSubject.toConversationCard(
                conversationsTimeAgoFormatter,
                conversationsTimeAgoFormatter.getCurrentDate(),
                userName,
            )
        }

        fun isConversationAboutMyPosting(userName: String) {
            actualConversationAboutMyPostingResult = testSubject.isConversationAboutMyPosting(userName)
        }

        fun findConversationUserDisplayName(userName: String, sellerDisplayName: String? = null) {
            actualConversationUserDisplayNameResult = testSubject.findConversationCounterPartyDisplayName(userName, sellerDisplayName)
        }

        fun findConversationCounterPartyEmail(userName: String) {
            actualConversationCounterPartyEmailResult = testSubject.findConversationCounterPartyEmail(userName)
        }

        fun findConversationCounterPartyUserId(userName: String) {
            actualConversationCounterPartyUserIdResult = testSubject.findConversationCounterPartyUserId(userName)
        }

        fun findConversationMyUserId(userName: String) {
            actualConversationMyUserIdResult = testSubject.findConversationMyUserId(userName)
        }

        fun checkConversationId(expected: String) {
            assertEquals(expected, actualConversationCardResult.conversationId)
        }

        fun checkConversationTitle(expected: String) {
            assertEquals(expected, actualConversationCardResult.title)
        }

        fun checkConversationImage(expected: List<Image>?) {
            assertEquals(expected, actualConversationCardResult.images)
        }

        fun checkConversationUnreadMessagesCount(expected: Int) {
            assertEquals(expected, actualConversationCardResult.unreadMessages)
        }

        fun checkConversationLastMessageType(expected: ConversationMessageDto.Type) {
            assertEquals(expected, actualConversationCardResult.lastMessage.type)
        }

        fun checkConversationLastMessageContent(expected: String?) {
            assertEquals(expected, actualConversationCardResult.lastMessage.content)
        }

        fun checkDestination(expected: DestinationDto) {
            assertEquals(expected, actualConversationCardResult.destination)
        }

        fun checkConversationAboutMyPosting(expected: Boolean) {
            assertEquals(expected, actualConversationAboutMyPostingResult)
        }

        fun checkConversationUserDisplayName(expected: String) {
            assertEquals(expected, actualConversationUserDisplayNameResult)
        }

        fun checkConversationCounterPartyEmail(expected: String) {
            assertEquals(expected, actualConversationCounterPartyEmailResult)
        }

        fun checkConversationCounterPartyUserId(expected: String) {
            assertEquals(expected, actualConversationCounterPartyUserIdResult)
        }

        fun checkConversationMyUserId(expected: String) {
            assertEquals(expected, actualConversationMyUserIdResult)
        }
    }
}