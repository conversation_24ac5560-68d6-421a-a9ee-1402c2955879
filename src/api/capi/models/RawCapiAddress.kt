package com.gumtree.mobile.api.capi.models

import com.gumtree.mobile.api.capi.Namespaces
import org.simpleframework.xml.Element
import org.simpleframework.xml.Namespace
import org.simpleframework.xml.Root

@Root(name = "ad-address", strict = false)
@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
data class RawCapiAddress(

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "full-address", required = false)
    @param:Element(name = "full-address")
    val fullAddress: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "street", required = false)
    @param:Element(name = "street")
    val street: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "city", required = false)
    @param:Element(name = "city")
    val city: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "radius", required = false)
    @param:Element(name = "radius")
    val radius: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "state", required = false)
    @param:Element(name = "state")
    val state: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "zip-code", required = false)
    @param:Element(name = "zip-code")
    val zipCode: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "visible-on-map", required = false)
    @param:Element(name = "visible-on-map")
    val visibleOnMap: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "country", required = false)
    @param:Element(name = "country")
    val country: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "longitude", required = false)
    @param:Element(name = "longitude")
    val longitude: String? = null,

    @Namespace(reference = Namespaces.TYPES)
    @field:Element(name = "latitude", required = false)
    @param:Element(name = "latitude")
    val latitude: String? = null,
)
