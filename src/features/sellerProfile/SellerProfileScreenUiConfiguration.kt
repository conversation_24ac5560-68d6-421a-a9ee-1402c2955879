package com.gumtree.mobile.features.sellerProfile

import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isZero
import com.gumtree.mobile.utils.extensions.optionalPluralSuffix
import com.gumtree.mobile.utils.runOrNull

/**
 * Defines the Seller profile screen UI components configurations
 */

object SellerProfileScreenUiConfiguration {

    const val SELLER_PROFILE_POSTING_SINCE_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'" // 2020-11-12T20:18:21Z
    const val FOR_SALE_TEXT = "for sale"
    const val POSTING_SINCE_TEXT = "Posting for"
    const val EMAIL_ADDRESS_VERIFIED_TEXT = "Email address verified"
    const val NO_RESULTS_TITLE_TEXT = "Sorry, nothing here today"
    const val NO_RESULTS_SUBTITLE_TEXT = "Check back again soon"

    fun getScreenTitle(
        userName: String?,
        page: String,
    ) = runOrNull(page.isZero() && userName.isNotNull()) { userName }

    fun getSellerOtherAdsTitle(totalNumber: Int) = "$totalNumber ${"item".optionalPluralSuffix(
        totalNumber.toLong(),
    )} $FOR_SALE_TEXT"

    fun getYearText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"year".optionalPluralSuffix(timeAgo)}"

    fun getMonthText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"month".optionalPluralSuffix(timeAgo)}"

    fun getWeekText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"week".optionalPluralSuffix(timeAgo)}"

    fun getDayText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"day".optionalPluralSuffix(timeAgo)}"

    fun getHourText(timeAgo: Long) = "$POSTING_SINCE_TEXT $timeAgo ${"hour".optionalPluralSuffix(timeAgo)}"
}
