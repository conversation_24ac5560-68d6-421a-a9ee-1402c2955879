package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

@Root(name = "feature-booked", strict = false)
@Namespace(reference = Namespaces.FEATURE, prefix = Namespaces.Prefix.FEAT)
public class RawCapiFeatureBooked {

    public RawCapiFeatureBooked(){}

    @Attribute(name = "group")
    public String group;

    @Attribute(name = "name")
    public String name;

    @Element(name = "option", required = false)
    public RawFeatureOption featureOption;
}
