package com.gumtree.mobile.features.chat

import api.capi.models.RawConversation
import com.gumtree.mobile.api.userService.models.RawUserServiceAccountDetails
import com.gumtree.mobile.features.screens.layoutsData.ToolbarDto
import com.gumtree.mobile.utils.runOrNull

/**
 * Provider of Chat Toolbar data
 */
class ChatToolbarProvider(private val chatToolbarActionsFactory: ChatToolbarActionsFactory) {

    /**
     * Get the list with Chat toolbar actions
     * @param username - my username
     * @param rawConversation - the raw conversation data
     * @param isCounterPartyBlocked - indicates if the counterparty user is blocked or NOT
     * @param counterPartyAccountDetails - account details for the other user - required to be able to view their profile
     * @return - an instance of Too<PERSON>bar<PERSON><PERSON> with the list with all Chat toolbar actions,
     * or NULL if not toolbar actions need to be displayed
     */
    fun createChatToolbar(
        username: String,
        rawConversation: RawConversation,
        isCounterPartyBlocked: Boolean,
        isConversationAlreadyReported: Boolean,
        counterPartyAccountDetails: RawUserServiceAccountDetails?,
    ): ToolbarDto? {
        val toolbarActions = listOfNotNull(
            chatToolbarActionsFactory.createBlockUserAction(username, rawConversation, isCounterPartyBlocked),
            chatToolbarActionsFactory.createReportConversationAction(
                username,
                rawConversation,
                isConversationAlreadyReported,
            ),
            counterPartyAccountDetails?.let { chatToolbarActionsFactory.createViewProfileAction(it) },
        )
        return runOrNull(toolbarActions.isNotEmpty()) {
            ToolbarDto(actions = toolbarActions, behavior = "inline")
        }
    }
}
