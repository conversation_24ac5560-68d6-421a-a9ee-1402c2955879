package com.gumtree.mobile.features.homeFeed

import api.capi.models.RawCapiSavedSearchList
import com.gumtree.mobile.api.capi.models.RawCapiSavedSearch
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.HomeFeedSavedSearchCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.appendViewMoreCardIfRequired
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_LOCATION_ID
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.data.getFiltersCount
import com.gumtree.mobile.utils.extensions.data.toQueryParams
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.extensions.isNull
import com.gumtree.mobile.utils.extensions.stripKeysAttrPrefix
import org.jetbrains.annotations.VisibleForTesting

/**
 * Mapping the SavedSearches raw data (CAPI data) into HomeFeedSavedSearches mobile application data
 * @property homeFeedScreenUiConfiguration - the configuration of the HomeFeedScreen
 */
class HomeFeedSavedSearchesMapper(
    private val homeFeedScreenUiConfiguration: HomeFeedScreenUiConfiguration = HomeFeedScreenUiConfiguration,
    private val locationsApi: LocationsApi,
) {

    /**
     * @param rawData - the raw Capi saved searches list
     * @return - RowLayout with any saved searches (Active and Inactive). If the user does not have any Active saved searches it returns null
     */
    suspend fun map(rawData: RawCapiSavedSearchList?): RowLayout<UiItem>? {
        val savedSearchesList: List<UiItem>? = rawData?.rawCapiSavedSearches
            ?.mapNotNull { mapFromRawCapiSavedSearch(it) }
            ?.appendViewMoreCardIfRequired( // todo: should we swap this with a take(SAVED_SEARCHES_LIMIT), so it only maps 8 or less
                homeFeedScreenUiConfiguration.SAVED_SEARCHES_LIMIT,
                HomeFeedScreenUiConfiguration.VIEW_MORE,
                homeFeedScreenUiConfiguration.savedSearchesViewMoreCardDestination,
            )

        return when {
            savedSearchesList.isNullOrEmpty() -> null
            else -> RowLayout(RowLayoutType.HOME_FEED_SAVED_SEARCHES_ROW, savedSearchesList)
        }
    }

    @VisibleForTesting
    suspend fun mapFromRawCapiSavedSearch(rawData: RawCapiSavedSearch): HomeFeedSavedSearchCardDto? {
        return if (rawData.searchDescription.isNullOrEmpty() || rawData.searchLink.isNull()) {
            null
        } else {
            val queryParams = rawData.searchLink.toQueryParams().toMutableMap()
            val modifiedParams = addLocationTypeAndIdParamsAndCleanOtherParams(queryParams).stripKeysAttrPrefix()

            HomeFeedSavedSearchCardDto(
                title = rawData.searchDescription,
                subtitle = rawData.searchLink.getFiltersCount(),
                destination = DestinationRoute.SRP.build(modifiedParams),
            )
        }
    }

    /**
     * use suggestions endpoint and take first, then set LocationType accordingly
     */
    private suspend fun addLocationTypeAndIdParamsAndCleanOtherParams(
        queryParams: MutableMap<String, String?>,
    ): QueryParams {
        val zipcode = queryParams[ApiQueryParams.ZIPCODE]
        if (zipcode.isNotNullOrEmpty()) {
            // use suggestions endpoint and take first, then set LocationType accordingly
            val location = locationsApi.getLocationSuggestions(zipcode, limit = 1).options.firstOrNull()
            if (location != null) {
                queryParams[ApiQueryParams.LOCATION_ID] = location.id.toString()
                queryParams[ApiQueryParams.LOCATION_TYPE] = location.type.name
            } else {
                queryParams[ApiQueryParams.LOCATION_ID] = DEFAULT_LOCATION_ID
                queryParams[ApiQueryParams.LOCATION_TYPE] = LocationType.LOCATION.name
            }
        } else {
            queryParams[ApiQueryParams.LOCATION_TYPE] = LocationType.LOCATION.name
        }
        queryParams.remove(ApiQueryParams.ZIPCODE)
        queryParams.remove(ApiQueryParams.LATITUDE)
        queryParams.remove(ApiQueryParams.LONGITUDE)

        return queryParams
    }
}
