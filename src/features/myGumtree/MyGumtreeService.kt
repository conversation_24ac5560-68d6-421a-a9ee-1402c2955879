package com.gumtree.mobile.features.myGumtree

import com.gumtree.mobile.api.capi.apis.CapiAdsApi
import com.gumtree.mobile.api.capi.apis.CapiOtherUserApi
import com.gumtree.mobile.api.capi.apis.CapiPostApi
import com.gumtree.mobile.api.userProfile.api.UserProfileApi
import com.gumtree.mobile.api.userService.api.UserServiceApi

class MyGumtreeService(
    private val capiAdsApi: CapiAdsApi,
    private val capiOtherUserApi: CapiOtherUserApi,
    private val capiPostApi: CapiPostApi,
    private val userProfileApi: UserProfileApi,
    private val userServiceApi: UserServiceApi,
) : CapiAdsApi by capiAdsApi,
    CapiOtherUserApi by capiOtherUserApi,
    CapiPostApi by capiPostApi,
    UserProfileApi by userProfile<PERSON><PERSON>,
    UserServiceApi by userService<PERSON>pi
