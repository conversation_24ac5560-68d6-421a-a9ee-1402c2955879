package features.vip.v2

import api.capi.bodies.RawReplyToAdEmail
import com.gumtree.mobile.features.vip.v2.ReplyToAdBodyCreator
import com.gumtree.mobile.features.vip.v2.ReplyToAdRequest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest
import kotlin.test.assertEquals

class ReplyToAdBodyCreatorTest {
    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should create reply to ad body with valid request and adId`() {
        runUnitTest(robot) {
            Given {
                stubReplyToAdRequest(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_PHONE_NUMBER,
                    DataFactory.SOME_MESSAGE_TEXT,
                    true
                )
            }
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            When { create() }
            Then { checkId(DataFactory.SOME_AD_ID) }
            Then { checkAdId(DataFactory.SOME_AD_ID) }
            Then { checkFirstName(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkEmail(DataFactory.SOME_USER_EMAIL) }
            Then { checkPhone(DataFactory.SOME_PHONE_NUMBER) }
            Then { checkMessage(DataFactory.SOME_MESSAGE_TEXT) }
            Then { checkOptInMarketing(true) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualRawReplyToAdEmailBody: RawReplyToAdEmail
        private lateinit var replyToAdRequest: ReplyToAdRequest
        private lateinit var adId: String

        private lateinit var testSubject: ReplyToAdBodyCreator

        override fun setup() {
            testSubject = ReplyToAdBodyCreator()
        }

        fun stubReplyToAdRequest(
            email: String,
            firstName: String,
            phoneNo: String,
            message: String,
            isMarketingOptIn: Boolean?
        ) {
            replyToAdRequest = ReplyToAdRequest(
                email = email,
                firstName = firstName,
                phoneNo = phoneNo,
                message = message,
                isMarketingOptIn = isMarketingOptIn
            )
        }

        fun stubAdId(adId: String) {
            this.adId = adId
        }

        fun create() {
            actualRawReplyToAdEmailBody = testSubject.create(adId, replyToAdRequest)
        }

        fun checkFirstName(expected: String) {
            assertEquals(expected, actualRawReplyToAdEmailBody.name)
        }

        fun checkId(expected: String) {
            assertEquals(expected, actualRawReplyToAdEmailBody.id)
        }

        fun checkAdId(expected: String) {
            assertEquals(expected, actualRawReplyToAdEmailBody.adId)
        }

        fun checkEmail(expected: String) {
            assertEquals(expected, actualRawReplyToAdEmailBody.emailAddress)
        }

        fun checkMessage(expected: String) {
            assertEquals(expected, actualRawReplyToAdEmailBody.message)
        }

        fun checkPhone(expected: String) {
            assertEquals(expected, actualRawReplyToAdEmailBody.phone)
        }

        fun checkOptInMarketing(expected: Boolean) {
            assertEquals(expected, actualRawReplyToAdEmailBody.optInMarketing)
        }
    }
}
