package com.gumtree.mobile.features.myGumtree.v2

/**
 * Pluralise String based on number
 * @param number - the number on which the pluralisation shall be based
 * @param singularString - the singular string value in case of number == 1
 * @param pluralString - the plural string value is case if number != 1
 */
fun pluraliseString(
    number: Int,
    singularString: String,
    pluralString: String,
): String {
    return when (number) {
        1 -> "$number$singularString"
        else -> "$number$pluralString"
    }
}
