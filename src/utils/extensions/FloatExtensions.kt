package com.gumtree.mobile.utils.extensions

/**
 * Compare this Float number with another Float number
 * @param number - the number to compare against
 * @return - true if this number is greater than the other number otherwise false
 */
fun Float?.isGreater<PERSON>han(number: Float?): Boolean {
    return when {
        this.isNotNull() && number.isNotNull() -> this > number
        else -> false
    }
}
