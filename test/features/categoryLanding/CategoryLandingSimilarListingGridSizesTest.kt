package features.categoryLanding

import com.gumtree.mobile.features.categoryLanding.CategoryLandingSimilarListingGridSizes
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class CategoryLandingSimilarListingGridSizesTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return double grid size in portrait mode`() {
        runUnitTest(robot) {
            When { getPortraitGridSize() }
            Then { checkGridSize(2) }
        }
    }

    @Test
    fun `should return zero grid size in landscape mode (NO landscape UI)`() {
        runUnitTest(robot) {
            When { getLandscapeGridSize() }
            Then { checkGridSize(0) }
        }
    }

    private class Robot: BaseRobot {
        private var actualGridSize: Int = 0

        private lateinit var testSubject: CategoryLandingSimilarListingGridSizes

        override fun setup() {
            testSubject = CategoryLandingSimilarListingGridSizes()
        }

        fun getPortraitGridSize() {
            actualGridSize = testSubject.getPortraitGridSize()
        }

        fun getLandscapeGridSize() {
            actualGridSize = testSubject.getLandscapeGridSize()
        }

        fun checkGridSize(expected: Int) {
            assertEquals(expected, actualGridSize)
        }
    }
}