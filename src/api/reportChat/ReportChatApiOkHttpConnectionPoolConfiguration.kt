package com.gumtree.mobile.api.reportChat

import com.gumtree.mobile.api.common.OkHttpConnectionPoolConfiguration
import java.util.concurrent.TimeUnit

// https://github.com/Netflix/Hystrix/wiki/Configuration#ThreadPool 7 RPS ×1.3 seconds + 1 = 10
const val REPORT_CHAT_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS = 10
const val REPORT_CHAT_API_CONNECTION_POOL_KEEP_ALIVE_DURATION = 300L

class ReportChatApiOkHttpConnectionPoolConfiguration : OkHttpConnectionPoolConfiguration {

    override val maxIdleConnections: Int = REPORT_CHAT_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS

    override val keepAliveDuration: Long = REPORT_CHAT_API_CONNECTION_POOL_KEEP_ALIVE_DURATION

    override val timeUnit: TimeUnit = TimeUnit.SECONDS
}
