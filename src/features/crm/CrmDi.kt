package com.gumtree.mobile.features.crm

import com.gumtree.mobile.di.PAPI_HEADERS_PROVIDER
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Provides all dependencies about the Crm feature
 */
val crmModule = module {
    single { CrmService(crmApi = getFromKoin()) }
    single<CrmRepository> {
        DefaultCrmRepository(
            crmService = getFromKoin(),
            papiHeadersProvider = getFromKoin(PAPI_HEADERS_PROVIDER),
            dispatcherProvider = getFromKoin(),
        )
    }
}
