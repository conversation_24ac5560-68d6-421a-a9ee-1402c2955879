plugins {
    kotlin("jvm") version "1.9.23"
    kotlin("plugin.allopen") version "1.9.23"

    // The following line allows to load io.gatling.gradle plugin and directly apply it
    id("io.gatling.gradle") version "3.10.5"
}

gatling {
    // WARNING: options below only work when logback config file isn't provided
    logLevel = "WARN" // logback root level
    logHttp = io.gatling.gradle.LogHttp.NONE // set to 'ALL' for all HTTP traffic in TRACE, 'FAILURES' for failed HTTP traffic in DEBUG
}

dependencies {
    gatlingImplementation("com.google.code.gson:gson:2.8.8")
}

repositories {
    mavenCentral()
    maven("https://nexus.gum-ops-prod.gumtree.cloud/repository/hosted-gtuk-releases")
}
