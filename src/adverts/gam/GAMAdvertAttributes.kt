package com.gumtree.mobile.adverts.gam

/**
 * Typealias about the GAM Advert attributes (pair of key -> value)
 */
typealias GAMAdvertAttributes = Map<String, String>

const val GAM_HOME_FEED_PAGE_TYPE = "home"
const val GAM_SRP_PAGE_TYPE = "search"
const val GAM_BRP_PAGE_TYPE = "browse"
const val GAM_VIP_PAGE_TYPE = "vip"

enum class GAMAdvertAttribute(val value: String) {
    PAGE_TYPE("page_type"),
    ENVIRONMENT("env"),
    LOCATION("loc"),
    LOCATION_POST_CODE("postcode"),
    LOCATION_OUT_CODE("outcode"),
    LOGGED_IN_STATUS("li"),
    POSITION("pos"),
    PTG("ptg"),
    APP_VERSION("app_ver"),
    PRICE("price"),
    SELLER_TYPE("seller_type"),
    AD_ID("g_adid"),
    CATEGORY_L2("l2"),
    CATEGORY_L3("l3"),
    CATEGORY_L4("l4"),
    CATEGORY_L5("l5"),
    KEYWORD("kw"),
    VEHICLE_MAKE("g_cm"),
    VEHICLE_MODEL("g_cmo"),
    VEHICLE_REGISTRATION_YEAR("g_cy"),
    VEHICLE_FUEL_TYPE("vehicle_fuel_type"),
    VEHICLE_BODY_TYPE("vehicle_body_type"),
    VEHICLE_ENGINE_SIZE("vehicle_engine_size"),
    VEHICLE_TRANSMISSION("vehicle_transmission"),
    PROPERTY_TYPE("property_type"),
    PROPERTY_NUMBER_BEDS("property_number_beds"),
}
