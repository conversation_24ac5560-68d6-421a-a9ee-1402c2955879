package features.srp

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.adverts.gam.GAMAdvertUtils
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.capi.apis.CapiCategoryApi
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.UserProfileService
import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.SrpPageCalculator
import com.gumtree.mobile.features.srp.ANALYTICS_VIEW_SEARCH_RESULTS_EVENT_NAME
import com.gumtree.mobile.features.srp.BingAdUrlProvider
import com.gumtree.mobile.features.srp.DefaultSrpRepository
import com.gumtree.mobile.features.srp.SrpAdvertsFactory
import com.gumtree.mobile.features.srp.SrpAdvertsProvider
import com.gumtree.mobile.features.srp.SrpAnalyticsProvider
import com.gumtree.mobile.features.srp.SrpButtonUIProvider
import com.gumtree.mobile.features.srp.SrpCapiQueryParamsFactory
import com.gumtree.mobile.features.srp.SrpChipsFactory
import com.gumtree.mobile.features.srp.SrpDominantCategoryHandler
import com.gumtree.mobile.features.srp.SrpExtendedSearchAdsProvider
import com.gumtree.mobile.features.srp.SrpListingMapper
import com.gumtree.mobile.features.srp.SrpListingsProvider
import com.gumtree.mobile.features.srp.SrpSavedSearchesBannerProvider
import com.gumtree.mobile.features.srp.SrpService
import com.gumtree.mobile.features.srp.SrpSortHeaderFactory
import com.gumtree.mobile.features.srp.SrpStickyBarProvider
import com.gumtree.mobile.features.srp.SrpTimeAgoFormatter
import com.gumtree.mobile.features.srp.SrpTotalResultsMapper
import com.gumtree.mobile.features.srp.SrpZipcodeHandler
import com.gumtree.mobile.features.srp.TotalResultsDto
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.extensions.filterNotNullValues
import common.AdjustTrackingData
import io.ktor.http.Headers
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CallHeadersFactory
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawLocationFactory
import tools.rawDataFactory.UserServiceDataFactory
import utils.TestDispatcherProvider

class DefaultSrpRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should return 0 total results number`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubRawAds(0) }
            When { readTotalResults() }
            Then { checkTotalResultsNumber(0) }
        }
    }

    @Test
    fun `should return 5 total results number`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubRawAds(5) }
            When { readTotalResults() }
            Then { checkTotalResultsNumber(5) }
        }
    }

    @Test
    fun `should return 222 total results number`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubRawAds(222) }
            When { readTotalResults() }
            Then { checkTotalResultsNumber(222) }
        }
    }

    @Test
    fun `should use DominantCategoryHandler to handle the original search params`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given {
                stubSearchParams(
                    hashMapOf(
                        CapiApiParams.IS_HIT_RANK_RELEVANT to "false",
                        CapiApiParams.IS_HIT_RECALL_ALL_CATE to "false",
                        CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE to "false",
                    ),
                )
            }
            Given { stubPage("0") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given { stubRawAds(10) }
            When { readScreen() }
            Then { checkDominantCategoryHandleSearchParams() }
        }
    }

    @Test
    fun `should return SRP screen rows on the expected positions on page 0 in PORTRAIT orientation for FOR_SALE category`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
                Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SAVE_SEARCH_BANNER_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(12, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(13) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
            }
        }

    @Test
    fun `should return SRP screen rows on the expected positions on page 1 in PORTRAIT orientation for FOR_SALE category`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("1") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
                Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(12) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
            }
        }

    @Test
    fun `should return SRP screen with 1 advertisement row in PORTRAIT orientation when listings size is NOT enough for full page with listing rows`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("1") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
                Given { stubRawAds(15) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(9) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
            }
        }

    @Test
    fun `should return SRP screen without advertisement rows in PORTRAIT orientation when listings size is NOT enough`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("1") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
                Given { stubRawAds(8) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(4) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
            }
        }

    @Test
    fun `should return SRP screen rows on the expected positions on page 0 in PORTRAIT orientation for CARS category`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id)) }
                Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SAVE_SEARCH_BANNER_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(12, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(23) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
            }
        }

    @Test
    fun `should return SRP screen rows on the expected positions on page 0 in PORTRAIT orientation when NO listings found`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id,
                            ApiQueryParams.LOCATION_ID to "123456",
                        ),
                    )
                }
                Given { stubRawAds(0) }
                When { readScreen() }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SEARCH_NO_RESULTS_ROW) }
                Then { checkPortraitScreenResponseListingsDataSize(1) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
            }
        }

    @Test
    fun `should return SRP screen with sticky bar and nextPage on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubPage("0") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to CategoryDefaults.JOBS.id,
                        ApiQueryParams.LOCATION_ID to DataFactory.ANOTHER_AD_LOCATION_ID,
                    ),
                )
            }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
            When { readScreen() }
            Then { checkScreenResponseStickyBarIsNotNull() }
            Then {
                checkNextPage(
                    "${ApiQueryParams.PAGE}=1&${ApiQueryParams.SIZE}=${DEFAULT_PAGE_SIZE}&${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.JOBS.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.ANOTHER_AD_LOCATION_ID}&${CapiApiParams.IS_HIT_RANK_RELEVANT}=false&${CapiApiParams.IS_HIT_RECALL_ALL_CATE}=false&${CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE}=false",
                )
            }
        }
    }

    @Test
    fun `should return SRP screen with nextPage and without sticky bar on page 1`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubPage("1") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id,
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                    ),
                )
            }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
            When { readScreen() }
            Then { checkScreenResponseStickyBarIsNull() }
            Then {
                checkNextPage(
                    "${ApiQueryParams.PAGE}=2&${ApiQueryParams.SIZE}=${DEFAULT_PAGE_SIZE}&${ApiQueryParams.CATEGORY_ID}=${CategoryDefaults.FOR_SALE.id}&${ApiQueryParams.LOCATION_ID}=${DataFactory.SOME_AD_LOCATION_ID}&${CapiApiParams.IS_HIT_RANK_RELEVANT}=false&${CapiApiParams.IS_HIT_RECALL_ALL_CATE}=false&${CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE}=false",
                )
            }
        }
    }

    @Test
    fun `should return SRP screen with empty portrait data, without landscape data, without nextPage and without sticky bar on page 5`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("5") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id,
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ),
                    )
                }
                Given { stubRawAds(0) }
                When { readScreen() }
                Then { checkScreenResponseStickyBarIsNull() }
                Then { checkPortraitScreenResponseListingsDataSize(0) }
                Then { checkLandscapeScreenResponseListingsDataSize(null) }
                Then { checkNextPage(null) }
            }
        }

    @Test
    fun `should apply zipcode to query params if locationtype is Postcode`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubPage("0") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id,
                        ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        ApiQueryParams.LOCATION_TYPE to LocationType.POSTCODE.name,
                    ),
                )
            }
            Given {
                stubRawLocationFetcherResponse(RawLocationFactory.createRawLocation(name = "LS42TS"))
            }
            Given { stubRawAds(0) }
            When { readScreen() }
            Then { checkSearchAdQueryParam(ApiQueryParams.ZIPCODE, "LS42TS") }
        }
    }

    @Test
    fun `should return SRP screen view analytics event and analytics parameters`() = runTest {
        runUnitTest(robot) {
            val searchParams = hashMapOf(
                ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                ApiQueryParams.Q to DataFactory.SOME_SEARCH_TERM,
                ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
            )
            Given {
                stubCallHeaders(
                    CallHeadersFactory.createAuthHeaders(
                        ApiHeaderParams.AUTHORISATION_USER_EMAIL to DataFactory.SOME_USER_EMAIL,
                        ApiHeaderParams.AUTHORISATION_USER_TOKEN to DataFactory.SOME_TOKEN,
                    ),
                )
            }
            Given { stubCategoryIdName(CategoryDefaults.CARS.idName) }
            Given { stubPage("0") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given { stubSearchParams(searchParams) }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }

            Given { stubUserDetails() }
            When { readScreen() }
            Then {
                checkScreenAnalyticsParameters(
                    mapOf(
                        AnalyticsParams.Search.CATEGORY_ID to CategoryDefaults.CARS.id,
                        AnalyticsParams.Search.CATEGORY_NAME to CategoryDefaults.CARS.idName,
                        AnalyticsParams.Search.RESULTS to DEFAULT_PAGE_SIZE,
                        AnalyticsParams.Search.PAGE_NUMBER to "0",
                        AnalyticsParams.Search.SEARCH_REQUEST_ID to DataFactory.REQUEST_ID,
                    ),
                )
            }
            Then {
                checkScreenAnalyticsEvents(
                    listOf(
                        AnalyticsEventData(
                            ANALYTICS_VIEW_SEARCH_RESULTS_EVENT_NAME,
                            mapOf(
                                AnalyticsParams.Search.SEARCH_REQUEST_ID to DataFactory.REQUEST_ID,
                                AnalyticsParams.Search.CATEGORY_ID to CategoryDefaults.CARS.id,
                                AnalyticsParams.Search.CATEGORY_NAME to CategoryDefaults.CARS.idName,
                                AnalyticsParams.Search.RESULTS to DEFAULT_PAGE_SIZE,
                                AnalyticsParams.Search.SEARCH_TERM to DataFactory.SOME_SEARCH_TERM,
                                AnalyticsParams.Search.PAGE_NUMBER to "0",
                                AnalyticsParams.Search.ATTRIBUTIONS_VALUE to Json.encodeToString(
                                    JsonObject.serializer(),
                                    buildJsonObject {
                                        searchParams.forEach { (key, value) ->
                                            put(key, JsonPrimitive(value))
                                        }
                                    },
                                ),
                            ) + stubSearchListEvent(),
                        ),
                    ),
                )
            }
        }
    }

    @Test
    fun `should NOT return SRP screen view analytics event on page grater than 0`() = runTest {
        runUnitTest(robot) {
            Given { stubCallHeaders() }
            Given { stubPage("1") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id)) }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
            When { readScreen() }

            Given { stubPage("2") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
            When { readScreen() }

            Given { stubPage("3") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
            When { readScreen() }
        }
    }

    @Test
    fun `should return extended search results when total results for MOTORS category less than threshold and search params meet criteria`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.MOTORS.id,
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                            ApiQueryParams.DISTANCE to Distance.FIFTY.name,
                        ),
                    )
                }
                Given { stubRawAds(10, 4) }
                When { readScreen() }
                Then { checkPortraitScreenResponseListingsDataSize(10) }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SAVE_SEARCH_BANNER_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.ADVERTISING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.EXTENDED_RESULTS_HEADER_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            }
        }

    @Test
    fun `should not return extended search results when total results for MOTORS category less than threshold and search params do NOT meet criteria`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.MOTORS.id,
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                            ApiQueryParams.DISTANCE to Distance.SEVENTY_FIVE.name,
                        ),
                    )
                }
                Given { stubRawAds(10, 4) }
                When { readScreen() }
                Then { checkPortraitScreenResponseListingsDataSize(7) }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SAVE_SEARCH_BANNER_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.ADVERTISING_ROW) }
            }
        }

    @Test
    fun `should return extended search results without header when total results for MOTORS category less than threshold and page size exceeds natural ads total`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("1") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.MOTORS.id,
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                            ApiQueryParams.DISTANCE to Distance.FIFTY.name,
                        ),
                    )
                }
                Given { stubRawAds(0, 10) }
                When { readScreen() }
                Then { checkPortraitScreenResponseListingsDataSize(5) }
                Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
                Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            }
        }

    @Test
    fun `should migrate distance from legacy example ZERO, QUARTER, HALF to ONE if locationtype is Postcode`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id,
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                            ApiQueryParams.LOCATION_TYPE to LocationType.POSTCODE.name,
                            ApiQueryParams.DISTANCE to Distance.HALF.name,
                        ),
                    )
                }
                Given {
                    stubRawLocationFetcherResponse(RawLocationFactory.createRawLocation(name = "LS42TS"))
                }
                Given { stubRawAds(0) }
                When { readScreen() }
                Then { checkSearchAdQueryParam(ApiQueryParams.DISTANCE, "ONE") }
            }
        }

    @Test
    fun `should migrate distance from legacy example ZERO, QUARTER, HALF to ZERO if locationtype is LOCATION or Outcode`() =
        runTest {
            runUnitTest(robot) {
                Given { stubCallHeaders() }
                Given { stubPage("0") }
                Given { stubSize(DEFAULT_PAGE_SIZE) }
                Given {
                    stubSearchParams(
                        hashMapOf(
                            ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id,
                            ApiQueryParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                            ApiQueryParams.LOCATION_TYPE to LocationType.LOCATION.name,
                            ApiQueryParams.DISTANCE to Distance.HALF.name,
                        ),
                    )
                }
                Given {
                    stubRawLocationFetcherResponse(RawLocationFactory.createRawLocation(name = "LS42TS"))
                }
                Given { stubRawAds(0) }
                When { readScreen() }
                Then { checkSearchAdQueryParam(ApiQueryParams.DISTANCE, "ZERO") }
            }
        }

    @Test
    fun `should include Adjust tracking data in ScreenResponse`() = runTest {
        runUnitTest(robot) {
            val expectedAdjustTrackingData = AdjustTrackingData(
                DataFactory.SOME_TOKEN,
                mapOf(SrpAdjustTrackingDataProvider.TOKEN_SCREEN_EVENT to DataFactory.SOME_AD_ID),
            )

            Given { stubCallHeaders() }
            Given { stubPage("0") }
            Given { stubSize(DEFAULT_PAGE_SIZE) }
            Given { stubSearchParams(hashMapOf(ApiQueryParams.CATEGORY_ID to CategoryDefaults.FOR_SALE.id)) }
            Given { stubRawAds(DEFAULT_PAGE_SIZE.toInt()) }
            Given { stubAdjustTrackingData(expectedAdjustTrackingData) }
            When { readScreen() }
            Then { checkAdjustTrackingDataIncluded(expectedAdjustTrackingData) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualTotalResultsResult: TotalResultsDto
        private lateinit var actualScreenResponseResult: ScreenResponse

        private lateinit var rawAds: RawCapiAdList
        private lateinit var searchParams: QueryParams
        private lateinit var page: String
        private lateinit var size: String
        private lateinit var callHeaders: Headers
        private var rawExtendedSearchAds: RawCapiAdList? = null

        private val capiSearchApi: CapiSearchApi = mockk(relaxed = true)
        private val capiCategoryApi: CapiCategoryApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val srpCapiQueryParamsFactory = SrpCapiQueryParamsFactory()
        private val locationsApi: LocationsApi = mockk(relaxed = true)
        private val rawLocationFetcher: RawLocationFetcher = mockk(relaxed = true)
        private val bingAdUrlProvider: BingAdUrlProvider = mockk(relaxed = true)
        private val srpAnalyticsProvider = SrpAnalyticsProvider(
            CommonAnalyticsProviderFactory.createInstance(),
            RawLocationFactory.createRawLocationFetcher(),
            SrpSortHeaderFactory(),
        )
        private val srpTimeAgoFormatter: SrpTimeAgoFormatter = mockk(relaxed = true)
        private val srpListingMapper = SrpListingMapper(srpAnalyticsProvider, srpTimeAgoFormatter)
        private val srpService = SrpService(capiSearchApi, srpCapiQueryParamsFactory, locationsApi)
        private val srpDominantCategoryHandler = spyk(SrpDominantCategoryHandler(capiCategoryApi))
        private val srpZipcodeHandler = SrpZipcodeHandler()
        private val srpTotalResultsMapper = SrpTotalResultsMapper()
        private val srpPageCalculator = SrpPageCalculator()
        private val srpExtendedSearchAdsProvider = SrpExtendedSearchAdsProvider(srpListingMapper, srpService)
        private val srpStickyBarProvider = SrpStickyBarProvider(
            SrpChipsFactory(rawLocationFetcher = rawLocationFetcher),
            SrpSortHeaderFactory(),
        )
        private val srpListingsProvider = SrpListingsProvider(srpListingMapper)
        private val srpSavedSearchesBannerProvider = SrpSavedSearchesBannerProvider()
        private val srpAdvertsProvider =
            SrpAdvertsProvider(SrpAdvertsFactory(bingAdUrlProvider), GAMAdvertUtils(CategoriesTreeCache))
        private val headersProvider = CapiHeadersProvider()
        private val srpAdjustTrackingDataProvider: SrpAdjustTrackingDataProvider = mockk(relaxed = true)
        private val srpButtonUIProvider = mockk<SrpButtonUIProvider>(relaxed = true)
        private val userProfileService = mockk<UserProfileService>(relaxed = true)

        private lateinit var testSubject: DefaultSrpRepository

        override fun setup() {
            mockkObject(CategoriesTreeCache)
            testSubject = DefaultSrpRepository(
                srpService,
                srpDominantCategoryHandler,
                srpZipcodeHandler,
                srpTotalResultsMapper,
                srpPageCalculator,
                srpStickyBarProvider,
                srpListingsProvider,
                srpSavedSearchesBannerProvider,
                srpAdvertsProvider,
                srpAnalyticsProvider,
                rawLocationFetcher,
                headersProvider,
                srpExtendedSearchAdsProvider,
                srpAdjustTrackingDataProvider,
                TestDispatcherProvider(),
                srpButtonUIProvider,
                userProfileService,
            )
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubPage(page: String) {
            this.page = page
        }

        fun stubSize(size: String) {
            this.size = size
        }

        fun stubRawAds(number: Int, extendedSearchNumber: Int? = null) {
            rawAds = RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
            rawExtendedSearchAds =
                extendedSearchNumber?.let { RawCapiAdsFactory.createRawCapiAdList(it, listOf(AdStatus.ACTIVE)) }

            coEvery { capiSearchApi.searchAds(any(), any(), any(), any()) } returnsMany listOfNotNull(
                rawAds,
                rawExtendedSearchAds,
            )
        }

        fun stubSearchListEvent(): Map<String, String> {
            return srpAnalyticsProvider.getSearchListEvent(rawAds);
        }

        fun stubCategoryIdName(categoryIdName: String) {
            every { CategoriesTreeCache.getCategoryIdName(any()) } returns categoryIdName
        }

        fun stubSearchParams(params: QueryParams) {
            searchParams = params
        }

        fun stubRawLocationFetcherResponse(rawLocation: RawLocation) {
            coEvery { rawLocationFetcher.fetchByLocationIdAndType(any(), any()) } returns rawLocation
        }

        fun stubCallHeaders(headers: Headers = CallHeadersFactory.createUnAuthHeaders()) {
            callHeaders = headers
        }

        fun stubUserDetails(userDetails: RawUserServiceUserDetails = UserServiceDataFactory.createRawUserDetails()) {
            coEvery { userServiceApi.getUserDetails(any()) } returns userDetails
        }

        fun stubAdjustTrackingData(adjustTrackingData: AdjustTrackingData) {
            every { srpAdjustTrackingDataProvider.getScreenTrackingData(any(), any()) } returns adjustTrackingData
        }

        suspend fun readTotalResults() {
            actualTotalResultsResult = testSubject.readTotalResults(callHeaders, emptyMap())
        }

        suspend fun readScreen() {
            actualScreenResponseResult = testSubject.readScreen(
                callHeaders,
                searchParams,
                page,
                size,
            )
        }

        fun checkDominantCategoryHandleSearchParams() {
            coVerify { srpDominantCategoryHandler.handleSearchParams(searchParams) }
        }

        fun checkScreenResponseStickyBarIsNotNull() {
            assertNotNull(actualScreenResponseResult.stickyBar)
        }

        fun checkScreenResponseStickyBarIsNull() {
            assertNull(actualScreenResponseResult.stickyBar)
        }

        fun checkPortraitScreenResponseListingsDataSize(expected: Int) {
            assertEquals(expected, actualScreenResponseResult.portraitData.size)
        }

        fun checkLandscapeScreenResponseListingsDataSize(expected: Int?) {
            assertEquals(expected, actualScreenResponseResult.landscapeData?.size)
        }

        fun checkPortraitScreenResponseRowAtPosition(
            position: Int,
            expectedType: RowLayoutType,
        ) {
            assertEquals(expectedType, actualScreenResponseResult.portraitData[position].type)
        }

        fun checkNextPage(expected: String?) {
            assertEquals(expected, actualScreenResponseResult.nextPage)
        }

        fun checkTotalResultsNumber(expected: Int) {
            assertEquals(expected, actualTotalResultsResult.numberFound)
            assertEquals(expected.toString(), actualTotalResultsResult.displayNumberFound)
        }

        fun checkSearchAdQueryParam(key: String, expected: String) {
            coVerify {
                capiSearchApi.searchAds(
                    any(),
                    withArg {
                        assertEquals(it[key], expected)
                    },
                    any(),
                    any(),
                )
            }
        }

        fun checkScreenAnalyticsParameters(expected: Map<String, String>) {
            assertEquals(expected, actualScreenResponseResult.analyticsParameters)
        }

        fun checkScreenAnalyticsEvents(expected: List<AnalyticsEventData>) {
            assertEquals(expected, actualScreenResponseResult.screenViewAnalyticsEvents)
        }

        fun checkAdjustTrackingDataIncluded(expectedAdjustTrackingData: AdjustTrackingData) {
            assertEquals(expectedAdjustTrackingData, actualScreenResponseResult.adjustTrackingData)
        }
    }
}
