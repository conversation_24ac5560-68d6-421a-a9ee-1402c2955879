package api.capi.models;


import com.gumtree.mobile.api.capi.Namespaces;
import org.jetbrains.annotations.NotNull;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;


@Root(name = "user-message", strict = false)
@Namespace(reference = Namespaces.USER, prefix = "user")
public class RawMessage {
    @Attribute
    public String id;

    @Element(name = "sender-id", required = false)
    @Namespace(reference = Namespaces.USER)
    public String senderId;

    @Element(name = "sender-name", required = false)
    @Namespace(reference = Namespaces.USER)
    public String senderName;

    @NotNull
    @Element(name = "msg-content", required = false)
    @Namespace(reference = Namespaces.USER)
    public String message = "";

    @Element(name = "post-time-stamp", required = false)
    @Namespace(reference = Namespaces.USER)
    public String postTimestamp;

    @Element(name = "direction", required = false)
    @Namespace(reference = Namespaces.USER)
    public String direction;
}
