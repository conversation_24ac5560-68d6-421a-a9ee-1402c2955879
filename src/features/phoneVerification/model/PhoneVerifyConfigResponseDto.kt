package com.gumtree.mobile.features.phoneVerification.model

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.routes.DestinationDto
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PhoneVerifyConfigResponseDto(

    @SerialName("code")
    val code: String?,
    @SerialName("data")
    val data: ScreenData?,

) {
    @Serializable
    class ScreenData(
        @SerialName("title")
        val title: String?,
        @SerialName("subTitle")
        val subTitle: String?,
        @SerialName("codeTips")
        val codeTips: String?,
        @SerialName("phone")
        val phone: Phone?,
        @SerialName("button")
        val button: Button?,
        @SerialName("help")
        val help: Help?,
        @SerialName("resend")
        val resend: Resend?,
        @SerialName("analyticsParameters")
        val analyticsParameters: AnalyticsEventData? = null,
    )

    @Serializable
    class Phone(
        @SerialName("label")
        val label: String? = "Mobile number",
        @SerialName("areaIcon")
        val areaIcon: String? = "",
        @SerialName("areaCode")
        val areaCode: String? = "+44",
        @SerialName("errorTips")
        val errorTips: String? = "This phone number cannot be used for verification",
    )

    @Serializable
    class Button(
        @SerialName("text")
        val text: String?,
        @SerialName("url")
        val url: String?,
        @SerialName("errorTips")
        val errorTips: String?,
    )

    @Serializable
    class Resend(
        @SerialName("text")
        val text: String? = "Didn't receive the code?",
        @SerialName("buttonText")
        val buttonText: String? = "Resend code",
        @SerialName("url")
        val url: String?,
    )

    @Serializable
    class Help(
        @SerialName("text")
        val text: String? = "Having trouble?",
        @SerialName("helpText")
        val helpText: String? = "Get help",
        @SerialName("destination")
        val destination: DestinationDto?,
    )
}
