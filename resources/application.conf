ktor {
  development = true

  deployment {
    port = 8080
#     sslPort = 8443
    connectionGroupSize = 2
    workerGroupSize = 3
    #https://stackoverflow.com/questions/78091418/ktor-netty-configuration-values-for-expected-load
    # CallGroup Size Concurrent tasks = Request rate * Response time = 150 requests/second * 0.8 seconds(99 percentile) = 120 concurrent tasks
    callGroupSize = 250
    requestReadTimeoutSeconds = 12
    responseWriteTimeoutSeconds = 15
    watch = [ classes ]
  }
  application {
    mainClass = "com.gumtree.mobile.ApplicationKt"
    modules = [
        com.gumtree.mobile.ApplicationKt.moduleKoin,
        com.gumtree.mobile.ApplicationKt.moduleEvents,
        com.gumtree.mobile.ApplicationKt.moduleHttp,
        com.gumtree.mobile.ApplicationKt.moduleRouting
    ]
  }
}

jwt {
    audience = "gt-audience"
    issuer = "http://0.0.0.0:8080/"
    realm = "User profile access"
    secret = ${BFF_USER_PROFILE_JWT_SECRET}
}
