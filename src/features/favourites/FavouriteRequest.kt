package com.gumtree.mobile.features.favourites

import com.gumtree.mobile.requests.RequestBody
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class FavouriteRequest(
    @SerialName("id")
    val id: String,
) : RequestBody {

    /**
     * Only favourites with NOT empty ID are considered as a valid payload
     * If the favourite ID is empty we throw BadRequestException
     */
    override fun isValid(): Boolean {
        return id.isNotEmpty()
    }
}
