package com.gumtree.mobile.features.reviews

import api.capi.models.RawConversation
import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.api.reviews.bodies.RawCanCreateReviewRequestBody
import com.gumtree.mobile.api.reviews.bodies.RawCreateReviewRequestBody
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.features.myGumtree.v2.MyGumtreeService
import com.gumtree.mobile.responses.ForbiddenException
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.ScreensFlowResponse
import com.gumtree.mobile.utils.extensions.data.findConversationCounterPartyDisplayName
import com.gumtree.mobile.utils.extensions.data.isConversationAboutMyPosting
import com.gumtree.mobile.utils.handleAuthRequestOrNull
import com.gumtree.mobile.utils.runSuspendOrNull
import io.ktor.http.Headers
import kotlinx.coroutines.withContext

/**
 * Handles all CRUD operations about the Reviews feature
 */
interface ReviewsRepository {

    suspend fun readConversationReviewFlow(
        callHeaders: Headers,
        userName: String,
        conversationId: String,
        reviewRating: Int,
    ): ScreensFlowResponse

    suspend fun createConversationReview(
        callHeaders: Headers,
        createConversationReviewRequest: CreateConversationReviewRequest,
        experiments: ClientExperiments,
        userName: String,
        conversationId: String,
    ): CreateReviewResponseDto
}

class DefaultReviewsRepository(
    private val reviewsService: ReviewsService,
    private val reviewsAccountsFetcher: ReviewsAccountsFetcher,
    private val reviewsBodyCreator: ReviewsBodyCreator,
    private val reviewsMapper: ReviewsMapper,
    private val reviewsConversationUIFlowProvider: ReviewsConversationUIFlowProvider,
    private val headersProvider: ApiHeadersProvider,
    private val dispatcherProvider: DispatcherProvider,
    private val myGumtreeService: MyGumtreeService,
) : ReviewsRepository {

    override suspend fun readConversationReviewFlow(
        callHeaders: Headers,
        userName: String,
        conversationId: String,
        reviewRating: Int,
    ): ScreensFlowResponse {
        val authHeaders = headersProvider.createAuthorisedHeaders(callHeaders)

        val rawConversationData = withContext(dispatcherProvider.io) {
            reviewsService.getConversationDetails(authHeaders, userName, conversationId)
        }

        val adId = rawConversationData.adId
        val rawAdDetails = myGumtreeService.getAdDetailsById(adId)

        val counterPartyDisplayName = rawConversationData.findConversationCounterPartyDisplayName(userName, null)
        val isConversationAboutMyPosting = rawConversationData.isConversationAboutMyPosting(userName)

        return when (reviewRating) {
            in (ReviewsScreenUiConfiguration.MIN_REVIEW_RATING..2) -> ScreensFlowResponse(
                flow = listOf(
                    reviewsConversationUIFlowProvider.createRatingStep(
                        counterPartyDisplayName,
                        conversationId,
                        reviewRating,
                        isConversationAboutMyPosting,
                        selectedStep = 1,
                        rawAdDetails,
                    ),
                    reviewsConversationUIFlowProvider.createNegativeFeedbackStep(
                        conversationId,
                        reviewRating = reviewRating,
                        isConversationAboutMyPosting = isConversationAboutMyPosting,
                        selectedStep = 2,
                        rawAdDetails,
                    ),
                    reviewsConversationUIFlowProvider.createPositiveFeedbackStep(
                        conversationId,
                        reviewRating = reviewRating,
                        isConversationAboutMyPosting = isConversationAboutMyPosting,
                        selectedStep = 3,
                        rawAdDetails,
                    ),
                    reviewsConversationUIFlowProvider.createSuccessStep(counterPartyDisplayName),
                ),
            )

            else -> ScreensFlowResponse(
                flow = listOfReviewsFlow(
                    counterPartyDisplayName,
                    conversationId,
                    reviewRating,
                    isConversationAboutMyPosting,
                    rawAdDetails,
                ),
            )
        }
    }

    override suspend fun createConversationReview(
        callHeaders: Headers,
        createConversationReviewRequest: CreateConversationReviewRequest,
        experiments: ClientExperiments,
        userName: String,
        conversationId: String,
    ): CreateReviewResponseDto {
        val capiAuthHeaders = headersProvider.createAuthorisedHeaders(callHeaders)

        val rawConversationData = withContext(dispatcherProvider.io) {
            reviewsService.getConversationDetails(capiAuthHeaders, userName, conversationId)
        }
        val (reviewerId, revieweeId) = reviewsAccountsFetcher.fetch(rawConversationData, userName)

        val canReviewerPostReview = withContext(dispatcherProvider.io) {
            handleAuthRequestOrNull(callHeaders) {
                reviewsService.canPostReview(
                    RawCanCreateReviewRequestBody(
                        reviewer = RawCanCreateReviewRequestBody.RawReviewer(reviewerId = reviewerId),
                        revieweeId = revieweeId,
                        itemId = rawConversationData.adId.toInt(),
                    ),
                )
            }?.value ?: false
        }

        return runSuspendOrNull(canReviewerPostReview) {
            val createConversationReviewBody = createConversationReviewBody(
                capiAuthHeaders,
                experiments,
                createConversationReviewRequest,
                rawConversationData,
                reviewerId,
                revieweeId,
                userName,
            )
            val rawPostReviewData = withContext(dispatcherProvider.io) {
                reviewsService.postReview(createConversationReviewBody)
            }
            reviewsMapper.mapCreateReviewResponse(rawPostReviewData)
        } ?: throw ForbiddenException("The user is forbidden to post conversation review")
    }

    private suspend fun createConversationReviewBody(
        capiAuthHeaders: ApiHeaders,
        experiments: ClientExperiments,
        createConversationReviewRequest: CreateConversationReviewRequest,
        rawConversationData: RawConversation,
        reviewerId: Int,
        revieweeId: Int,
        userName: String,
    ): RawCreateReviewRequestBody {
        return if (experiments.isB(Experiment.GTNA_4301) || experiments.isB(Experiment.GTNA_4302)) {
            val rawAdDetailsData = withContext(dispatcherProvider.io) {
                reviewsService.getAdDetailsById(rawConversationData.adId)
            }
            reviewsBodyCreator.createNew(
                request = createConversationReviewRequest,
                rawConversationData = rawConversationData,
                rawAdDetailsData = rawAdDetailsData,
                reviewerId = reviewerId,
                revieweeId = revieweeId,
                userName = userName,
            )
        } else {
            val rawAdDetailsData = withContext(dispatcherProvider.io) {
                reviewsService.getAdDetail(capiAuthHeaders, rawConversationData.adId)
            }
            reviewsBodyCreator.create(
                request = createConversationReviewRequest,
                rawConversationData = rawConversationData,
                rawAdDetailsData = rawAdDetailsData,
                reviewerId = reviewerId,
                revieweeId = revieweeId,
                userName = userName,
            )
        }
    }

    private fun listOfReviewsFlow(
        counterPartyDisplayName: String,
        conversationId: String,
        reviewRating: Int,
        isConversationAboutMyPosting: Boolean,
        rawAdDetails: RawFlatAd,
    ): List<ScreenResponse> {
        return listOf(
            reviewsConversationUIFlowProvider.createRatingStep(
                counterPartyDisplayName,
                conversationId,
                reviewRating,
                isConversationAboutMyPosting,
                selectedStep = 1,
                rawAdDetails,
            ),
            reviewsConversationUIFlowProvider.createPositiveFeedbackStep(
                conversationId,
                reviewRating,
                isConversationAboutMyPosting,
                selectedStep = 2,
                rawAdDetails,
            ),
            reviewsConversationUIFlowProvider.createNegativeFeedbackStep(
                conversationId,
                reviewRating,
                isConversationAboutMyPosting,
                selectedStep = 3,
                rawAdDetails,
            ),
            reviewsConversationUIFlowProvider.createSuccessStep(counterPartyDisplayName),
        )
    }
}
