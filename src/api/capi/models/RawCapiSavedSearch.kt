package com.gumtree.mobile.api.capi.models

import com.gumtree.mobile.api.capi.Namespaces
import org.simpleframework.xml.Attribute
import org.simpleframework.xml.Element
import org.simpleframework.xml.ElementList
import org.simpleframework.xml.Namespace
import org.simpleframework.xml.Root

@Root(name = "alert", strict = false)
@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
data class RawCapiSavedSearch(

    @field:Attribute(name = "id", required = false)
    @param:Attribute(name = "id")
    val id: String,

    @param:ElementList(name = "alert-destinations", entry = "alert-destination", required = false)
    @get:ElementList(name = "alert-destinations", entry = "alert-destination", required = false)
    @Namespace(reference = Namespaces.AD)
    val alertDestinations: ArrayList<RawCapiAlertDestination>? = null,

    @Namespace(reference = Namespaces.AD)
    @field:Element(name = "alert-type")
    @param:Element(name = "alert-type")
    val alertType: RawCapiAlertType? = null,

    @Namespace(reference = Namespaces.AD)
    @field:Element(name = "search-link")
    @param:Element(name = "search-link")
    val searchLink: RawCapiSearchLink? = null,

    @Namespace(reference = Namespaces.AD)
    @field:Element(name = "search-description")
    @param:Element(name = "search-description")
    val searchDescription: String? = null,

)

@Root(name = "alert-destination", strict = false)
@Namespace(reference = Namespaces.AD)
data class RawCapiAlertDestination(

    @Namespace(reference = Namespaces.AD)
    @field:Element(name = "destination-type")
    @param:Element(name = "destination-type")
    val destinationType: RawCapiDestinationType? = null,

    @Namespace(reference = Namespaces.AD)
    @field:Element(name = "status")
    @param:Element(name = "status")
    val statusType: RawCapiStatusType? = null,

)

@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
data class RawCapiDestinationType(

    @Namespace(reference = Namespaces.AD)
    @field:Element(name = "value")
    @param:Element(name = "value")
    val value: SavedSearchDestination? = null,

)

@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
data class RawCapiStatusType(

    @Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
    @field:Element(name = "value")
    @param:Element(name = "value")
    val value: SavedSearchStatus? = null,

)

@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
data class RawCapiAlertType(

    @field:Element(name = "value")
    @param:Element(name = "value")
    val value: SavedSearchAlert? = null,

)

@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
data class RawCapiSearchLink(

    @field:Attribute(name = "rel")
    @param:Attribute(name = "rel")
    val rel: String? = null,

    @field:Attribute(name = "href")
    @param:Attribute(name = "href")
    val href: String? = null,

)
