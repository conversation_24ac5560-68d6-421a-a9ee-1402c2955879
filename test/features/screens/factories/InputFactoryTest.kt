package features.screens.factories

import com.gumtree.mobile.features.filters.FiltersScreenUiConfiguration
import com.gumtree.mobile.features.screens.factories.InputFactory
import com.gumtree.mobile.features.screens.layoutsData.DoubleInputCardDto
import com.gumtree.mobile.features.screens.layoutsData.InputCardDto
import com.gumtree.mobile.features.screens.layoutsData.ResetToggle
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.LocationDefaults
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class InputFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return input card with param, only`() {
        runUnitTest(robot) {
            When { buildInputCardWithParamOnly(param = ApiQueryParams.LOCATION_ID) }
            Then { checkInputCardParam(ApiQueryParams.LOCATION_ID) }
            Then { checkInputCardText(null) }
            Then { checkInputCardTitle(null) }
            Then { checkInputCardHint(null) }
            Then { checkInputCardIconType(null) }
            Then { checkInputCardInputType(null) }
        }
    }

    @Test
    fun `should return input card with text, param, title, hint, icon type, input type and destination`() {
        runUnitTest(robot) {
            When {
                buildInputCard(
                    param = ApiQueryParams.LOCATION_ID,
                    text = LocationDefaults.ALL_UK.text,
                    title = "Location",
                    hint = "Set your location",
                    iconType = InputCardDto.IconType.SEARCH,
                    inputType = InputCardDto.InputType.NUMBER
                )
            }
            Then { checkInputCardText(LocationDefaults.ALL_UK.text) }
            Then { checkInputCardParam(ApiQueryParams.LOCATION_ID) }
            Then { checkInputCardTitle("Location") }
            Then { checkInputCardHint("Set your location") }
            Then { checkInputCardIconType(InputCardDto.IconType.SEARCH) }
            Then { checkInputCardInputType(InputCardDto.InputType.NUMBER) }
        }
    }

    @Test
    fun `should return input card with text, param, and title`() {
        runUnitTest(robot) {
            When {
                buildInputCard(
                    param = ApiQueryParams.LOCATION_ID,
                    text = LocationDefaults.ALL_UK.text,
                    title = "Location",
                    hint = null,
                    iconType = null,
                    inputType = null
                )
            }
            Then { checkInputCardText(LocationDefaults.ALL_UK.text) }
            Then { checkInputCardParam(ApiQueryParams.LOCATION_ID) }
            Then { checkInputCardTitle("Location") }
            Then { checkInputCardHint(null) }
            Then { checkInputCardIconType(null) }
            Then { checkInputCardInputType(null) }
        }
    }

    @Test
    fun `should return double input card with param1, text1, title1, hint1, inputType1, param2, text2, title2, hint2, inputType2 and separator`() {
        runUnitTest(robot) {
            When {
                buildDoubleInputCard(
                    param1 = ApiQueryParams.MIN_PRICE,
                    text1 = "50",
                    title1 = FiltersScreenUiConfiguration.MIN_TEXT,
                    hint1 = FiltersScreenUiConfiguration.MIN_PRICE_HINT_TEXT,
                    inputType1 = InputCardDto.InputType.CURRENCY,
                    param2 = ApiQueryParams.MAX_PRICE,
                    text2 = "10000",
                    title2 = FiltersScreenUiConfiguration.MAX_TEXT,
                    hint2 = FiltersScreenUiConfiguration.MAX_PRICE_HINT_TEXT,
                    inputType2 = InputCardDto.InputType.CURRENCY,
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                    resetToggle = ResetToggle(text = FiltersScreenUiConfiguration.PRICE_RESET_TEXT),
                )
            }
            Then { checkDoubleInputCardParam1(ApiQueryParams.MIN_PRICE) }
            Then { checkDoubleInputCardText1("50") }
            Then { checkDoubleInputCardTitle1(FiltersScreenUiConfiguration.MIN_TEXT) }
            Then { checkDoubleInputCardHint1(FiltersScreenUiConfiguration.MIN_PRICE_HINT_TEXT) }
            Then { checkDoubleInputCardInputType1(InputCardDto.InputType.CURRENCY) }
            Then { checkDoubleInputCardParam2(ApiQueryParams.MAX_PRICE) }
            Then { checkDoubleInputCardText2("10000") }
            Then { checkDoubleInputCardTitle2(FiltersScreenUiConfiguration.MAX_TEXT) }
            Then { checkDoubleInputCardHint2(FiltersScreenUiConfiguration.MAX_PRICE_HINT_TEXT) }
            Then { checkDoubleInputCardInputType2(InputCardDto.InputType.CURRENCY) }
            Then { checkDoubleInputCardSeparatorText(FiltersScreenUiConfiguration.TO_TEXT) }
            Then { checkDoubleInputResetText(FiltersScreenUiConfiguration.PRICE_RESET_TEXT) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualInputCardResult: InputCardDto
        private lateinit var actualDoubleInputCardResult: DoubleInputCardDto

        private val testSubject = InputFactory()

        fun buildInputCardWithParamOnly(param: String) {
            actualInputCardResult = testSubject.buildInputCard(param) as InputCardDto
        }

        fun buildInputCard(
            param: String,
            text: String,
            title: String,
            hint: String?,
            iconType: InputCardDto.IconType?,
            inputType: InputCardDto.InputType?
        ) {
            actualInputCardResult = testSubject.buildInputCard(
                param,
                text,
                title,
                hint,
                iconType,
                inputType
            ) as InputCardDto
        }

        fun buildDoubleInputCard(
            param1: String,
            text1: String,
            title1: String,
            hint1: String,
            inputType1: InputCardDto.InputType,
            param2: String,
            text2: String,
            title2: String,
            hint2: String,
            inputType2: InputCardDto.InputType,
            separatorText: String,
            resetToggle: ResetToggle?,
        ) {
            actualDoubleInputCardResult = testSubject.buildDoubleInputCard(
                param1,
                text1,
                title1,
                hint1,
                inputType1,
                param2,
                text2,
                title2,
                hint2,
                inputType2,
                separatorText,
                resetToggle,
            ) as DoubleInputCardDto
        }

        fun checkInputCardText(expected: String?) {
            assertEquals(expected, actualInputCardResult.text)
        }

        fun checkInputCardParam(expected: String) {
            assertEquals(expected, actualInputCardResult.param)
        }

        fun checkInputCardTitle(expected: String?) {
            assertEquals(expected, actualInputCardResult.title)
        }

        fun checkInputCardHint(expected: String?) {
            assertEquals(expected, actualInputCardResult.hint)
        }

        fun checkInputCardIconType(expected: InputCardDto.IconType?) {
            assertEquals(expected, actualInputCardResult.iconType)
        }

        fun checkInputCardInputType(expected: InputCardDto.InputType?) {
            assertEquals(expected, actualInputCardResult.inputType)
        }

        fun checkDoubleInputCardText1(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.text1)
        }

        fun checkDoubleInputCardParam1(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.param1)
        }

        fun checkDoubleInputCardTitle1(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.title1)
        }

        fun checkDoubleInputCardHint1(expected: String?) {
            assertEquals(expected, actualDoubleInputCardResult.hint1)
        }

        fun checkDoubleInputCardInputType1(expected: InputCardDto.InputType?) {
            assertEquals(expected, actualDoubleInputCardResult.inputType1)
        }

        fun checkDoubleInputCardText2(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.text2)
        }

        fun checkDoubleInputCardParam2(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.param2)
        }

        fun checkDoubleInputCardTitle2(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.title2)
        }

        fun checkDoubleInputCardHint2(expected: String?) {
            assertEquals(expected, actualDoubleInputCardResult.hint2)
        }

        fun checkDoubleInputCardInputType2(expected: InputCardDto.InputType?) {
            assertEquals(expected, actualDoubleInputCardResult.inputType2)
        }

        fun checkDoubleInputCardSeparatorText(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.separatorText)
        }

        fun checkDoubleInputResetText(expected: String) {
            assertEquals(expected, actualDoubleInputCardResult.resetToggle?.text)
        }
    }
}