package com.gumtree.mobile.utils

import com.gumtree.mobile.plugins.appMicrometerRegistry
import com.gumtree.mobile.utils.annotations.NotCovered
import io.micrometer.core.instrument.Counter

private const val METRICS_PREFIX = "mobile_bff_"

/**
 * Mobile BFF API custom Micrometer Metrics
 */
@NotCovered
enum class Metric {
    ERROR,
    LOGIN,
    LOGIN_EXCHANGE,
    REGISTRATION,
    ;

    @NotCovered
    enum class Tag {
        URI,
        CAUSE,
        TYPE,
        SOCIAL,
        MARKETING,
    }

    @NotCovered
    fun record(vararg tagValues: Pair<Tag, String>) {
        with(Counter.builder(METRICS_PREFIX + name.lowercase())) {
            tagValues.forEach {
                tag(it.first.name.lowercase(), it.second)
            }
            register(appMicrometerRegistry).increment()
        }
    }
}
