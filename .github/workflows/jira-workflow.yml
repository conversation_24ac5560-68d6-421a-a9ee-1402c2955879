name: jira
on:
  pull_request:
    branches:
      - main
    types: [opened, closed]

jobs:

  code-review:
    if: github.event.action == 'opened'
    uses: ./.github/workflows/reusable-workflow-jira-move-ticket.yml
    with:
      branch_name: ${{ github.head_ref || github.ref_name }}
      jira_column: ${{ vars.JIRA_PR_OPEN_COLUMN }}
    secrets:
      jira_base_url: ${{ vars.JIRA_BASE_URL }}
      jira_user_email: ${{ secrets.JIRA_USER_EMAIL }}
      jira_api_toke: ${{ secrets.JIRA_API_TOKEN }}


  ready-for-release:
    if: github.event.pull_request.merged == true
    uses: ./.github/workflows/reusable-workflow-jira-move-ticket.yml
    with:
      branch_name: ${{ github.head_ref || github.ref_name }}
      jira_column: ${{ vars.JIRA_MERGE_COMPLETE_COLUMN }}
    secrets:
      jira_base_url: ${{ vars.JIRA_BASE_URL }}
      jira_user_email: ${{ secrets.JIRA_USER_EMAIL }}
      jira_api_toke: ${{ secrets.JIRA_API_TOKEN }}
