package com.gumtree.mobile.features.reportListing

import com.gumtree.mobile.requests.RequestBody
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * The information for report listing from the client
 */
@Serializable
data class ReportListingRequest(
    @SerialName("adId")
    val adId: String,
    @SerialName("reason")
    val reason: String,
    @SerialName("comment")
    val comment: String,
) : RequestBody {
    override fun isValid(): Boolean {
        return adId.isNotEmpty() && reason.isNotEmpty()
    }
}
