package api.fullAdsSearch

import com.gumtree.mobile.api.fullAdsSearch.FULL_ADS_SEARCH_API_CONNECTION_POOL_KEEP_ALIVE_DURATION
import com.gumtree.mobile.api.fullAdsSearch.FULL_ADS_SEARCH_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS
import com.gumtree.mobile.api.fullAdsSearch.FullAdsSearchApiOkHttpConnectionPoolConfiguration
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.annotations.ParallelTest
import tools.runUnitTest
import java.util.concurrent.TimeUnit

@ParallelTest
class FullAdsSearchApiOkHttpConnectionPoolConfigurationTest {

    private val robot = Robot()

    @Test
    fun `should return FullAdsSearchApi OkHttp connection pool with expected settings`() {
        runUnitTest(robot) {
            Then { checkMaxIdleConnections(FULL_ADS_SEARCH_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS) }
            Then { checkKeepAliveDuration(FULL_ADS_SEARCH_API_CONNECTION_POOL_KEEP_ALIVE_DURATION) }
            Then { checkTimeUnit(TimeUnit.SECONDS) }
        }
    }
    private class Robot: BaseRobot {

        private val testSubject = FullAdsSearchApiOkHttpConnectionPoolConfiguration()

        fun checkMaxIdleConnections(expected: Int) {
            assertEquals(expected, testSubject.maxIdleConnections)
        }

        fun checkKeepAliveDuration(expected: Long) {
            assertEquals(expected, testSubject.keepAliveDuration)
        }

        fun checkTimeUnit(expected: TimeUnit) {
            assertEquals(expected, testSubject.timeUnit)
        }
    }
}