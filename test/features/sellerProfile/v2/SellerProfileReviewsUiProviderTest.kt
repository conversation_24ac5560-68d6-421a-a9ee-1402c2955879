package features.sellerProfile.v2

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.api.reviews.models.RawReview
import com.gumtree.mobile.api.reviews.models.RawReviews
import com.gumtree.mobile.features.reviews.ReviewsTag
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.TextSegment
import com.gumtree.mobile.features.screens.Typography
import com.gumtree.mobile.features.screens.createGridSizes
import com.gumtree.mobile.features.screens.factories.ReviewsChipsFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.ChipDto
import com.gumtree.mobile.features.screens.layoutsData.FIVE_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.FOUR_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.ONE_STAR_RATING
import com.gumtree.mobile.features.screens.layoutsData.ReviewCardDto
import com.gumtree.mobile.features.screens.layoutsData.ReviewsOverviewCardDto
import com.gumtree.mobile.features.screens.layoutsData.ReviewsOverviewCardDto.RatingDistribution
import com.gumtree.mobile.features.screens.layoutsData.THREE_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.TWO_STARS_RATING
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileReviewDateFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileReviewsGridSizes
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileReviewsOverviewCalculator
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileReviewsUiProvider
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.utils.CategoryDefaults
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.rawDataFactory.RawReviewsFactory
import tools.runUnitTest

class SellerProfileReviewsUiProviderTest {

    private val robot = Robot()

    @ParameterizedTest
    @CsvSource(
        "3, 1, 0, 0, 0, 1.2f, 4, 75, 25, 0, 0, 0",
        "3, 3, 0, 0, 0, 1.5f, 6, 50, 50, 0, 0, 0",
        "0, 2, 3, 4, 0, 3.2f, 9, 0, 22, 33, 44, 0",
        "0, 1, 0, 5, 3, 4.1f, 9, 0, 11, 0, 55, 33",
        "0, 0, 0, 2, 5, 4.7f, 7, 0, 0, 0, 28, 71",
        "0, 0, 0, 1, 12, 4.9f, 13, 0, 0, 0, 7, 92",
        "0, 0, 0, 2, 0, 4.0f, 2, 0, 0, 0, 100, 0",
        "0, 0, 0, 0, 4, 5.0f, 4, 0, 0, 0, 0, 100",
    )
    fun `should return seller profile reviews overview row on page 0`(
        numberOfOneStarRatings: Int,
        numberOfTwoStarsRatings: Int,
        numberOfThreeStarsRatings: Int,
        numberOfFourStarsRatings: Int,
        numberOfFiveStarsRatings: Int,
        expectedAverage: Float,
        expectedTotal: Int,
        expectedOneStarPercentage: Int,
        expectedTwoStarsPercentage: Int,
        expectedThreeStarsPercentage: Int,
        expectedFourStarsPercentage: Int,
        expectedFiveStarsPercentage: Int,
    ) {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given {
                stubRawReviews(
                    RawReviewsFactory.createRawReviews(
                        ONE_STAR_RATING to numberOfOneStarRatings,
                        TWO_STARS_RATING to numberOfTwoStarsRatings,
                        THREE_STARS_RATING to numberOfThreeStarsRatings,
                        FOUR_STARS_RATING to numberOfFourStarsRatings,
                        FIVE_STARS_RATING to numberOfFiveStarsRatings,
                    ),
                )
            }
            Given { stubFormattedReviewDate(DataFactory.SOME_REVIEW_DATE) }
            When { createSellerProfileReviewsOverviewRow() }
            Then { checkRowLayoutType(RowLayoutType.REVIEW_OVERVIEW_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkRowLayoutDataTypeAtPosition(0, ReviewsOverviewCardDto::class.java) }
            Then {
                checkSellerProfileReviewsOverviewCard(
                    ReviewsOverviewCardDto(
                        averageRating = expectedAverage,
                        totalReviews = expectedTotal,
                        ratingDistribution = listOf(
                            RatingDistribution(
                                rating = ONE_STAR_RATING,
                                percentage = expectedOneStarPercentage,
                            ),
                            RatingDistribution(
                                rating = TWO_STARS_RATING,
                                percentage = expectedTwoStarsPercentage,
                            ),
                            RatingDistribution(
                                rating = THREE_STARS_RATING,
                                percentage = expectedThreeStarsPercentage,
                            ),
                            RatingDistribution(
                                rating = FOUR_STARS_RATING,
                                percentage = expectedFourStarsPercentage,
                            ),
                            RatingDistribution(
                                rating = FIVE_STARS_RATING,
                                percentage = expectedFiveStarsPercentage,
                            ),
                        ),
                    ),
                )
            }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1,2,3,4,5])
    fun `should NOT return seller profile reviews overview row when seller has reviews but page is grater than 0`(page: Int) {
        runUnitTest(robot) {
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(10)) }
            Given { stubPage(page) }
            When { createSellerProfileReviewsOverviewRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should NOT return seller profile reviews overview row when page is 0 but seller has NO reviews`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(0)) }
            When { createSellerProfileReviewsOverviewRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should return seller profile feedback overview title row on page 0`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(10)) }
            When { createFeedbackOverviewTitleRow() }
            Then { checkRowLayoutType(RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto.Left::class.java) }
            Then {
                checkSellerProfileFeedbackOverviewTitleCard(
                    TitleCardDto.Left(
                        text = SellerProfileScreenUiConfiguration.TOP_FEEDBACK,
                        size = TitleCardDto.Size.SMALL,
                        iconLeft = null,
                        colour = TitleCardDto.Colour.FOREGROUND_DEFAULT,
                    ),
                )
            }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1,2,3,4,5])
    fun `should NOT return seller profile feedback overview title row when seller has reviews but page is grater than 0`(page: Int) {
        runUnitTest(robot) {
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(10)) }
            Given { stubPage(page) }
            When { createFeedbackOverviewTitleRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should NOT return seller profile feedback overview title row when page is 0 but seller has NO reviews`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(0)) }
            When { createFeedbackOverviewTitleRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should return seller profile feedback overview chips row on page 0`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given {
                stubRawReviews(
                    RawReviewsFactory.createRawReviews(
                        numberOfReviews = 10,
                        RawReviewsFactory.createRawReviewFeedbackSummary(
                            positiveFeedbackSummaryTags = RawReviewsFactory.createListOfRawFeedbackSummaryTags(
                                ReviewsTag.FRIENDLY to 2,
                                ReviewsTag.POLITE to 3,
                                ReviewsTag.HELPFUL to 3,
                            ),
                            negativeFeedbackTags = RawReviewsFactory.createListOfRawFeedbackSummaryTags(
                                ReviewsTag.RUDE to 1,
                                ReviewsTag.CANCELLED_OFFER to 2,
                            ),
                        ),
                    ),
                )
            }
            When { createFeedbackOverviewChipsRow() }
            Then { checkRowLayoutType(RowLayoutType.CHIPS_FEEDBACK_ROW) }
            Then { checkRowLayoutDataSize(5) }
            Then { checkRowLayoutDataTypeAtPosition(0, ChipDto.StandardStyled::class.java) }
            Then { checkRowLayoutDataTypeAtPosition(1, ChipDto.StandardStyled::class.java) }
            Then { checkRowLayoutDataTypeAtPosition(2, ChipDto.StandardStyled::class.java) }
            Then { checkRowLayoutDataTypeAtPosition(3, ChipDto.StandardStyled::class.java) }
            Then { checkRowLayoutDataTypeAtPosition(4, ChipDto.StandardStyled::class.java) }
            Then {
                checkSellerProfileFeedbackOverviewChipAtPosition(
                    position = 0,
                    expected = ChipDto.StandardStyled(
                        title = "${ReviewsTag.FRIENDLY.displayText} 2",
                        textSegments = listOf(
                            TextSegment(
                                text = ReviewsTag.FRIENDLY.displayText,
                                typography = Typography.BODY_SMALL_REGULAR,
                            ),
                            TextSegment(
                                text = 2.toString(),
                                typography = Typography.BODY_SMALL_SEMIBOLD,
                            ),
                        ),
                    )
                )
            }
            Then {
                checkSellerProfileFeedbackOverviewChipAtPosition(
                    position = 1,
                    expected = ChipDto.StandardStyled(
                        title = "${ReviewsTag.POLITE.displayText} 3",
                        textSegments = listOf(
                            TextSegment(
                                text = ReviewsTag.POLITE.displayText,
                                typography = Typography.BODY_SMALL_REGULAR,
                            ),
                            TextSegment(
                                text = 3.toString(),
                                typography = Typography.BODY_SMALL_SEMIBOLD,
                            ),
                        ),
                    )
                )
            }
            Then {
                checkSellerProfileFeedbackOverviewChipAtPosition(
                    position = 2,
                    expected = ChipDto.StandardStyled(
                        title = "${ReviewsTag.HELPFUL.displayText} 3",
                        textSegments = listOf(
                            TextSegment(
                                text = ReviewsTag.HELPFUL.displayText,
                                typography = Typography.BODY_SMALL_REGULAR,
                            ),
                            TextSegment(
                                text = 3.toString(),
                                typography = Typography.BODY_SMALL_SEMIBOLD,
                            ),
                        ),
                    )
                )
            }
            Then {
                checkSellerProfileFeedbackOverviewChipAtPosition(
                    position = 3,
                    expected = ChipDto.StandardStyled(
                        title = "${ReviewsTag.RUDE.displayText} 1",
                        textSegments = listOf(
                            TextSegment(
                                text = ReviewsTag.RUDE.displayText,
                                typography = Typography.BODY_SMALL_REGULAR,
                            ),
                            TextSegment(
                                text = 1.toString(),
                                typography = Typography.BODY_SMALL_SEMIBOLD,
                            ),
                        ),
                    )
                )
            }
            Then {
                checkSellerProfileFeedbackOverviewChipAtPosition(
                    position = 4,
                    expected = ChipDto.StandardStyled(
                        title = "${ReviewsTag.CANCELLED_OFFER.displayText} 2",
                        textSegments = listOf(
                            TextSegment(
                                text = ReviewsTag.CANCELLED_OFFER.displayText,
                                typography = Typography.BODY_SMALL_REGULAR,
                            ),
                            TextSegment(
                                text = 2.toString(),
                                typography = Typography.BODY_SMALL_SEMIBOLD,
                            ),
                        ),
                    )
                )
            }
            Then { checkRowLayoutBottomDivider(null) }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1,2,3,4,5])
    fun `should NOT return seller profile feedback overview chips row when seller has reviews but page is grater than 0`(page: Int) {
        runUnitTest(robot) {
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(10)) }
            Given { stubPage(page) }
            When { createFeedbackOverviewChipsRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should NOT return seller profile feedback overview chips row when page is 0 but seller has NO reviews`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(0)) }
            When { createFeedbackOverviewChipsRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should NOT return seller profile reviews rows when seller has NO reviews`() {
        runUnitTest(robot) {
            Given { stubRawUserProfiles(emptyMap()) }
            Given { stubRawReviews(RawReviewsFactory.createRawReviews(0)) }
            When { createReviewsRows() }
            Then { checkReviewsRowsSize(0) }
        }
    }

    @Test
    fun `should return seller profile reviews rows`() {
        runUnitTest(robot) {
            Given { stubFormattedReviewDate(DataFactory.SOME_REVIEW_DATE) }
            Given {
                stubRawUserProfiles(
                    mapOf(
                        DataFactory.SOME_USER_ID.toInt() to RawPapiUserProfileFactory.createRawUserProfile(
                            displayName = DataFactory.SOME_USER_FIRST_NAME,
                        ),
                        DataFactory.ANOTHER_USER_ID.toInt() to RawPapiUserProfileFactory.createRawUserProfile(
                            displayName = DataFactory.ANOTHER_USER_FIRST_NAME,
                        ),
                    ),
                )
            }
            Given {
                stubRawReviews(
                    RawReviews(
                        reviews = listOf(
                            RawReviewsFactory.createRawReview(
                                reviewerId = DataFactory.SOME_USER_ID.toInt(),
                                rating = 3,
                                categoryId = CategoryDefaults.ALL_CATEGORIES.id,
                                direction = RawReview.Direction.B2S,
                                feedback = null,
                            ),
                            RawReviewsFactory.createRawReview(
                                reviewerId = DataFactory.SOME_USER_ID.toInt(),
                                rating = 4,
                                categoryId = CategoryDefaults.ALL_CATEGORIES.id,
                                direction = RawReview.Direction.B2S,
                                feedback = null,
                            ),
                            RawReviewsFactory.createRawReview(
                                reviewerId = DataFactory.ANOTHER_USER_ID.toInt(),
                                rating = 5,
                                categoryId = CategoryDefaults.ALL_CATEGORIES.id,
                                direction = RawReview.Direction.S2B,
                                feedback = null,
                            ),
                        ),
                        feedbackSummary = null,
                    ),
                )
            }
            When { createReviewsRows() }
            Then { checkReviewsRowsSize(3) }
            Then { checkReviewsRowsType(0, RowLayoutType.REVIEW_ROW) }
            Then { checkReviewsRowsType(1, RowLayoutType.REVIEW_ROW) }
            Then { checkReviewsRowsType(2, RowLayoutType.REVIEW_ROW) }
            Then { checkReviewsRowsBottomDivider(0, true) }
            Then { checkReviewsRowsBottomDivider(1, true) }
            Then { checkReviewsRowsBottomDivider(2, true) }
            Then {
                checkReviewsRowsData(
                    rowPosition = 0,
                    expected = ReviewCardDto(
                        reviewerName = DataFactory.SOME_USER_FIRST_NAME,
                        reviewerAbbreviation = DataFactory.SOME_USER_FIRST_NAME.first().toString(),
                        title = "${SellerProfileScreenUiConfiguration.BUYER_TEXT}: ${CategoryDefaults.ALL_CATEGORIES.text}",
                        rating = 3,
                        timeSincePosted = DataFactory.SOME_REVIEW_DATE,
                        feedback = null,
                    ),
                )
            }
            Then {
                checkReviewsRowsData(
                    rowPosition = 1,
                    expected = ReviewCardDto(
                        reviewerName = DataFactory.SOME_USER_FIRST_NAME,
                        reviewerAbbreviation = DataFactory.SOME_USER_FIRST_NAME.first().toString(),
                        title = "${SellerProfileScreenUiConfiguration.BUYER_TEXT}: ${CategoryDefaults.ALL_CATEGORIES.text}",
                        rating = 4,
                        timeSincePosted = DataFactory.SOME_REVIEW_DATE,
                        feedback = null,
                    ),
                )
            }
            Then {
                checkReviewsRowsData(
                    rowPosition = 2,
                    expected = ReviewCardDto(
                        reviewerName = DataFactory.ANOTHER_USER_FIRST_NAME,
                        reviewerAbbreviation = DataFactory.ANOTHER_USER_FIRST_NAME.first().toString(),
                        title = "${SellerProfileScreenUiConfiguration.SELLER_TEXT}: ${CategoryDefaults.ALL_CATEGORIES.text}",
                        rating = 5,
                        timeSincePosted = DataFactory.SOME_REVIEW_DATE,
                        feedback = null,
                    ),
                )
            }
        }
    }

    @Test
    fun `should return seller profile reviews rows with empty reviewer name and abbreviation`() {
        runUnitTest(robot) {
            Given { stubFormattedReviewDate(DataFactory.SOME_REVIEW_DATE) }
            Given { stubRawUserProfiles(emptyMap()) }
            Given {
                stubRawReviews(
                    RawReviews(
                        reviews = listOf(
                            RawReviewsFactory.createRawReview(
                                rating = 3,
                                itemTitle = DataFactory.SOME_AD_TITLE,
                                categoryId = CategoryDefaults.ALL_CATEGORIES.id,
                                direction = RawReview.Direction.S2B,
                                feedback = null,
                            ),
                            RawReviewsFactory.createRawReview(
                                rating = 4,
                                itemTitle = DataFactory.ANOTHER_AD_TITLE,
                                categoryId = CategoryDefaults.ALL_CATEGORIES.id,
                                direction = RawReview.Direction.S2B,
                                feedback = null,
                            ),
                        ),
                        feedbackSummary = null,
                    ),
                )
            }
            When { createReviewsRows() }
            Then { checkReviewsRowsSize(2) }
            Then { checkReviewsRowsType(0, RowLayoutType.REVIEW_ROW) }
            Then { checkReviewsRowsType(1, RowLayoutType.REVIEW_ROW) }
            Then { checkReviewsRowsBottomDivider(0, true) }
            Then { checkReviewsRowsBottomDivider(1, true) }
            Then {
                checkReviewsRowsData(
                    rowPosition = 0,
                    expected = ReviewCardDto(
                        reviewerName = EMPTY_STRING,
                        reviewerAbbreviation = EMPTY_STRING,
                        title = "${SellerProfileScreenUiConfiguration.SELLER_TEXT}: ${CategoryDefaults.ALL_CATEGORIES.text}",
                        rating = 3,
                        timeSincePosted = DataFactory.SOME_REVIEW_DATE,
                        feedback = null,
                    ),
                )
            }
            Then {
                checkReviewsRowsData(
                    rowPosition = 1,
                    expected = ReviewCardDto(
                        reviewerName = EMPTY_STRING,
                        reviewerAbbreviation = EMPTY_STRING,
                        title = "${SellerProfileScreenUiConfiguration.SELLER_TEXT}: ${CategoryDefaults.ALL_CATEGORIES.text}",
                        rating = 4,
                        timeSincePosted = DataFactory.SOME_REVIEW_DATE,
                        feedback = null,
                    ),
                )
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "0, 20",
        "1, 20",
        "2, 10",
        "3, 0",
    )
    fun `should NOT return no reviews row`(page: Int, numberOfReviews: Int) {
        runUnitTest(robot) {
            When { createSellerNoReviewsRow(page, numberOfReviews) }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should return no reviews row`() {
        runUnitTest(robot) {
            When { createSellerNoReviewsRow(0, 0) }
            Then { checkSellerProfileRowType(RowLayoutType.SELLER_PROFILE_NO_RESULTS_ROW) }
        }
    }

    private class Robot: BaseRobot {
        private var actualRowLayoutResult: RowLayout<UiItem>? = null
        private lateinit var actualReviewsRowsResult: Array<RowLayout<UiItem>?>

        private val sellerProfileReviewDateFormatter: SellerProfileReviewDateFormatter = mockk(relaxed = true)
        private lateinit var page: String
        private lateinit var rawReviews: RawReviews
        private lateinit var rawUserProfiles: Map<Int, RawPapiUserProfile?>

        private val testSubject = SellerProfileReviewsUiProvider(
            TitleFactory(),
            ReviewsChipsFactory(),
            SellerProfileReviewsOverviewCalculator(),
            sellerProfileReviewDateFormatter,
        )

        fun stubFormattedReviewDate(date: String?) {
            every { sellerProfileReviewDateFormatter.getTimeAgoLabel(olderDate = any(), newerDate = any()) } returns date
        }

        fun stubPage(page: Int) {
            this.page = page.toString()
        }

        fun stubRawReviews(reviews: RawReviews) {
            rawReviews = reviews
        }

        fun stubRawUserProfiles(profiles: Map<Int, RawPapiUserProfile?>) {
            rawUserProfiles = profiles
        }

        fun createSellerProfileReviewsOverviewRow() {
            actualRowLayoutResult = testSubject.createSellerProfileReviewsOverviewRow(rawReviews, page)
        }

        fun createFeedbackOverviewTitleRow() {
            actualRowLayoutResult = testSubject.createFeedbackOverviewTitleRow(rawReviews, page)
        }

        fun createFeedbackOverviewChipsRow() {
            actualRowLayoutResult = testSubject.createFeedbackOverviewChipsRow(rawReviews, page)
        }

        fun createReviewsRows() {
            actualReviewsRowsResult = testSubject.createReviewsRows(
                rawReviews,
                rawUserProfiles,
                createGridSizes(factory = ::SellerProfileReviewsGridSizes),
            )
        }

        fun createSellerNoReviewsRow(
            page: Int,
            numberOfReviews: Int,
        ) {
            actualRowLayoutResult = testSubject.createSellerNoReviewsRow(page.toString(), numberOfReviews)
        }

        fun checkRowLayoutBottomDivider(expected: Boolean?) {
            assertEquals(expected, actualRowLayoutResult?.bottomDivider)
        }

        fun checkRowLayoutType(expected: RowLayoutType) {
            assertEquals(expected, actualRowLayoutResult?.type)
        }

        fun checkRowLayoutIsNull() {
            assertNull(actualRowLayoutResult)
        }

        fun checkRowLayoutDataSize(expected: Int) {
            assertEquals(expected, actualRowLayoutResult?.data?.size)
        }

        fun checkSellerProfileReviewsOverviewCard(expected: ReviewsOverviewCardDto) {
            assertEquals(expected, actualRowLayoutResult?.data?.get(0))
        }

        fun checkSellerProfileFeedbackOverviewTitleCard(expected: TitleCardDto.Left) {
            assertEquals(expected, actualRowLayoutResult?.data?.get(0))
        }

        fun checkSellerProfileFeedbackOverviewChipAtPosition(
            position: Int,
            expected: ChipDto.StandardStyled,
        ) {
            assertEquals(expected, actualRowLayoutResult?.data?.get(position))
        }

        fun<T> checkRowLayoutDataTypeAtPosition(
            position: Int,
            expected: Class<T>,
        ) {
            assertInstanceOf(expected, actualRowLayoutResult?.data?.get(position))
        }

        fun checkReviewsRowsSize(expected: Int) {
            assertEquals(expected, actualReviewsRowsResult.size)
        }

        fun checkReviewsRowsType(
            rowPosition: Int,
            expected: RowLayoutType,
        ) {
            assertEquals(expected, actualReviewsRowsResult[rowPosition]?.type)
        }

        fun checkReviewsRowsBottomDivider(
            rowPosition: Int,
            expected: Boolean,
        ) {
            assertEquals(expected, actualReviewsRowsResult[rowPosition]?.bottomDivider)
        }

        fun checkReviewsRowsData(
            rowPosition: Int,
            expected: ReviewCardDto,
        ) {
            assertEquals(expected, actualReviewsRowsResult[rowPosition]?.data?.first())
        }

        fun checkSellerProfileRowType(expected: RowLayoutType) {
            assertEquals(expected, actualRowLayoutResult?.type)
        }
    }
}