package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.ArrayList;
import java.util.List;


@Root(name = "ad-search-suggestion", strict = false)
@Namespace(reference = Namespaces.SUGGESTION, prefix = Namespaces.Prefix.SUGGESTION)
public class RawCapiSearchSingleSuggestion {

    @Element(name = "keyword")
    @Namespace(reference = Namespaces.SUGGESTION)
    public String keyword;

    @ElementList(name = "categories", entry = "category", required = false)
    @Namespace(reference = Namespaces.CATEGORY, prefix = Namespaces.Prefix.CATEGORY)
    public List<RawCapiSearchSuggestionCategory> categories = new ArrayList<>();
}
