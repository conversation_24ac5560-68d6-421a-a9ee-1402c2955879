package com.gumtree.mobile.api.userService.bodies

import com.google.gson.annotations.SerializedName
import com.gumtree.mobile.api.userService.models.RawUserServiceAuthProvider

data class UserServiceLoginRequestBody(
    @SerializedName("authProvider")
    val authProvider: RawUserServiceAuthProvider,
    @SerializedName("password")
    val password: String,
    @SerializedName("username")
    val username: String,
    @SerializedName("threatMetrixSessionId")
    val threatMetrixSessionId: String? = null,
    @SerializedName("client")
    val client: String? = null,
    @SerializedName("ipAddress")
    val ipAddress: String? = null,
)
