package com.gumtree.mobile.features.login

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import com.gumtree.mobile.common.BEARER
import com.gumtree.mobile.common.UserProfileJWTService

class LoginMapper(
    private val userProfileJWTService: UserProfileJWTService,
    private val issuedDateFormatter: LoginIssuedDateFormatter,
) {

    fun map(
        rawData: RawUserServiceUserDetails,
        email: String,
        token: String,
    ): UserLoginDto {
        UserLoginDto(
            userId = rawData.userId,
            userEmail = email,
            userDisplayName = rawData.firstName,
            userToken = token,
            accountId = rawData.accountIds.first(),
            userAuthorization = EMPTY_STRING,
            issuedDate = issuedDateFormatter.getCurrentDate(),
            userType = rawData.userType.toString(),
        ).apply {
            val authorization = userProfileJWTService.createToken(
                this,
                rawData.lastName,
                rawData.registrationDate,
                rawData.postingSinceDate.orEmpty(),
            )
            return copy(userAuthorization = "$BEARER $authorization")
        }
    }
}
