package features.registration

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.features.registration.ActivateRegistrationRequest
import com.gumtree.mobile.features.registration.CreateRegistrationRequest
import com.gumtree.mobile.features.registration.REGISTRATION_ACTIVATE_PATH
import com.gumtree.mobile.features.registration.REGISTRATION_PATH
import com.gumtree.mobile.features.registration.RegistrationRepository
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.plugins.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.jsonHttpClient
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runUnitTest
import kotlin.test.assertEquals


class RegistrationRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete POST new user registration request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateNewUserRegistrationResponse() }
            When {
                createUserRegistration(
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_LAST_NAME,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    true,
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete POST new user registration request with BadRequest exception if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateResponseError(BadRequestException("User already exists")) }
            When {
                createUserRegistration(
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_LAST_NAME,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    true,
                    DataFactory.SOME_THREAT_METRIX_SESSION,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST new user registration request with BadRequest status code if threat metrix header is empty`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                createUserRegistration(
                    DataFactory.SOME_USER_FIRST_NAME,
                    DataFactory.SOME_USER_LAST_NAME,
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_USER_SECRET,
                    true,
                    EMPTY_STRING,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST user registration activation request with success and status code NoContent`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryActivateUserRegistrationResponse() }
            When {
                activateUserRegistration(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should complete POST user registration activation request with BadRequestException exception if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryActivateUserRegistrationError(BadRequestException("Some error message")) }
            When {
                activateUserRegistration(
                    DataFactory.SOME_USER_EMAIL,
                    DataFactory.SOME_TOKEN
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: RegistrationRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse

        fun stubRepositoryCreateNewUserRegistrationResponse() {
            coEvery {
                repository.create(any(), any())
            } returns Unit
        }

        fun stubRepositoryActivateUserRegistrationResponse() {
            coEvery {
                repository.activate(any(), any())
            } returns Unit
        }

        fun stubRepositoryCreateResponseError(error: Throwable) {
            coEvery {
                repository.create(any(), any())
            } throws error
        }

        fun stubRepositoryActivateUserRegistrationError(error: Throwable) {
            coEvery {
                repository.activate(any(), any())
            } throws error
        }

        suspend fun createUserRegistration(
            firstName: String,
            lastName: String,
            userName: String,
            userSecret: String,
            isMarketingOptIn: Boolean,
            threatMetrixSessionId: String,
        ) {
            actualResponse = client.post(REGISTRATION_PATH) {
                headers.append(ApiHeaderParams.THREATMETRIX_SESSION, threatMetrixSessionId)
                setBody(
                    CreateRegistrationRequest(
                        firstName = firstName,
                        lastName = lastName,
                        userName = userName,
                        userSecret = userSecret,
                        isMarketingOptIn = isMarketingOptIn
                    )
                )
                contentType(ContentType.Application.Json)
            }
        }

        suspend fun activateUserRegistration(
            userName: String,
            activationToken: String
        ) {
            actualResponse = client.post(REGISTRATION_ACTIVATE_PATH) {
                setBody(
                    ActivateRegistrationRequest(
                        userName = userName,
                        activationToken = activationToken
                    )
                )
                contentType(ContentType.Application.Json)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }

    }
}
