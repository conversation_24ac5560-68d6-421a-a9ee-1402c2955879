package com.gumtree.mobile.features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.UiItem

/**
 * Mapping the RawCapiAd data into VipPostedSinceCardDto mobile application data
 */
class VipPostedSinceMapper(
    private val vipTimeAgoFormatter: VipTimeAgoFormatter,
) {

    fun map(
        rawData: RawCapiAd,
    ): RowLayout<UiItem>? {
        val postedSinceCardDto = rawData.toVipPostedSinceCardDto(vipTimeAgoFormatter)
        return when {
            CategoriesTreeCache.isServices(rawData.category.id) -> null
            postedSinceCardDto == null -> null
            else -> RowLayout(
                type = RowLayoutType.VIP_POSTED_SINCE_ROW,
                data = listOf(postedSinceCardDto),
                style = Style(
                    spacing = createSpacing(
                        leftMargin = Space.SMALL,
                        rightMargin = Space.SMALL,
                        bottomMargin = Space.LARGE,
                    ),
                ),
            )
        }
    }
}
