package com.gumtree.mobile.features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.utils.extensions.data.toVipDescriptionCardDto

/**
 * Mapping the RawCapiAd data into VipDescriptionCardDto mobile application data
 */
class VipDescriptionMapper(
    private val vipScreenUiConfiguration: VipScreenUiConfiguration = VipScreenUiConfiguration,
) {

    fun map(
        rawData: RawCapiAd,
        labelText: String?,
    ): RowLayout<UiItem> {
        val vipDescriptionCardDto = rawData.toVipDescriptionCardDto(
            labelText ?: vipScreenUiConfiguration.DESCRIPTION_LABEL_TEXT,
        )
        return RowLayout(
            type = RowLayoutType.VIP_DESCRIPTION_ROW,
            data = listOf(vipDescriptionCardDto),
            style = Style(
                spacing = createSpacing(
                    leftMargin = Space.SMALL,
                    rightMargin = Space.SMALL,
                    bottomMargin = Space.X_SMALL,
                ),
            ),
        )
    }
}
