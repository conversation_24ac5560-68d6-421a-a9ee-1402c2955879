package features.settings

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.settings.ANALYTICS_USER_LOGOUT_EVENT_NAME
import com.gumtree.mobile.features.settings.SettingsAnalyticsProvider
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class SettingsAnalyticsProviderTest {

    private val robot = Robot()

    @Test
    fun `should return correct logout event`() = runTest {
        runUnitTest(robot) {
            When { getUserLogoutEvent() }
            Then { checkAnalyticsEventData(AnalyticsEventData(eventName = ANALYTICS_USER_LOGOUT_EVENT_NAME)) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualAnalyticsEventResult: AnalyticsEventData

        private val commonAnalyticsProvider = CommonAnalyticsProviderFactory.createInstance()

        private val testSubject = SettingsAnalyticsProvider(commonAnalyticsProvider)

        fun getUserLogoutEvent() {
            actualAnalyticsEventResult = testSubject.getUserLogoutEvent()
        }

        fun checkAnalyticsEventData(expected: AnalyticsEventData) {
            assertEquals(expected, actualAnalyticsEventResult)
        }
    }
}