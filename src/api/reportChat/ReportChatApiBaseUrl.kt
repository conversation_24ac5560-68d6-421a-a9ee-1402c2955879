package com.gumtree.mobile.api.reportChat

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val REPORT_CHAT_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "REPORT_CHAT_API_BASE_URL"
const val REPORT_CHAT_API_QA_PORT = 8098

class ReportChatApiBaseUrl : ApiBaseUrl() {

    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(
            REPORT_CHAT_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY,
        ).toBaseUrl()
    } // see helm-chart/values.yaml
    override val localUrl: BaseUrl by lazy { LOCAL_HOST_BASE_URL.toBaseUrl() }
    override val localPort: Int = REPORT_CHAT_API_QA_PORT
}
