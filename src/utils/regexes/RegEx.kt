package com.gumtree.mobile.utils.regexes

enum class RegEx(val pattern: String) {
    /**
     * The registration first name verification regex
     */
    FIRST_NAME(".{1,}"),

    /**
     * The registration last name verification regex
     */
    LAST_NAME(".{1,}"),

    /**
     * The email verification regex
     */
    EMAIL("[^\\s@]+@([^\\s@.,]+\\.)+[^\\s@.,]{2,}"),

    /**
     * The secret verification regex
     */
    SECRET("^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@\$%^&*-]).{8,}\$"),

    /**
     * The UK phone number regex
     */
    PHONE_NUMBER("^(\\+44|0)\\s*(?:[1-9]\\s*){1}(?:\\d\\s*){9}$|^$"),

    /**
     * Numerical regex - usually used for ad id and user id (not hashed)
     */
    NUMERICAL_ID("^\\d+$"),

    /**
     * UDID regex - usually used for conversation id
     */
    UDID("^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\$"),

    /**
     * Hexadecimal 24-digit regex - usually used for saved search id
     */
    HEX24("^[0-9a-f]{24}$"),
}
