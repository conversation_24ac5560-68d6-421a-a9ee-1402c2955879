package com.gumtree.mobile.features.srp

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.StickyBar
import com.gumtree.mobile.utils.runOrNull
import com.gumtree.mobile.utils.runSuspendOrNull

/**
 * Provider of the SRP Sticky Bar data (SRP chips + SRP Sort Header)
 * @param srpChipsFactory - the SRP chips factory
 * @param srpSortHeaderFactory - the SRP sort header factory
 */
class SrpStickyBarProvider(
    private val srpChipsFactory: SrpChipsFactory,
    private val srpSortHeaderFactory: SrpSortHeaderFactory,
) {

    /**
     * Create an instance of the SRP StickyBar (List with RowLayout<UiItem>)
     * @param page - the page number
     * @param rawAdsData - the raw Ads
     * @param searchParameters - the query params
     * @param sortOrderAnalyticsEventData - the analytics event data supplied when the sort order is changed
     */
    suspend fun createSrpStickyBar(
        page: String,
        rawAdsData: RawCapiAdList,
        searchParameters: QueryParams,
        sortOrderAnalyticsEventData: AnalyticsEventData?,
    ): StickyBar? {
        return runSuspendOrNull(SrpScreenUiConfiguration.shouldStickyBarAppearOnPage(page)) {
            listOfNotNull(
                generateSrpChips(searchParameters),
                generateSrpSortHeader(rawAdsData, searchParameters, sortOrderAnalyticsEventData),
            )
        }
    }

    private suspend fun generateSrpChips(searchParameters: QueryParams): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.CHIPS_ROW,
            data = srpChipsFactory.buildSrpChips(searchParameters),
        )
    }

    private fun generateSrpSortHeader(
        rawAdsData: RawCapiAdList,
        searchParameters: QueryParams,
        analyticsEventData: AnalyticsEventData?,
    ): RowLayout<UiItem>? {
        return runOrNull(SrpScreenUiConfiguration.shouldSortHeaderAppear(rawAdsData.rawPaging.numFound)) {
            RowLayout(
                type = RowLayoutType.SORT_HEADER_ROW,
                data = srpSortHeaderFactory.buildSrpSortHeader(rawAdsData, searchParameters, analyticsEventData),
            )
        }
    }
}
