package api.capi.models;


import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.List;

@Root(name = "flags")
@Namespace(reference = Namespaces.CATEGORY, prefix = Namespaces.Prefix.CATEGORY)
public class RawCategoryFlags {

    @ElementList(inline = true, required = false)
    public List<RawCategoryFlag> mList = null;
}
