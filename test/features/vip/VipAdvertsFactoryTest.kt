package features.vip

import com.gumtree.mobile.adverts.gam.GAMAdvertAttribute
import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.adverts.gam.GAMAdvertSize
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto
import com.gumtree.mobile.features.vip.GAM_ANDROID_VIP_UNIT_ID
import com.gumtree.mobile.features.vip.GAM_IOS_VIP_UNIT_ID
import com.gumtree.mobile.features.vip.GAM_VIP_ATTAPPTR_BOTTOM_PLACEMENT_ID
import com.gumtree.mobile.features.vip.GAM_VIP_ATTAPPTR_GALLERY_PLACEMENT_ID
import com.gumtree.mobile.features.vip.GAM_VIP_ATTAPPTR_TOP_PLACEMENT_ID
import com.gumtree.mobile.features.vip.GAM_VIP_BOTTOM_SLOT
import com.gumtree.mobile.features.vip.GAM_VIP_GALLERY_SLOT
import com.gumtree.mobile.features.vip.GAM_VIP_TOP_SLOT
import com.gumtree.mobile.features.vip.VipAdvertsFactory
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class VipAdvertsFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return VIP top advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to "$GAM_VIP_TOP_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}",
                        GAMAdvertAttribute.VEHICLE_MAKE.value to DataFactory.SOME_VEHICLE_MAKE,
                        GAMAdvertAttribute.VEHICLE_MODEL.value to DataFactory.SOME_VEHICLE_MODEL,
                    )
                )
            }
            When { buildVipTopAdvert("$GAM_VIP_TOP_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}", DataFactory.SOME_PAGE_URL) }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_VIP_UNIT_ID + "$GAM_VIP_TOP_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_VIP_UNIT_ID + "$GAM_VIP_TOP_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertSlotName("$GAM_VIP_TOP_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad320x50) }
            Then { checkGamAdvertPageUrl(DataFactory.SOME_PAGE_URL) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_VIP_ATTAPPTR_TOP_PLACEMENT_ID) }
            Then { checkGAMAdvertAttributesSize(7) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to "$GAM_VIP_TOP_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}",
                        GAMAdvertAttribute.VEHICLE_MAKE.value to DataFactory.SOME_VEHICLE_MAKE,
                        GAMAdvertAttribute.VEHICLE_MODEL.value to DataFactory.SOME_VEHICLE_MODEL,
                    )
                )
            }
        }
    }

    @Test
    fun `should return VIP bottom advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to "$GAM_VIP_BOTTOM_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}",
                        GAMAdvertAttribute.SELLER_TYPE.value to DataFactory.SOME_SELLER_TYPE,
                    )
                )
            }
            When { buildVipBottomAdvert("$GAM_VIP_BOTTOM_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}", DataFactory.SOME_PAGE_URL) }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_VIP_UNIT_ID + "$GAM_VIP_BOTTOM_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_VIP_UNIT_ID + "$GAM_VIP_BOTTOM_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertSlotName("$GAM_VIP_BOTTOM_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad320x50) }
            Then { checkGamAdvertPageUrl(DataFactory.SOME_PAGE_URL) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_VIP_ATTAPPTR_BOTTOM_PLACEMENT_ID) }
            Then { checkGAMAdvertAttributesSize(6) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to "$GAM_VIP_BOTTOM_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}",
                        GAMAdvertAttribute.SELLER_TYPE.value to DataFactory.SOME_SELLER_TYPE,
                    )
                )
            }
        }
    }

    @Test
    fun `should return VIP gallery advert with expected data`() {
        runUnitTest(robot) {
            Given {
                stubGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to "$GAM_VIP_GALLERY_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}",
                        GAMAdvertAttribute.VEHICLE_MAKE.value to DataFactory.SOME_VEHICLE_MAKE,
                        GAMAdvertAttribute.VEHICLE_MODEL.value to DataFactory.SOME_VEHICLE_MODEL,
                    )
                )
            }
            When { buildVipGalleryAdvert("$GAM_VIP_GALLERY_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}", DataFactory.SOME_PAGE_URL) }
            Then { checkGAMAdvertAndroidUnitId(GAM_ANDROID_VIP_UNIT_ID + "$GAM_VIP_GALLERY_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertIOSUnitId(GAM_IOS_VIP_UNIT_ID + "$GAM_VIP_GALLERY_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertSlotName("$GAM_VIP_GALLERY_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}") }
            Then { checkGAMAdvertDisplaySizeAtPosition(0, GAMAdvertSize.Ad300x250) }
            Then { checkGamAdvertPageUrl(DataFactory.SOME_PAGE_URL) }
            Then { checkGAMAdvertAddApptrPlacementId(GAM_VIP_ATTAPPTR_GALLERY_PLACEMENT_ID) }
            Then { checkGAMAdvertAttributesSize(7) }
            Then {
                checkGAMAdvertAttributes(
                    mapOf(
                        GAMAdvertAttribute.LOCATION.value to DataFactory.SOME_LOCATION_NAME,
                        GAMAdvertAttribute.LOGGED_IN_STATUS.value to "1",
                        GAMAdvertAttribute.PTG.value to DataFactory.SOME_EXPERIMENT,
                        GAMAdvertAttribute.APP_VERSION.value to DataFactory.SOME_APP_VERSION,
                        GAMAdvertAttribute.POSITION.value to "$GAM_VIP_GALLERY_SLOT/${DataFactory.SOME_AD_CATEGORY_NAME}",
                        GAMAdvertAttribute.VEHICLE_MAKE.value to DataFactory.SOME_VEHICLE_MAKE,
                        GAMAdvertAttribute.VEHICLE_MODEL.value to DataFactory.SOME_VEHICLE_MODEL,
                    )
                )
            }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualGAMAdvertDtoResult: GAMAdvertDto
        private lateinit var gamAdvertAttributes: GAMAdvertAttributes

        private var testSubject = VipAdvertsFactory()

        fun stubGAMAdvertAttributes(attributes: GAMAdvertAttributes) {
            gamAdvertAttributes = attributes
        }

        fun buildVipTopAdvert(slotName: String, pageUrl: String?) {
            actualGAMAdvertDtoResult =
                testSubject.buildVipTopAdvert(slotName, gamAdvertAttributes, pageUrl) as GAMAdvertDto
        }

        fun buildVipBottomAdvert(slotName: String, pageUrl: String?) {
            actualGAMAdvertDtoResult =
                testSubject.buildVipBottomAdvert(slotName, gamAdvertAttributes, pageUrl) as GAMAdvertDto
        }

        fun buildVipGalleryAdvert(slotName: String, pageUrl: String?) {
            actualGAMAdvertDtoResult =
                testSubject.buildVipGalleryAdvert(slotName, gamAdvertAttributes, pageUrl) as GAMAdvertDto
        }

        fun checkGAMAdvertAndroidUnitId(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.androidUnitId)
        }

        fun checkGAMAdvertIOSUnitId(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.iosUnitId)
        }

        fun checkGAMAdvertSlotName(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.slotName)
        }

        fun checkGAMAdvertDisplaySizeAtPosition(
            position: Int,
            expected: GAMAdvertSize
        ) {
            assertEquals(expected, actualGAMAdvertDtoResult.displaySize[position])
        }

        fun checkGamAdvertPageUrl(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.pageUrl)
        }

        fun checkGAMAdvertAttributesSize(expected: Int) {
            assertEquals(expected, actualGAMAdvertDtoResult.attributes.size)
        }

        fun checkGAMAdvertAttributes(expected: GAMAdvertAttributes) {
            assertEquals(expected, actualGAMAdvertDtoResult.attributes)
        }

        fun checkGAMAdvertAddApptrPlacementId(expected: String) {
            assertEquals(expected, actualGAMAdvertDtoResult.addApptrPlacementId)
        }
    }
}