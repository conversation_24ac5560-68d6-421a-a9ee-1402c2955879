package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("VIP_SPECIFICATION_CARD")
data class VipSpecificationCardDto(

    @SerialName("label")
    val label: String,
    @SerialName("iconType")
    val iconType: IconType,
    @SerialName("dropdownItems")
    val dropdownItems: List<SpecificationItem>,
) : UiItem {
    enum class IconType {
        OVERVIEW,
        PERFORMANCE,
        RUNNING_COST,
        SAFETY_AND_SECURITY,
        DRIVING_CONVENIENCE,
        INTERIOR,
        EXTERIOR,
    }
}
