package com.gumtree.mobile.features.locations

import com.gumtree.mobile.api.locations.api.LocationsApi
import com.gumtree.mobile.api.locations.models.RawLocation
import io.ktor.server.plugins.BadRequestException

class LocationsService(
    private val locationsApi: LocationsApi,
) : LocationsApi by locationsApi {

    suspend fun getLocationByLatLng(
        latitude: String,
        longitude: String,
    ): RawLocation {
        return locationsApi
            .getLocationByLatLng(latitude, longitude)
            .locations
            .firstOrNull()
            ?: throw BadRequestException("Can't find near location for the provided latitude and longitude")
    }
}
