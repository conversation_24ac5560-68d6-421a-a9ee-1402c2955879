package com.gumtree.mobile.di

import api.capi.CapiSimpleXmlConverterFactory
import api.phoneNumber.PhoneNumberApiBaseUrl
import api.phoneNumberAuthenticator.PhoneNumberAuthenticatorApiBaseUrl
import api.similarItems.SimilarItemsBaseUrl
import api.userProfile.UserProfileApiBaseUrl
import com.google.gson.FieldNamingPolicy
import com.google.gson.GsonBuilder
import com.gumtree.mobile.api.DefaultRetrofitClient
import com.gumtree.mobile.api.blockUser.BlockUserApiBaseUrl
import com.gumtree.mobile.api.capi.CapiApiBaseUrl
import com.gumtree.mobile.api.capi.CapiConverterFactoryAdapter
import com.gumtree.mobile.api.capi.CapiHeadersProvider
import com.gumtree.mobile.api.capi.CapiRetrofitClient
import com.gumtree.mobile.api.categories.CategoriesApiBaseUrl
import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.api.common.CustomTikXmlConverterFactory
import com.gumtree.mobile.api.common.DefaultApiOkHttpConfiguration
import com.gumtree.mobile.api.common.OkHttpConfiguration
import com.gumtree.mobile.api.common.RetrofitClient
import com.gumtree.mobile.api.common.SensitiveHttpLoggingInterceptor
import com.gumtree.mobile.api.common.XmlEscapeStringConverter
import com.gumtree.mobile.api.conversations.ConversationsBaseUrl
import com.gumtree.mobile.api.coreChat.CoreChatApiBaseUrl
import com.gumtree.mobile.api.crm.CrmApiBaseUrl
import com.gumtree.mobile.api.favouriteAdverts.FavouriteAdvertsApiBaseUrl
import com.gumtree.mobile.api.fullAdsSearch.FullAdsSearchApiBaseUrl
import com.gumtree.mobile.api.homeFeed.HomeFeedApiBaseUrl
import com.gumtree.mobile.api.locations.LocationsApiBaseUrl
import com.gumtree.mobile.api.papi.PapiHeadersProvider
import com.gumtree.mobile.api.partnerships.PartnershipAdsBaseUrl
import com.gumtree.mobile.api.reportChat.ReportChatApiBaseUrl
import com.gumtree.mobile.api.reviews.ReviewsApiBaseUrl
import com.gumtree.mobile.api.savedSearch.SavedSearchApiBaseUrl
import com.gumtree.mobile.api.seller.SellerApiBaseUrl
import com.gumtree.mobile.api.seller.SellerHeadersProvider
import com.gumtree.mobile.api.userService.UserServiceApiBaseUrl
import com.gumtree.mobile.common.DefaultDispatcherProvider
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.OkHttpClientConnectionPoolProvider
import com.gumtree.mobile.common.OkHttpClientFactory
import com.gumtree.mobile.utils.extensions.getFromKoin
import com.tickaroo.tikxml.TikXml
import com.tickaroo.tikxml.retrofit.TikXmlConverterFactory
import okhttp3.Interceptor
import org.koin.core.qualifier.named
import org.koin.dsl.module
import retrofit2.CallAdapter
import retrofit2.Converter
import retrofit2.converter.gson.GsonConverterFactory
import utils.HttpStatusConverterCallAdapterFactory

/**
 * The application networking DI setup (OkHttp clients, Interceptors, ConverterFactories, Retrofit etc.)
 */
val networkDIModule = module {

    single<Interceptor>(named(HTTP_SENSITIVE_BASIC_LOGGING)) {
        SensitiveHttpLoggingInterceptor()
    }

    single<OkHttpConfiguration>(named(DEFAULT_API_OK_HTTP_CONFIG)) {
        DefaultApiOkHttpConfiguration()
    }

    single<DispatcherProvider> { DefaultDispatcherProvider() }

    single(named(DEFAULT_GSON_SETUP)) {
        GsonBuilder()
            .enableComplexMapKeySerialization()
            .serializeNulls()
            .setDateFormat("yyyy-MM-dd'T'HH:mm:ss")
            .setFieldNamingPolicy(FieldNamingPolicy.UPPER_CAMEL_CASE)
            .setPrettyPrinting()
            .create()
    }

    single<Converter.Factory>(named(GSON_CONVERTER_FACTORY)) {
        GsonConverterFactory.create(getFromKoin(DEFAULT_GSON_SETUP))
    }

    single<CallAdapter.Factory>(named(HTTP_STATUS_CONVERTER_FACTORY)) {
        HttpStatusConverterCallAdapterFactory()
    }

    single<ApiHeadersProvider>(named(PAPI_HEADERS_PROVIDER)) { PapiHeadersProvider() }

    /**** CAPI ****/

    single<ApiHeadersProvider>(named(CAPI_HEADERS_PROVIDER)) { CapiHeadersProvider() }

    single<ApiHeadersProvider>(named(SELLER_HEADERS_PROVIDER)) { SellerHeadersProvider() }

    single<Converter.Factory>(named(XML_CONVERTER_FACTORY)) {
        CapiConverterFactoryAdapter(CapiSimpleXmlConverterFactory())
            .also {
                it.addCustomFactory(
                    CustomTikXmlConverterFactory(
                        TikXmlConverterFactory.create(
                            TikXml.Builder()
                                .addTypeConverter(
                                    String::class.java,
                                    XmlEscapeStringConverter(),
                                )
                                .exceptionOnUnreadXml(false)
                                .build(),
                        ),
                    ),
                )
            }
    }

    single<ApiBaseUrl>(named(CAPI_API_CONFIGURATION)) {
        CapiApiBaseUrl()
    }

    single(named(CAPI_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.CAPI_API,
        )
    }

    single<RetrofitClient>(named(CAPI_API_RETROFIT_CLIENT)) {
        CapiRetrofitClient(
            converterFactory = getFromKoin(XML_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(CAPI_API_OK_HTTP_CLIENT),
            baseUrl = getFromKoin(CAPI_API_CONFIGURATION),
        )
    }

    /**** Locations API ****/

    single<ApiBaseUrl>(named(LOCATIONS_API_BASE_URL)) {
        LocationsApiBaseUrl()
    }

    single(named(LOCATIONS_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.LOCATIONS_API,
        )
    }

    single<RetrofitClient>(named(LOCATIONS_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(LOCATIONS_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(LOCATIONS_API_BASE_URL),
        )
    }

    /**** Categories API ****/

    single<ApiBaseUrl>(named(CATEGORIES_API_BASE_URL)) {
        CategoriesApiBaseUrl()
    }

    single(named(CATEGORIES_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.CATEGORIES_API,
        )
    }

    single<RetrofitClient>(named(CATEGORIES_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(CATEGORIES_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(CATEGORIES_API_BASE_URL),
        )
    }

    /**** Core chat API ****/

    single<ApiBaseUrl>(named(CORE_CHAT_API_BASE_URL)) {
        CoreChatApiBaseUrl()
    }

    single(named(CORE_CHAT_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.CORE_CHAT_API,
        )
    }

    single<RetrofitClient>(named(CORE_CHAT_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(CORE_CHAT_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(CORE_CHAT_API_BASE_URL),
        )
    }

    /**** User service API ****/

    single<ApiBaseUrl>(named(USER_SERVICE_API_BASE_URL)) {
        UserServiceApiBaseUrl()
    }

    single(named(USER_SERVICE_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.USER_SERVICE_API,
        )
    }

    single<RetrofitClient>(named(USER_SERVICE_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(USER_SERVICE_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(USER_SERVICE_API_BASE_URL),
        )
    }

    /**** HomeFeed API ****/

    single<ApiBaseUrl>(named(HOME_FEED_API_BASE_URL)) {
        HomeFeedApiBaseUrl()
    }

    single(named(HOME_FEED_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.HOME_FEED_API,
        )
    }

    single<RetrofitClient>(named(HOME_FEED_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(HOME_FEED_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(HOME_FEED_API_BASE_URL),
        )
    }

    /**** Conversations API ****/

    single<ApiBaseUrl>(named(CONVERSATIONS_API_BASE_URL)) {
        ConversationsBaseUrl()
    }

    single(named(CONVERSATIONS_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.CONVERSATIONS_API,
        )
    }

    single<RetrofitClient>(named(CONVERSATIONS_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(CONVERSATIONS_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(CONVERSATIONS_API_BASE_URL),
        )
    }

    /**** UserProfile API ****/

    single<ApiBaseUrl>(named(USER_PROFILE_API_BASE_URL)) {
        UserProfileApiBaseUrl()
    }

    single(named(USER_PROFILE_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.USER_PROFILE_API,
        )
    }

    single<RetrofitClient>(named(USER_PROFILE_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(USER_PROFILE_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(USER_PROFILE_API_BASE_URL),
        )
    }

    /**** SimilarItems API ****/

    single<ApiBaseUrl>(named(SIMILAR_ITEMS_API_BASE_URL)) {
        SimilarItemsBaseUrl()
    }

    single(named(SIMILAR_ITEMS_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.SIMILAR_ITEMS_API,
        )
    }

    single<RetrofitClient>(named(SIMILAR_ITEMS_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(SIMILAR_ITEMS_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(SIMILAR_ITEMS_API_BASE_URL),
        )
    }

    /**** BlockUser API ****/

    single<ApiBaseUrl>(named(BLOCK_USER_API_BASE_URL)) {
        BlockUserApiBaseUrl()
    }

    single(named(BLOCK_USER_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.BLOCK_USER_API,
        )
    }

    single<RetrofitClient>(named(BLOCK_USER_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(BLOCK_USER_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(BLOCK_USER_API_BASE_URL),
        )
    }

    /**** Phone API ****/

    single<ApiBaseUrl>(named(PHONE_NUMBER_API_BASE_URL)) {
        PhoneNumberApiBaseUrl()
    }

    single(named(PHONE_NUMBER_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.PHONE_NUMBER_API,
        )
    }

    single<RetrofitClient>(named(PHONE_NUMBER_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(PHONE_NUMBER_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(PHONE_NUMBER_API_BASE_URL),
        )
    }

    /**** CRM API ****/

    single<ApiBaseUrl>(named(CRM_API_BASE_URL)) {
        CrmApiBaseUrl()
    }

    single(named(CRM_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.CRM_API,
        )
    }

    single<RetrofitClient>(named(CRM_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(CRM_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(CRM_API_BASE_URL),
        )
    }

    /**** PARTNERSHIP ADS API ****/

    single<ApiBaseUrl>(named(PARTNERSHIP_ADS_API_BASE_URL)) {
        PartnershipAdsBaseUrl()
    }

    single(named(PARTNERSHIP_ADS_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.PARTNERSHIPS_API,
        )
    }

    single<RetrofitClient>(named(PARTNERSHIP_ADS_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(PARTNERSHIP_ADS_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(PARTNERSHIP_ADS_API_BASE_URL),
        )
    }

    /**** REVIEWS API ****/

    single<ApiBaseUrl>(named(REVIEWS_API_BASE_URL)) {
        ReviewsApiBaseUrl()
    }

    single(named(REVIEWS_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.REVIEWS_API,
        )
    }

    single<RetrofitClient>(named(REVIEWS_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(REVIEWS_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(REVIEWS_API_BASE_URL),
        )
    }

    /**** PHONE-VERIFY API ****/
    single<ApiBaseUrl>(named(PHONE_VERIFY_API_BASE_URL)) {
        PhoneNumberAuthenticatorApiBaseUrl()
    }

    single(named(PHONE_VERIFY_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.PHONE_VERIFY_API,
        )
    }

    single<RetrofitClient>(named(PHONE_VERIFY_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(PHONE_VERIFY_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(PHONE_VERIFY_API_BASE_URL),
        )
    }

    /**** SELLER API ****/
    single<ApiBaseUrl>(named(SELLER_API_BASE_URL)) {
        SellerApiBaseUrl()
    }

    single(named(SELLER_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.SELLER_API,
        )
    }

    single<RetrofitClient>(named(SELLER_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(SELLER_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(SELLER_API_BASE_URL),
        )
    }

    /**** FullAdsSearch API ****/

    single<ApiBaseUrl>(named(FULL_ADS_SEARCH_API_BASE_URL)) {
        FullAdsSearchApiBaseUrl()
    }

    single(named(FULL_ADS_SEARCH_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.FULL_ADS_SEARCH_API,
        )
    }

    single<RetrofitClient>(named(FULL_ADS_SEARCH_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(FULL_ADS_SEARCH_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(FULL_ADS_SEARCH_API_BASE_URL),
        )
    }

    /**** Favourite Adverts API ****/

    single<ApiBaseUrl>(named(FAVOURITE_ADVERTS_API_BASE_URL)) {
        FavouriteAdvertsApiBaseUrl()
    }

    single(named(FAVOURITE_ADVERTS_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.FAVOURITE_ADVERTS_API,
        )
    }

    single<RetrofitClient>(named(FAVOURITE_ADVERTS_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(FAVOURITE_ADVERTS_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(FAVOURITE_ADVERTS_API_BASE_URL),
        )
    }

    /**** Saved Search API ****/

    single<ApiBaseUrl>(named(SAVED_SEARCH_API_BASE_URL)) {
        SavedSearchApiBaseUrl()
    }

    single(named(SAVED_SEARCH_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.SAVED_SEARCH_API,
        )
    }

    single<RetrofitClient>(named(SAVED_SEARCH_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(SAVED_SEARCH_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(SAVED_SEARCH_API_BASE_URL),
        )
    }

    /**** Report Chat Service ****/
    single<ApiBaseUrl>(named(REPORT_CHAT_API_BASE_URL)) {
        ReportChatApiBaseUrl()
    }

    single(named(REPORT_CHAT_API_OK_HTTP_CLIENT)) {
        OkHttpClientFactory.buildOkHttpClient(
            configuration = getFromKoin(DEFAULT_API_OK_HTTP_CONFIG),
            loggingInterceptor = getFromKoin(HTTP_SENSITIVE_BASIC_LOGGING),
            connectionPoolKey = OkHttpClientConnectionPoolProvider.Key.REPORT_CHAT_API,
        )
    }

    single<RetrofitClient>(named(REPORT_CHAT_API_RETROFIT_CLIENT)) {
        DefaultRetrofitClient(
            converterFactory = getFromKoin(GSON_CONVERTER_FACTORY),
            callAdapterFactory = getFromKoin(HTTP_STATUS_CONVERTER_FACTORY),
            okHttpClient = getFromKoin(REPORT_CHAT_API_OK_HTTP_CLIENT),
            baseURL = getFromKoin(REPORT_CHAT_API_BASE_URL),
        )
    }
}
