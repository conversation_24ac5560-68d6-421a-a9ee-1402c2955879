package com.gumtree.mobile

import io.ktor.server.application.Application
import io.ktor.server.plugins.swagger.swaggerUI
import io.ktor.server.routing.Route
import io.ktor.server.routing.routing

const val SWAGGER_PATH = "swagger"
const val CONTRACTS_PATH = "specifications/"

fun Application.installSwaggerSpecs() {
    routing {
        generateSwaggerUIForAllContracts(
            "block-user-contract.yaml",
            "categories-contract.yaml",
            "category-landing-contract.yaml",
            "chat-contract.yaml",
            "conversations-contract.yaml",
            "crm-contract.yaml",
            "favourites-contract.yaml",
            "filters-attribute-contract.yaml",
            "filters-contract.yaml",
            "forgot-password-contract.yaml",
            "home-feed-contract.yaml",
            "locations-contract.yaml",
            "login-contract.yaml",
            "my-gumtree-contract.yaml",
            "my-gumtree-v2-contract.yaml",
            "phone-verification-contract.yaml",
            "promote-contract.yaml",
            "push-notifications-contract.yaml",
            "registration-contract.yaml",
            "report-chat-contract.yaml",
            "report-listing-contract.yaml",
            "reviews-contract.yaml",
            "saved-searches-contract.yaml",
            "search-suggestions-contract.yaml",
            "search-suggestions-contract-v2.yaml",
            "srp-contract.yaml",
            "settings-contract.yaml",
            "vip-contract.yaml",
            "my-gumtree-contract.yaml",
            "my-gumtree-v2-contract.yaml",
            "promote-contract.yaml",
            "filters-contract.yaml",
            "filters-attribute-contract.yaml",
            "seller-profile-contract.yaml",
            "seller-profile-contract-v2.yaml",
            "settings-contract.yaml",
            "srp-contract.yaml",
            "sync-phone-verify-contract.yaml",
            "vip-contract.yaml",
            "report-chat-contract.yaml",
        )
    }
}

fun Route.generateSwaggerUIForAllContracts(vararg contractFiles: String) {
    contractFiles.forEach {
        swaggerUI(path = SWAGGER_PATH, swaggerFile = "$CONTRACTS_PATH/$it")
    }
}
