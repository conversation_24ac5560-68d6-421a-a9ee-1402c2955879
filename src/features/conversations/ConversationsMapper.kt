package com.gumtree.mobile.features.conversations

import api.capi.bodies.RawReplyToAdConversation
import api.capi.models.RawConversationList
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.wrapScreenDataWithRowLayouts
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.extensions.data.onlyConversationId
import com.gumtree.mobile.utils.extensions.data.toConversationCard

/**
 * Mapping the RawConversationList data (CAPI data) into conversations mobile application data
 * @property conversationsTimeAgoFormatter - the conversation date time ago formatter,
 * to calculate the time difference between the last message post timestamp and the current date (now)
 */
class ConversationsMapper(private val conversationsTimeAgoFormatter: ConversationsTimeAgoFormatter) {

    fun mapScreen(
        rawData: RawConversationList,
        listingGridSizes: GridSizes,
        myUsername: String,
    ): Pair<PortraitData, LandscapeData?> {
        val now = conversationsTimeAgoFormatter.getCurrentDate()
        val listWithConversationCards = rawData.rawConversations.mapNotNull {
            it.toConversationCard(conversationsTimeAgoFormatter, now, myUsername)
        }

        return listWithConversationCards.wrapScreenDataWithRowLayouts(
            listingGridSizes,
            RowLayoutType.CONVERSATION_ROW,
        )
    }

    fun mapCreateConversationResponse(
        rawData: RawReplyToAdConversation,
    ): CreateConversationResponseDto {
        return CreateConversationResponseDto(
            destination = DestinationRoute.CONVERSATION.build(
                ApiQueryParams.CONVERSATION_ID to rawData.onlyConversationId(),
            ),
        )
    }
}
