package com.gumtree.mobile.utils.extensions

import com.gumtree.mobile.utils.regexes.RegEx

/**
 * The method strips the host from HttpUrl pathSegments and also replaces the path params with "/?" placeholder
 * @return - the clean url path uri without the personal data in the path params
 */
fun List<String>.toCleanUriPath(): String {
    return StringBuilder().apply {
        <EMAIL> {
            append(
                if (it.matchesRegex(RegEx.NUMERICAL_ID) || it.matchesRegex(RegEx.EMAIL) || it.matchesRegex(RegEx.UDID) || it.matchesRegex(RegEx.HEX24)) {
                    "/?"
                } else {
                    "/$it"
                },
            )
        }
    }.toString()
}
