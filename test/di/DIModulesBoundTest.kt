package di

import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.di.apisDIModule
import com.gumtree.mobile.di.applicationDIModule
import com.gumtree.mobile.di.commonDIModule
import com.gumtree.mobile.di.networkDIModule
import com.gumtree.mobile.di.viewFactoriesDIModule
import com.gumtree.mobile.features.blockUser.blockUserModule
import com.gumtree.mobile.features.categories.categoriesModule
import com.gumtree.mobile.features.categoryLanding.categoryLandingModule
import com.gumtree.mobile.features.chat.chatModule
import com.gumtree.mobile.features.conversations.conversationsModule
import com.gumtree.mobile.features.crm.crmModule
import com.gumtree.mobile.features.favourites.favouritesModule
import com.gumtree.mobile.features.filters.attribute.filtersAttributeModule
import com.gumtree.mobile.features.filters.filtersModule
import com.gumtree.mobile.features.forgotPassword.forgotPasswordModule
import com.gumtree.mobile.features.homeFeed.homeFeedModule
import com.gumtree.mobile.features.locations.locationsModule
import com.gumtree.mobile.features.login.loginModule
import com.gumtree.mobile.features.myGumtree.myGumtreeModule
import com.gumtree.mobile.features.myGumtree.v2.myGumtreeModule as myGumtreeModuleV2
import com.gumtree.mobile.features.notifications.pushNotificationsModule
import com.gumtree.mobile.features.phoneVerification.phoneVerificationModule
import com.gumtree.mobile.features.promote.promoteModule
import com.gumtree.mobile.features.registration.registrationModule
import com.gumtree.mobile.features.reportChat.reportChatModule
import com.gumtree.mobile.features.reportListing.reportListingModule
import com.gumtree.mobile.features.reviews.reviewsModule
import com.gumtree.mobile.features.savedSearches.savedSearchesModule
import com.gumtree.mobile.features.searchSuggestions.searchSuggestionsModule
import com.gumtree.mobile.features.sellerProfile.sellerProfileModule
import com.gumtree.mobile.features.sellerProfile.v2.sellerProfileModule as sellerProfileModuleV2
import com.gumtree.mobile.features.searchSuggestions.v2.searchSuggestionsModule as searchSuggestionsModuleV2
import com.gumtree.mobile.features.settings.settingsModule
import com.gumtree.mobile.features.srp.srpModule
import com.gumtree.mobile.features.vip.v2.vipV2Module
import com.gumtree.mobile.features.syncPhoneVerifyResult.syncPhoneVerifyStateModule
import com.gumtree.mobile.features.vip.vipModule
import com.gumtree.mobile.utils.ApplicationEnvironment
import io.ktor.server.application.Application
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.context.GlobalContext
import org.koin.core.context.GlobalContext.loadKoinModules
import org.koin.core.context.stopKoin
import org.koin.test.check.checkModules
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.runUnitTest
import org.koin.core.module.Module as KoinModule

class DIModulesBoundTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should bound network DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound APIs DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound common DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound view factories DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(viewFactoriesDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound block user DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, blockUserModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound user login DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, loginModule, commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound user registration DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, registrationModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound report listing DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, reportListingModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound report chat DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, reportChatModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound favourites DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, favouritesModule, commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound saved searches DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, savedSearchesModule, commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound search suggestions DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, searchSuggestionsModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound search suggestions V2 DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, searchSuggestionsModuleV2) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound home feed DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, viewFactoriesDIModule, homeFeedModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound forgot password DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, forgotPasswordModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound conversations DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, conversationsModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound chat DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    myGumtreeModuleV2,
                    networkDIModule,
                    reviewsModule,
                    apisDIModule,
                    commonDIModule,
                    viewFactoriesDIModule,
                    chatModule,
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound locations DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, locationsModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound categories DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, categoriesModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound category landing DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    commonDIModule,
                    networkDIModule,
                    apisDIModule,
                    viewFactoriesDIModule,
                    categoryLandingModule,
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound srp DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, srpModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound vip DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, vipModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound vip v2 DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, commonDIModule, vipV2Module, viewFactoriesDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound settings DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    commonDIModule,
                    networkDIModule,
                    apisDIModule,
                    viewFactoriesDIModule,
                    settingsModule,
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound myGumtree DI module hierarchy `() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, myGumtreeModule, commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound myGumtree v2 DI module hierarchy `() {
        runUnitTest(robot) {
            Given { stubKoinModules(chatModule, reviewsModule, viewFactoriesDIModule, networkDIModule, apisDIModule, myGumtreeModuleV2, commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound promote DI module hierarchy `() {
        runUnitTest(robot) {
            Given { stubKoinModules(promoteModule, apisDIModule, viewFactoriesDIModule, networkDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound push notifications DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(networkDIModule, apisDIModule, pushNotificationsModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound filters DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    networkDIModule,
                    apisDIModule,
                    commonDIModule,
                    viewFactoriesDIModule,
                    filtersModule,
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound filters attribute values DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    networkDIModule,
                    apisDIModule,
                    commonDIModule,
                    viewFactoriesDIModule,
                    filtersAttributeModule
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound crm DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules(crmModule, apisDIModule, networkDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound seller profile DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    sellerProfileModule,
                    apisDIModule,
                    networkDIModule,
                    commonDIModule,
                    viewFactoriesDIModule
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound seller profile v2 DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    sellerProfileModuleV2,
                    apisDIModule,
                    networkDIModule,
                    commonDIModule,
                    viewFactoriesDIModule
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound reviews DI module hierarchy`() {
        runUnitTest(robot) {
            Given {
                stubKoinModules(
                    myGumtreeModuleV2,
                    chatModule,
                    reviewsModule,
                    apisDIModule,
                    networkDIModule,
                    commonDIModule,
                    viewFactoriesDIModule
                )
            }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound phone verify DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules( phoneVerificationModule,apisDIModule,networkDIModule,commonDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    @Test
    fun `should bound sync phone verify state DI module hierarchy`() {
        runUnitTest(robot) {
            Given { stubKoinModules( syncPhoneVerifyStateModule,apisDIModule,networkDIModule) }
            Then { checkDIModuleBound() }
        }
    }

    private class Robot : BaseRobot {
        val application: Application = mockk(relaxed = true)
        private lateinit var koinModules: List<KoinModule>

        override fun setup() {
            stopKoin()
            mockkObject(ApplicationEnvironment)
            every { ApplicationEnvironment.getProperty(any()) } returns LOCAL_HOST_BASE_URL
            every { application.environment.config.property(any()).getString() } returns DataFactory.anyString()
        }

        override fun tearsDown() {
            unmockkObject(ApplicationEnvironment)
            stopKoin()
        }

        fun stubKoinModules(vararg modules: KoinModule) {
            koinModules = modules.toMutableList().apply {
                add(0, applicationDIModule(application))
            }
        }

        fun checkDIModuleBound() {
            checkModules {
                if (GlobalContext.getOrNull() == null) {
                    modules(koinModules)
                } else {
                    loadKoinModules(koinModules)
                }
            }
        }

    }
}