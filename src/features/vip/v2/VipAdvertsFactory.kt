package com.gumtree.mobile.features.vip.v2

import com.gumtree.mobile.adverts.gam.GAMAdvertAttributes
import com.gumtree.mobile.adverts.gam.GAMAdvertSize
import com.gumtree.mobile.features.screens.layoutsData.GAMAdvertDto

/*******************************
 * GAM VIP ADVERT UNIT IDS
 ******************************/
const val GAM_ANDROID_VIP_UNIT_ID = "/5144/android.m/vip/"
const val GAM_IOS_VIP_UNIT_ID = "/5144/ios.m/vip/"

/*********************************
 * GAM VIP ADVERT SLOT NAMES
 ********************************/
const val GAM_VIP_TOP_SLOT = "top"
const val GAM_VIP_BOTTOM_SLOT = "bottom"
const val GAM_VIP_GALLERY_SLOT = "gallery"

/*********************************
 * GAM VIP ATT APPTR ADVERT PLACEMENT IDS
 ********************************/
const val GAM_VIP_ATTAPPTR_TOP_PLACEMENT_ID = "vip_top"
const val GAM_VIP_ATTAPPTR_BOTTOM_PLACEMENT_ID = "vip_bottom"
const val GAM_VIP_ATTAPPTR_GALLERY_PLACEMENT_ID = "vip_gallery"

/**
 * Factory about the SRP adverts
 */
class VipAdvertsFactory {

    fun buildVipTopAdvert(
        slotName: String,
        attributes: GAMAdvertAttributes,
        pageUrl: String?,
    ): GAMAdvertDto {
        return GAMAdvertDto(
            androidUnitId = GAM_ANDROID_VIP_UNIT_ID + slotName,
            iosUnitId = GAM_IOS_VIP_UNIT_ID + slotName,
            slotName = slotName,
            displaySize = listOf(GAMAdvertSize.Ad320x50),
            attributes = attributes,
            pageUrl = pageUrl,
            addApptrPlacementId = GAM_VIP_ATTAPPTR_TOP_PLACEMENT_ID,
        )
    }

    fun buildVipBottomAdvert(
        slotName: String,
        attributes: GAMAdvertAttributes,
        pageUrl: String?,
    ): GAMAdvertDto {
        return GAMAdvertDto(
            androidUnitId = GAM_ANDROID_VIP_UNIT_ID + slotName,
            iosUnitId = GAM_IOS_VIP_UNIT_ID + slotName,
            slotName = slotName,
            displaySize = listOf(GAMAdvertSize.Ad320x50),
            attributes = attributes,
            pageUrl = pageUrl,
            addApptrPlacementId = GAM_VIP_ATTAPPTR_BOTTOM_PLACEMENT_ID,
        )
    }

    fun buildVipGalleryAdvert(
        slotName: String,
        attributes: GAMAdvertAttributes,
        pageUrl: String?,
    ): GAMAdvertDto {
        return GAMAdvertDto(
            androidUnitId = GAM_ANDROID_VIP_UNIT_ID + slotName,
            iosUnitId = GAM_IOS_VIP_UNIT_ID + slotName,
            slotName = slotName,
            displaySize = listOf(GAMAdvertSize.Ad300x250),
            attributes = attributes,
            pageUrl = pageUrl,
            addApptrPlacementId = GAM_VIP_ATTAPPTR_GALLERY_PLACEMENT_ID,
        )
    }
}
