package com.gumtree.mobile.features.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class UserLoginDto(
    @SerialName("userId")
    val userId: String,
    @SerialName("userEmail")
    val userEmail: String,
    @SerialName("userDisplayName")
    val userDisplayName: String,
    @SerialName("userToken")
    val userToken: String,
    @SerialName("accountId")
    val accountId: String,
    @SerialName("userAuthorization")
    val userAuthorization: String,
    @SerialName("issuedDate")
    val issuedDate: String,
    @SerialName("userType")
    val userType: String?,
)
