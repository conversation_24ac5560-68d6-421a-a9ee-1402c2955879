package com.gumtree.mobile.features.categoryLanding

import com.gumtree.mobile.api.locations.RawLocationFetcher
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.filterNotNullValues

const val ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME = "submit_car_search"

class CarsCategoryLandingAnalyticsProvider(
    commonAnalyticsProvider: CommonAnalyticsProvider,
    private val rawLocationFetcher: RawLocationFetcher,
) : CommonAnalyticsProvider by commonAnalyticsProvider {

    companion object {
        fun getBodyTypeParams(bodyType: String) = mapOf(AnalyticsParams.Search.BODY_TYPE to bodyType)
    }

    fun getSubmitCarSearchEvent(): AnalyticsEventData = AnalyticsEventData(
        eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
    )

    suspend fun getScreenParams(
        queryParams: QueryParams,
    ): Map<String, String> {
        val locationId = queryParams[ApiQueryParams.LOCATION_ID]
        val categoryId = queryParams[ApiQueryParams.CATEGORY_ID]
        val locationType = LocationType.fromString(queryParams[ApiQueryParams.LOCATION_TYPE])
        val locationName = locationId?.let { rawLocationFetcher.fetchByLocationIdAndType(it, locationType) }?.name
        return mapOf(
            AnalyticsParams.Search.LOCATION to locationName,
            AnalyticsParams.Search.LOCATION_ID to locationId,
            AnalyticsParams.Search.CATEGORY_ID to categoryId,
            AnalyticsParams.Search.CATEGORY to CategoriesTreeCache.getCategoryIdName(categoryId),
        ).filterNotNullValues()
    }
}
