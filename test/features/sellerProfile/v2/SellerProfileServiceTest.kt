package features.sellerProfile.v2

import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.apis.CapiSearchApi
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.reviews.api.ReviewsApi
import com.gumtree.mobile.api.userProfile.api.UserProfileApi
import com.gumtree.mobile.api.userService.api.UserServiceApi
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileService
import com.gumtree.mobile.responses.QueryParams
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class SellerProfileServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use CAPI search API to get seller other listings`() = runTest {
        runUnitTest(robot) {
            Given { stubPage(DataFactory.SOME_PAGE_NUMBER) }
            Given { stubSize(DataFactory.SOME_SIZE) }
            Given {
                stubSearchOptions(
                    hashMapOf(
                        CapiApiParams.LOCATION_ID to DataFactory.SOME_AD_LOCATION_ID,
                        CapiApiParams.CATEGORY_ID to DataFactory.SOME_AD_CATEGORY_ID
                    )
                )
            }
            When { searchAds() }
            Then { checkCapiSearchAdsApiCalled() }
        }
    }

    @Test
    fun `should use PAPI profile API to get seller profile`() = runTest {
        runUnitTest(robot) {
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            When { getBaseUserProfile() }
            Then { checkPapiBaseUserProfileApiCalled() }
        }
    }

    @Test
    fun `should use PAPI profile API to get seller profile by account id`() = runTest {
        runUnitTest(robot) {
            Given { stubAccountId(DataFactory.SOME_ACCOUNT_ID) }
            When { getBaseUserProfileByAccountId() }
            Then { checkPapiBaseUserProfileByAccountIdApiCalled() }
        }
    }

    @Test
    fun `should use Reviews API to get all seller reviews with feedback`() = runTest {
        runUnitTest(robot) {
            Given { stubReviewerId(DataFactory.SOME_USER_ID) }
            Given { stubPage(DataFactory.SOME_PAGE_NUMBER) }
            Given { stubSize(DataFactory.SOME_SIZE) }
            When { getUserReviews() }
            Then { checkReviewsApiGetUserReviewsWithFeedbackCalled() }
        }
    }

    private class Robot: BaseRobot {
        private val capiSearchApi: CapiSearchApi = mockk(relaxed = true)
        private val reviewsApi: ReviewsApi = mockk(relaxed = true)
        private val userServiceApi: UserServiceApi = mockk(relaxed = true)
        private val userProfileApi: UserProfileApi = mockk(relaxed = true)
        private val apiHeaders: ApiHeaders = mockk(relaxed = true)
        private lateinit var searchOptions: QueryParams
        private lateinit var reviewerId: String
        private lateinit var publicUserId: String
        private lateinit var accountId: String
        private lateinit var page: String
        private lateinit var size: String

        private lateinit var testSubject: SellerProfileService

        override fun setup() {
            testSubject = SellerProfileService(capiSearchApi, reviewsApi, userServiceApi, userProfileApi)
        }

        fun stubReviewerId(reviewerId: String) {
            this.reviewerId = reviewerId
        }

        fun stubPublicUserId(publicUserId: String) {
            this.publicUserId = publicUserId
        }

        fun stubAccountId(accountId: String) {
            this.accountId = accountId
        }

        fun stubPage(page: String) {
            this.page = page
        }

        fun stubSize(size: String) {
            this.size = size
        }

        fun stubSearchOptions(queryParams: QueryParams) {
            searchOptions = queryParams
        }

        suspend fun searchAds() {
            testSubject.searchAds(apiHeaders, searchOptions, page, size)
        }

        suspend fun getBaseUserProfile() {
            testSubject.getBaseUserProfile(apiHeaders, publicUserId)
        }

        suspend fun getBaseUserProfileByAccountId() {
            testSubject.getBaseUserProfileByAccountId(apiHeaders, accountId)
        }

        suspend fun getUserReviews() {
            testSubject.getUserReviewsWithFeedback(reviewerId, page, size)
        }

        fun checkCapiSearchAdsApiCalled() {
            coVerify { capiSearchApi.searchAds(apiHeaders, searchOptions, page, size) }
        }

        fun checkReviewsApiGetUserReviewsWithFeedbackCalled() {
            coVerify { reviewsApi.getUserReviewsWithFeedback(reviewerId, page, size) }
        }

        fun checkPapiBaseUserProfileApiCalled() {
            coVerify { userProfileApi.getBaseUserProfile(apiHeaders, publicUserId) }
        }

        fun checkPapiBaseUserProfileByAccountIdApiCalled() {
            coVerify { userProfileApi.getBaseUserProfileByAccountId(apiHeaders, accountId) }
        }
    }
}