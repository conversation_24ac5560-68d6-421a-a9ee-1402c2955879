package com.gumtree.mobile.plugins.metrics

import com.gumtree.mobile.plugins.appMicrometerRegistry
import io.ktor.server.application.call
import io.ktor.server.response.respond
import io.ktor.server.routing.Route
import io.ktor.server.routing.get

const val INTERNAL_METRICS_PATH = "/internal/metrics"

fun Route.internalMetricsRoute() {
    get(INTERNAL_METRICS_PATH) {
        call.respond(appMicrometerRegistry.scrape())
    }
}
