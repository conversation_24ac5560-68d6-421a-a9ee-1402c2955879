package utils.extensions

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.userService.models.RawUserServiceUserType
import com.gumtree.mobile.common.JWT_ACCOUNT_ID_KEY
import com.gumtree.mobile.common.JWT_USER_DISPLAY_NAME_KEY
import com.gumtree.mobile.common.JWT_USER_EMAIL_KEY
import com.gumtree.mobile.common.JWT_USER_ID_KEY
import com.gumtree.mobile.common.JWT_USER_LAST_NAME_KEY
import com.gumtree.mobile.common.JWT_USER_POSTING_DATE_KEY
import com.gumtree.mobile.common.JWT_USER_REG_DATE_KEY
import com.gumtree.mobile.common.JWT_USER_TOKEN_KEY
import com.gumtree.mobile.common.JWT_USER_TYPE_KEY
import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.features.savedSearches.SavedSearchRequest
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.UnauthorisedJWTException
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_LOCATION_ID
import com.gumtree.mobile.routes.DEFAULT_PAGE_NUMBER
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import com.gumtree.mobile.routes.ID_PATH
import com.gumtree.mobile.utils.extensions.getHeaderOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.isNotNullOrEmpty
import com.gumtree.mobile.utils.extensions.readAllQueryParams
import com.gumtree.mobile.utils.extensions.readBodyParamOrThrowBadRequest
import com.gumtree.mobile.utils.extensions.readBooleanQueryParam
import com.gumtree.mobile.utils.extensions.readPagingParams
import com.gumtree.mobile.utils.extensions.readPathParam
import com.gumtree.mobile.utils.extensions.readQueryParam
import com.gumtree.mobile.utils.extensions.readQueryParamOrDefault
import com.gumtree.mobile.utils.extensions.readUserProfileData
import com.gumtree.mobile.utils.extensions.readUserProfileDataOrThrow
import com.gumtree.mobile.utils.extensions.respondCreated
import com.gumtree.mobile.utils.extensions.respondNoContent
import com.gumtree.mobile.utils.extensions.respondSuccess
import com.gumtree.mobile.utils.regexes.RegEx
import io.ktor.http.Headers
import io.ktor.http.HttpStatusCode
import io.ktor.http.ParametersBuilderImpl
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.principal
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.request.ApplicationRequest
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.coroutines.And
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.dataFactory.JWTPrincipalFactory
import tools.dataFactory.UserProfileDataFactory
import tools.runUnitTest
import tools.runUnitTestForException
import java.util.*

class ApplicationCallExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should respond with screen response and HttpStatusCode OK`() = runTest {
        runUnitTest(robot) {
            When { screenRespondSuccess() }
            Then { checkScreenRespondSuccess() }
        }
    }

    @Test
    fun `should respond with HttpStatusCode Created`() = runTest {
        runUnitTest(robot) {
            When { respondCreated() }
            Then { checkRespondStatus(HttpStatusCode.Created) }
        }
    }

    @Test
    fun `should respond with HttpStatusCode NoContent`() = runTest {
        runUnitTest(robot) {
            When { respondNoContent() }
            Then { checkRespondStatus(HttpStatusCode.NoContent) }
        }
    }

    @Test
    fun `should read saved search request path parameter`() = runTest {
        runUnitTest(robot) {
            Given { stubPathParam(ID_PATH, DataFactory.SOME_SAVED_SEARCH_ID) }
            When { readPathParam(ID_PATH, RegEx.HEX24) }
            Then { checkPathPram(DataFactory.SOME_SAVED_SEARCH_ID) }

            Given { stubPathParam(ID_PATH, DataFactory.SOME_AD_ID) }
            When { readPathParam(ID_PATH, RegEx.NUMERICAL_ID) }
            Then { checkPathPram(DataFactory.SOME_AD_ID) }

            Given { stubPathParam(ID_PATH, DataFactory.SOME_USER_EMAIL) }
            When { readPathParam(ID_PATH, RegEx.EMAIL) }
            Then { checkPathPram(DataFactory.SOME_USER_EMAIL) }

            Given { stubPathParam(ID_PATH, DataFactory.SOME_CONVERSATION_ID) }
            When { readPathParam(ID_PATH, RegEx.UDID) }
            Then { checkPathPram(DataFactory.SOME_CONVERSATION_ID) }
        }
    }

    @Test
    fun `should throw IllegalArgumentException if saved search request path parameter is missing`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubNoPathParam() }
            When { readPathParam(ID_PATH, RegEx.HEX24) }
        }
    }

    @Test
    fun `should throw IllegalArgumentException if saved search request path parameter value is NOT numerical format`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubPathParam(ID_PATH, DataFactory.SOME_SAVED_SEARCH_ID) }
            When { readPathParam(ID_PATH, RegEx.NUMERICAL_ID) }

            Given { stubPathParam(ID_PATH, DataFactory.SOME_AD_ID) }
            When { readPathParam(ID_PATH, RegEx.HEX24) }

            Given { stubPathParam(ID_PATH, DataFactory.SOME_AD_ID) }
            When { readPathParam(ID_PATH, RegEx.EMAIL) }
        }
    }

    @Test
    fun `should throw IllegalArgumentException if saved search request path parameter value is NOT in HEX24 format`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubPathParam(ID_PATH, DataFactory.SOME_AD_ID) }
            When { readPathParam(ID_PATH, RegEx.HEX24) }
        }
    }

    @Test
    fun `should throw IllegalArgumentException if saved search request path parameter value is NOT in email format`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubPathParam(ID_PATH, DataFactory.SOME_AD_ID) }
            When { readPathParam(ID_PATH, RegEx.EMAIL) }
        }
    }

    @Disabled("Fails with CannotTransformContentToTypeException. We need to find a way to make it work")
    @Test
    fun `should read POST new saved search request body parameter`() = runTest {
        runUnitTest(robot) {
            Given {
                stubSavedSearchBodyParam(
                    SavedSearchRequest(
                        params = hashMapOf(
                            "zipcode" to DataFactory.SOME_ZIP_CODE
                        )
                    )
                )
            }
            When { readPostSavedSearchBodyParamOrThrowBadRequest() }
            Then { checkSavedSearchBodyParam() }
        }
    }

    @Test
    fun `should read request user profile JWT principle`() = runTest {
        runUnitTest(robot) {
            Given { stubJWTPrincipal(DataFactory.SOME_USER_AUTHORIZATION_WITHOUT_BEARER) }
            When { readUserProfileData() }
            Then {
                checkUserProfile(
                    UserProfileDataFactory.create(
                        userId = "2871937",
                        accountId = "2881782",
                        displayName = "Jam",
                        userLastName = "Jam",
                        userRegDate = "**********.*********",
                        userPostingDate = "**********.*********",
                        userEmail = "<EMAIL>",
                        userToken = "8f4UIR5WHZq3Sdz4P/vgLkyJRlrf3jjpAuHlyoJ155AhdRjWOvKxN/NGgZ7udRO7cVGK3uxQlmBhaCz3CPs9maTBIEDaPjFzdECS3JUN+u0=",
                        userType = RawUserServiceUserType.STANDARD.name,
                    ),
                )
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = [JWT_USER_ID_KEY, JWT_ACCOUNT_ID_KEY, JWT_USER_DISPLAY_NAME_KEY, JWT_USER_EMAIL_KEY, JWT_USER_TOKEN_KEY])
    fun `should NOT read request user profile if JWT principle can NOT find required claim`(requiredClaimKey: String) = runTest {
        runUnitTest(robot) {
            Given { stubJWTPrincipalClaim(requiredClaimKey, null) }
            When { readUserProfileData() }
            Then { checkUserProfile(null) }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = [JWT_USER_LAST_NAME_KEY, JWT_USER_REG_DATE_KEY, JWT_USER_POSTING_DATE_KEY, JWT_USER_TYPE_KEY])
    fun `should read request user profile if JWT principle can NOT find optional claim`(optionalClaimKey: String) = runTest {
        runUnitTest(robot) {
            Given { stubJWTPrincipalClaim(optionalClaimKey, null) }
            When { readUserProfileData() }
            Then {
                checkUserProfile(
                    UserProfileData(
                        userId = EMPTY_STRING,
                        accountId = EMPTY_STRING,
                        userDisplayName = EMPTY_STRING,
                        userLastName = EMPTY_STRING,
                        userRegDate = EMPTY_STRING,
                        userPostingDate = EMPTY_STRING,
                        userEmail = EMPTY_STRING,
                        userToken = EMPTY_STRING,
                        userType = EMPTY_STRING,
                    ),
                )
            }
        }
    }

    @Test
    fun `should NOT read request user profile JWT principle`() = runTest {
        runUnitTest(robot) {
            Given { stubJWTPrincipal(null) }
            When { readUserProfileData() }
            Then { checkUserProfile(null) }
        }
    }

    @Test
    fun `should read request user profile JWT principle and NOT throw an exception`() = runTest {
        runUnitTest(robot) {
            Given { stubJWTPrincipal(DataFactory.SOME_USER_AUTHORIZATION_WITHOUT_BEARER) }
            When { readUserProfileDataOrThrow() }
            Then {
                checkUserProfile(
                    UserProfileDataFactory.create(
                        userId = "2871937",
                        accountId = "2881782",
                        displayName = "Jam",
                        userLastName = "Jam",
                        userRegDate = "**********.*********",
                        userPostingDate = "**********.*********",
                        userEmail = "<EMAIL>",
                        userToken = "8f4UIR5WHZq3Sdz4P/vgLkyJRlrf3jjpAuHlyoJ155AhdRjWOvKxN/NGgZ7udRO7cVGK3uxQlmBhaCz3CPs9maTBIEDaPjFzdECS3JUN+u0=",
                        userType = RawUserServiceUserType.STANDARD.name,
                    ),
                )
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = [JWT_USER_ID_KEY, JWT_ACCOUNT_ID_KEY, JWT_USER_DISPLAY_NAME_KEY, JWT_USER_EMAIL_KEY, JWT_USER_TOKEN_KEY])
    fun `should throw UnauthorisedJWT exception if the request user profile JWT `(requiredClaimKey: String) = runTest {
        runUnitTestForException(robot, UnauthorisedJWTException::class) {
            Given { stubJWTPrincipalClaim(requiredClaimKey, null) }
            When { readUserProfileDataOrThrow() }
        }
    }

    @Test
    fun `should read request single query param`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    hashMapOf(
                        "zipcode" to DataFactory.SOME_ZIP_CODE
                    )
                )
            }
            When { readQueryParam("zipcode", true) }
            Then { checkQueryParamValue(DataFactory.SOME_ZIP_CODE) }
        }
    }

    @Test
    fun `should read request multiple query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    hashMapOf(
                        "categoryId" to DataFactory.SOME_AD_CATEGORY_ID,
                        "distance" to DataFactory.SOME_DISTANCE,
                        "zipcode" to DataFactory.SOME_ZIP_CODE,
                    )
                )
            }
            When { readQueryParam("zipcode", true) }
            Then { checkQueryParamValue(DataFactory.SOME_ZIP_CODE) }

            And { readQueryParam("distance", true) }
            Then { checkQueryParamValue(DataFactory.SOME_DISTANCE) }

            And { readQueryParam("categoryId", true) }
            Then { checkQueryParamValue(DataFactory.SOME_AD_CATEGORY_ID) }
        }
    }

    @Test
    fun `should throw IllegalArgumentException if required query param is missing`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubQueryParams(hashMapOf("distance" to DataFactory.SOME_DISTANCE)) }
            When { readQueryParam("someMissingRequiredQueryParamKey", true) }
        }
    }

    @Test
    fun `should read request single boolean query param`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf(ApiQueryParams.IS_SAVED_SEARCH_PUSH_ENABLED to "true")) }
            When { readBooleanQueryParam(ApiQueryParams.IS_SAVED_SEARCH_PUSH_ENABLED, false) }
            Then { checkQueryParamValue(true) }
        }
    }

    @Test
    fun `should read request multiple boolean query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    hashMapOf(
                        ApiQueryParams.IS_SAVED_SEARCH_PUSH_ENABLED to "true",
                        ApiQueryParams.IS_CHAT_PUSH_ENABLED to "true",
                        ApiQueryParams.IS_MARKETING_PUSH_ENABLED to "false",
                    )
                )
            }
            When { readBooleanQueryParam(ApiQueryParams.IS_SAVED_SEARCH_PUSH_ENABLED, true) }
            Then { checkQueryParamValue(true) }

            And { readBooleanQueryParam(ApiQueryParams.IS_CHAT_PUSH_ENABLED, false) }
            Then { checkQueryParamValue(true) }

            And { readBooleanQueryParam(ApiQueryParams.IS_MARKETING_PUSH_ENABLED, false) }
            Then { checkQueryParamValue(false) }
        }
    }

    @Test
    fun `should throw IllegalArgumentException if required boolean query param is missing`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubQueryParams(hashMapOf("distance" to DataFactory.SOME_DISTANCE)) }
            When { readBooleanQueryParam("isMissingRequiredQueryParamKey", true) }
            Then { checkQueryParamValue(null) }
        }
    }

    @Test
    fun `should return NULL if NOT required boolean query param is missing`() {
        runUnitTestForException(robot, IllegalArgumentException::class) {
            Given { stubQueryParams(hashMapOf("distance" to DataFactory.SOME_DISTANCE)) }
            When { readBooleanQueryParam(ApiQueryParams.IS_CHAT_PUSH_ENABLED, true) }
        }
    }

    @Test
    fun `should read Int query param or default`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("categoryId" to DataFactory.SOME_AD_CATEGORY_ID)) }
            When { readQueryParamOrDefault("categoryId", 1) }
            Then { checkQueryParamValue(DataFactory.SOME_AD_CATEGORY_ID.toInt()) }
            Then { checkQueryParamType(Integer::class.java) }
        }
    }

    @Test
    fun `should return default value if Int query param is NOT there`() {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("locationId" to "1234567890")) }
            When { readQueryParamOrDefault("categoryId", 1) }
            Then { checkQueryParamValue(1) }
            Then { checkQueryParamType(Integer::class.java) }
        }
    }

    @Test
    fun `should read Long query param or default`() {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("locationId" to "1234567890")) }
            When { readQueryParamOrDefault("locationId", 11223344556677) }
            Then { checkQueryParamValue(1234567890L) }
            Then { checkQueryParamType(java.lang.Long::class.java) }
        }
    }

    @Test
    fun `should return default value if Long query param is NOT there`() {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("searchSuggestions" to "true")) }
            When { readQueryParamOrDefault("locationId", 11223344556677) }
            Then { checkQueryParamValue(11223344556677) }
            Then { checkQueryParamType(java.lang.Long::class.java) }
        }
    }

    @Test
    fun `should read Boolean query param or default`() {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("searchSuggestions" to "true")) }
            When { readQueryParamOrDefault("searchSuggestions", false) }
            Then { checkQueryParamValue(true) }
            Then { checkQueryParamType(java.lang.Boolean::class.java) }
        }
    }

    @Test
    fun `should return default value if Boolean query param is NOT there`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("locationId" to DataFactory.SOME_AD_LOCATION_ID)) }
            When { readQueryParamOrDefault("searchSuggestions", false) }
            Then { checkQueryParamValue(false) }
            Then { checkQueryParamType(java.lang.Boolean::class.java) }
        }
    }

    @Test
    fun `should read String query param or default`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("locationId" to DataFactory.SOME_AD_LOCATION_ID)) }
            When { readQueryParamOrDefault("locationId", "1") }
            Then { checkQueryParamValue(DataFactory.SOME_AD_LOCATION_ID) }
            Then { checkQueryParamType(String::class.java) }
        }
    }

    @Test
    fun `should return default if the expected query param is NOT Int, Long or String`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("distance" to DataFactory.SOME_DISTANCE)) }
            When { readQueryParamOrDefault("locationId", DEFAULT_LOCATION_ID) }
            Then { checkQueryParamValue(DEFAULT_LOCATION_ID) }
            Then { checkQueryParamType(String::class.java) }
        }
    }

    @Test
    fun `should return default if the expected query param is missing`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf("categoryId" to DataFactory.SOME_AD_CATEGORY_ID)) }
            When { readQueryParamOrDefault("distance", 1.23f) }
            Then { checkQueryParamValue(1.23f) }
            Then { checkQueryParamType(java.lang.Float::class.java) }
        }
    }

    @Test
    fun `should return all request query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    hashMapOf(
                        "categoryId" to DataFactory.SOME_AD_CATEGORY_ID,
                        "distance" to DataFactory.SOME_DISTANCE,
                        "zipcode" to DataFactory.SOME_ZIP_CODE,
                        "minPrice" to DataFactory.SOME_PRICE_NUMBER,
                        "maxPrice" to DataFactory.SOME_PRICE_NUMBER,
                    )
                )
            }
            When { readAllQueryParams() }
            Then { checkAllQueryParamsSize(5) }
        }
    }

    @Test
    fun `should return NO request query params`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf()) }
            When { readAllQueryParams() }
            Then { checkAllQueryParamsSize(0) }
        }
    }

    @Test
    fun `should filter all empty query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    hashMapOf(
                        "q" to EMPTY_STRING,
                        "categoryId" to EMPTY_STRING,
                        "distance" to EMPTY_STRING,
                        "zipcode" to DataFactory.SOME_ZIP_CODE,
                        "minPrice" to EMPTY_STRING,
                        "maxPrice" to DataFactory.SOME_PRICE_NUMBER,
                    )
                )
            }
            When { readAllQueryParams() }
            Then { checkAllQueryParamsSize(2) }
        }
    }

    @Test
    fun `should read paging query params`() = runTest {
        runUnitTest(robot) {
            Given {
                stubQueryParams(
                    hashMapOf(
                        ApiQueryParams.PAGE to "3",
                        ApiQueryParams.SIZE to "50",
                    )
                )
            }
            When { readPagingParams() }
            Then { checkPagingQueryParams("3", DEFAULT_PAGE_SIZE) }
        }
    }

    @Test
    fun `should return default paging query params`() = runTest {
        runUnitTest(robot) {
            Given { stubQueryParams(hashMapOf()) }
            When { readPagingParams() }
            Then { checkPagingQueryParams(DEFAULT_PAGE_NUMBER, DEFAULT_PAGE_SIZE) }
        }
    }

    @Test
    fun `should return header value`() = runTest {
        runUnitTest(robot) {
            Given { stubRequestHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL to DataFactory.SOME_USER_EMAIL) }
            When { getHeaderOrThrowBadRequest(ApiHeaderParams.AUTHORISATION_USER_EMAIL) }
            Then { checkHeader(DataFactory.SOME_USER_EMAIL) }

            Given { stubRequestHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN to DataFactory.SOME_TOKEN) }
            When { getHeaderOrThrowBadRequest(ApiHeaderParams.AUTHORISATION_USER_TOKEN) }
            Then { checkHeader(DataFactory.SOME_TOKEN) }

            Given { stubRequestHeader(ApiHeaderParams.THREATMETRIX_SESSION to DataFactory.SOME_THREAT_METRIX_SESSION) }
            When { getHeaderOrThrowBadRequest(ApiHeaderParams.THREATMETRIX_SESSION) }
            Then { checkHeader(DataFactory.SOME_THREAT_METRIX_SESSION) }
        }
    }

    @Test
    fun `should throw BadRequestException if required header param is missing`() {
        runUnitTestForException(robot, BadRequestException::class) {
            Given { stubRequestHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL to DataFactory.SOME_USER_EMAIL) }
            When { getHeaderOrThrowBadRequest(ApiHeaderParams.THREATMETRIX_SESSION) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualPathParamResult: String
        private lateinit var actualHeaderResult: String
        private var actualQueryParamResult: Any? = null
        private var actualUserProfileResult: UserProfileData? = null
        private lateinit var actualQueryParamsResult: QueryParams
        private lateinit var actualPagingParamsResult: Pair<String, String>
        private lateinit var actualSavedSearchRequestResult: SavedSearchRequest

        private val screenResponse: ScreenResponse = mockk(relaxed = true)
        private val request: ApplicationRequest = mockk(relaxed = true)

        private val testSubject: ApplicationCall = mockk(relaxed = true)

        fun stubPathParam(
            paramKey: String,
            paramValue: String
        ) {
            every { testSubject.parameters } returns ParametersBuilderImpl().apply { append(paramKey, paramValue) }.build()
        }

        fun stubNoPathParam() {
            every { testSubject.parameters } returns ParametersBuilderImpl().build()
        }

        fun stubJWTPrincipal(jwt: String?) {
            every { testSubject.principal<JWTPrincipal>() } returns when {
                jwt.isNotNullOrEmpty() -> JWTPrincipalFactory.create(jwt)
                else -> null
            }
        }

        fun stubJWTPrincipalClaim(key: String, value: String?) {
            val mockedJWTPrincipal = mockk<JWTPrincipal>(relaxed = true)
            every { testSubject.principal<JWTPrincipal>() } returns mockedJWTPrincipal
            every { mockedJWTPrincipal.payload.getClaim(key)?.asString() } returns value
        }

        fun stubSavedSearchBodyParam(savedSearchRequest: SavedSearchRequest) {
            coEvery { testSubject.receive<SavedSearchRequest>() } returns savedSearchRequest
        }

        fun stubQueryParams(queryPrams: HashMap<String, String>) {
            every { testSubject.request } returns request
            every { request.queryParameters } returns ParametersBuilderImpl().apply {
                queryPrams.forEach { (paramKey, paramValue) ->
                    append(paramKey, paramValue)
                }
            }.build()
        }

        fun stubRequestHeader(header: Pair<String, String>) {
            every { testSubject.request } returns request
            every { request.headers } returns Headers.build{ append(header.first, header.second) }
        }

        suspend fun screenRespondSuccess() {
            testSubject.respondSuccess(screenResponse)
        }

        suspend fun respondCreated() {
            testSubject.respondCreated()
        }

        suspend fun respondNoContent() {
            testSubject.respondNoContent()
        }

        fun readPathParam(
            paramKey: String,
            regex: RegEx,
        ) {
            actualPathParamResult = testSubject.readPathParam(paramKey, regex)
        }

        suspend fun readPostSavedSearchBodyParamOrThrowBadRequest() {
            actualSavedSearchRequestResult = testSubject.readBodyParamOrThrowBadRequest()
        }

        fun readUserProfileData() {
            actualUserProfileResult = testSubject.readUserProfileData()
        }

        fun readUserProfileDataOrThrow() {
            actualUserProfileResult = testSubject.readUserProfileDataOrThrow()
        }

        fun readQueryParam(
            paramName: String,
            required: Boolean
        ) {
            actualQueryParamResult = testSubject.readQueryParam(paramName, required)
        }

        fun readBooleanQueryParam(
            paramName: String,
            required: Boolean
        ) {
            actualQueryParamResult = testSubject.readBooleanQueryParam(paramName, required)
        }

        fun readQueryParamOrDefault(
            paramName: String,
            default: Any
        ) {
            actualQueryParamResult = testSubject.readQueryParamOrDefault(paramName, default)
        }

        fun readAllQueryParams() {
            actualQueryParamsResult = testSubject.readAllQueryParams()
        }

        fun readPagingParams() {
            actualPagingParamsResult = testSubject.readPagingParams()
        }

        fun getHeaderOrThrowBadRequest(headerKey: String) {
            actualHeaderResult = testSubject.getHeaderOrThrowBadRequest(headerKey)
        }

        fun checkScreenRespondSuccess() {
            coVerify { testSubject.respond(HttpStatusCode.OK, screenResponse) }
        }

        fun checkRespondStatus(expected: HttpStatusCode) {
            coVerify { testSubject.respond(expected) }
        }

        fun checkPathPram(expected: String) {
            assertEquals(expected, actualPathParamResult)
        }

        fun checkSavedSearchBodyParam() {
            assertNotNull(actualSavedSearchRequestResult)
        }

        fun checkUserProfile(expected: UserProfileData?) {
            assertEquals(expected, actualUserProfileResult)
        }

        fun checkQueryParamValue(expected: Any?) {
            assertEquals(expected, actualQueryParamResult)
        }

        fun checkQueryParamType(expected: Class<*>) {
            assertEquals(expected, actualQueryParamResult?.javaClass)
        }

        fun checkPagingQueryParams(
            expectedPage: String,
            expectedSize: String
        ) {
            assertEquals(expectedPage, actualPagingParamsResult.first)
            assertEquals(expectedSize, actualPagingParamsResult.second)
        }

        fun checkAllQueryParamsSize(expected: Int) {
            assertEquals(expected, actualQueryParamsResult.size)
        }

        fun checkHeader(expected: String) {
            assertEquals(expected, actualHeaderResult)
        }
    }
}