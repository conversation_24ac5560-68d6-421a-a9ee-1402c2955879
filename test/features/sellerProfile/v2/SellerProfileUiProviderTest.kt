package features.sellerProfile.v2

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.conversations.models.RawUserInfo
import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.SellerHistoryCardDto
import com.gumtree.mobile.features.screens.layoutsData.SellerProfileCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileActiveStatusFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileListingsGridSizes
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileHistoryMapper
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileListingMapper
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfilePostingSinceFormatter
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.features.sellerProfile.v2.SellerProfileUiProvider
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertInstanceOf
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import tools.runUnitTest
import java.util.*

class SellerProfileUiProviderTest {

    private val robot = Robot()

    @Test
    fun `should return seller profile row with seller profile card`() {
        runUnitTest(robot) {
            Given { stubPostingSinceDate(DataFactory.SOME_POSTING_SINCE_TEXT) }
            Given { stubActiveStatusDate(DataFactory.SOME_SELLER_ACTIVE_STATUS_DATE) }
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(20, listOf(AdStatus.ACTIVE))) }
            Given {
                stubSellerProfile(
                    RawPapiUserProfileFactory.createRawUserProfileWithRating(
                        displayName = DataFactory.SOME_USER_FIRST_NAME,
                        lastName = DataFactory.SOME_USER_LAST_NAME,
                        registrationDate = Date(),
                        averageRating = 4.3f,
                        totalRatingsCount = 4,
                    )
                )
            }
            When { createSellerProfileRow() }
            Then { checkRowLayoutType(RowLayoutType.STICKY_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkRowLayoutDataTypeAtPosition(0, SellerProfileCardDto::class.java) }

            Then { checkSellerProfileCardDtoSellerName(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkSellerProfileCardDtoSellerAbbreviation(DataFactory.SOME_USER_FIRST_NAME.first().toString() + DataFactory.SOME_USER_LAST_NAME.first().toString()) }
            Then { checkSellerProfileCardDtoSellerActiveStatus(DataFactory.SOME_SELLER_ACTIVE_STATUS_DATE) }
            Then { checkSellerProfileCardDtoSellerAverageRating(4.3f) }
            Then { checkSellerProfileCardDtoSellerTotalRatings(4) }
            Then { checkSellerProfileCardDtoSellerMembership(DataFactory.SOME_POSTING_SINCE_TEXT) }

            Then { checkRowLayoutBottomDivider(null) }
            Then { checkRowLayoutScrollingBehaviour(ScrollingCollapseBehaviour.COLLAPSE_AT_TOP) }
        }
    }

    @Test
    fun `should NOT return seller profile row`() {
        runUnitTest(robot) {
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(20, listOf(AdStatus.ACTIVE))) }
            Given { stubSellerProfile(null) }
            When { createSellerProfileRow() }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should return seller profile location row`() {
        runUnitTest(robot) {
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(5, listOf(AdStatus.ACTIVE))) }
            When { createSellerProfileLocationTitleRow() }
            Then { checkRowLayoutType(RowLayoutType.STICKY_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto::class.java) }

            Then { checkTitleCardDtoTextAtPosition(0, DataFactory.SOME_AD_LOCATION) }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.X_SMALL) }
            Then { checkTitleCardDtoIconLeftAtPosition(0, TitleCardDto.Icon.LOCATION) }
            Then { checkTitleCardDtoColourAtPosition(0, TitleCardDto.Colour.FOREGROUND_SUBDUED) }

            Then { checkRowLayoutBottomDivider(null) }
            Then { checkRowLayoutScrollingBehaviour(ScrollingCollapseBehaviour.COLLAPSE_AT_TOP) }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [0, 1])
    fun `should NOT return seller profile location row when seller has less than 2 listings`(numberOfListings: Int) {
        runUnitTest(robot) {
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(numberOfListings, listOf(AdStatus.ACTIVE))) }
            When { createSellerProfileLocationTitleRow() }
            Then { checkRowLayoutDataSize(null) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "1234, 9999",
        "8844, 8888"
    )
    fun `should NOT return seller profile location row when seller last 2 listings have different locations`(locationId1: String, locationId2: String) {
        runUnitTest(robot) {
            Given {
                stubRawAds(
                    RawCapiAdsFactory.createRawCapiAdList(
                        rawCapiAds = listOf(
                            RawCapiAdsFactory.createRawCapiAdWithLocation(locationId = locationId1),
                            RawCapiAdsFactory.createRawCapiAdWithLocation(locationId = locationId2),
                        ),
                    ),
                )
            }
            When { createSellerProfileLocationTitleRow() }
            Then { checkRowLayoutDataSize(null) }
        }
    }

    @Test
    fun `should return seller profile email verified row`() {
        runUnitTest(robot) {
            When { createSellerProfileEmailVerifiedTitleRow() }
            Then { checkRowLayoutType(RowLayoutType.STICKY_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkRowLayoutDataTypeAtPosition(0, TitleCardDto::class.java) }

            Then { checkTitleCardDtoTextAtPosition(0, SellerProfileScreenUiConfiguration.EMAIL_ADDRESS_VERIFIED_TEXT) }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.X_SMALL) }
            Then { checkTitleCardDtoIconLeftAtPosition(0, TitleCardDto.Icon.EMAIL_VERIFIED) }
            Then { checkTitleCardDtoColourAtPosition(0, TitleCardDto.Colour.FOREGROUND_SUBDUED) }

            Then { checkRowLayoutBottomDivider(true) }
            Then { checkRowLayoutScrollingBehaviour(ScrollingCollapseBehaviour.COLLAPSE_AT_TOP) }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [5, 10])
    fun `should return seller profile history row`(numberOfListings: Int) {
        runUnitTest(robot) {
            When { createSellerProfileHistoryRow(ads = RawCapiAdsFactory.createRawCapiAdList(numberOfListings, listOf(AdStatus.ACTIVE))) }
            Then { checkRowLayoutType(RowLayoutType.STICKY_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkRowLayoutDataTypeAtPosition(0, SellerHistoryCardDto::class.java) }

            Then { checkSellerHistoryCardDtoTitleAtPosition(0, SellerProfileScreenUiConfiguration.SELLER_HISTORY_TITLE) }
            Then { checkSellerHistoryCardDtoItemsSizeAtPosition(0, 2) }
            Then {
                checkSellerHistoryCardDtoItemAtPosition(
                    rowPosition = 0,
                    itemPosition = 0,
                    expected = SellerHistoryCardDto.HistoryItem(
                        label = SellerProfileScreenUiConfiguration.SELLER_HISTORY_TOTAL_ITEMS_TEXT,
                        value = numberOfListings.toString(),
                    )
                )
            }
            Then {
                checkSellerHistoryCardDtoItemAtPosition(
                    rowPosition = 0,
                    itemPosition = 1,
                    expected = SellerHistoryCardDto.HistoryItem(
                        label = SellerProfileScreenUiConfiguration.SELLER_HISTORY_CATEGORIES_TEXT,
                        value = EMPTY_STRING,
                    )
                )
            }

            Then { checkRowLayoutBottomDivider(null) }
            Then { checkRowLayoutScrollingBehaviour(ScrollingCollapseBehaviour.COLLAPSE_AT_TOP) }
        }
    }

    @Test
    fun `should NOT return seller profile history row when seller has NO listings`() {
        runUnitTest(robot) {
            When { createSellerProfileHistoryRow(ads = null) }
            Then { checkRowLayoutDataSize(null) }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [5, 15, 20])
    fun `should return seller other ads title row when page is 0`(numberOfListings: Int) {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(numberOfListings, listOf(AdStatus.ACTIVE))) }
            When { createSellerOtherAdsTitleRow(numberOfListings) }
            Then { checkRowLayoutType(RowLayoutType.TITLE_ROW) }
            Then { checkRowLayoutDataSize(1) }
            Then { checkTitleCardDtoTextAtPosition(0, SellerProfileScreenUiConfiguration.getSellerOtherAdsTitle(numberOfListings)) }
            Then { checkTitleCardDtoSizeAtPosition(0, TitleCardDto.Size.X_SMALL) }
            Then { checkTitleCardDtoIconLeftAtPosition(0, null) }
            Then { checkTitleCardDtoColourAtPosition(0, TitleCardDto.Colour.FOREGROUND_SUBDUED) }
        }
    }

    @ParameterizedTest
    @CsvSource("1", "2", "3", "4", "5")
    fun `should NOT return seller other ads title row when page greater than 0`(page: Int) {
        runUnitTest(robot) {
            Given { stubPage(page) }
            When { createSellerOtherAdsTitleRow(20) }
            Then { checkRowLayoutIsNull() }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [6, 12, 20])
    fun `should return seller other ads rows`(numberOfListings: Int) {
        runUnitTest(robot) {
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(numberOfListings, listOf(AdStatus.ACTIVE))) }
            When { createSellerOtherAdsRows() }
            Then { checkSellerOtherAdsPortraitDataSize(numberOfListings / 2) }
            Then { checkSellerOtherAdsLandscapeDataSize(null) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "0, 20",
        "1, 20",
        "2, 10",
        "3, 0",
    )
    fun `should NOT return no listings row`(page: Int, numberOfListings: Int) {
        runUnitTest(robot) {
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(numberOfListings, listOf(AdStatus.ACTIVE))) }
            When { createSellerNoListingsRow(page) }
            Then { checkRowLayoutIsNull() }
        }
    }

    @Test
    fun `should return no listings row`() {
        runUnitTest(robot) {
            Given { stubRawAds(RawCapiAdsFactory.createRawCapiAdList(0, listOf(AdStatus.ACTIVE))) }
            When { createSellerNoListingsRow(0) }
            Then { checkSellerProfileRowType(RowLayoutType.SELLER_PROFILE_NO_RESULTS_ROW) }
        }
    }

    private class Robot: BaseRobot {
        private var actualRowLayoutResult: RowLayout<UiItem>? = null
        private lateinit var actualSellerOtherAdsResult: Pair<PortraitData, LandscapeData?>

        private val sellerProfilePostingSinceFormatter: SellerProfilePostingSinceFormatter = mockk(relaxed = true)
        private val sellerProfileActiveStatusFormatter: SellerProfileActiveStatusFormatter = mockk(relaxed = true)
        private lateinit var page: String
        private lateinit var rawAds: RawCapiAdList
        private var rawSellerProfile: RawPapiUserProfile? = null
        private val commonAnalyticsProvider = CommonAnalyticsProviderFactory.createInstance()

        private val testSubject = SellerProfileUiProvider(
            TitleFactory(),
            SellerProfileListingMapper(commonAnalyticsProvider),
            SellerProfileHistoryMapper(),
            sellerProfilePostingSinceFormatter,
            sellerProfileActiveStatusFormatter,
        )

        fun stubPostingSinceDate(date: String?) {
            every { sellerProfilePostingSinceFormatter.getTimeAgoLabel(olderDate = any(), newerDate = any()) } returns date
        }

        fun stubActiveStatusDate(date: String?) {
            every { sellerProfileActiveStatusFormatter.getTimeAgoLabel(olderDate = any(), newerDate = any()) } returns date
        }

        fun stubPage(page: Int) {
            this.page = page.toString()
        }

        fun stubSellerProfile(profile: RawPapiUserProfile?) {
            rawSellerProfile = profile
        }

        fun stubRawAds(ads: RawCapiAdList) {
            rawAds = ads
        }

        fun createSellerProfileRow() {
            actualRowLayoutResult = testSubject.createSellerProfileRow(
                rawSellerProfile,
                RawUserInfo(lastActive = DataFactory.SOME_SELLER_ACTIVE_STATUS_DATE),
            )
        }

        fun createSellerProfileLocationTitleRow() {
            actualRowLayoutResult = testSubject.createSellerProfileLocationRow(rawAds)
        }

        fun createSellerProfileEmailVerifiedTitleRow() {
            actualRowLayoutResult = testSubject.createSellerProfileEmailVerifiedRow()
        }

        fun createSellerProfileHistoryRow(ads: RawCapiAdList?) {
            actualRowLayoutResult = testSubject.createSellerProfileHistoryRow(ads)
        }

        fun createSellerOtherAdsTitleRow(totalNumber: Int) {
            actualRowLayoutResult = testSubject.createSellerOtherAdsTitleRow(totalNumber, page)
        }

        fun createSellerOtherAdsRows() {
            actualSellerOtherAdsResult = testSubject.createSellerOtherAdsRows(rawAds, SellerProfileListingsGridSizes())
        }

        fun createSellerNoListingsRow(page: Int) {
            actualRowLayoutResult = testSubject.createSellerNoListingsRow(page.toString(), rawAds.rawPaging.numFound)
        }

        fun checkRowLayoutBottomDivider(expected: Boolean?) {
            assertEquals(expected, actualRowLayoutResult?.bottomDivider)
        }

        fun checkRowLayoutScrollingBehaviour(expected: ScrollingCollapseBehaviour?) {
            assertEquals(expected, actualRowLayoutResult?.scrollingBehaviour)
        }

        fun checkRowLayoutType(expected: RowLayoutType) {
            assertEquals(expected, actualRowLayoutResult?.type)
        }

        fun checkRowLayoutIsNull() {
            assertNull(actualRowLayoutResult)
        }

        fun checkRowLayoutDataSize(expected: Int?) {
            assertEquals(expected, actualRowLayoutResult?.data?.size)
        }

        fun checkSellerProfileCardDtoSellerName(expected: String) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(0) as SellerProfileCardDto).sellerName)
        }

        fun checkSellerProfileCardDtoSellerAbbreviation(expected: String) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(0) as SellerProfileCardDto).sellerAbbreviation)
        }

        fun checkSellerProfileCardDtoSellerAverageRating(expected: Float?) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(0) as SellerProfileCardDto).sellerAverageRating)
        }

        fun checkSellerProfileCardDtoSellerTotalRatings(expected: Int?) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(0) as SellerProfileCardDto).sellerTotalRatings)
        }

        fun checkSellerProfileCardDtoSellerActiveStatus(expected: String?) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(0) as SellerProfileCardDto).sellerActiveStatus)
        }

        fun checkSellerProfileCardDtoSellerMembership(expected: String?) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(0) as SellerProfileCardDto).sellerMembership)
        }

        fun checkSellerProfileRowType(expected: RowLayoutType) {
            assertEquals(expected, actualRowLayoutResult?.type)
        }

        fun checkTitleCardDtoTextAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(position) as TitleCardDto).text)
        }

        fun checkTitleCardDtoSizeAtPosition(
            position: Int,
            expected: TitleCardDto.Size,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(position) as TitleCardDto).size)
        }

        fun checkTitleCardDtoIconLeftAtPosition(
            position: Int,
            expected: TitleCardDto.Icon?,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(position) as TitleCardDto).iconLeft)
        }

        fun checkTitleCardDtoColourAtPosition(
            position: Int,
            expected: TitleCardDto.Colour?,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(position) as TitleCardDto).colour)
        }

        fun checkSellerHistoryCardDtoTitleAtPosition(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(position) as SellerHistoryCardDto).title)
        }

        fun checkSellerHistoryCardDtoItemsSizeAtPosition(
            position: Int,
            expected: Int,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(position) as SellerHistoryCardDto).historyItems?.size)
        }

        fun checkSellerHistoryCardDtoItemAtPosition(
            rowPosition: Int,
            itemPosition: Int,
            expected: SellerHistoryCardDto.HistoryItem,
        ) {
            assertEquals(expected, (actualRowLayoutResult?.data?.get(rowPosition) as SellerHistoryCardDto).historyItems?.get(itemPosition))
        }

        fun<T> checkRowLayoutDataTypeAtPosition(
            position: Int,
            expected: Class<T>,
        ) {
            assertInstanceOf(expected, actualRowLayoutResult?.data?.get(position))
        }

        fun checkSellerOtherAdsPortraitDataSize(expected: Int) {
            assertEquals(expected, actualSellerOtherAdsResult.first.size)
        }

        fun checkSellerOtherAdsLandscapeDataSize(expected: Int?) {
            assertEquals(expected, actualSellerOtherAdsResult.second?.size)
        }
    }
}