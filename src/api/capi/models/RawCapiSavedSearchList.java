package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import com.gumtree.mobile.api.capi.models.RawCapiSavedSearch;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.ArrayList;
import java.util.List;


@Root(name = "alerts", strict = false)
@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
public class RawCapiSavedSearchList {

    @ElementList(entry = "alert", inline = true, required = false)
    @Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
    public List<RawCapiSavedSearch> rawCapiSavedSearches = new ArrayList<>();

    @Element(required = false, name = "paging")
    @Namespace(reference = Namespaces.TYPES)
    public RawCapiPaging rawPaging;
}
