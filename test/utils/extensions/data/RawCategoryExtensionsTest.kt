package utils.extensions.data

import com.gumtree.mobile.api.categories.models.RawCategory
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.extensions.data.toCategoryDto
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.rawDataFactory.RawCategoryFactory
import tools.runUnitTest

@ParallelTest
class RawCategoryExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should map raw category into category DTO with id, text and NO children`() {
        runUnitTest(robot) {
            Given {
                stubRawCategory(
                    CategoryDefaults.ALL_CATEGORIES.id,
                    CategoryDefaults.ALL_CATEGORIES.name,
                    null
                )
            }
            When { toCategoryDto() }
            Then { checkCategoryId(CategoryDefaults.ALL_CATEGORIES.id) }
            Then { checkCategoryText(CategoryDefaults.ALL_CATEGORIES.name) }
            Then { checkCategoryChildrenSize(null) }
        }
    }

    @Test
    fun `should map raw category into category DTO with id, text and children`() {
        runUnitTest(robot) {
            Given {
                stubRawCategory(
                    CategoryDefaults.ALL_CATEGORIES.id,
                    CategoryDefaults.ALL_CATEGORIES.name,
                    listOf(
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.PETS.id,
                            CategoryDefaults.PETS.name,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.SPORTS_AND_LEISURE.id,
                            CategoryDefaults.SPORTS_AND_LEISURE.name,
                        )
                    )
                )
            }
            When { toCategoryDto() }
            Then { checkCategoryId(CategoryDefaults.ALL_CATEGORIES.id) }
            Then { checkCategoryText(CategoryDefaults.ALL_CATEGORIES.name) }
            Then { checkCategoryChildrenSize(2) }
            Then { checkCategoryChildIdAtPosition(0, CategoryDefaults.PETS.id) }
            Then { checkCategoryChildIdAtPosition(1, CategoryDefaults.SPORTS_AND_LEISURE.id) }
            Then { checkCategoryChildTextAtPosition(0, CategoryDefaults.PETS.name) }
            Then { checkCategoryChildTextAtPosition(1, CategoryDefaults.SPORTS_AND_LEISURE.name) }
        }
    }

    @Test
    fun `should filter hidden categories when map raw category into category DTO`() {
        runUnitTest(robot) {
            Given {
                stubRawCategory(
                    CategoryDefaults.ALL_CATEGORIES.id,
                    CategoryDefaults.ALL_CATEGORIES.name,
                    listOf(
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.PETS.id,
                            CategoryDefaults.PETS.name,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.PROPERTIES.id,
                            CategoryDefaults.PROPERTIES.name,
                            hidden = true
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.COMMUNITY.id,
                            CategoryDefaults.COMMUNITY.name,
                        ),
                    )
                )
            }
            When { toCategoryDto() }
            Then { checkCategoryId(CategoryDefaults.ALL_CATEGORIES.id) }
            Then { checkCategoryText(CategoryDefaults.ALL_CATEGORIES.name) }
            Then { checkCategoryChildrenSize(2) }
            Then { checkCategoryChildIdAtPosition(0, CategoryDefaults.PETS.id) }
            Then { checkCategoryChildIdAtPosition(1, CategoryDefaults.COMMUNITY.id) }
            Then { checkCategoryChildTextAtPosition(0, CategoryDefaults.PETS.name) }
            Then { checkCategoryChildTextAtPosition(1, CategoryDefaults.COMMUNITY.name) }
        }
    }

    @Test
    fun `should filter disabled(NOT enabled) categories when map raw category into category DTO`() {
        runUnitTest(robot) {
            Given {
                stubRawCategory(
                    CategoryDefaults.ALL_CATEGORIES.id,
                    CategoryDefaults.ALL_CATEGORIES.name,
                    listOf(
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.PETS.id,
                            CategoryDefaults.PETS.name,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.PROPERTIES.id,
                            CategoryDefaults.PROPERTIES.name,
                            hidden = true
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.COMMUNITY.id,
                            CategoryDefaults.COMMUNITY.name,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.SPORTS_AND_LEISURE.id,
                            CategoryDefaults.SPORTS_AND_LEISURE.name,
                            enabled = false,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.FOR_SALE.id,
                            CategoryDefaults.FOR_SALE.name,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.SERVICES.id,
                            CategoryDefaults.SERVICES.name,
                            hidden = true
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.MOTORS.id,
                            CategoryDefaults.MOTORS.name,
                            enabled = false,
                        ),
                        RawCategoryFactory.createRawCategory(
                            CategoryDefaults.JOBS.id,
                            CategoryDefaults.JOBS.name,
                        ),
                    )
                )
            }
            When { toCategoryDto() }
            Then { checkCategoryId(CategoryDefaults.ALL_CATEGORIES.id) }
            Then { checkCategoryText(CategoryDefaults.ALL_CATEGORIES.name) }
            Then { checkCategoryChildrenSize(4) }
            Then { checkCategoryChildIdAtPosition(0, CategoryDefaults.PETS.id) }
            Then { checkCategoryChildIdAtPosition(1, CategoryDefaults.COMMUNITY.id) }
            Then { checkCategoryChildIdAtPosition(2, CategoryDefaults.FOR_SALE.id) }
            Then { checkCategoryChildIdAtPosition(3, CategoryDefaults.JOBS.id) }
            Then { checkCategoryChildTextAtPosition(0, CategoryDefaults.PETS.name) }
            Then { checkCategoryChildTextAtPosition(1, CategoryDefaults.COMMUNITY.name) }
            Then { checkCategoryChildTextAtPosition(2, CategoryDefaults.FOR_SALE.name) }
            Then { checkCategoryChildTextAtPosition(3, CategoryDefaults.JOBS.name) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualCategoryDtoResult: CategoryDto
        private lateinit var rawCategory: RawCategory

        fun stubRawCategory(
            categoryId: String,
            categoryName: String,
            categoryChildren: List<RawCategory>?,
        ) {
            rawCategory = RawCategoryFactory.createRawCategory(
                categoryId,
                categoryName,
                categoryChildren
            )
        }

        fun toCategoryDto() {
            actualCategoryDtoResult = rawCategory.toCategoryDto()
        }

        fun checkCategoryId(expected: String) {
            assertEquals(expected, actualCategoryDtoResult.id)
        }

        fun checkCategoryText(expected: String) {
            assertEquals(expected, actualCategoryDtoResult.text)
        }

        fun checkCategoryChildrenSize(expected: Int?) {
            assertEquals(expected, actualCategoryDtoResult.children?.size)
        }

        fun checkCategoryChildIdAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, actualCategoryDtoResult.children?.get(position)?.id)
        }

        fun checkCategoryChildTextAtPosition(
            position: Int,
            expected: String
        ) {
            assertEquals(expected, actualCategoryDtoResult.children?.get(position)?.text)
        }

    }
}