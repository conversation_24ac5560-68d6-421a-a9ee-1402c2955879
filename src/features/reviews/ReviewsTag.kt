package com.gumtree.mobile.features.reviews

/**
 * The BFF Review tags enums
 * @property displayText - The display text, which the mobile app user sees on their screen when selecting Review tags
 */
enum class ReviewsTag(val displayText: String) {
    // Positive tags
    FRIENDLY(displayText = "Friendly"),
    P<PERSON>ITE(displayText = "Polite"),
    HELPFUL(displayText = "Helpful"),
    SPEEDY_RESPONDER(displayText = "Speedy responder"),
    ITEM_AS_DESCRIBED(displayText = "Item as described"),
    QUICK_TRANSACTION(displayText = "Quick transaction"),
    SHOWED_UP_ON_TIME(displayText = "Showed up on time"),
    FAIR_NEGOTIATION(displayText = "Fair negotiation"),

    // Negative tags
    RUDE(displayText = "Rude"),
    UNHELPFUL(displayText = "Unhelpful"),
    UNRESPONSIVE(displayText = "Unresponsive"),
    ITEM_NOT_AS_DESCRIBED(displayText = "Item not as described"),
    CANCELLED_OFFER(displayText = "Cancelled offer"),
    DIDN_T_SHOW_UP(displayText = "Didn't show up"),
    TOO_MUCH_HAGGLING(displayText = "Too much haggling"),

    NONE_OF_THESE(displayText = "None of these"),
    ;

    fun getKeyValue(): String? {
        return when (this) {
            NONE_OF_THESE -> null
            else -> name
        }
    }
}
