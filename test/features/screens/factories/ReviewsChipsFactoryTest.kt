package features.screens.factories

import com.gumtree.mobile.features.screens.factories.ReviewsChipsFactory
import com.gumtree.mobile.features.reviews.ReviewsTag
import com.gumtree.mobile.features.screens.TextSegment
import com.gumtree.mobile.features.screens.Typography
import com.gumtree.mobile.features.screens.layoutsData.ChipDto
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class ReviewsChipsFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return review positive chips with 9 chips`() {
        runUnitTest(robot) {
            When { buildReviewPositiveChips() }
            Then { checkReviewChipsSize(9) }
        }
    }

    @Test
    fun `should return review positive chips with expected title`() {
        runUnitTest(robot) {
            When { buildReviewPositiveChips() }
            Then { checkReviewChipTitle(0, ReviewsTag.FRIENDLY.displayText) }
            Then { checkReviewChipTitle(1, ReviewsTag.POLITE.displayText) }
            Then { checkReviewChipTitle(2, ReviewsTag.HELPFUL.displayText) }
            Then { checkReviewChipTitle(3, ReviewsTag.SPEEDY_RESPONDER.displayText) }
            Then { checkReviewChipTitle(4, ReviewsTag.ITEM_AS_DESCRIBED.displayText) }
            Then { checkReviewChipTitle(5, ReviewsTag.QUICK_TRANSACTION.displayText) }
            Then { checkReviewChipTitle(6, ReviewsTag.SHOWED_UP_ON_TIME.displayText) }
            Then { checkReviewChipTitle(7, ReviewsTag.FAIR_NEGOTIATION.displayText) }
            Then { checkReviewChipTitle(8, ReviewsTag.NONE_OF_THESE.displayText) }
        }
    }

    @Test
    fun `should return review positive chips with expected isSelected`() {
        runUnitTest(robot) {
            When { buildReviewPositiveChips() }
            Then { checkReviewChipIsSelected(0, false) }
            Then { checkReviewChipIsSelected(1, false) }
            Then { checkReviewChipIsSelected(2, false) }
            Then { checkReviewChipIsSelected(3, false) }
            Then { checkReviewChipIsSelected(4, false) }
            Then { checkReviewChipIsSelected(5, false) }
            Then { checkReviewChipIsSelected(6, false) }
            Then { checkReviewChipIsSelected(7, false) }
            Then { checkReviewChipIsSelected(8, false) }
        }
    }

    @Test
    fun `should return review positive chips with expected value`() {
        runUnitTest(robot) {
            When { buildReviewPositiveChips() }
            Then { checkReviewChipValue(0, ReviewsTag.FRIENDLY.name) }
            Then { checkReviewChipValue(1, ReviewsTag.POLITE.name) }
            Then { checkReviewChipValue(2, ReviewsTag.HELPFUL.name) }
            Then { checkReviewChipValue(3, ReviewsTag.SPEEDY_RESPONDER.name) }
            Then { checkReviewChipValue(4, ReviewsTag.ITEM_AS_DESCRIBED.name) }
            Then { checkReviewChipValue(5, ReviewsTag.QUICK_TRANSACTION.name) }
            Then { checkReviewChipValue(6, ReviewsTag.SHOWED_UP_ON_TIME.name) }
            Then { checkReviewChipValue(7, ReviewsTag.FAIR_NEGOTIATION.name) }
            Then { checkReviewChipValue(8, null) }
        }
    }

    @Test
    fun `should return review negative chips with 8 chips`() {
        runUnitTest(robot) {
            When { buildReviewNegativeChips() }
            Then { checkReviewChipsSize(8) }
        }
    }

    @Test
    fun `should return review negative chips with expected title`() {
        runUnitTest(robot) {
            When { buildReviewNegativeChips() }
            Then { checkReviewChipTitle(0, ReviewsTag.RUDE.displayText) }
            Then { checkReviewChipTitle(1, ReviewsTag.UNHELPFUL.displayText) }
            Then { checkReviewChipTitle(2, ReviewsTag.UNRESPONSIVE.displayText) }
            Then { checkReviewChipTitle(3, ReviewsTag.ITEM_NOT_AS_DESCRIBED.displayText) }
            Then { checkReviewChipTitle(4, ReviewsTag.CANCELLED_OFFER.displayText) }
            Then { checkReviewChipTitle(5, ReviewsTag.DIDN_T_SHOW_UP.displayText) }
            Then { checkReviewChipTitle(6, ReviewsTag.TOO_MUCH_HAGGLING.displayText) }
            Then { checkReviewChipTitle(7, ReviewsTag.NONE_OF_THESE.displayText) }
        }
    }

    @Test
    fun `should return review negative chips with expected isSelected`() {
        runUnitTest(robot) {
            When { buildReviewNegativeChips() }
            Then { checkReviewChipIsSelected(0, false) }
            Then { checkReviewChipIsSelected(1, false) }
            Then { checkReviewChipIsSelected(2, false) }
            Then { checkReviewChipIsSelected(3, false) }
            Then { checkReviewChipIsSelected(4, false) }
            Then { checkReviewChipIsSelected(5, false) }
            Then { checkReviewChipIsSelected(6, false) }
            Then { checkReviewChipIsSelected(7, false) }
        }
    }

    @Test
    fun `should return review negative chips with expected value`() {
        runUnitTest(robot) {
            When { buildReviewNegativeChips() }
            Then { checkReviewChipValue(0, ReviewsTag.RUDE.name) }
            Then { checkReviewChipValue(1, ReviewsTag.UNHELPFUL.name) }
            Then { checkReviewChipValue(2, ReviewsTag.UNRESPONSIVE.name) }
            Then { checkReviewChipValue(3, ReviewsTag.ITEM_NOT_AS_DESCRIBED.name) }
            Then { checkReviewChipValue(4, ReviewsTag.CANCELLED_OFFER.name) }
            Then { checkReviewChipValue(5, ReviewsTag.DIDN_T_SHOW_UP.name) }
            Then { checkReviewChipValue(6, ReviewsTag.TOO_MUCH_HAGGLING.name) }
            Then { checkReviewChipValue(7, null) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "Showed up on time, 1",
        "Friendly, 2",
        "Polite, 1",
        "Fair negotiation, 5",
        "Helpful, 10",
        "Too much haggling, 14",
    )
    fun `should return review chip with expected value`(tag: String, tagCount: Int) {
        runUnitTest(robot) {
            When { buildReviewStandardStyledChip(tag, tagCount) }
            Then {
                checkReviewChip(
                    ChipDto.StandardStyled(
                        title = "$tag $tagCount",
                        textSegments = listOf(
                            TextSegment(
                                text = tag,
                                typography = Typography.BODY_SMALL_REGULAR,
                            ),
                            TextSegment(
                                text = tagCount.toString(),
                                typography = Typography.BODY_SMALL_SEMIBOLD,
                            ),
                        ),
                    )
                )
            }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualReviewChipsResult: List<ChipDto>
        private lateinit var actualReviewChipResult: ChipDto

        private var testSubject = ReviewsChipsFactory()

        fun buildReviewPositiveChips() {
            actualReviewChipsResult = testSubject.buildReviewPositiveChips()
        }

        fun buildReviewNegativeChips() {
            actualReviewChipsResult = testSubject.buildReviewNegativeChips()
        }

        fun buildReviewStandardStyledChip(
            reviewTag: String,
            reviewTagCount: Int,
        ) {
            actualReviewChipResult = testSubject.buildReviewStandardStyledChip(reviewTag, reviewTagCount)
        }

        fun checkReviewChipsSize(expected: Int) {
            assertEquals(expected, actualReviewChipsResult.size)
        }

        fun checkReviewChipTitle(
            position: Int,
            expected: String,
        ) {
            assertEquals(expected, (actualReviewChipsResult[position] as ChipDto.CapsuleSelect).title)
        }

        fun checkReviewChipIsSelected(
            position: Int,
            expected: Boolean,
        ) {
            assertEquals(expected, (actualReviewChipsResult[position] as ChipDto.CapsuleSelect).isSelected)
        }

        fun checkReviewChipValue(
            position: Int,
            expected: String?,
        ) {
            assertEquals(expected, (actualReviewChipsResult[position] as ChipDto.CapsuleSelect).value)
        }

        fun checkReviewChip(expected: ChipDto) {
            assertEquals(expected, actualReviewChipResult)
        }
    }
}