package com.gumtree.mobile.api.reviews.bodies

import com.google.gson.annotations.SerializedName
import com.gumtree.mobile.features.reviews.ReviewsTag

data class RawFeedback(
    @SerializedName("tags")
    val tags: RawTags,
) {
    data class RawTags(
        @SerializedName("positive")
        val positive: List<RawPositiveTag>?,

        @SerializedName("negative")
        val negative: List<RawNegativeTag>?,
    )
}

enum class RawPositiveTag {
    Friendly,
    Polite,
    Helpful,
    SpeedyResponder,
    ItemAsDescribed,
    QuickTransaction,
    ShowedUpOnTime,
    FairNegotiation,
    ;

    companion object {
        fun fromString(reviewsTagString: String): RawPositiveTag? {
            return when (reviewsTagString) {
                ReviewsTag.FRIENDLY.name -> Friendly
                ReviewsTag.POLITE.name -> Polite
                ReviewsTag.HELPFUL.name -> Helpful
                ReviewsTag.SPEEDY_RESPONDER.name -> SpeedyResponder
                ReviewsTag.ITEM_AS_DESCRIBED.name -> ItemAsDescribed
                ReviewsTag.QUICK_TRANSACTION.name -> QuickTransaction
                ReviewsTag.SHOWED_UP_ON_TIME.name -> ShowedUpOnTime
                ReviewsTag.FAIR_NEGOTIATION.name -> FairNegotiation
                else -> null
            }
        }
    }
}

enum class RawNegativeTag {
    Rude,
    Unhelpful,
    Unresponsive,
    ItemNotAsDescribed,
    CancelledOffer,
    DidntShowUp,
    TooMuchHaggling,
    ;

    companion object {
        fun fromString(reviewsTagString: String): RawNegativeTag? {
            return when (reviewsTagString) {
                ReviewsTag.RUDE.name -> Rude
                ReviewsTag.UNHELPFUL.name -> Unhelpful
                ReviewsTag.UNRESPONSIVE.name -> Unresponsive
                ReviewsTag.ITEM_NOT_AS_DESCRIBED.name -> ItemNotAsDescribed
                ReviewsTag.CANCELLED_OFFER.name -> CancelledOffer
                ReviewsTag.DIDN_T_SHOW_UP.name -> DidntShowUp
                ReviewsTag.TOO_MUCH_HAGGLING.name -> TooMuchHaggling
                else -> null
            }
        }
    }
}
