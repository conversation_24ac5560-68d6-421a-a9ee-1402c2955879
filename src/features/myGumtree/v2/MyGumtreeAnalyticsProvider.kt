package com.gumtree.mobile.features.myGumtree.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.common.analytics.AD_PREFIX
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.CONTENT_GROUP_KEY
import com.gumtree.mobile.common.analytics.CONTENT_TYPE_KEY
import com.gumtree.mobile.common.analytics.CommonAnalyticsProvider
import com.gumtree.mobile.common.analytics.FORM_NAME_KEY
import com.gumtree.mobile.common.analytics.FORM_VALIDATION_KEY
import com.gumtree.mobile.common.analytics.SUCCESS
import com.gumtree.mobile.requests.ClientSemantics
import org.jetbrains.annotations.VisibleForTesting

const val AD_POSTED_EVENT_NAME = "ad_posted_success"
const val AD_DELETED_EVENT_NAME = "ad_deleted"

@VisibleForTesting
const val FORM_STEP_KEY = "form_step"

@VisibleForTesting
const val FORM_STEP_VALUE = "submit_ad"

@VisibleForTesting
const val CONTENT_TYPE = "manage ads page app "

@VisibleForTesting
const val FORM_NAME = "syi-form"

@VisibleForTesting
const val AD_TYPE_KEY = "ad_type"

@VisibleForTesting
const val AD_STATUS_KEY = "ad_status"

@VisibleForTesting
const val AD_TYPE_FREE = "free"

@VisibleForTesting
const val AD_TYPE_PAID = "paid"

class MyGumtreeAnalyticsProvider(
    commonAnalyticsProvider: CommonAnalyticsProvider,
) : CommonAnalyticsProvider by commonAnalyticsProvider {

    fun getAdPostedAnalyticsEvent(
        ad: RawFlatAd,
        clientSemantics: ClientSemantics,
        postType: PostType,
        isPaidAd: Boolean,
    ): AnalyticsEventData {
        val adParams = getCommonListingParams(AD_PREFIX, ad)
        val adType = if (isPaidAd) AD_TYPE_PAID else AD_TYPE_FREE
        val platform = clientSemantics.platform.name.lowercase()
        val repostParams = mapOf(
            FORM_STEP_KEY to FORM_STEP_VALUE,
            CONTENT_GROUP_KEY to postType.contentGroup,
            CONTENT_TYPE_KEY to "$CONTENT_TYPE$platform",
            FORM_NAME_KEY to FORM_NAME,
            FORM_VALIDATION_KEY to SUCCESS,
            AD_TYPE_KEY to adType,
            AD_STATUS_KEY to PostType.analyticsEventValue(postType),
        )

        return AnalyticsEventData(AD_POSTED_EVENT_NAME, adParams + repostParams)
    }

    fun getDeleteAdAnalyticsEvent(
        ad: RawCapiAd,
    ): AnalyticsEventData {
        return AnalyticsEventData(
            eventName = AD_DELETED_EVENT_NAME,
            parameters = getCommonListingParams(AD_PREFIX, ad),
        )
    }
}
