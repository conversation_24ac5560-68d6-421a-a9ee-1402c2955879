package utils

import com.gumtree.mobile.utils.runOrNull
import com.gumtree.mobile.utils.runSuspendOrNull
import com.gumtree.mobile.utils.withContextTimeout
import kotlin.coroutines.CoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.annotations.ParallelTest
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

@ParallelTest
class BlockHelperOperationsTest {

    private val robot = Robot()

    @Test
    fun `should execute a non suspended block if condition == true`() = runTest {
        runUnitTest(robot) {
            When { executeRunOrNull(true) { 5 + 5 } }
            Then { checkBlockOperationResult(10) }
        }
    }

    @Test
    fun `should NOT execute a non suspended block if condition == false`() = runTest {
        runUnitTest(robot) {
            When { executeRunOrNull(false) { 5 + 5 } }
            Then { checkBlockOperationResult(null) }
        }
    }

    @Test
    fun `should execute a suspended block if condition == true`() = runTest {
        runUnitTest(robot) {
            When { executeSuspendRunOrNull(true) { 5 + 5 } }
            Then { checkBlockOperationResult(10) }
        }
    }

    @Test
    fun `should NOT execute a suspended block if condition == false`() = runTest {
        runUnitTest(robot) {
            When { executeSuspendRunOrNull(false) { 5 + 5 } }
            Then { checkBlockOperationResult(null) }
        }
    }

    @Test
    fun `should execute a suspended block without timing out`() = runTest {
        runUnitTest(robot) {
            When {
                executeWithContextTimeout(
                    coroutineContext = UnconfinedTestDispatcher(),
                    timeout = 1000L,
                ) {
                    5 + 5
                }
            }
            Then { checkBlockOperationResult(10) }
        }
    }

    @Test
    fun `should execute a suspended block with timing out`() = runTest {
        runUnitTest(robot) {
            When {
                executeWithContextTimeout(
                    coroutineContext = UnconfinedTestDispatcher(),
                    timeout = 100L,
                ) {
                    delay(200)
                    5 + 5
                }
            }
            Then { checkBlockOperationResult(null) }
        }
    }

    private class Robot: BaseRobot {

        private var actualResult: Int? = null

        fun executeRunOrNull(
            condition: Boolean,
            blockResult: () -> Int,
        ) {
            actualResult = runOrNull(condition) { blockResult() }
        }

        suspend fun executeSuspendRunOrNull(
            condition: Boolean,
            blockResult: () -> Int,
        ) {
            actualResult = runSuspendOrNull(condition) { blockResult() }
        }

        suspend fun executeWithContextTimeout(
            coroutineContext: CoroutineContext,
            timeout: Long,
            blockResult: suspend () -> Int,
        ) {
            actualResult = withContextTimeout(
                context = coroutineContext,
                timeout = timeout,
            ) {
                blockResult()
            }
        }

        fun checkBlockOperationResult(expected: Int?) {
            assertEquals(expected, actualResult)
        }
    }
}