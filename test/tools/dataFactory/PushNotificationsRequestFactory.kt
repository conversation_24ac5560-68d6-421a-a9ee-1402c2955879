package tools.dataFactory

import com.gumtree.mobile.features.notifications.PushNotificationsRequest
import tools.DataFactory

object PushNotificationsRequestFactory {

    fun create(
        userId: String = DataFactory.SOME_USER_ID,
        pushToken: String = DataFactory.SOME_PUSH_TOKEN,
        topics: List<PushNotificationsRequest.Topic> = emptyList(),
    ): PushNotificationsRequest {
        return PushNotificationsRequest(
            userId = userId,
            pushToken = pushToken,
            topics = topics,
        )
    }
}
