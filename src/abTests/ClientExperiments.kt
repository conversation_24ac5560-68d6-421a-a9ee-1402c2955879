package com.gumtree.mobile.abTests

import com.gumtree.mobile.abTests.Variant.A
import com.gumtree.mobile.abTests.Variant.B
import com.gumtree.mobile.abTests.Variant.C
import com.gumtree.mobile.abTests.Variant.D
import com.gumtree.mobile.abTests.Variant.UNKNOWN
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.utils.extensions.getClientExperiments
import io.ktor.http.Headers

/**
 * The client experiments provided by the client request data (HTTP header)
 * @param experiments - a map of all client experiments with their specific variant
 */
class ClientExperiments(val experiments: Map<Experiment, Variant>?) {
    /**
     * @return - true if the user got the default/control variant of the experiment, otherwise false
     */
    fun isA(experiment: Experiment): Boolean = getVariant(experiment) == A

    /**
     * @return - true if the user got the first non default/control variant of the experiment, otherwise false
     */
    fun isB(experiment: Experiment): Boolean = getVariant(experiment) == B

    /**
     * @return - true if the user got the second non default/control variant of the experiment, otherwise false
     */
    fun isC(experiment: Experiment): Boolean = getVariant(experiment) == C

    /**
     * @return - true if the user got the third non default/control variant of the experiment, otherwise false
     */
    fun isD(experiment: Experiment): Boolean = getVariant(experiment) == D

    fun toGamExperimentsString(): String? {
        return experiments?.entries?.joinToString(separator = ",") {
            "${it.key.key}-${it.value}"
        }
    }

    fun toPartnershipExperimentsString(): String? {
        return experiments?.entries?.joinToString(separator = ",") {
            "${it.key.key}_${it.value}"
        }
    }

    private fun getVariant(experiment: Experiment): Variant {
        return experiments?.get(experiment) ?: UNKNOWN
    }

    companion object {
        fun shouldUseCoreChatRepo(headers: Headers): Boolean {
            val clientExperiments = headers.getClientExperiments()
            return clientExperiments.isB(Experiment.GTNA_4299) || clientExperiments.isB(Experiment.GTNA_4300)
        }

        fun getHomeFeedCategoriesExperimentValue(headers: Headers): Variant? {
            val clientExperiments = headers.getClientExperiments()

            val iOS = clientExperiments.getVariant(Experiment.GTB_262)
            val android = clientExperiments.getVariant(Experiment.GTB_263)

            return when {
                iOS != UNKNOWN -> iOS
                android != UNKNOWN -> android
                else -> null
            }
        }
    }
}

/**
 * Enum representing app/client experiment name/key
 * The BFF will parse/use only recognised experiments.
 * If the client passes experiment value which is not an enum value in the Experiment class, then that experiment shall not be processed in the BFF code
 * @param key - the key of the experiment, should match what's in the experiments dashboard (GrowthBook)
 */
enum class Experiment(val key: String) {
    GTNA_1(key = "GTNA-1"), // test experiment used in tests
    GTNA_2(key = "GTNA-2"), // test experiment used in tests
    GTNA_4005(key = "GTNA-4005"), // Filters location + distance AB test on IOS
    GTNA_4006(key = "GTNA-4006"), // Filters location + distance AB test on Android
    GTNA_4301(key = "GTNA-4301"), // POST new Review CAPI -> FullAdsSearch API AB test on Android
    GTNA_4302(key = "GTNA-4302"), // POST new Review CAPI -> FullAdsSearch API AB test on IOS
    GTNA_4299(key = "GTNA-4299"), // Conversations list switch from capi to core chat on Android
    GTNA_4300(key = "GTNA-4300"), // Conversations list switch from capi to core chat on IOS
    GTB_262(key = "GTB-262"), // home feed categories experiment on iOS
    GTB_263(key = "GTB-263"), // home feed categories experiment on Android
    GTB_499(key = "GTB-499"), // free toggle in filters on iOS
    GTB_500(key = "GTB-500"), // free toggle in filters on Android
    SRP_RANK_RELEVANT_FLAG(key = "srp_rank_relevant_flag"), // srp relevant experiment on both of Android and IOS
    SRP_RECALL_ALL_CATEGORY_FLAG(
        key = "srp_recall_all_cate_flag",
    ), // srp recall all cate experiment on both of Android and IOS
    SRP_FILTER_MOBILE_COLOUR_STORAGE_CONDITION(
        key = "srp_filter_mobile_colour_storage_condition",
    ), // SRP filters : mobile color、storage capacity、condition
    SRP_FILTER_MOBILE_APPLE_SAMSUNG_MODEL(
        key = "srp_filter_mobile_apple_samsung_model",
    ), // SRP filters : GJH-1494-p1 && mobile model
    SRP_FILTER_MOBILE_MODEL_GOOGLE_XIAOMI_HUAWEI(
        key = "srp_filter_mobile_model_google_xiaomi_huawei",
    ), // SRP filters : mobile model
    SRP_FILTER_DIY_TOOLS_MATERIALS_CONDITION(
        key = "srp_filter_diy_tools_materials_condition",
    ),
    SERVICE_NEW_UI(key = "service-new-ui"), // Service VIP page UI optimization experiment
    UNKNOWN(key = EMPTY_STRING),
    ;

    companion object {
        fun fromString(string: String?): Experiment {
            return Experiment
                .entries
                .firstOrNull { it.key.equals(string, ignoreCase = true) }
                ?: UNKNOWN
        }
    }
}

/**
 * Class representing client experiment variant
 */
enum class Variant {
    A,
    B,
    C,
    D,
    UNKNOWN,
    ;

    companion object {
        fun fromString(string: String?): Variant {
            return Variant
                .entries
                .firstOrNull { it.name.equals(string, ignoreCase = true) }
                ?: UNKNOWN
        }
    }
}
