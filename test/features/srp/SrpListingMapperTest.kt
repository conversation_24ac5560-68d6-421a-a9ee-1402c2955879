package features.srp

import api.capi.models.RawCapiAd
import api.capi.models.RawCapiAdList
import api.capi.models.RawCapiPaging
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.layoutsData.ContactButtonCardDto
import com.gumtree.mobile.features.screens.layoutsData.ContactButtonRowDto
import com.gumtree.mobile.features.screens.layoutsData.LargeListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.ListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.PhoneActionDto
import com.gumtree.mobile.features.screens.layoutsData.SingleListingCardDto
import com.gumtree.mobile.features.screens.layoutsData.SrpButtonType
import com.gumtree.mobile.features.screens.layoutsData.SrpIconType
import com.gumtree.mobile.features.screens.layoutsData.SrpSize
import com.gumtree.mobile.features.srp.SrpAnalyticsProvider
import com.gumtree.mobile.features.srp.SrpListingGridSizes
import com.gumtree.mobile.features.srp.SrpListingMapper
import com.gumtree.mobile.features.srp.SrpTimeAgoFormatter
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest
import kotlin.reflect.KClass
import tools.DataFactory
import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.abTests.Variant
import tools.ClientExperimentsFactory

class SrpListingMapperTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should wrap SRP ads with the supported orientations for default grid sizes`() {
        runUnitTest(robot) {
            Given { stubRawAds(50) }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkPortraitOrientationExist() }
            Then { checkLandscapeOrientationIsNull() }
        }
    }

    @Test
    fun `should wrap SRP ads with the supported orientations for cars grid sizes`() {
        runUnitTest(robot) {
            Given { stubRawAds(50) }
            Given { stubGridSizes(SrpListingGridSizes.Cars()) }
            Given { stubCategoryId(CategoryDefaults.CARS.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkPortraitOrientationExist() }
            Then { checkLandscapeOrientationIsNull() }
        }
    }

    @Test
    fun `should map raw SRP ads in expected number of rows in portrait orientation for default grid sizes`() {
        runUnitTest(robot) {
            Given { stubRawAds(24) }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkRowsCountInPortraitOrientation(12) }
        }
    }

    @Test
    fun `should map raw SRP ads to expected type and in default numbers when cars category`() {
        runUnitTest(robot) {
            Given { stubRawAds(24) }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.CARS.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkRowsCountInPortraitOrientation(12) }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, LargeListingCardDto::class) }
        }
    }

    @Test
    fun `should map 24 raw SRP ads into 24 SRP cards in all supported orientations`() {
        runUnitTest(robot) {
            Given { stubRawAds(24) }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkPortraitOrientationExist() }
            Then { checkLandscapeOrientationIsNull() }
            Then { checkAdsCountInPortraitOrientation(24) }
        }
    }

    @Test
    fun `should map raw SRP ad into listing card for FOR_SALE category`() {
        runUnitTest(robot) {
            Given { stubRawAds(1) }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, ListingCardDto::class) }
        }
    }

    @Test
    fun `should map raw SRP ad into cars listing card for CARS category`() {
        runUnitTest(robot) {
            Given { stubRawAds(1) }
            Given { stubGridSizes(SrpListingGridSizes.Cars()) }
            Given { stubCategoryId(CategoryDefaults.CARS.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, LargeListingCardDto::class) }//todo update the card dto type when implementing the cars SRP card
        }
    }

    @Test
    fun `should map raw SRP ad into single listing card for SERVICES category`() {
        runUnitTest(robot) {
            Given { 
                every { CategoriesTreeCache.isServices(CategoryDefaults.SERVICES.id) } returns true
                stubRawAdsWithServiceCategory(1) 
            }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.SERVICES.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            Given { stubExperimentsWithVariantB() }
            When { map() }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, SingleListingCardDto::class) }
        }
    }

    @Test
    fun `should map raw SRP ad into single listing card for service subcategory`() {
        runUnitTest(robot) {
            Given { 
                every { CategoriesTreeCache.isServices(CategoryDefaults.TRADESMEN_AND_CONSTRUCTION.id) } returns true
                stubRawAdsWithServiceSubcategory(1) 
            }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.TRADESMEN_AND_CONSTRUCTION.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            Given { stubExperimentsWithVariantB() }
            When { map() }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, SingleListingCardDto::class) }
        }
    }

    @Test
    fun `should map multiple service ads into single listing cards for SERVICES category`() {
        runUnitTest(robot) {
            Given { 
                every { CategoriesTreeCache.isServices(CategoryDefaults.SERVICES.id) } returns true
                stubRawAdsWithServiceCategory(24) 
            }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.SERVICES.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            Given { stubExperimentsWithVariantB() }
            When { map() }
            Then { checkRowsCountInPortraitOrientation(12) }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, SingleListingCardDto::class) }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 1, SingleListingCardDto::class) }
            Then { checkCardTypeForPortraitOrientationAtPosition(1, 0, SingleListingCardDto::class) }
            Then { checkCardTypeForPortraitOrientationAtPosition(1, 1, SingleListingCardDto::class) }
        }
    }

    @Test
    fun `should map service ads with service buttons for SERVICES category`() {
        runUnitTest(robot) {
            Given { 
                every { CategoriesTreeCache.isServices(CategoryDefaults.SERVICES.id) } returns true
                stubRawAdsWithServiceCategory(1) 
            }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.SERVICES.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            Given { stubServiceButtonsMap() }
            Given { stubExperimentsWithVariantB() }
            When { map() }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, SingleListingCardDto::class) }
        }
    }

    @Test
    fun `should map mixed ads with service and non-service categories correctly`() {
        runUnitTest(robot) {
            Given { 
                every { CategoriesTreeCache.isServices(CategoryDefaults.FOR_SALE.id) } returns false
                stubMixedRawAds() 
            }
            Given { stubGridSizes(SrpListingGridSizes.Default()) }
            Given { stubCategoryId(CategoryDefaults.FOR_SALE.id) }
            Given {
                stubQueryParams(
                    ApiQueryParams.REQUEST_ID to DataFactory.REQUEST_ID,
                )
            }
            When { map() }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 0, ListingCardDto::class) }
            Then { checkCardTypeForPortraitOrientationAtPosition(0, 1, ListingCardDto::class) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualSrpListingRowsResult: Pair<PortraitData, LandscapeData?>
        private lateinit var rawAds: RawCapiAdList
        private lateinit var categoryId: String
        private lateinit var gridSizes: GridSizes
        private lateinit var queryParams: QueryParams
        private var serviceButtonsMap: Map<String, List<ContactButtonRowDto>?>? = null
        private var experiments: ClientExperiments? = null

        private val srpAnalyticsProvider: SrpAnalyticsProvider = mockk(relaxed = true)
        private val srpTimeAgoFormatter: SrpTimeAgoFormatter = mockk(relaxed = true)

        private lateinit var testSubject: SrpListingMapper

        override fun setup() {
            mockkObject(CategoriesTreeCache)
            every { CategoriesTreeCache.isServices(any()) } returns false
            testSubject = SrpListingMapper(srpAnalyticsProvider, srpTimeAgoFormatter)
        }

        override fun tearsDown() {
            unmockkObject(CategoriesTreeCache)
        }

        fun stubRawAds(number: Int) {
            rawAds = RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
        }

        fun stubRawAdsWithServiceCategory(number: Int) {
            val rawAdsList = mutableListOf<RawCapiAd>()
            repeat(number) {
                val serviceAd = RawCapiAdsFactory.createRawCapiAd(
                    categoryId = CategoryDefaults.SERVICES.id,
                    status = AdStatus.ACTIVE
                )
                rawAdsList.add(serviceAd)
            }
            rawAds = RawCapiAdList().apply {
                rawAds = rawAdsList
                rawPaging = RawCapiPaging().apply {
                    numFound = number
                    links = emptyList()
                }
            }
        }

        fun stubRawAdsWithServiceSubcategory(number: Int) {
            val rawAdsList = mutableListOf<RawCapiAd>()
            repeat(number) {
                val serviceAd = RawCapiAdsFactory.createRawCapiAd(
                    categoryId = CategoryDefaults.TRADESMEN_AND_CONSTRUCTION.id,
                    status = AdStatus.ACTIVE
                )
                rawAdsList.add(serviceAd)
            }
            rawAds = RawCapiAdList().apply {
                rawAds = rawAdsList
                rawPaging = RawCapiPaging().apply {
                    numFound = number
                    links = emptyList()
                }
            }
        }

        fun stubMixedRawAds() {
            val serviceAd = RawCapiAdsFactory.createRawCapiAd(
                categoryId = CategoryDefaults.SERVICES.id,
                status = AdStatus.ACTIVE
            )
            val regularAd = RawCapiAdsFactory.createRawCapiAd(
                categoryId = CategoryDefaults.FOR_SALE.id,
                status = AdStatus.ACTIVE
            )
            rawAds = RawCapiAdList().apply {
                rawAds = listOf(serviceAd, regularAd)
                rawPaging = RawCapiPaging().apply {
                    numFound = 2
                    links = emptyList()
                }
            }
        }

        fun stubGridSizes(grid: GridSizes) {
            gridSizes = grid
        }

        fun stubCategoryId(categoryId: String) {
            this.categoryId = categoryId
        }

        fun stubQueryParams(vararg params: Pair<String, String>) {
            queryParams = params.toMap()
        }

        fun stubServiceButtonsMap() {
            // Create a mock service buttons map for testing
            serviceButtonsMap = mapOf(
                "test-ad-id" to listOf(
                    ContactButtonRowDto(
                        data = listOf(
                            ContactButtonCardDto(
                                text = "Contact",
                                size = SrpSize.MEDIUM,
                                buttonType = SrpButtonType.SECONDARY,
                                iconType = SrpIconType.PHONE,
                                action = PhoneActionDto(
                                    number = null,
                                    analyticsEventData = null,
                                    adjustTrackingData = null,
                                ),
                            )
                        )
                    )
                )
            )
        }

        fun stubExperimentsWithVariantB() {
            experiments = ClientExperimentsFactory.createExperiments(
                Experiment.SERVICE_NEW_UI to Variant.B
            )
        }

        fun map() {
            actualSrpListingRowsResult = testSubject.map(
                rawAdsData = rawAds,
                listingGridSizes = gridSizes,
                categoryId = categoryId,
                isVehiclesSingleColumnLayout = true,
                queryParams = queryParams,
                serviceButtonsMap = serviceButtonsMap,
                searchLocationData = null,
                experiments = experiments,
            )
        }

        fun checkPortraitOrientationExist() {
            assertNotNull(actualSrpListingRowsResult.first)
        }

        fun checkLandscapeOrientationIsNull() {
            assertNull(actualSrpListingRowsResult.second)
        }

        fun checkRowsCountInPortraitOrientation(
            expectedRowsCount: Int
        ) {
            assertEquals(
                expectedRowsCount,
                actualSrpListingRowsResult.first.size
            )
        }

        fun checkAdsCountInPortraitOrientation(expectedAdsCount: Int) {
            assertEquals(
                expectedAdsCount,
                actualSrpListingRowsResult.first
                    .filter { it.data.isNotEmpty() }
                    .map { it.data }
                    .flatten()
                    .size
            )
        }

        fun checkCardTypeForPortraitOrientationAtPosition(
            rowPosition: Int,
            adRowPosition: Int,
            expectedType: KClass<*>
        ) {
            assertEquals(
                expectedType,
                actualSrpListingRowsResult.first[rowPosition]
                    .data[adRowPosition]
                    .javaClass
                    .kotlin
            )
        }
    }
}
