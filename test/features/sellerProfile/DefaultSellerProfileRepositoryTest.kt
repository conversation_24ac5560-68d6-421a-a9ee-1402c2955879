package features.sellerProfile

import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.features.screens.LimitedPageCalculator
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.sellerProfile.DefaultSellerProfileRepository
import com.gumtree.mobile.features.sellerProfile.SellerProfileListingMapper
import com.gumtree.mobile.features.sellerProfile.SellerProfilePostingSinceFormatter
import com.gumtree.mobile.features.sellerProfile.SellerProfileScreenUiConfiguration
import com.gumtree.mobile.features.sellerProfile.SellerProfileService
import com.gumtree.mobile.features.sellerProfile.SellerProfileUiProvider
import com.gumtree.mobile.features.srp.SORT_TYPE_DATE_DESCENDING
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_PAGE_SIZE
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.LocationDefaults
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import tools.rawDataFactory.RawPapiUserProfileFactory
import utils.TestDispatcherProvider

class DefaultSellerProfileRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return expected seller other ads search options`() = runTest {
        runUnitTest(robot) {
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            When { createSellerOtherAdsSearchOptions() }
            Then {
                checkSellerOtherAdsSearchOptionsResult(
                    mapOf(
                        CapiApiParams.USER_IDS to DataFactory.SOME_USER_ID,
                        CapiApiParams.LOCATION_ID to LocationDefaults.ALL_UK.id,
                        CapiApiParams.CATEGORY_ID to CategoryDefaults.ALL_CATEGORIES.id,
                        CapiApiParams.AD_STATUS to AdStatus.ACTIVE.toString(),
                        CapiApiParams.SORT_TYPE to SORT_TYPE_DATE_DESCENDING,
                        CapiApiParams.INCLUDE_TOP_ADS to false.toString(),
                        CapiApiParams.SEARCH_OPTIONS_EXACT_MATCH to true.toString(),
                        CapiApiParams.PICTURE_REQUIRED to false.toString(),
                    )
                )
            }
        }
    }

    @Test
    fun `should NOT return seller profile screen with nextPage when page 0 has less listings`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(5) }
            When { readScreen() }
            Then { checkNextPage(null) }
        }
    }

    @Test
    fun `should return screen title on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkScreenResponseTitle(DataFactory.SOME_USER_FIRST_NAME) }
        }
    }

    @Test
    fun `should NOT return screen title on page greater than 0`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("1") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkScreenResponseTitle(null) }

            Given { stubPage("2") }
            When { readScreen() }
            Then { checkScreenResponseTitle(null) }

            Given { stubPage("3") }
            When { readScreen() }
            Then { checkScreenResponseTitle(null) }

            Given { stubPage("4") }
            When { readScreen() }
            Then { checkScreenResponseTitle(null) }
        }
    }

    @Test
    fun `should return seller profile screen with nextPage up until page 3`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkNextPage("${ApiQueryParams.PAGE}=1&${ApiQueryParams.SIZE}=20&${ApiQueryParams.USER_ID}=${DataFactory.SOME_USER_ID}&${ApiQueryParams.PUBLIC_USER_ID}=${DataFactory.SOME_USER_PUBLIC_ID}") }

            Given { stubPage("1") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkNextPage("${ApiQueryParams.PAGE}=2&${ApiQueryParams.SIZE}=20&${ApiQueryParams.USER_ID}=${DataFactory.SOME_USER_ID}&${ApiQueryParams.PUBLIC_USER_ID}=${DataFactory.SOME_USER_PUBLIC_ID}") }

            Given { stubPage("2") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkNextPage("${ApiQueryParams.PAGE}=3&${ApiQueryParams.SIZE}=20&${ApiQueryParams.USER_ID}=${DataFactory.SOME_USER_ID}&${ApiQueryParams.PUBLIC_USER_ID}=${DataFactory.SOME_USER_PUBLIC_ID}") }

            Given { stubPage("3") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(14) }
            When { readScreen() }
            Then { checkNextPage(null) }
        }
    }

    @Test
    fun `should return seller profile screen with seller profile, other ads title and full listings size on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SELLER_PROFILE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.TITLE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(10, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(11, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(12) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }
        }
    }

    @Test
    fun `should return seller profile screen with seller profile, other ads title and some listings on page 0`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(12) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SELLER_PROFILE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.TITLE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(8) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }
        }
    }

    @Test
    fun `should return seller profile screen only with seller other listings on page 1, 2, 3 etc`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("1") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(10) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }

            Given { stubPage("2") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(10) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }

            Given { stubPage("3") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(20) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(2, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(3, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(4, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(5, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(6, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(7, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(8, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(9, RowLayoutType.LISTING_ROW) }
            Then { checkPortraitScreenResponseListingsDataSize(10) }
            Then { checkLandscapeScreenResponseListingsDataSize(null) }
        }
    }

    @Test
    fun `should return seller profile screen with no listings row when no listings are returned`() = runTest {
        runUnitTest(robot) {
            Given { stubPage("0") }
            Given { stubUserId(DataFactory.SOME_USER_ID) }
            Given { stubPublicUserId(DataFactory.SOME_USER_PUBLIC_ID) }
            Given { stubSellerProfile() }
            Given { stubSellerOtherListings(0) }
            When { readScreen() }
            Then { checkPortraitScreenResponseRowAtPosition(0, RowLayoutType.SELLER_PROFILE_ROW) }
            Then { checkPortraitScreenResponseRowAtPosition(1, RowLayoutType.SELLER_PROFILE_NO_RESULTS_ROW) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualScreenResponseResult: ScreenResponse
        private lateinit var actualSellerOtherAdsSearchOptionsResult: QueryParams

        private val sellerProfileService: SellerProfileService = mockk(relaxed = true)
        private val papiHeadersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val capiHeadersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)

        private val pageCalculator = LimitedPageCalculator()
        private val sellerProfileUiProvider = SellerProfileUiProvider(
            TitleFactory(),
            SellerProfileListingMapper(),
            SellerProfilePostingSinceFormatter(
                createUKDateTimeFormatter(SellerProfileScreenUiConfiguration.SELLER_PROFILE_POSTING_SINCE_DATE_FORMAT),
                SellerProfileScreenUiConfiguration,
            )
        )

        private lateinit var userId: String
        private lateinit var publicUserId: String
        private lateinit var page: String

        private lateinit var testSubject: DefaultSellerProfileRepository

        override fun setup() {
            testSubject = DefaultSellerProfileRepository(
                sellerProfileService,
                sellerProfileUiProvider,
                pageCalculator,
                papiHeadersProvider,
                capiHeadersProvider,
                TestDispatcherProvider(),
            )
        }

        fun stubSellerOtherListings(number: Int) {
            coEvery { sellerProfileService.searchAds(any(), any(), any(), any()) } returns RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
        }

        fun stubSellerProfile() {
            coEvery { sellerProfileService.getBaseUserProfile(any(), any()) } returns RawPapiUserProfileFactory.createRawUserProfileWithRating()
        }

        fun stubUserId(userId: String) {
            this.userId = userId
        }

        fun stubPublicUserId(publicUserId: String) {
            this.publicUserId = publicUserId
        }

        fun stubPage(page: String) {
            this.page = page
        }

        suspend fun readScreen() {
            actualScreenResponseResult = testSubject.readScreen(
                callHeaders,
                userId,
                publicUserId,
                page,
                DEFAULT_PAGE_SIZE,
            )
        }

        fun createSellerOtherAdsSearchOptions() {
            actualSellerOtherAdsSearchOptionsResult = testSubject.createSellerOtherAdsSearchOptions(userId)
        }

        fun checkNextPage(expected: String?) {
            assertEquals(expected, actualScreenResponseResult.nextPage)
        }

        fun checkScreenResponseTitle(expected: String?) {
            assertEquals(expected, actualScreenResponseResult.title)
        }

        fun checkPortraitScreenResponseListingsDataSize(expected: Int) {
            assertEquals(expected, actualScreenResponseResult.portraitData.size)
        }

        fun checkLandscapeScreenResponseListingsDataSize(expected: Int?) {
            assertEquals(expected, actualScreenResponseResult.landscapeData?.size)
        }

        fun checkPortraitScreenResponseRowAtPosition(
            position: Int,
            expectedType: RowLayoutType
        ) {
            assertEquals(expectedType, actualScreenResponseResult.portraitData[position].type)
        }

        fun checkSellerOtherAdsSearchOptionsResult(expected: QueryParams) {
            assertEquals(expected, actualSellerOtherAdsSearchOptionsResult)
        }
    }
}