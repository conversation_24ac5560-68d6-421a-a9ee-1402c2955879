package features.conversations

import api.capi.models.RawConversationList
import com.gumtree.mobile.api.capi.apis.CapiConversationApi
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.conversations.api.ConversationsApi
import com.gumtree.mobile.api.coreChat.api.CoreChatAuthApi
import com.gumtree.mobile.features.conversations.ConversationsService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiConversationsFactory

class ConversationsServiceTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use service to get users conversations`() = runTest {
        runUnitTest(robot) {
            Given { stubNumberOfRandomRawConversations(24) }
            When { getUsersFavorites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_OFFSET) }
            Then { checkApiGetUserFavorites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_OFFSET) }
        }
    }

    @Test
    fun `should use service to delete conversation`() = runTest {
        runUnitTest(robot) {
            When { deleteAdFromUsersConversation(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID) }
            Then { checkApiDeleteConversation(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_CONVERSATION_ID) }
        }
    }

    private class Robot: BaseRobot {

        private val capiConversationApi: CapiConversationApi = mockk(relaxed = true)
        private val conversationsApi: ConversationsApi = mockk(relaxed = true)
        private val coreChatAuthApi: CoreChatAuthApi = mockk(relaxed = true)
        private val authHeaders: ApiHeaders = mockk(relaxed = true)

        private lateinit var actualRawConversationsList: RawConversationList

        private lateinit var tested: ConversationsService

        override fun setup() {
            tested = ConversationsService(capiConversationApi, conversationsApi, coreChatAuthApi)
        }

        fun stubNumberOfRandomRawConversations(number: Int) {
            coEvery { capiConversationApi.getAllConversations(any(), any(), any()) } returns
                RawCapiConversationsFactory.createRawConversationsList(number)
        }

        suspend fun getUsersFavorites(
            userName: String,
            offset: String
        ) {
            actualRawConversationsList = tested.getAllConversations(authHeaders, userName, offset)
        }

        suspend fun deleteAdFromUsersConversation(
            username: String,
            conversationId: String
        ) {
            tested.deleteConversation(authHeaders, username, conversationId)
        }

        fun checkApiGetUserFavorites(
            expectedUserName: String,
            expectedOffset: String
        ) {
            coVerify { capiConversationApi.getAllConversations(authHeaders, expectedUserName, expectedOffset) }
        }

        fun checkApiDeleteConversation(
            expectedUsername: String,
            expectedConversationId: String
        ) {
            coVerify { capiConversationApi.deleteConversation(authHeaders, expectedUsername, expectedConversationId) }
        }
    }
}