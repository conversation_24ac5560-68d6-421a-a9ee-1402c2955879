package com.gumtree.mobile.features.srp

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.abTests.ClientExperiments
import com.gumtree.mobile.abTests.Experiment
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.locations.models.RawLocation
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.categories.CategoryDto
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.ContactButtonRowDto
import com.gumtree.mobile.features.screens.layoutsData.SrpNoResultsCardDto
import com.gumtree.mobile.features.screens.layoutsData.SrpNoResultsCardTitleDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.LandscapeData
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.CategoryDefaults
import com.gumtree.mobile.utils.extensions.isNotNull
import com.gumtree.mobile.utils.extensions.isNull
import com.gumtree.mobile.utils.runOrNull

/**
 * Provider of SRP Listing Views
 */
class SrpListingsProvider(
    private val srpListingMapper: SrpListingMapper,
) {
    /**
     * Create an instance of RowLayoutType.SAVE_SEARCH_BANNER_ROW type with SRP Saved Searches Banner data
     * @param page - the SRP page
     * @param totalResults - the total result of ads on that page
     * @param searchParameters - the search query params
     */
    fun createNoResultsRow(
        page: String,
        totalResults: Int,
        searchParameters: QueryParams,
        categoriesTreeCache: CategoryDto? = CategoriesTreeCache.data,
    ): RowLayout<UiItem>? {
        return runOrNull(SrpScreenUiConfiguration.shouldNoResultsRowAppearOnPage(page, totalResults)) {
            RowLayout(
                RowLayoutType.SEARCH_NO_RESULTS_ROW,
                listOf(
                    SrpNoResultsCardDto(
                        title = SrpNoResultsCardTitleDto(
                            text = searchParameters.toTitleTextForNoResults(),
                            query = searchParameters.toTitleQueryForNoResults(categoriesTreeCache),
                        ),
                        subtitle = SrpScreenUiConfiguration.NO_RESULTS_SUBTITLE,
                        buttonTitle = SrpScreenUiConfiguration.NO_RESULTS_BUTTON_TITLE,
                    ),
                ),
            )
        }
    }

    fun createSrpListingsRows(
        rawAdsData: RawCapiAdList,
        queryParams: QueryParams,
        serviceButtonsMap: Map<String, List<ContactButtonRowDto>?>? = null,
        searchLocationData: RawLocation? = null,
        experiments: ClientExperiments? = null,
    ): Pair<PortraitData, LandscapeData?> {
        // todo use the new method to get categoryId or default after merge of Filters screen work
        val categoryId = queryParams[ApiQueryParams.CATEGORY_ID] ?: CategoryDefaults.ALL_CATEGORIES.id
        val isVehiclesSingleColumnLayout = true // TODO: Cars listings should be single column for now until we introduce a toggle

        // Determine if we should use service layout based on category and experiment
        val shouldUseServiceLayout = CategoriesTreeCache.isServices(categoryId) &&
            experiments?.isB(Experiment.SERVICE_NEW_UI) == true

        val gridSizes = when {
            shouldUseServiceLayout -> SrpScreenUiConfiguration.getServiceListingsGridSizes()
            else -> SrpScreenUiConfiguration.getListingsGridSizes(
                categoryId = categoryId,
                isVehiclesSingleColumnLayout = isVehiclesSingleColumnLayout,
            )
        }

        return srpListingMapper.map(
            rawAdsData = rawAdsData,
            listingGridSizes = gridSizes,
            categoryId = categoryId,
            isVehiclesSingleColumnLayout = isVehiclesSingleColumnLayout,
            queryParams = queryParams,
            serviceButtonsMap = serviceButtonsMap,
            searchLocationData = searchLocationData,
            experiments = experiments,
        )
    }
}

// todo move this in MapExtensions file after the merge of Filters screen
private fun List<CategoryDto>.flattenCategories(): List<CategoryDto>? {
    return this + map { it.children?.flattenCategories() ?: emptyList() }.flatten()
}

// todo move this in MapExtensions file after the merge of Filters screen
fun QueryParams.toTitleQueryForNoResults(categoriesTreeCache: CategoryDto?): String {
    val query = this[CapiApiParams.Q]
    val categoryId = this[CapiApiParams.CATEGORY_ID]
    return when {
        query.isNull() && (categoryId.isNull() || categoryId == CategoryDefaults.ALL_CATEGORIES.id) -> SrpScreenUiConfiguration.ALL_CATEGORIES_TITLE
        query.isNotNull() -> query
        else ->
            categoriesTreeCache?.children
                ?.flattenCategories()
                ?.firstOrNull { it.id == categoryId }
                ?.text
                ?: EMPTY_STRING
    }
}

// todo move this in MapExtensions file after the merge of Filters screen
fun QueryParams.toTitleTextForNoResults(): String {
    return when (this[CapiApiParams.Q]) {
        null -> SrpScreenUiConfiguration.NO_RESULTS_TITLE_FOR_CATEGORY
        else -> SrpScreenUiConfiguration.NO_RESULTS_TITLE_FOR_SEARCH_QUERY
    }
}
