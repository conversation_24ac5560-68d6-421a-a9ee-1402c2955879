package com.gumtree.mobile.features.screens

import com.gumtree.mobile.features.screens.layoutsData.UiItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Describes the API row layouts
 * @property type - the type of the row layout (must be one of the RowLayoutType)
 * @property data - the actual data describing the row items
 * @property topDivider - when we need top divider presentation we set the property = true, otherwise is NULL
 * @property bottomDivider - when we need bottom divider presentation we set the property = true, otherwise is NULL
 * @property formKey - when we need RowLayout to act as form Row, otherwise is NULL
 * @property style - when we need RowLayout with specific style on the Row (spacing etc.), otherwise is NULL
 */
@Serializable
data class RowLayout<T : UiItem>(
    @SerialName("type")
    val type: RowLayoutType,
    @SerialName("data")
    val data: List<T>,
    @SerialName("scrollingBehaviour")
    val scrollingBehaviour: ScrollingCollapseBehaviour? = null,
    @SerialName("topDivider")
    val topDivider: Boolean? = null,
    @SerialName("bottomDivider")
    val bottomDivider: Boolean? = null,
    @SerialName("formKey")
    val formKey: String? = null,
    @SerialName("style")
    val style: Style? = null,
)

/**
 * Describes the different types of ways the row can collapse when scrolling, either at the very top of the page, or anywhere
 */
enum class ScrollingCollapseBehaviour {
    COLLAPSE_AT_TOP,
    COLLAPSE_ANYWHERE,
    STICK_TO_TOP,
}

/**
 * Describes the different types of row layout, supported by the mobile BFF
 */
@Serializable
enum class RowLayoutType {
    LISTING_ROW,
    SINGLE_LISTING_ROW,
    ADVERTISING_ROW,
    HOME_FEED_SAVED_SEARCHES_ROW,
    CONVERSATION_ROW,
    AD_DETAILS_ROW,
    TITLE_ROW,
    TITLE_IMAGE_ROW,
    VIP_IMAGE_GALLERY_ROW,
    VIP_TITLE_ROW,
    VIP_PRICE_ROW,
    VIP_LOCATION_ROW,
    VIP_DESCRIPTION_ROW,
    VIP_POSTED_SINCE_ROW,
    VIP_SELLER_PROFILE_ROW,
    VIP_SELLER_SKILLS_ROW,
    SELLER_PROFILE_ROW,
    REVIEW_OVERVIEW_ROW,
    REVIEW_ROW,
    VIP_MAP_ROW,
    VIP_BUTTON_ROW,
    PARTNERSHIP_ADVERT_ROW,
    OTHER_INFO_ROW,
    VIP_SPECIFICATION_ROW,
    SAVE_SEARCH_BANNER_ROW,
    SEARCH_NO_RESULTS_ROW,
    FAVOURITES_SAVED_SEARCHES_ROW,
    SEARCH_SUGGESTIONS_SAVED_SEARCHES_ROW,
    CHIPS_ROW,
    CHIPS_FEEDBACK_ROW,
    CHIPS_REVIEW_ROW,
    SORT_HEADER_ROW,
    SETTING_ROW,
    MY_GUMTREE_LISTING_ROW,
    PROFILE_ROW,
    MY_GUMTREE_TITLE_ROW,
    IMAGE_ROW,
    ICON_ROW,
    BUTTON_ROW,
    DROPDOWN_ROW,
    VEHICLE_BODY_TYPE_ROW,
    TODAY_PICKS_ROW,
    WEBVIEW_ROW,
    DOUBLE_INPUT_ROW,
    DOUBLE_DROPDOWN_ROW,
    LINK_ROW,
    FILTER_LOCATION_ROW,
    FILTER_ATTRIBUTE_VALUE_ROW,
    KEY_INFO_ROW,
    CONTACT_METHOD_ROW,
    STREAM_ROW,
    HPI_CHECK_ROW,
    EXTENDED_RESULTS_HEADER_ROW,
    SELLER_PROFILE_NO_RESULTS_ROW,
    EXTERNAL_PARTNERSHIP_ADVERT_ROW,
    STEP_ROW,
    STARS_ROW,
    STICKY_ROW,
    CAROUSEL_ROW,
    FILTER_ATTRIBUTE_SEARCH_ROW,
    LABELS_ROW,
    SPACE_ROW,
    OVERVIEW_ROW,
    POST_PUBLISHER_INFO_ROW,
    PORTFOLIO_IMAGE_ROW,
    VIP_SECTION_TABS_ROW,
    DIVIDER_ROW,
}
