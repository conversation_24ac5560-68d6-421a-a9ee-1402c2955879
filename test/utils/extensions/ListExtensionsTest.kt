package utils.extensions

import com.gumtree.mobile.api.common.ZERO
import com.gumtree.mobile.utils.extensions.dropLastIfOdd
import com.gumtree.mobile.utils.extensions.firstOrNotFoundException
import com.gumtree.mobile.utils.extensions.getSizeOrZero
import com.gumtree.mobile.utils.extensions.ifEmptyNull
import com.gumtree.mobile.utils.extensions.isSizeGreaterOrEqualsTo
import com.gumtree.mobile.utils.extensions.slicePageOrEmptyList
import com.gumtree.mobile.utils.extensions.sliceTailedPageOrEmptyList
import io.ktor.server.plugins.NotFoundException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest
import tools.runUnitTestForException

@ParallelTest
class ListExtensionsTest {

    private val robot = Robot()

    @ParameterizedTest
    @CsvSource(
        "49, 0, 24, 24",
        "17, 1, 5, 5",
        "33, 2, 10, 10",
        "105, 1, 50, 50",
    )
    fun `should slice page with full page size`(listSize: Int, page: Int, pageSize: Int, expected: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "49, 2, 24, 2",
        "17, 3, 5, 3",
        "33, 3, 10, 4",
        "105, 2, 50, 6",
    )
    fun `should slice page with partial page size when last page`(listSize: Int, page: Int, pageSize: Int, expected: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "49, 3, 24",
        "17, 4, 5",
        "33, 4, 10",
        "105, 3, 50",
    )
    fun `should slice NO page when after last page`(listSize: Int, page: Int, pageSize: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(0) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "0, 24",
        "1, 5",
        "2, 10",
        "1, 50",
    )
    fun `should slice NO page when list is empty`(page: Int, pageSize: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(0) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(0) }
        }
    }

    @Test
    fun `should slice all pages with expected size`() {
        runUnitTest(robot) {
            Given { stubTestedList(95) }

            Given { stubPage(0) }
            Given { stubSize(20) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(1) }
            Given { stubSize(20) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(2) }
            Given { stubSize(20) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(3) }
            Given { stubSize(20) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(4) }
            Given { stubSize(20) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(16) }

            Given { stubPage(5) }
            Given { stubSize(20) }
            When { slicePageOrEmptyList() }
            Then { checkPageSize(0) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "49, 0, 24, 24",
        "17, 1, 5, 5",
        "33, 2, 10, 10",
        "105, 1, 50, 50",
    )
    fun `should slice tailed page with full page size`(listSize: Int, page: Int, pageSize: Int, expected: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "49, 2, 24, 2",
        "17, 3, 5, 3",
        "33, 3, 10, 4",
        "105, 2, 50, 6",
    )
    fun `should slice tailed page with partial page size when last page`(listSize: Int, page: Int, pageSize: Int, expected: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "49, 3, 24",
        "17, 4, 5",
        "33, 4, 10",
        "105, 3, 50",
    )
    fun `should slice NO tailed page when after last page`(listSize: Int, page: Int, pageSize: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(0) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "0, 24",
        "1, 5",
        "2, 10",
        "1, 50",
    )
    fun `should slice NO tailed page when list is empty`(page: Int, pageSize: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(0) }
            Given { stubPage(page) }
            Given { stubSize(pageSize) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(0) }
        }
    }

    @Test
    fun `should slice all tailed pages with expected size`() {
        runUnitTest(robot) {
            Given { stubTestedList(95) }
            Given { stubPage(0) }
            Given { stubSize(20) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(1) }
            Given { stubSize(20) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(2) }
            Given { stubSize(20) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(3) }
            Given { stubSize(20) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(20) }

            Given { stubPage(4) }
            Given { stubSize(20) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(16) }

            Given { stubPage(5) }
            Given { stubSize(20) }
            When { sliceTailedPageOrEmptyList() }
            Then { checkPageSize(0) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "1, 2",
        "5, 6",
        "20, 21",
    )
    fun `should return the actual list size`(listSize: Int, expected: Int) {
        runUnitTest(robot) {
            Given { stubTestedList(listSize) }
            When { getSizeOrZero() }
            Then { checkListSize(expected) }
        }
    }

    @Test
    fun `should return zero if the list is NULL`() {
        runUnitTest(robot) {
            Given { stubTestedList(null) }
            When { getSizeOrZero() }
            Then { checkListSize(0) }
        }
    }

    @Test
    fun `should return even number of items in list when dropLastIfOdd and given either odd or even total results and condition is true`() {
        runUnitTest(robot) {
            Given { stubList(listOf(1, 2, 3, 4, 5)) }
            When { dropLastIfOdd(true) }
            Then { checkList(listOf(1, 2, 3, 4)) }

            Given { stubList(listOf(1, 2, 3)) }
            When { dropLastIfOdd(true) }
            Then { checkList(listOf(1, 2)) }

            Given { stubList(listOf(1, 2, 3, 4)) }
            When { dropLastIfOdd(true) }
            Then { checkList(listOf(1, 2, 3, 4)) }
        }
    }

    @Test
    fun `should return actual list size when dropLastIfOdd and given either odd or even total results and condition is false`() {
        runUnitTest(robot) {
            Given { stubList(listOf(1, 2, 3, 4, 5)) }
            When { dropLastIfOdd(false) }
            Then { checkList(listOf(1, 2, 3, 4, 5)) }

            Given { stubList(listOf(1, 2)) }
            When { dropLastIfOdd(false) }
            Then { checkList(listOf(1, 2)) }
        }
    }

    @Test
    fun `should return the same list if NOT empty`() {
        runUnitTest(robot) {
            Given { stubList(listOf(1, 2, 3, 4, 5)) }
            When { ifEmptyNull() }
            Then { checkList(listOf(1, 2, 3, 4, 5)) }

            Given { stubList(listOf("one", "three", "five")) }
            When { ifEmptyNull() }
            Then { checkList(listOf("one", "three", "five")) }
        }
    }

    @Test
    fun `should return NULL if list is empty`() {
        runUnitTest(robot) {
            Given { stubList(listOf()) }
            When { ifEmptyNull() }
            Then { checkList(null) }
        }
    }

    @Test
    fun `should return first item`() {
        runUnitTest(robot) {
            Given { stubList(listOf("one", "three", "five")) }
            When { firstOrNotFoundException() }
            Then { checkListFirst("one") }
        }
    }

    @Test
    fun `should throw NotFoundException when first item does NOT exist`() {
        runUnitTestForException(robot, NotFoundException::class) {
            Given { stubList(emptyList()) }
            When { firstOrNotFoundException() }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [0, 1, 2, 3, 4])
    fun `should return true if list size is greater or equals to`(size: Int) {
        runUnitTest(robot) {
            Given { stubList(listOf("one", "three", "five", "ten", "twenty")) }
            When { isSizeGreaterOrEqualsTo(size) }
            Then { checkIsSizeGreaterOrEqualsTo(true) }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [3, 4])
    fun `should return false if list size is NOT greater or equals to`(size: Int) {
        runUnitTest(robot) {
            Given { stubList(emptyList()) }
            When { isSizeGreaterOrEqualsTo(size) }
            Then { checkIsSizeGreaterOrEqualsTo(false) }

            Given { stubList(listOf("one", "three")) }
            When { isSizeGreaterOrEqualsTo(size) }
            Then { checkIsSizeGreaterOrEqualsTo(false) }
        }
    }

    private class Robot: BaseRobot {
        private var actualSliceResult: List<Any>? = null
        private var actualListResult: List<Any>? = null
        private var actualListFirstResult: Any? = null
        private var actualListSizeResult: Int = 0
        private var actualIsSizeGreaterOrEqualsToResult: Boolean? = null

        private lateinit var page: String
        private lateinit var size: String

        private var testSubject: List<Any>? = null

        fun stubTestedList(listSize: Int?) {
            testSubject = when (listSize) {
                null -> null
                0 -> emptyList()
                else -> (0..listSize).map { DataFactory.anyString() }
            }
        }

        fun <T : Any> stubList(list: List<T>) {
            testSubject = list
        }

        fun stubPage(page: Int) {
            this.page = page.toString()
        }

        fun stubSize(size: Int) {
            this.size = size.toString()
        }

        fun slicePageOrEmptyList() {
            actualSliceResult = testSubject?.slicePageOrEmptyList(page, size, testSubject?.size ?: ZERO)
        }

        fun sliceTailedPageOrEmptyList() {
            actualSliceResult = testSubject?.sliceTailedPageOrEmptyList(page, size)
        }

        fun getSizeOrZero() {
            actualListSizeResult = testSubject.getSizeOrZero()
        }

        fun dropLastIfOdd(condition: Boolean) {
            actualListResult = testSubject?.dropLastIfOdd(condition)
        }

        fun ifEmptyNull() {
            actualListResult = testSubject?.ifEmptyNull()
        }

        fun firstOrNotFoundException() {
            actualListFirstResult = testSubject?.firstOrNotFoundException()
        }

        fun isSizeGreaterOrEqualsTo(size: Int) {
            actualIsSizeGreaterOrEqualsToResult = testSubject?.isSizeGreaterOrEqualsTo(size)
        }

        fun checkPageSize(expected: Int) {
            assertEquals(expected, actualSliceResult?.size)
        }

        fun checkListSize(expected: Int) {
            assertEquals(expected, actualListSizeResult)
        }

        fun <T : Any> checkList(expected: T?) {
            assertEquals(expected, actualListResult)
        }

        fun <T : Any> checkListFirst(expected: T?) {
            assertEquals(expected, actualListFirstResult)
        }

        fun checkIsSizeGreaterOrEqualsTo(expected: Boolean) {
            assertEquals(expected, actualIsSizeGreaterOrEqualsToResult)
        }
    }
}