openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-seller-profile-v2
  license:
    name: MIT
tags:
  - name: BFF-seller-profile
    description: Mobile Apps BFF Seller profile API
servers:
  - url: 'http://localhost:8080/v2'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v2'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v2'
    description: Mobile BFF production environment base url
components:
  parameters:
    PlatformHeader:
      in: header
      name: Platform
      description: The type of platform - ios or android
      required: true
      schema:
        type: string
    DeviceTypeHeader:
      in: header
      name: Device-Type
      description: The type of device - mobile or tablet
      required: false
      schema:
        type: string
        default: 'mobile'
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    PublicUserIdQuery:
      in: query
      name: publicUserId
      description: Public User ID (seller's hashed public user id)
      required: true
      schema:
        type: string
    UserIdQuery:
      in: query
      name: userId
      description: User ID (seller's user id)
      required: true
      schema:
        type: integer
        format: int64
  schemas:
    ScreenResponse:
      type: object
      properties:
        title:
          type: string
        stickyBar:
          $ref: '#/components/schemas/StickyBar'
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        landscapeData:
          $ref: '#/components/schemas/RowLayoutItems'
        screenViewAnalyticsEvent:
          $ref: '#/components/schemas/AnalyticsEventData'
        analyticsParameters:
          type: object
          additionalProperties:
            type: string
      required:
        - portraitData
    RowLayoutItems:
      type: array
      items:
        $ref: '#/components/schemas/Row'
    Row:
      type: object
      properties:
        type:
          type: string
          enum:
            - TITLE_ROW
            - CHIPS_ROW
            - REVIEW_OVERVIEW_ROW
            - REVIEW_ROW
            - LISTING_ROW
            - SELLER_PROFILE_NO_RESULTS_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/TitleCardDto'
              - $ref: '#/components/schemas/ListingCardDto'
              - $ref: '#/components/schemas/SellerProfileNoResultsCardDto'
              - $ref: '#/components/schemas/ReviewsOverviewCardDto'
              - $ref: '#/components/schemas/FeedbackOverviewChipsCardDto'
              - $ref: '#/components/schemas/ReviewCardDto'
        topDivider:
          type: boolean
        bottomDivider:
          type: boolean
      required:
        - type
        - data
    StickyBar:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/StickyRow'
    StickyRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - STICKY_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/SellerProfileCardDto'
              - $ref: '#/components/schemas/SellerHistoryCardDto'
              - $ref: '#/components/schemas/TitleCardDto'
        scrollingBehaviour:
          type: string
          enum:
            - COLLAPSE_ANYWHERE
            - COLLAPSE_AT_TOP
    SellerProfileCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - SELLER_PROFILE_CARD
        sellerName:
          type: string
        sellerAbbreviation:
          type: string
        sellerAverageRating:
          type: number
        sellerTotalRatings:
          type: number
        sellerType:
          type: string
        sellerMembership:
          type: string
        sellerActiveStatus:
          type: string
        sellerEmailVerified:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
      required:
        - type
        - sellerName
        - sellerAbbreviation
    TitleCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - TITLE_CARD
        text:
          type: string
        size:
          type: string
          enum:
            - LARGE
            - MEDIUM
            - SMALL
            - X_SMALL
            - XX_SMALL
        iconLeft:
          type: string
          enum:
            - POSTING_TIME
            - EMAIL_VERIFIED
        colour:
          type: string
          enum:
            - FOREGROUND_SUBDUED
            - FOREGROUND_DEFAULT
      required:
        - type
        - text
    SellerHistoryCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - SELLER_HISTORY_CARD
        title:
          type: string
        historyItems:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/HistoryItem'
      required:
        - type
    HistoryItem:
      type: object
      properties:
        label:
          type: string
        value:
          type: string
      required:
        - label
        - value
    ListingCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_CARD
        adId:
          type: integer
          format: int64
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        location:
          type: string
        price:
          type: string
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
        isUrgent:
          type: boolean
        isFeatured:
          type: boolean
      required:
        - type
        - adId
        - title
        - destination
    SellerProfileNoResultsCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - SELLER_PROFILE_NO_RESULTS_CARD
        icon:
          type: string
          enum:
            - STAR
            - STORE
        title:
          type: string
        subtitle:
          type: string
      required:
        - title
        - subtitle
    ReviewsOverviewCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - REVIEWS_OVERVIEW_CARD
        averageRating:
          type: number
          format: float
        totalReviews:
          type: string
        ratingDistribution:
          type: array
          items:
            $ref: '#/components/schemas/RatingDistributionItem'
      required:
        - type
        - averageRating
        - totalReviews
        - ratingDistribution
    RatingDistributionItem:
      type: object
      properties:
        rating:
          type: integer
        percentage:
          type: integer
      required:
        - rating
        - percentage
    FeedbackOverviewChipsCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - FEEDBACK_OVERVIEW_CHIPS_CARD
        chips:
          type: array
          items:
            $ref: '#/components/schemas/StandardStyledChipDto'
    ReviewCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - REVIEW_CARD
        reviewerName:
          type: string
        reviewerAbbreviation:
          type: string
        title:
          type: string
        rating:
          type: number
        timeSincePosted:
          type: string
        feedback:
          type: array
          items:
            type: string
      required:
        - reviewerName
        - reviewerAbbreviation
        - title
        - rating
    StandardStyledChipDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - STANDARD_STYLED
        title:
          type: string
        textSegments:
          type: array
          items:
            $ref: '#/components/schemas/TextSegment'
      required:
        - type
        - title
        - textSegments
    TextSegment:
      type: object
      properties:
        text:
          type: string
        typography:
          $ref: '#/components/schemas/Typography'
      required:
        - text
        - typography
    Typography:
      type: string
      enum:
        - DISPLAY_LARGE
        - DISPLAY_SMALL
        - HEADING_LARGE
        - HEADING_MEDIUM
        - HEADING_SMALL
        - CARD_TITLE_LARGE
        - CARD_TITLE_SMALL
        - BODY_LARGE_REGULAR
        - BODY_LARGE_SEMIBOLD
        - BODY_SMALL_REGULAR
        - BODY_SMALL_SEMIBOLD
        - CAPTION_LARGE_REGULAR
        - CAPTION_LARGE_SEMIBOLD
        - CAPTION_SMALL_REGULAR
        - CAPTION_SMALL_SEMIBOLD
        - LINK_LARGE
        - LINK_SMALL
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
    Image:
      type: object
      properties:
        url:
          type: string
        width:
          type: integer
        height:
          type: integer
      required:
        - url
    AnalyticsEventData:
      type: object
      properties:
        eventName:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
      required:
        - eventName
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
paths:
  /sellerprofile/screen:
    get:
      summary: Get the Mobile apps Seller profile screen
      parameters:
        - $ref: '#/components/parameters/PlatformHeader'
        - $ref: '#/components/parameters/DeviceTypeHeader'
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/PublicUserIdQuery"
        - $ref: "#/components/parameters/UserIdQuery"
      responses:
        '200':
          description: Get the mobile apps Seller profile screen (contains all UI items about the Seller profile screen)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '404':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /sellerprofile/listings/screen:
    get:
      summary: Get the Mobile apps Seller profile screen
      parameters:
        - $ref: '#/components/parameters/PlatformHeader'
        - $ref: '#/components/parameters/DeviceTypeHeader'
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/UserIdQuery"
      responses:
        '200':
          description: Get the mobile apps Seller profile listings screen (contains all the sellers listings)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '404':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /sellerprofile/reviews/screen:
    get:
      summary: Get the Mobile apps Seller profile screen
      parameters:
        - $ref: '#/components/parameters/PlatformHeader'
        - $ref: '#/components/parameters/DeviceTypeHeader'
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/PublicUserIdQuery"
        - $ref: "#/components/parameters/UserIdQuery"
      responses:
        '200':
          description: Get the mobile apps Seller profile reviews screen (contains all the sellers reviews)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '404':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
