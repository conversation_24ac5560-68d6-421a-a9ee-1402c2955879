package features.homeFeed

import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.features.categories.CategoryAnalyticsProvider
import com.gumtree.mobile.features.homeFeed.HomeFeedChipsFactory
import com.gumtree.mobile.features.homeFeed.HomeFeedStickyBarProvider
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.ScrollingCollapseBehaviour
import com.gumtree.mobile.responses.StickyBar
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class HomeFeedStickyBarProviderTest {

    private val robot = Robot()

    @Test
    fun `should return sticky bar with 1 row layout item`() {
        runUnitTest(robot) {
            Given { stubLocationId(DataFactory.SOME_AD_LOCATION_ID) }
            Given { stubLocationType(LocationType.LOCATION) }
            Given { stubLocationName(DataFactory.SOME_LOCATION_NAME) }
            Given { stubDistance(Distance.HUNDRED) }
            Given { stubCategoryExperimentVariant(null) }
            When { createHomeFeedStickyBar() }
            Then { checkScrollingBehaviour(null) }
            Then { checkHomeFeedStickyBarRowLayoutsSize(1) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualCategoryChipsResult: StickyBar
        private lateinit var locationId: String
        private lateinit var locationType: LocationType
        private lateinit var locationName: String
        private lateinit var distance: Distance
        private var categoryExperimentVariant: Variant? = null

        private val categoryAnalyticsProvider = CategoryAnalyticsProvider()
        private var testSubject = HomeFeedStickyBarProvider(
            homeFeedChipsFactory = HomeFeedChipsFactory(analyticsProvider = categoryAnalyticsProvider),
        )

        fun stubLocationId(locationId: String) {
            this.locationId = locationId
        }

        fun stubLocationType(locationType: LocationType) {
            this.locationType = locationType
        }

        fun stubLocationName(locationName: String) {
            this.locationName = locationName
        }

        fun stubDistance(distance: Distance) {
            this.distance = distance
        }

        fun stubCategoryExperimentVariant(categoryExperimentVariant: Variant?) {
            this.categoryExperimentVariant = categoryExperimentVariant
        }

        fun createHomeFeedStickyBar() {
            actualCategoryChipsResult = testSubject.createHomeFeedStickyBar(
                locationId = locationId,
                locationType = locationType,
                locationName = locationName,
                distance = distance,
            )
        }

        fun checkHomeFeedStickyBarRowLayoutsSize(
            expected: Int
        ) {
            assertEquals(expected, actualCategoryChipsResult.size)
        }

        fun checkScrollingBehaviour(expected: ScrollingCollapseBehaviour?) {
            assertEquals(expected, actualCategoryChipsResult[0].scrollingBehaviour)
        }
    }
}