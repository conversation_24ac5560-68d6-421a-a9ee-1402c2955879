package com.gumtree.mobile.utils

import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Locale

/**
 * The Mobile BFF service should use unified Date format Locale
 * This shall allow all feature to be aligned with the same date format standard and avoid differences
 */
fun createUKDateTimeFormatter(datePattern: String): DateTimeFormatter {
    return DateTimeFormatter.ofPattern(datePattern, Locale.UK).withZone(ZoneOffset.UTC)
}
