package features.conversations

import com.gumtree.mobile.features.conversations.ConversationsScreenUiConfiguration.CONVERSATION_LAST_MESSAGE_POST_DATE_FORMAT
import com.gumtree.mobile.features.conversations.ConversationsScreenUiConfiguration.CONVERSATION_NOW_TIME_AGO_TEXT
import com.gumtree.mobile.features.conversations.ConversationsTimeAgoFormatter
import com.gumtree.mobile.utils.CurrentDateProvider
import com.gumtree.mobile.utils.createUKDateTimeFormatter
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class ConversationsTimeAgoFormatterTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearDown() {
        robot.tearsDown()
    }

    @ParameterizedTest
    @CsvSource(
        "2023-07-01T08:20:59.070+01:00, 2023-07-08T08:20:59.070+01:00, 1w",
        "2023-07-01T05:45:59.070+01:00, 2023-07-08T08:20:59.070+01:00, 1w",
        "2023-07-01T05:45:59.070+01:00, 2023-07-11T09:55:34.070+01:00, 1w",
        "2023-07-01T08:20:59.070+01:00, 2023-07-15T08:20:59.070+01:00, 2w",
        "2022-02-05T10:30:59.070+01:00, 2022-02-23T03:25:44.070+01:00, 2w",
        "2022-04-22T10:30:19.070+01:00, 2022-05-07T05:15:14.070+01:00, 2w",
        "2023-01-03T08:20:59.070+01:00, 2023-03-15T08:20:59.070+01:00, 10w",
        "2023-01-01T00:12:39.070+01:00, 2023-07-15T09:22:29.070+01:00, 27w",
    )
    fun `should return conversation time ago in weeks`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgoLabel() }
            Then { checkConversationTimeAgo(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2023-07-01T08:20:59.070+01:00, 2023-07-02T08:20:59.070+01:00, 1d",
        "2023-07-01T05:45:59.070+01:00, 2023-07-02T08:20:59.070+01:00, 1d",
        "2023-07-11T08:20:59.070+01:00, 2023-07-13T08:20:59.070+01:00, 2d",
        "2022-02-05T10:30:59.070+01:00, 2022-02-08T10:25:44.070+01:00, 2d",
        "2023-01-13T08:20:59.070+01:00, 2023-01-20T05:20:59.070+01:00, 6d",
        "2023-12-24T00:12:39.070+01:00, 2023-12-30T09:22:29.070+01:00, 6d",
    )
    fun `should return conversation time ago in days`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgoLabel() }
            Then { checkConversationTimeAgo(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2020-08-01T08:20:59.070+01:00, 2020-08-01T09:21:09.070+01:00, 1h",
        "2023-07-11T10:20:59.070+01:00, 2023-07-11T11:22:38.070+01:00, 1h",
        "2022-02-05T10:30:49.070+01:00, 2022-02-05T12:40:59.070+01:00, 2h",
        "2023-01-13T08:20:29.070+01:00, 2023-01-13T13:40:40.070+01:00, 5h",
        "2023-12-24T12:12:39.070+01:00, 2023-12-25T01:17:50.070+01:00, 13h",
        "2023-12-24T10:10:10.070+01:00, 2023-12-25T06:15:00.070+01:00, 20h",
        "2022-11-30T03:10:10.070+01:00, 2022-12-01T02:15:00.070+01:00, 23h",
    )
    fun `should return conversation time ago in hours`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgoLabel() }
            Then { checkConversationTimeAgo(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2020-08-01T08:20:59.070+01:00, 2020-08-01T08:21:59.070+01:00, 1m",
        "2023-07-11T08:20:59.070+01:00, 2023-07-11T08:22:59.070+01:00, 2m",
        "2022-02-05T10:30:49.070+01:00, 2022-02-05T10:40:59.070+01:00, 10m",
        "2023-01-13T08:20:29.070+01:00, 2023-01-13T08:40:40.070+01:00, 20m",
        "2023-12-24T00:12:39.070+01:00, 2023-12-24T01:07:50.070+01:00, 55m",
        "2023-12-24T10:10:10.070+01:00, 2023-12-24T11:10:00.070+01:00, 59m",
    )
    fun `should return conversation time ago in minutes`(oldDate: String, newDate: String, expected: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgoLabel() }
            Then { checkConversationTimeAgo(expected) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2023-07-01T05:45:59, 2023-07-01T05:46:19.070+01:00",
        "2023-07-01T05:45:59, asdasdjadkasjkdjj23j23233kk23j",
        "j233j2j33jjj3j2j3j2, 2023-07-01T05:46:19.070+01:00",
    )
    fun `should return NULL conversation time ago if the date is NOT in the correct format`(oldDate: String, newDate: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgoLabel() }
            Then { checkConversationTimeAgo(null) }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2023-07-01T05:45:59.070+01:00, 2023-07-01T05:46:19.070+01:00",
        "2023-08-01T10:46:19.070+01:00, 2023-08-01T10:46:44.070+01:00",
    )
    fun `should return Now conversation time ago if the dates difference is lower tha 1 minute`(oldDate: String, newDate: String) {
        runUnitTest(robot) {
            Given { stubOlderDate(oldDate) }
            Given { stubNewerDate(newDate) }
            When { getTimeAgoLabel() }
            Then { checkConversationTimeAgo(CONVERSATION_NOW_TIME_AGO_TEXT) }
        }
    }

    private class Robot: BaseRobot {
        private var actualTimeAgoResultResult: String? = null

        private lateinit var olderDate: String
        private lateinit var newerDate: String

        private val testSubject = ConversationsTimeAgoFormatter(createUKDateTimeFormatter(CONVERSATION_LAST_MESSAGE_POST_DATE_FORMAT))

        override fun setup() {
            mockkObject(CurrentDateProvider)
        }

        override fun tearsDown() {
            unmockkObject(CurrentDateProvider)
        }

        fun stubOlderDate(date: String) {
            olderDate = date
        }

        fun stubNewerDate(date: String) {
            newerDate = date
        }

        fun getTimeAgoLabel() {
            actualTimeAgoResultResult = testSubject.getTimeAgoLabel(olderDate, newerDate)
        }

        fun checkConversationTimeAgo(expected: String?) {
            assertEquals(expected, actualTimeAgoResultResult)
        }
    }
}