package com.gumtree.mobile.features.homeFeed

import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.common.toDistance
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.getDistanceOrDefault
import com.gumtree.mobile.utils.extensions.getLocationIdOrDefault
import com.gumtree.mobile.utils.extensions.getLocationTypeOrDefault
import com.gumtree.mobile.utils.extensions.getPagingOrDefault
import com.gumtree.mobile.utils.extensions.readAllQueryParams
import com.gumtree.mobile.utils.extensions.respondSuccess
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import org.koin.ktor.ext.inject

const val HOME_FEED_SCREEN_PATH = "$API_V1_MAIN_PATH/home-feed/screen"

fun Route.homeFeedRoute() {
    val repository by inject<HomeFeedRepository>()

    get(HOME_FEED_SCREEN_PATH) {
        val callHeaders: Headers = call.request.headers
        val homeFeedQueryParams = call.readAllQueryParams()
        val locationId: String = homeFeedQueryParams.getLocationIdOrDefault()
        val locationType: LocationType = homeFeedQueryParams.getLocationTypeOrDefault()
        val (pageNumber, pageSize) = homeFeedQueryParams.getPagingOrDefault()
        val userId: String? = homeFeedQueryParams[ApiQueryParams.USER_ID]
        val distance: Distance = homeFeedQueryParams.getDistanceOrDefault().toDistance()
        val response = repository.readScreen(
            callHeaders,
            locationId,
            locationType,
            distance,
            userId,
            pageNumber,
            pageSize,
        )

        call.respondSuccess(response)
    }
}
