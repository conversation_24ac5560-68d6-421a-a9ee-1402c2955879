package features.vip

import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.analytics.AnalyticsParams
import com.gumtree.mobile.features.vip.ANALYTICS_BANNER_TYPE_COMPACT
import com.gumtree.mobile.features.vip.ANALYTICS_HPI_CLICK_EVENT_NAME
import com.gumtree.mobile.features.vip.ANALYTICS_PARTNERSHIP_AD_CLICK_EVENT_NAME
import com.gumtree.mobile.features.vip.ANALYTICS_PARTNER_NAME_HPI
import com.gumtree.mobile.features.vip.VipPartnershipAnalyticsProvider
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest

class VipPartnershipAnalyticsProviderTest {

    private val robot = Robot()

    @Test
    fun `should return partnership ad click event`() = runTest {
        runUnitTest(robot) {
            When { getPartnershipAdClickEvent(DataFactory.SOME_PARTNERSHIP_ID) }
            Then { checkAnalyticEventName(ANALYTICS_PARTNERSHIP_AD_CLICK_EVENT_NAME) }
            Then {
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_PARTNER_NAME, DataFactory.SOME_PARTNERSHIP_ID)
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_BANNER_TYPE, ANALYTICS_BANNER_TYPE_COMPACT)
            }
            Then { checkAnalyticEventParamsSize(2) }

            When { getPartnershipAdClickEvent(DataFactory.ANOTHER_PARTNERSHIP_ID) }
            Then { checkAnalyticEventName(ANALYTICS_PARTNERSHIP_AD_CLICK_EVENT_NAME) }
            Then {
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_PARTNER_NAME, DataFactory.ANOTHER_PARTNERSHIP_ID)
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_BANNER_TYPE, ANALYTICS_BANNER_TYPE_COMPACT)
            }
            Then { checkAnalyticEventParamsSize(2) }
        }
    }

    @Test
    fun `should return partnership ad click event without partner name`() = runTest {
        runUnitTest(robot) {
            When { getPartnershipAdClickEvent(null) }
            Then { checkAnalyticEventName(ANALYTICS_PARTNERSHIP_AD_CLICK_EVENT_NAME) }
            Then {
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_PARTNER_NAME, null)
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_BANNER_TYPE, ANALYTICS_BANNER_TYPE_COMPACT)
            }
            Then { checkAnalyticEventParamsSize(1) }
        }
    }

    @Test
    fun `should return HPI click event`() = runTest {
        runUnitTest(robot) {
            When { getHPIClickEvent() }
            Then { checkAnalyticEventName(ANALYTICS_HPI_CLICK_EVENT_NAME) }
            Then {
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_PARTNER_NAME, ANALYTICS_PARTNER_NAME_HPI)
                checkAnalyticEventParamValue(AnalyticsParams.Partnership.ADVERTISING_BANNER_TYPE, ANALYTICS_BANNER_TYPE_COMPACT)
            }
            Then { checkAnalyticEventParamsSize(2) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualEventDataResult: AnalyticsEventData

        private val testSubject = VipPartnershipAnalyticsProvider()

        fun getPartnershipAdClickEvent(partnerId: String?) {
            actualEventDataResult = testSubject.getPartnershipAdClickEvent(partnerId)
        }

        fun getHPIClickEvent() {
            actualEventDataResult = testSubject.getHPIClickEvent()
        }

        fun checkAnalyticEventName(expected: String?) {
            assertEquals(expected, actualEventDataResult.eventName)
        }

        fun checkAnalyticEventParamValue(
            key: String,
            expected: String?,
        ) {
            assertEquals(expected, actualEventDataResult.parameters?.get(key))
        }

        fun checkAnalyticEventParamsSize(expected: Int) {
            assertEquals(expected, actualEventDataResult.parameters?.size)
        }
    }
}