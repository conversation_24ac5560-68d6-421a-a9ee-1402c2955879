package utils.extensions.data

import com.gumtree.mobile.api.capi.models.RawCapiSearchLink
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.features.homeFeed.HomeFeedScreenUiConfiguration.FILTERS_APPLIED
import com.gumtree.mobile.features.homeFeed.HomeFeedScreenUiConfiguration.FILTER_APPLIED
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.utils.extensions.data.getFiltersCount
import com.gumtree.mobile.utils.extensions.data.toQueryParams
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class RawCapiSearchLinkExtensionsTest {

    private val robot = Robot()

    @Test
    fun `should return saved search query params with locationId`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_ONE_FILTER) }
            When { toQueryParams() }
            Then { checkSavedSearchDestination("{locationId=${DataFactory.SOME_AD_LOCATION_ID}}") }
        }
    }

    @Test
    fun `should return saved search query params with locationId, distance and categoryId`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_THREE_FILTERS) }
            When { toQueryParams() }
            Then { checkSavedSearchDestination("{locationId=${DataFactory.SOME_AD_LOCATION_ID}, distance=${DataFactory.SOME_DISTANCE}, categoryId=${DataFactory.SOME_AD_CATEGORY_ID}}") }
        }
    }

    @Test
    fun `should return empty saved search query params when no filters`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_NO_FILTERS) }
            When { toQueryParams() }
            Then { checkSavedSearchDestination("{}") }
        }
    }

    @Test
    fun `should return empty saved search query params when no link url`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(EMPTY_STRING) }
            When { toQueryParams() }
            Then { checkSavedSearchDestination("{}") }
        }
    }

    @Test
    fun `should count one filter`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_ONE_FILTER) }
            When { countFilters() }
            Then { checkFiltersCount("1 $FILTER_APPLIED") }
        }
    }

    @Test
    fun `should count two filters and exclude locationType, lat and lng`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_TWO_FILTERS) }
            When { countFilters() }
            Then { checkFiltersCount("2 $FILTERS_APPLIED") }
        }
    }

    @Test
    fun `should count three filters`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_THREE_FILTERS) }
            When { countFilters() }
            Then { checkFiltersCount("3 $FILTERS_APPLIED") }
        }
    }

    @Test
    fun `should count NO filters`() {
        runUnitTest(robot) {
            Given { stubRawSavedSearch(DataFactory.SAVED_SEARCH_LINK_WITH_NO_FILTERS) }
            When { countFilters() }
            Then { checkFiltersCountIsNull() }
        }
    }

    private class Robot: BaseRobot {

        private lateinit var actualQueryParamsResult: QueryParams
        private var actualFiltersCountResult: String? = null
        private lateinit var testSubject: RawCapiSearchLink

        fun stubRawSavedSearch(searchLinkHref: String) {
            testSubject = RawCapiSearchLink(href = searchLinkHref)
        }

        fun toQueryParams() {
            actualQueryParamsResult = testSubject.toQueryParams()
        }

        fun countFilters() {
            actualFiltersCountResult = testSubject.getFiltersCount()
        }

        fun checkSavedSearchDestination(expected: String) {
            assertEquals(expected, actualQueryParamsResult.toString())
        }

        fun checkFiltersCount(expected: String) {
            assertEquals(expected, actualFiltersCountResult)
        }

        fun checkFiltersCountIsNull() {
            assertNull(actualFiltersCountResult)
        }
    }
}