package features.vip

import com.gumtree.mobile.features.screens.layoutsData.StaticMapDto
import com.gumtree.mobile.features.vip.StaticMapFactory
import com.gumtree.mobile.utils.ApplicationEnvironment
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.When
import tools.Then
import tools.runUnitTest
import java.net.URLEncoder

class StaticMapFactoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setUp() {
        mockkObject(ApplicationEnvironment)
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `should log error and return null when Google Maps API key is not available`() {
        runUnitTest(robot) {
            Given { stubGoogleMapsApiKey(null) }
            Given { createStaticMapFactory() }
            When { buildStaticMap() }
            Then { checkStaticMapIsNull() }
            Then { verifyErrorLogged("Failed to build static map: Google Maps API key is null") }
        }
    }

    @Test
    fun `should log error and return null when Google Maps URL signing key is not available`() {
        runUnitTest(robot) {
            Given { stubGoogleMapsApiKey(DataFactory.SOME_API_KEY) }
            Given { stubGoogleMapsUrlSigningKey(null) }
            Given { createStaticMapFactory() }
            When { buildStaticMap() }
            Then { checkStaticMapIsNull() }
            Then { verifyErrorLogged("Failed to build static map: Google Maps URL signing key is null") }
        }
    }

    @Test
    fun `should return static map with correct dimensions when aspect ratio is greater than 1`() {
        runUnitTest(robot) {
            Given { stubGoogleMapsApiKey(DataFactory.SOME_API_KEY) }
            Given { stubGoogleMapsUrlSigningKey(DataFactory.SOME_SIGNING_KEY) }
            Given { createStaticMapFactory() }
            When { buildStaticMap(aspectRatio = 1.5) }
            Then { checkStaticMapWidth(640) }
            Then { checkStaticMapHeight(426) }
        }
    }

    @Test
    fun `should return static map with correct dimensions when aspect ratio is less than 1`() {
        runUnitTest(robot) {
            Given { stubGoogleMapsApiKey(DataFactory.SOME_API_KEY) }
            Given { stubGoogleMapsUrlSigningKey(DataFactory.SOME_SIGNING_KEY) }
            Given { createStaticMapFactory() }
            When { buildStaticMap(aspectRatio = 0.75) }
            Then { checkStaticMapWidth(480) }
            Then { checkStaticMapHeight(640) }
        }
    }

    @Test
    fun `should return static map with correct URL parameters`() {
        runUnitTest(robot) {
            Given { stubGoogleMapsApiKey(DataFactory.SOME_API_KEY) }
            Given { stubGoogleMapsUrlSigningKey(DataFactory.SOME_SIGNING_KEY) }
            Given { createStaticMapFactory() }
            When { buildStaticMap(latitude = 51.5074, longitude = -0.1278, zoom = 14, aspectRatio = 1.0) }
            Then { checkStaticMapUrlContains("center=${URLEncoder.encode("51.5074,-0.1278", "UTF-8")}") }
            Then { checkStaticMapUrlContains("zoom=14") }
            Then { checkStaticMapUrlContains("size=640x640") }
            Then { checkStaticMapUrlContains("scale=2") }
            Then { checkStaticMapUrlContains("key=${DataFactory.SOME_API_KEY}") }
            Then { checkStaticMapUrlContains("signature=gLpkr_nbZv9SeJ4VsxMr2iQoh4M=") }
        }
    }

    class Robot : BaseRobot {
        private lateinit var staticMapFactory: StaticMapFactory
        private var staticMapResult: StaticMapDto? = null
        private val mockedLogger: Logger = mockk(relaxed = true)

        fun stubGoogleMapsApiKey(apiKey: String?) {
            every { ApplicationEnvironment.getProperty("GOOGLE_MAPS_API_KEY") } returns apiKey
        }

        fun stubGoogleMapsUrlSigningKey(signingKey: String?) {
            every { ApplicationEnvironment.getProperty("GOOGLE_MAPS_URL_SIGNING_KEY") } returns signingKey
        }

        fun createStaticMapFactory() {
            staticMapFactory = StaticMapFactory(mockedLogger)
        }

        fun buildStaticMap(
            latitude: Double = DataFactory.SOME_LOCATION_LAT_DOUBLE,
            longitude: Double = DataFactory.SOME_LOCATION_LON_DOUBLE,
            zoom: Int = DataFactory.SOME_ZOOM_LEVEL,
            aspectRatio: Double = DataFactory.SOME_ASPECT_RATIO
        ) {
            staticMapResult = staticMapFactory.buildStaticMap(latitude, longitude, zoom, aspectRatio)
        }

        fun checkStaticMapIsNull() {
            assertNull(staticMapResult)
        }

        fun checkStaticMapWidth(expected: Int) {
            assertEquals(expected, staticMapResult?.width)
        }

        fun checkStaticMapHeight(expected: Int) {
            assertEquals(expected, staticMapResult?.height)
        }

        fun checkStaticMapUrlContains(expected: String) {
            assert(staticMapResult?.url?.contains(expected) == true)
        }

        fun verifyErrorLogged(expectedMessage: String) {
            verify(exactly = 1) { mockedLogger.error(expectedMessage) }
        }
    }
}
