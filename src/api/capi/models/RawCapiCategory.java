package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import javax.annotation.Nullable;
import java.util.List;


@Root(strict = false)
@Namespace(reference = Namespaces.CATEGORY, prefix = Namespaces.Prefix.CATEGORY)
public class RawCapiCategory {

    @Attribute(name = "id", required = false)
    public String id;

    @Element(name = "id-name", required = false)
    @Namespace(reference = Namespaces.CATEGORY)
    public String idName;

    @Element(name = "localized-name", required = false)
    @Namespace(reference = Namespaces.CATEGORY)
    public String name;

    @Element(name = "max-location-level", required = false)
    @Namespace(reference = Namespaces.CATEGORY)
    public String maxLocationLevel;

    @ElementList(entry = "category", required = false, inline = true)
    @Namespace(reference = Namespaces.CATEGORY)
    @Nullable
    public List<RawCapiCategory> childCategories;

    @Element(name = "flags", required = false)
    public RawCategoryFlags flags;
}
