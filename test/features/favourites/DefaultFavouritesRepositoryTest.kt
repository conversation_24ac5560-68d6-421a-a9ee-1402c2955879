package features.favourites

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.features.favourites.DefaultFavouritesRepository
import com.gumtree.mobile.features.favourites.FavouriteRequest
import com.gumtree.mobile.features.favourites.FavouritesListingGridSizes
import com.gumtree.mobile.features.favourites.FavouritesMapper
import com.gumtree.mobile.features.favourites.FavouritesService
import com.gumtree.mobile.features.screens.LimitedPageCalculator
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.createGridSizes
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import retrofit2.Response
import tools.BaseRobot
import tools.CommonAnalyticsProviderFactory
import tools.DataFactory
import tools.GRID_SIZES_FILE_PATH
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.rawDataFactory.RawCapiAdsFactory
import utils.TestDispatcherProvider

class DefaultFavouritesRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @AfterEach
    fun tearsDown() {
        robot.tearsDown()
    }

    @Test
    fun `should use service to add new favourite ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulAddFavouriteAdHttpResult() }
            When { create(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkServiceAddAd(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should read user favourite screen`() = runTest {
        runUnitTest(robot) {
            Given { stubUnAuthHeaders() }
            Given { stubRawFavouriteAds(10, listOf(AdStatus.ACTIVE, AdStatus.EXPIRED)) }
            When { readScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkServiceGetUserFavourites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should use service to read user favourite screen`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubRawFavouriteAds(10, listOf(AdStatus.ACTIVE, AdStatus.EXPIRED)) }
            When { readScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkServiceGetUserFavourites(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should use service to delete favourite ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulDeleteFavouriteAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkServiceDeleteFavouriteAd(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should use mapper to map the raw favourite ads`() = runTest {
        runUnitTest(robot) {
            Given { stubAllRawFavorites() }
            Given { stubAuthHeaders() }
            When { read(DataFactory.SOME_USER_EMAIL) }
            Then { checkMapperMapsFavouriteAds() }
        }
    }

    @Test
    fun `should read favourite ad ids`() = runTest {
        runUnitTest(robot) {
            Given { stubAllRawFavorites() }
            Given { stubUnAuthHeaders() }
            When { read(DataFactory.SOME_USER_EMAIL) }
            Then { checkMapperMapsFavouriteAds() }
        }
    }

    @Test
    fun `should use mapper to map the favourites screen`() = runTest {
        runUnitTest(robot) {
            Given { stubGridSizes() }
            Given { stubAuthHeaders() }
            Given { stubRawFavorites() }
            When { readScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkMapperMapsFavouritesScreen() }
        }
    }

    @Test
    fun `should create authorisation headers when create favourite ad`() = runTest {
        runUnitTest(robot) {
            Given { stubSuccessfulAddFavouriteAdHttpResult() }
            When { create(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkProviderCreateAuthorisationHeaders() }
        }
    }

    @Test
    fun `should create authorisation headers when read favourite ads`() = runTest {
        runUnitTest(robot) {
            When { read(DataFactory.SOME_USER_EMAIL) }
            Then { checkProviderCreateAuthorisationHeaders() }
        }
    }

    @Test
    fun `should create authorisation headers when read favourites screen`() = runTest {
        runUnitTest(robot) {
            When { readScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkProviderCreateAuthorisationHeaders() }
        }
    }

    @Test
    fun `should create authorisation headers when delete favourite ad`() = runTest {
        runUnitTest(robot) {
            Given { stubSuccessfulDeleteFavouriteAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkProviderCreateAuthorisationHeaders() }
        }
    }

    @Test
    fun `should create authorised headers, call favourites service in order when adding favourite ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulAddFavouriteAdHttpResult() }
            When { create(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkAddFavouriteActionsInOrder(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should create authorised headers, call favourites service and map favourite ads in order when reading favourite ads`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubAllRawFavorites() }
            When { read(DataFactory.SOME_USER_EMAIL) }
            Then { checkGetFavouritesActionsInOrder(DataFactory.SOME_USER_EMAIL) }
        }
    }

    @Test
    fun `should create authorised headers, call favourites service and map raw favourite ads data in order when reading favourite screen`() = runTest {
        runUnitTest(robot) {
            Given { stubGridSizes() }
            Given { stubRawFavorites() }
            Given { stubAuthHeaders() }
            When { readScreen(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
            Then { checkGetFavouritesScreenActionsInOrder(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_PAGE_NUMBER, DataFactory.SOME_SIZE) }
        }
    }

    @Test
    fun `should create authorised headers, call favourites service in order when deleting favourite ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulDeleteFavouriteAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkDeleteFavouriteActionsInOrder(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    private class Robot: BaseRobot {
        private val favouritesService: FavouritesService = mockk(relaxed = true)
        private val headersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val rawFavoritesData: RawCapiAdList = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)
        private val authHeaders: ApiHeaders = mockk(relaxed = true)
        private val favouritesMapper = spyk(FavouritesMapper(CommonAnalyticsProviderFactory.createInstance()))
        private val listingGridSizes = spyk(FavouritesListingGridSizes())
        private val favouritesPageCalculator: PageCalculator = LimitedPageCalculator()

        private lateinit var testSubject: DefaultFavouritesRepository

        override fun setup() {
            testSubject = DefaultFavouritesRepository(
                favouritesService,
                favouritesMapper,
                favouritesPageCalculator,
                headersProvider,
                TestDispatcherProvider(),
            )
            mockkStatic(GRID_SIZES_FILE_PATH)
        }

        override fun tearsDown() {
            unmockkStatic(GRID_SIZES_FILE_PATH)
        }

        fun stubAuthHeaders() {
            every { headersProvider.createAuthorisedHeaders(callHeaders) } returns authHeaders
        }

        fun stubUnAuthHeaders() {
            every { callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL] } returns null
            every { callHeaders[ApiHeaderParams.AUTHORISATION_USER_TOKEN] } returns null
            every { headersProvider.createAuthorisedHeaders(callHeaders) } returns authHeaders
        }

        fun stubSuccessfulAddFavouriteAdHttpResult() {
            coEvery { favouritesService.addAdToUsersFavorites(any(), any(), any()) } returns Response.success(Unit)
        }

        fun stubSuccessfulDeleteFavouriteAdHttpResult() {
            coEvery { favouritesService.deleteAdFromUsersFavorites(any(), any(), any()) } returns Response.success(Unit)
        }

        fun stubRawFavorites() {
            coEvery { favouritesService.getUsersFavorites(any(), any(), any(), any()) } returns rawFavoritesData
        }

        fun stubAllRawFavorites() {
            coEvery { favouritesService.getAllUsersFavorites(any(), any()) } returns rawFavoritesData
        }

        fun stubGridSizes() {
            every { createGridSizes<FavouritesListingGridSizes>(any()) } returns listingGridSizes
        }

        fun stubRawFavouriteAds(
            number: Int,
            adStatuses: List<AdStatus> = emptyList()
        ) {
            coEvery { favouritesService.getUsersFavorites(any(), any(), any(), any()) } returns
                RawCapiAdsFactory.createRawCapiAdList(number, adStatuses)
        }

        suspend fun create(
            userName: String,
            adId: String
        ) {
            val favouriteRequest = FavouriteRequest(id = adId)
            testSubject.create(callHeaders, userName, favouriteRequest)
        }

        suspend fun read(userName: String) {
            testSubject.read(callHeaders, userName)
        }

        suspend fun readScreen(
            userName: String,
            page: String,
            size: String
        ) {
            testSubject.readScreen(callHeaders, userName, page, size)
        }

        suspend fun delete(
            userName: String,
            adId: String
        ) {
            testSubject.delete(callHeaders, userName, adId)
        }

        fun checkServiceAddAd(
            expectedUserName: String,
            expectedAdId: String
        ) {
            coVerify { favouritesService.addAdToUsersFavorites(authHeaders, expectedUserName, expectedAdId) }
        }

        fun checkServiceGetUserFavourites(
            expectedUserName: String,
            expectedPage: String,
            expectedSize: String,
        ) {
            coVerify { favouritesService.getUsersFavorites(authHeaders, expectedUserName, expectedPage, expectedSize) }
        }

        fun checkMapperMapsFavouriteAds() {
            verify { favouritesMapper.map(rawFavoritesData) }
        }

        fun checkMapperMapsFavouritesScreen() {
            verify { favouritesMapper.mapScreen(rawFavoritesData, listingGridSizes) }
        }

        fun checkServiceDeleteFavouriteAd(
            expectedUserName: String,
            expectedAdId: String
        ) {
            coVerify { favouritesService.deleteAdFromUsersFavorites(authHeaders, expectedUserName, expectedAdId) }
        }

        fun checkProviderCreateAuthorisationHeaders() {
            verify { headersProvider.createAuthorisedHeaders(callHeaders) }
        }

        fun checkAddFavouriteActionsInOrder(
            expectedUserName: String,
            expectedAdId: String
        ) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                favouritesService.addAdToUsersFavorites(authHeaders, expectedUserName, expectedAdId)
            }
        }

        fun checkGetFavouritesActionsInOrder(expectedUsername: String) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                favouritesService.getAllUsersFavorites(authHeaders, expectedUsername)
                favouritesMapper.map(rawFavoritesData)
            }
        }

        fun checkGetFavouritesScreenActionsInOrder(
            expectedUsername: String,
            expectedPage: String,
            expectedSize: String,
        ) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                favouritesService.getUsersFavorites(authHeaders, expectedUsername, expectedPage, expectedSize)
                favouritesMapper.mapScreen(rawFavoritesData, listingGridSizes)
            }
        }

        fun checkDeleteFavouriteActionsInOrder(
            expectedUserName: String,
            expectedAdId: String
        ) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                favouritesService.deleteAdFromUsersFavorites(authHeaders, expectedUserName, expectedAdId)
            }
        }
    }
}