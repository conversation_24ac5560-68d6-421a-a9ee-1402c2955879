package tools.dataFactory

import com.gumtree.mobile.api.userService.models.RawUserServiceUserType
import com.gumtree.mobile.common.UserProfileData
import tools.DataFactory

object UserProfileDataFactory {

    fun create(
        userId: String = DataFactory.SOME_USER_ID,
        accountId: String = DataFactory.SOME_ACCOUNT_ID,
        displayName: String = DataFactory.SOME_USER_DISPLAY_NAME,
        userLastName: String = DataFactory.SOME_USER_LAST_NAME,
        userRegDate: String = DataFactory.SOME_USER_REGISTRATION_DATE_TIMESTAMP,
        userPostingDate: String = DataFactory.SOME_USER_POSTING_SINCE_DATE_TIMESTAMP,
        userEmail: String = DataFactory.SOME_USER_EMAIL,
        userToken: String = DataFactory.SOME_TOKEN,
        userType: String = RawUserServiceUserType.STANDARD.name,
    ): UserProfileData {
        return UserProfileData(
            userId = userId,
            accountId = accountId,
            userDisplayName = displayName,
            userLastName = userLastName,
            userRegDate = userRegDate,
            userPostingDate = userPostingDate,
            userEmail = userEmail,
            userToken = userToken,
            userType = userType,
        )
    }
}
