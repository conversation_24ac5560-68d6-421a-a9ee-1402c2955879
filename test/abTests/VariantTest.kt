package abTests

import com.gumtree.mobile.abTests.Variant
import com.gumtree.mobile.api.common.EMPTY_STRING
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class VariantTest {

    private val robot = Robot()

    @Test
    fun `should return variant A`() {
        runUnitTest(robot) {
            When { fromString(Variant.A.name) }
            Then { checkVariant(Variant.A) }

            When { fromString(Variant.A.name.lowercase()) }
            Then { checkVariant(Variant.A) }
        }
    }

    @Test
    fun `should return variant B`() {
        runUnitTest(robot) {
            When { fromString(Variant.B.name) }
            Then { checkVariant(Variant.B) }

            When { fromString(Variant.B.name.lowercase()) }
            Then { checkVariant(Variant.B) }
        }
    }

    @Test
    fun `should return variant C`() {
        runUnitTest(robot) {
            When { fromString(Variant.C.name) }
            Then { checkVariant(Variant.C) }

            When { fromString(Variant.C.name.lowercase()) }
            Then { checkVariant(Variant.C) }
        }
    }

    @Test
    fun `should return variant D`() {
        runUnitTest(robot) {
            When { fromString(Variant.D.name) }
            Then { checkVariant(Variant.D) }

            When { fromString(Variant.D.name.lowercase()) }
            Then { checkVariant(Variant.D) }
        }
    }

    @Test
    fun `should return UNKNOWN variant`() {
        runUnitTest(robot) {
            When { fromString("Y") }
            Then { checkVariant(Variant.UNKNOWN) }

            When { fromString("X") }
            Then { checkVariant(Variant.UNKNOWN) }

            When { fromString("E") }
            Then { checkVariant(Variant.UNKNOWN) }

            When { fromString(EMPTY_STRING) }
            Then { checkVariant(Variant.UNKNOWN) }

            When { fromString("something") }
            Then { checkVariant(Variant.UNKNOWN) }

            When { fromString(" ") }
            Then { checkVariant(Variant.UNKNOWN) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualVariantResult: Variant

        fun fromString(string: String) {
            actualVariantResult = Variant.fromString(string)
        }

        fun checkVariant(expected: Variant) {
            assertEquals(expected, actualVariantResult)
        }
    }
}