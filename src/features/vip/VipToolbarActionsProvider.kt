package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.features.screens.layoutsData.ReportAdToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ShareToolbarActionDto
import com.gumtree.mobile.features.screens.layoutsData.ToolbarActionDto
import com.gumtree.mobile.utils.DefaultPriceFormatter
import com.gumtree.mobile.utils.extensions.data.getAdSelfPublicWebsite
import com.gumtree.mobile.utils.extensions.isNotNull

class VipToolbarActionsProvider(
    private val vipAdjustTrackingDataProvider: VipAdjustTrackingDataProvider,
    private val vipScreenUiConfiguration: VipScreenUiConfiguration = VipScreenUiConfiguration,
) {

    fun createReportAdAction(rawAdDetailsData: RawCapiAd, isUserOwnAd: Boolean): ToolbarActionDto? {
        return when {
            isUserOwnAd -> null
            else -> ReportAdToolbarActionDto(
                title = VipScreenUiConfiguration.TOOLBAR_REPORT_AD_ACTION_TITLE,
                isHidden = vipScreenUiConfiguration.IS_TOOLBAR_REPORT_AD_HIDDEN,
                adId = rawAdDetailsData.id,
            )
        }
    }

    fun createShareAdAction(rawAdDetailsData: RawCapiAd): ToolbarActionDto? {
        val shareUrl = rawAdDetailsData.getAdSelfPublicWebsite()
        return when {
            shareUrl.isNotNull() -> ShareToolbarActionDto(
                title = VipScreenUiConfiguration.TOOLBAR_SHARE_ACTION_TITLE,
                isHidden = vipScreenUiConfiguration.IS_TOOLBAR_SHARE_ACTION_HIDDEN,
                message = generateShareAdMessage(rawAdDetailsData),
                adjustTrackingData = vipAdjustTrackingDataProvider.getShareTrackingData(rawAdDetailsData),
            )
            else -> null
        }
    }

    private fun generateShareAdMessage(rawAdDetailsData: RawCapiAd): String {
        return with(StringBuilder()) {
            append(VipScreenUiConfiguration.PLEASE_VIEW_THIS_AD)
            append("\n\n")
            append(rawAdDetailsData.title).append("\n")
            append(generateShareAdPriceText(rawAdDetailsData))
            append("\n")
            append(rawAdDetailsData.getAdSelfPublicWebsite())
            append("?")
        }.toString()
    }

    private fun generateShareAdPriceText(rawAdDetailsData: RawCapiAd): String {
        val priceText = DefaultPriceFormatter.formatPrice(
            rawAdDetailsData.price?.amount,
            rawAdDetailsData.priceFrequency?.value,
        )
        return when {
            priceText.isNotNull() -> VipScreenUiConfiguration.PRICE + priceText
            else -> EMPTY_STRING
        }
    }
}
