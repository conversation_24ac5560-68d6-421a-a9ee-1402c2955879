package com.gumtree.mobile.api.seller

import com.gumtree.mobile.api.common.OkHttpConnectionPoolConfiguration
import java.util.concurrent.TimeUnit

const val SELLER_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS = 10
const val SELLER_API_CONNECTION_POOL_KEEP_ALIVE_DURATION = 300L

class SellerApiOkHttpConnectionPoolConfiguration : OkHttpConnectionPoolConfiguration {

    override val maxIdleConnections: Int = SELLER_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS

    override val keepAliveDuration: Long = SELLER_API_CONNECTION_POOL_KEEP_ALIVE_DURATION

    override val timeUnit: TimeUnit = TimeUnit.SECONDS
}
