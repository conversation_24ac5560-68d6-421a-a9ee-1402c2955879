package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.ArrayList;
import java.util.List;

@Root(strict = false)
public class RawDependentAttribute {
    @Attribute(required = false)
    public String name;

    @Attribute(name = "dependent-name", required = false)
    public String dependentName;

    @Namespace(reference = Namespaces.ATTRIBUTE)
    @ElementList(inline = true, entry = "dependent-supported-value", required = false, empty = false)
    public List<RawDependentSupportedValue> mRawDependentSupportedValues = new ArrayList<>();
}
