package com.gumtree.mobile.features.searchSuggestions

import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.core.qualifier.named
import org.koin.dsl.module

const val DEFAULT_REPO_NAME = "default_repo"

val searchSuggestionsModule = module {

    factory { SearchSuggestionsAnalyticsProvider(commonAnalyticsProvider = getFromKoin()) }

    single { SearchSuggestionsMapper() }

    single<SearchSuggestionsRepository>(named(DEFAULT_REPO_NAME)) {
        DefaultSearchSuggestionsRepository(
            capiSearchApi = getFromKoin(),
            searchSuggestionsMapper = getFromKoin(),
            searchSuggestionsAnalyticsProvider = getFromKoin(),
            dispatcherProvider = getFromKoin(),
        )
    }
}
