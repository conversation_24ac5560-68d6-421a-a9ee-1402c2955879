package com.gumtree.mobile.api.userService

import com.gumtree.mobile.api.common.OkHttpConnectionPoolConfiguration
import java.util.concurrent.TimeUnit

// https://github.com/Netflix/Hystrix/wiki/Configuration#ThreadPool 40 RPS ×0.5 seconds = 20
const val USER_SERVICE_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS = 20
const val USER_SERVICE_API_CONNECTION_POOL_KEEP_ALIVE_DURATION = 300L
class UserServiceApiOkHttpConnectionPoolConfiguration : OkHttpConnectionPoolConfiguration {

    override val maxIdleConnections: Int = USER_SERVICE_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS

    override val keepAliveDuration: Long = USER_SERVICE_API_CONNECTION_POOL_KEEP_ALIVE_DURATION

    override val timeUnit: TimeUnit = TimeUnit.SECONDS
}
