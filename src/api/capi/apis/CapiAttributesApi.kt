package com.gumtree.mobile.api.capi.apis

import api.capi.models.RawAttributeList
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.CapiEndpointUrls
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.QueryMap

interface CapiAttributesApi {

    @GET(CapiEndpointUrls.GET_ATTRIBUTES_ENDPOINT)
    suspend fun getAttributeAvailableValues(
        @Path(CapiApiParams.CATEGORY_ID) categoryId: String,
        @Path(CapiApiParams.ATTRIBUTE) attribute: String,
        @QueryMap attributeValues: Map<String, String>,
    ): RawAttributeList

    @GET(CapiEndpointUrls.GET_ATTRIBUTES_DEFAULT_VALUES_ENDPOINT)
    suspend fun getDefaultsForAttributes(
        @Path(CapiApiParams.CATEGORY_ID) categoryId: String,
        @QueryMap attributeValues: Map<String, String>,
    ): RawAttributeList
}
