package com.gumtree.mobile.features.screens.layoutsData

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("TITLE_IMAGE_CARD")
data class TitleImageCardDto(
    @SerialName("text")
    val text: String,
    @SerialName("icon")
    val icon: Icon,
) : UiItem {

    /**
     * The Title image icon
     */
    enum class Icon {
        HPI,
    }
}
