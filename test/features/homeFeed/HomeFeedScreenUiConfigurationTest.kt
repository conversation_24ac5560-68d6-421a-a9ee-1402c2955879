package features.homeFeed

import com.gumtree.mobile.features.homeFeed.HomeFeedScreenUiConfiguration
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class HomeFeedScreenUiConfigurationTest {

    private val robot = Robot()

    @Test
    fun `should return home feed saved searches layout row on position 1`() {
        runUnitTest(robot) {
            When { getSavedSearchesRowPosition() }
            Then { checkSavedSearchesRowPosition(1) }
        }
    }

    @Test
    fun `should return limit of 7 home feed saved searches cards`() {
        runUnitTest(robot) {
            When { getSavedSearchesLimit() }
            Then { checkSavedSearchesLimit(7) }
        }
    }

    @Test
    fun `should return true for sticky bar appear on page 0`() {
        runUnitTest(robot) {
            When { shouldStickyBarAppearOnPage("0") }
            Then { checkStickyBarAppear(true) }
        }
    }

    @Test
    fun `should return false for sticky bar appear on page 1, 2 and 3`() {
        runUnitTest(robot) {
            When { shouldStickyBarAppearOnPage("1") }
            Then { checkStickyBarAppear(false) }

            When { shouldStickyBarAppearOnPage("2") }
            Then { checkStickyBarAppear(false) }

            When { shouldStickyBarAppearOnPage("3") }
            Then { checkStickyBarAppear(false) }
        }
    }

    @Test
    fun `should return true for saved searches row appear on page 0`() {
        runUnitTest(robot) {
            When { shouldSavedSearchesRowAppearOnPage("0") }
            Then { checkSavedSearchesRowAppear(true) }
        }
    }

    @Test
    fun `should return false for saved searches row appear on page 1, 2 and 3`() {
        runUnitTest(robot) {
            When { shouldSavedSearchesRowAppearOnPage("1") }
            Then { checkSavedSearchesRowAppear(false) }

            When { shouldSavedSearchesRowAppearOnPage("2") }
            Then { checkSavedSearchesRowAppear(false) }

            When { shouldSavedSearchesRowAppearOnPage("3") }
            Then { checkSavedSearchesRowAppear(false) }
        }
    }

    @Test
    fun `should return position 1 when getTitleRowPosition given NO Saved Searches`() {
        runUnitTest(robot) {
            Given { stubHasSavedSearches(false) }
            When { getTitleRowPosition("0") }
            Then { checkTitleRowPosition(1) }
        }
    }

    @Test
    fun `should return 2 when getTitleRowPosition given Saved Searches`() {
        runUnitTest(robot) {
            Given { stubHasSavedSearches(true) }
            When { getTitleRowPosition("0") }
            Then { checkTitleRowPosition(2) }
        }
    }

    private class Robot: BaseRobot {
        private var actualSavedSearchesRowPosition: Int = 0
        private var actualSavedSearchesLimit: Int = 0
        private var actualStickyBarAppearOnPageResult: Boolean = false
        private var actualSavedSearchesRowAppearResult: Boolean = false
        private var stubHasSavedSearches: Boolean = false
        private var actualTitleRowPosition: Int = -1

        private val testSubject = HomeFeedScreenUiConfiguration

        fun getSavedSearchesRowPosition() {
            actualSavedSearchesRowPosition = testSubject.SAVED_SEARCH_ROW_POSITION
        }

        fun getSavedSearchesLimit() {
            actualSavedSearchesLimit = testSubject.SAVED_SEARCHES_LIMIT
        }

        fun stubHasSavedSearches(hasSavedSearches: Boolean) {
            stubHasSavedSearches = hasSavedSearches
        }

        fun getTitleRowPosition(page: String) {
            actualTitleRowPosition = testSubject.getTitleRowPosition(stubHasSavedSearches, page)
        }

        fun checkTitleRowPosition(expected: Int) {
            assertEquals(expected, actualTitleRowPosition)
        }

        fun shouldStickyBarAppearOnPage(page: String) {
            actualStickyBarAppearOnPageResult = testSubject.shouldStickyBarAppearOnPage(page)
        }

        fun shouldSavedSearchesRowAppearOnPage(page: String) {
            actualSavedSearchesRowAppearResult = testSubject.shouldSavedSearchesRowAppearOnPage(page)
        }

        fun checkSavedSearchesRowPosition(expected: Int) {
            assertEquals(expected, actualSavedSearchesRowPosition)
        }

        fun checkSavedSearchesLimit(expected: Int) {
            assertEquals(expected, actualSavedSearchesLimit)
        }

        fun checkStickyBarAppear(expected: Boolean) {
            assertEquals(expected, actualStickyBarAppearOnPageResult)
        }

        fun checkSavedSearchesRowAppear(expected: Boolean) {
            assertEquals(expected, actualSavedSearchesRowAppearResult)
        }
    }

}