package api.capi.models;


import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;

import java.util.ArrayList;
import java.util.List;


@Namespace(reference = Namespaces.PICTURE)
public class RawCapiPicture {

    @Namespace(reference = Namespaces.PICTURE)
    @ElementList(inline = true, entry = "link", required = false)
    private List<RawCapiPictureLink> links;

    public RawCapiPicture() {
        setLinks(new ArrayList<>());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        RawCapiPicture that = (RawCapiPicture) o;

        return !(getLinks() != null ? !getLinks().equals(that.getLinks()) : that.getLinks() != null);
    }

    @Override
    public int hashCode() {
        return getLinks() != null ? getLinks().hashCode() : 0;
    }

    public List<RawCapiPictureLink> getLinks() {
        if (links == null) {
            links = new ArrayList<>();
        }
        return links;
    }

    public void setLinks(List<RawCapiPictureLink> links) {
        this.links = links;
    }

    @Namespace(reference = Namespaces.PICTURE)
    public static class RawCapiPictureLink extends RawCapiLink {

        public static final String NORMAL = "normal";

        public RawCapiPictureLink() {
            super();
        }

        public RawCapiPictureLink(String href, String rel) {
            super(href, rel);
        }
    }
}
