openapi: "3.0.3"
info:
  version: 1.0.0
  title: mobile-apps-bff-srp
  license:
    name: MIT
tags:
  - name: BFF-SRP
    description: Mobile Apps BFF SRP API
servers:
  - url: 'http://localhost:8080/v1'
    description: Mobile BFF local host environment base url
  - url: 'https://mobile-apps-bff.{environment}.gumtree.io/v1'
    description: Mobile BFF testing environments base url
    variables:
      environment:
        enum:
          - zoidberg
          - bixi
          - staging
        default: zoidberg
  - url: 'https://mobile-apps-bff.gumtree.com/v1'
    description: Mobile BFF production environment base url
components:
  parameters:
    PlatformHeader:
      in: header
      name: Platform
      description: The type of platform - ios or android
      required: true
      schema:
        type: string
    DeviceTypeHeader:
      in: header
      name: Device-Type
      description: The type of device - mobile or tablet
      required: false
      schema:
        type: string
        default: 'mobile'
    AuthUserConsentHeader:
      in: header
      name: Auth-User-Consent
      description: The consent string retrieved from OneTrust
      required: false
      schema:
        type: string
    UDIDHeader:
      in: header
      name: UDID
      description: The Universal device ID
      required: true
      schema:
        type: string
        default: '315d63b3-9be7-4f42-b294-74b8d1630bb4'
    ExperimentsHeader:
      in: header
      name: Experiments
      description: Comma separated list with all client AB Tests
      required: false
      schema:
        type: string
        default: 'GTNA-19486.A,GTNA-25503.B,GTNA-18299.B'
    SearchKeywordQuery:
      in: query
      name: keyword
      description: Search term
      required: false
      schema:
        type: string
    MinPriceQuery:
      in: query
      name: minPrice
      description: Minimum Price
      required: false
      schema:
        type: string
    MaxPriceQuery:
      in: query
      name: maxPrice
      description: Maximum Price
      required: false
      schema:
        type: string
    SortTypeQuery:
      in: query
      name: sortType
      description: Sort type
      required: false
      schema:
        type: string
        enum:
          - DATE_DESCENDING
          - PRICE_DESCENDING
          - PRICE_ASCENDING
          - DISTANCE_ASCENDING
        default: DATE_DESCENDING
    CategoryIdQuery:
      in: query
      name: categoryId
      description: Category ID
      required: false
      schema:
        type: integer
        format: int64
    LocationIdQuery:
      in: query
      name: locationId
      description: Location ID
      required: false
      schema:
        type: integer
        format: int64
    LocationTypeQuery:
      in: query
      name: locationType
      required: false
      schema:
        type: string
        enum:
          - LOCATION
          - POSTCODE
          - OUTCODE
    PageQuery:
      in: query
      name: page
      description: Specify which page of the list with items
      required: false
      schema:
        type: integer
        format: int32
    ConsentString:
      in: header
      name: Authorisation-User-Consent
      description: tcf string from one trust in the apps
      required: false
      schema:
        type: string
    Device:
      in: header
      name: Device
      description: type of device (i.e., mobile or tablet)
      required: false
      schema:
        type: string
        enum:
          - MOBILE
          - TABLET
  schemas:
    ScreenResponse:
      type: object
      properties:
        portraitData:
          $ref: '#/components/schemas/RowLayoutItems'
        landscapeData:
          $ref: '#/components/schemas/RowLayoutItems'
        stickyBar:
          $ref: '#/components/schemas/StickyBar'
        nextPage:
          type: string
        screenViewAnalyticsEvent:
          $ref: '#/components/schemas/AnalyticsEventData'
        screenViewAnalyticsEvents:
          type: array
          items:
            $ref: '#/components/schemas/AnalyticsEventData'
        analyticsParameters:
          type: object
          additionalProperties:
            type: string
        gamAdvertsData:
          type: array
          items:
            $ref: '#/components/schemas/GAMAdvertDto'
        adjustTrackingData:
          $ref: '#/components/schemas/AdjustTrackingData'
      required:
        - portraitData
    RowLayoutItems:
      type: array
      items:
        $ref: '#/components/schemas/Row'
    Row:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_ROW
            - LISTING_ROW_LARGE
            - ADVERTISING_ROW
            - SAVE_SEARCH_BANNER_ROW
            - SEARCH_NO_RESULTS_ROW
            - EXTENDED_RESULTS_HEADER_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/ListingCardDto'
              - $ref: '#/components/schemas/ListingCardLargeDto'
              - $ref: '#/components/schemas/BingAdvertCardDto'
              - $ref: '#/components/schemas/GAMAdvertDto'
              - $ref: '#/components/schemas/SaveSearchBannerCard'
              - $ref: '#/components/schemas/SearchNoResultsCard'
              - $ref: '#/components/schemas/ExtendedResultsHeaderDto'
        scrollingBehaviour:
          type: string
          enum:
            - COLLAPSE_AT_TOP
            - COLLAPSE_ANYWHERE
        topDivider:
          type: boolean
        bottomDivider:
          type: boolean
      required:
        - type
        - data
    ListingCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_CARD
        adId:
          type: integer
          format: int64
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        location:
          type: string
        price:
          type: string
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
        isUrgent:
          type: boolean
        isFeatured:
          type: boolean
      required:
        - type
        - adId
        - title
        - destination
    ListingCardLargeDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - LISTING_CARD_LARGE
        adId:
          type: integer
          format: int64
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        location:
          type: string
        price:
          type: string
        priceInfo:
          $ref: '#/components/schemas/PriceInfoDto'
        images:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Image'
        isUrgent:
          type: boolean
        isFeatured:
          type: boolean
        timePostedLabel:
          type: string
        attributes:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/Label'
      required:
        - type
        - adId
        - title
        - destination
        - timePostedLabel
    PriceInfoDto:
      type: object
      properties:
        amount:
          type: string
        vat:
          type: string
        tooltipText:
          type: string
      required:
        - amount
    Label:
      type: object
      properties:
        type:
          type: string
          enum:
            - LABEL
        text:
          $ref: '#/components/schemas/TextSegment'
        iconStart:
          type: string
          enum:
            - CALENDAR
            - SPEEDOMETER
            - TRANSMISSION
            - BUILDING
            - FUEL
        labelStyle:
          type: string
          enum:
            - TEXT_ONLY
            - BACKGROUND
        labelType:
          type: string
          enum:
            - ATTRIBUTE
            - FEATURED
            - SPOTLIGHT
            - BUMP_UP
            - SOLD
            - URGENT
            - EXPIRED
            - DELETED
            - REMOVED
            - DRAFT
      required:
        - text
        - labelType
        - labelStyle
    BingAdvertCardDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - BING_ADVERT
        url:
          type: string
      required:
        - type
        - url
    AdjustTrackingData:
      type: object
      properties:
        eventToken:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
      required:
        - eventToken
    GAMAdvertDto:
      type: object
      properties:
        type:
          type: string
          enum:
            - GAM_ADVERT
        androidUnitId:
          type: string
        iosUnitId:
          type: string
        slotName:
          type: string
        displaySize:
          type: string
          enum:
            - Ad300x250
            - Ad320x50
        pageUrl:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        key:
          type: string
        addApptrPlacementId:
          type: string
      required:
        - type
        - androidUnitId
        - iosUnitId
        - slotName
        - displaySize
        - attributes
    Image:
      type: object
      properties:
        url:
          type: string
        width:
          type: integer
        height:
          type: integer
      required:
        - url
    SaveSearchBannerCard:
      type: object
      properties:
        type:
          type: string
          enum:
            - SAVE_SEARCH_BANNER_CARD
        text:
          type: string
        buttonTitle:
          type: string
      required:
        - type
        - text
        - buttonTitle
    SearchNoResultsRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - SEARCH_NO_RESULTS_ROW
        data:
          type: array
          items:
            $ref: '#/components/schemas/SearchNoResultsCard'
    SearchNoResultsCard:
      type: object
      properties:
        type:
          type: string
          enum:
            - SEARCH_NO_RESULTS_CARD
        title:
          type: object
          properties:
            text:
              type: string
            query:
              type: string
        subtitle:
          type: string
        buttonTitle:
          type: string
    StickyBar:
      type: array
      items:
        anyOf:
          - $ref: '#/components/schemas/ChipsRow'
          - $ref: '#/components/schemas/SortHeaderRow'
    ChipsRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - CHIPS_ROW
        data:
          type: array
          items:
            anyOf:
              - $ref: '#/components/schemas/StandardChipDto'
              - $ref: '#/components/schemas/OptionalChipDto'
    StandardChipDto:
      type: object
      properties:
        title:
          type: string
        destination:
          $ref: '#/components/schemas/DestinationDto'
        secondaryText:
          type: string
        iconType:
          type: string
          enum:
            - FILTER
            - LOCATION
      required:
        - title
        - destination
    OptionalChipDto:
      type: object
      properties:
        title:
          type: string
        params:
          type: string
      required:
        - title
        - params
    SortHeaderRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - SORT_HEADER_ROW
        data:
          type: array
          items:
            $ref: '#/components/schemas/SortHeaderDto'
    SortHeaderDto:
      type: object
      properties:
        amountOfResults:
          type: string
        currentlySelected:
          type: string
        sortTypes:
          type: object
          additionalProperties:
            type: string
        analyticsEventData:
          $ref: '#/components/schemas/AnalyticsEventData'
      required:
        - amountOfResults
        - currentlySelected
        - sortTypes
    ExtendedResultsHeaderRow:
      type: object
      properties:
        type:
          type: string
          enum:
            - EXTENDED_RESULTS_HEADER_ROW
        data:
          type: array
          items:
            $ref: '#/components/schemas/ExtendedResultsHeaderDto'
    ExtendedResultsHeaderDto:
      type: object
      properties:
        title:
          type: string
        text:
          type: string
      required:
        - title
        - text
    TotalResponse:
      type: object
      properties:
        numberFound:
          type: integer
          format: int64
        displayNumberFound:
          type: string
      required:
        - numberFound
        - displayNumberFound
    DestinationDto:
      type: object
      properties:
        route:
          type: string
        type:
          type: string
          enum:
            - SCREEN
            - EXTERNAL_LINK
            - EXTERNAL_LINK_DIALOG
            - ACTION
      required:
        - route
        - type
    AnalyticsEventData:
      type: object
      properties:
        eventName:
          type: string
        parameters:
          type: object
          additionalProperties:
            type: string
      required:
        - eventName
    TextSegment:
      type: object
      properties:
        text:
          type: string
        typography:
          $ref: '#/components/schemas/Typography'
      required:
        - text
        - typography
    Typography:
      type: string
      enum:
        - DISPLAY_LARGE
        - DISPLAY_SMALL
        - HEADING_LARGE
        - HEADING_MEDIUM
        - HEADING_SMALL
        - CARD_TITLE_LARGE
        - CARD_TITLE_SMALL
        - BODY_LARGE_REGULAR
        - BODY_LARGE_SEMIBOLD
        - BODY_SMALL_REGULAR
        - BODY_SMALL_SEMIBOLD
        - CAPTION_LARGE_REGULAR
        - CAPTION_LARGE_SEMIBOLD
        - CAPTION_SMALL_REGULAR
        - CAPTION_SMALL_SEMIBOLD
        - LINK_LARGE
        - LINK_SMALL
    Error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
paths:
  /srp/screen:
    get:
      summary: Get the Mobile apps SRP screen (contains all UI items about the SRP screen)
      parameters:
        - $ref: '#/components/parameters/PlatformHeader'
        - $ref: '#/components/parameters/DeviceTypeHeader'
        - $ref: '#/components/parameters/AuthUserConsentHeader'
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/SearchKeywordQuery"
        - $ref: "#/components/parameters/SortTypeQuery"
        - $ref: "#/components/parameters/MinPriceQuery"
        - $ref: "#/components/parameters/MaxPriceQuery"
        - $ref: "#/components/parameters/CategoryIdQuery"
        - $ref: "#/components/parameters/LocationIdQuery"
        - $ref: "#/components/parameters/PageQuery"
        - $ref: "#/components/parameters/ConsentString"
        - $ref: "#/components/parameters/Device"
      responses:
        '200':
          description: Get the mobile apps SRP screen (contains all UI items within the SRP screen)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScreenResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /srp/totalResults:
    get:
      summary: Get the total search results number
      parameters:
        - $ref: "#/components/parameters/UDIDHeader"
        - $ref: "#/components/parameters/ExperimentsHeader"
        - $ref: "#/components/parameters/SearchKeywordQuery"
        - $ref: "#/components/parameters/SortTypeQuery"
        - $ref: "#/components/parameters/MinPriceQuery"
        - $ref: "#/components/parameters/MaxPriceQuery"
        - $ref: "#/components/parameters/CategoryIdQuery"
        - $ref: "#/components/parameters/LocationIdQuery"
      responses:
        '200':
          description: Get the total found results of a search
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TotalResponse"
        '400':
          description: Bad request error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"