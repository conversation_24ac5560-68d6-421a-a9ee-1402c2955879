package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Attribute;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.Root;

import java.util.ArrayList;
import java.util.List;

import static com.gumtree.mobile.api.common.ConstantsKt.UNSUPPORTED;

@Root(strict = false)
public class SupportedValuesContainer {
    @Namespace(reference = Namespaces.TYPES)
    @ElementList(entry = "supported-value", inline = true, required = false, empty = false)
    public List<SupportedValue> supportedValues = new ArrayList<>();

    public String getLabelForValue(String value) {
        SupportedValue found = getForValue(value);
        return found == null ? "" : found.localizedLabel;
    }

    public int getIndexOfValue(String value) {
        for (int i = 0; i < supportedValues.size(); i++) {
            if (supportedValues.get(i).value.equalsIgnoreCase(value)) {
                return i;
            }
        }
        return 0;
    }

    public SupportedValue getForValue(String value) {
        for (SupportedValue supportedValue : supportedValues) {
            if (supportedValue.value.equalsIgnoreCase(value)) {
                return supportedValue;
            }
        }
        return null;
    }

    @Attribute(name = "search-param", required = false)
    public String searchParam = UNSUPPORTED;

    @Attribute(name = "write", required = false)
    public String write = UNSUPPORTED;

    public boolean supportsPost(){
        return !UNSUPPORTED.equals(write);
    }

    public boolean supportsSearch(){
        return !UNSUPPORTED.equals(searchParam);
    }
}

