package com.gumtree.mobile.features.srp

import com.gumtree.mobile.common.Distance
import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.appendDefaultParamsIfNotProvided
import com.gumtree.mobile.utils.extensions.compareMinMaxParams
import com.gumtree.mobile.utils.extensions.filterAny
import com.gumtree.mobile.utils.extensions.readAllQueryParams
import com.gumtree.mobile.utils.extensions.readPagingParams
import com.gumtree.mobile.utils.extensions.readUserProfileData
import com.gumtree.mobile.utils.extensions.remove
import com.gumtree.mobile.utils.extensions.respondSuccess
import com.gumtree.mobile.utils.extensions.stripSearchCategoryIfProvidedAndCategoryIdFound
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import org.koin.ktor.ext.inject
import java.util.*

const val SRP_PATH = "$API_V1_MAIN_PATH/srp"
const val SRP_SCREEN_PATH = "$SRP_PATH/screen"
const val SRP_TOTAL_RESULTS_PATH = "$SRP_PATH/totalResults"

fun Route.srpRoute() {
    val repository by inject<SrpRepository>()

    get(SRP_SCREEN_PATH) {
        val callHeaders: Headers = call.request.headers
        val (pageNumber, pageSize) = call.readPagingParams()
        val requestId = UUID.randomUUID().toString()
        val userProfile = call.readUserProfileData()
        val searchParametersWithoutPaging = call
            .readAllQueryParams()
            .filterAny()
            .compareMinMaxParams()
            .stripSearchCategoryIfProvidedAndCategoryIdFound()
            .appendDefaultParamsIfNotProvided(ApiQueryParams.DISTANCE to Distance.NATIONWIDE.toString())
            .remove(
                ApiQueryParams.PAGE,
                ApiQueryParams.SIZE,
            )
            .plus(ApiQueryParams.REQUEST_ID to requestId)
        val response = repository.readScreen(
            callHeaders,
            searchParametersWithoutPaging,
            pageNumber,
            pageSize,
            userProfile,
        )

        call.respondSuccess(response)
    }

    get(SRP_TOTAL_RESULTS_PATH) {
        val callHeaders: Headers = call.request.headers
        val searchParameters = call
            .readAllQueryParams()
            .filterAny()
        val response = repository.readTotalResults(callHeaders, searchParameters)

        call.respondSuccess(response)
    }
}
