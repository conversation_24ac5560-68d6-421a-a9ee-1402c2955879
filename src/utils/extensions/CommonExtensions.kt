package com.gumtree.mobile.utils.extensions

import kotlin.contracts.contract

/**
 * Checks if an object is NULL
 * @return - true if the object is NULL or false otherwise
 */
fun <T : Any> T?.isNull(): Boolean {
    contract {
        returns(true) implies(this@isNull == null)
    }
    return this == null
}

/**
 * Checks if an object is not NULL
 * @return - true if the object is not NULL or false otherwise
 */
fun <T : Any> T?.isNotNull(): Boolean {
    contract {
        returns(true) implies(this@isNotNull != null)
    }
    return this != null
}

/**
 * Checks if String is not null and not empty
 * @return - true if the string is not null and not empty or false otherwise
 */
fun String?.isNotNullOrEmpty(): Bo<PERSON>an {
    contract {
        returns(true) implies (this@isNotNullOrEmpty != null)
    }

    return !this.isNullOrEmpty()
}

/**
 * Checks if a List is NOT empty and returns it, or if it is empty returns NULL instead
 * @return - the same List if NOT empty or NULL if the List is empty
 */
fun <T> List<T>.thisOrNullIfEmpty(): List<T>? {
    return when {
        isEmpty() -> null
        else -> this
    }
}
