package features.screens.factories

import com.gumtree.mobile.features.screens.factories.LinkFactory
import com.gumtree.mobile.features.screens.layoutsData.LinkCardDto
import com.gumtree.mobile.features.settings.SettingsScreenUiConfiguration
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationDto
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.CategoryDefaults
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Then
import tools.When
import tools.runUnitTest

class LinkFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return setting link card with text and destination`() {
        runUnitTest(robot) {
            When {
                buildSettingLinkCard(
                    text = SettingsScreenUiConfiguration.LOGOUT_TEXT,
                    destination = DestinationRoute.LOGOUT.buildAction()
                )
            }
            Then { checkSettingLinkCardText(SettingsScreenUiConfiguration.LOGOUT_TEXT) }
            Then { checkSettingLinkCardDestinationRoute(DestinationRoute.LOGOUT) }
        }
    }

    @Test
    fun `should return text link card with text and destination`() {
        runUnitTest(robot) {
            When {
                buildTextLinkCard(
                    text = SettingsScreenUiConfiguration.DELETE_ACCOUNT_TEXT,
                    destination = DestinationRoute.DELETE_ACCOUNT_CONFIRMATION.buildAction()
                )
            }
            Then { checkTextLinkCardText(SettingsScreenUiConfiguration.DELETE_ACCOUNT_TEXT) }
            Then { checkTextLinkCardDestinationRoute(DestinationRoute.DELETE_ACCOUNT_CONFIRMATION) }
        }
    }

    @Test
    fun `should return filter link card with text, two params, destination, subtitle and change action`() {
        runUnitTest(robot) {
            When {
                buildFilterLinkCard(
                    text = DataFactory.SOME_USER_EMAIL,
                    params = listOf(ApiQueryParams.USER_ID, ApiQueryParams.CATEGORY_ID),
                    destination = DestinationRoute.LOGOUT.buildAction(),
                    subTitle = DataFactory.SOME_USER_FIRST_NAME,
                    changeAction = LinkCardDto.Filter.Action.REFRESH
                )
            }
            Then { checkFilterLinkCardText(DataFactory.SOME_USER_EMAIL) }
            Then { checkFilterLinkCardDestinationRoute(DestinationRoute.LOGOUT) }
            Then { checkFilterLinkCardParamsSize(2) }
            Then { checkFilterLinkCardSubtitle(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkFilterLinkCardChangeAction(LinkCardDto.Filter.Action.REFRESH) }
        }
    }

    @Test
    fun `should return filter link card with RESET change action`() {
        runUnitTest(robot) {
            When {
                buildFilterLinkCard(
                    text = DataFactory.SOME_USER_EMAIL,
                    params = listOf(ApiQueryParams.USER_ID, ApiQueryParams.CATEGORY_ID),
                    destination = DestinationRoute.LOGOUT.buildAction(),
                    subTitle = DataFactory.SOME_USER_FIRST_NAME,
                    changeAction = LinkCardDto.Filter.Action.RESET
                )
            }
            Then { checkFilterLinkCardText(DataFactory.SOME_USER_EMAIL) }
            Then { checkFilterLinkCardDestinationRoute(DestinationRoute.LOGOUT) }
            Then { checkFilterLinkCardParamsSize(2) }
            Then { checkFilterLinkCardSubtitle(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkFilterLinkCardChangeAction(LinkCardDto.Filter.Action.RESET) }
        }
    }

    @Test
    fun `should return filter link card with text, one param and destination`() {
        runUnitTest(robot) {
            When {
                buildFilterLinkCard(
                    text = CategoryDefaults.PETS.text,
                    params = listOf(ApiQueryParams.CATEGORY_ID),
                    destination = DestinationRoute.SET_CATEGORY.buildAction(),
                    subTitle = null,
                    changeAction = null
                )
            }
            Then { checkFilterLinkCardText(CategoryDefaults.PETS.text) }
            Then { checkFilterLinkCardDestinationRoute(DestinationRoute.SET_CATEGORY) }
            Then { checkFilterLinkCardParamsSize(1) }
            Then { checkFilterLinkCardSubtitle(null) }
            Then { checkFilterLinkCardChangeAction(null) }
        }
    }

    @Test
    fun `should return filter attribute link card with text, destination, isSelected and selectedValue`() {
        runUnitTest(robot) {
            When {
                buildFilterAttributeLinkCard(
                    text = "Up to 1 year",
                    destination = DestinationRoute.FILTER.buildAction(),
                    isSelected = true,
                    selectedValue = "up_to_1"
                )
            }
            Then { checkFilterAttributeLinkCardText("Up to 1 year") }
            Then { checkFilterAttributeLinkCardDestinationRoute(DestinationRoute.FILTER) }
            Then { checkFilterAttributeLinkCardIsSelected(true) }
            Then { checkFilterAttributeLinkCardSelectedValue("up_to_1") }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualLinkCardResult: LinkCardDto

        private val testSubject = LinkFactory()

        fun buildSettingLinkCard(
            text: String,
            destination: DestinationDto
        ) {
            actualLinkCardResult = testSubject.buildSettingLinkCard(text, destination) as LinkCardDto
        }

        fun buildTextLinkCard(
            text: String,
            destination: DestinationDto
        ) {
            actualLinkCardResult = testSubject.buildTextLinkCard(text, destination) as LinkCardDto
        }

        fun buildFilterLinkCard(
            text: String,
            params: List<String>,
            destination: DestinationDto,
            subTitle: String?,
            changeAction: LinkCardDto.Filter.Action?,
        ) {
            actualLinkCardResult = testSubject.buildFilterLinkCard(text, params, destination, null, subTitle, changeAction) as LinkCardDto
        }

        fun buildFilterAttributeLinkCard(
            text: String,
            destination: DestinationDto,
            isSelected: Boolean,
            selectedValue: String?,
        ) {
            actualLinkCardResult = testSubject.buildFilterAttributeLinkCard(text, destination, isSelected, selectedValue) as LinkCardDto
        }

        fun checkSettingLinkCardText(expected: String) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.Setting).text)
        }

        fun checkSettingLinkCardDestinationRoute(expected: DestinationRoute) {
            assertEquals(expected.screenName, (actualLinkCardResult as LinkCardDto.Setting).destination.route)
        }

        fun checkTextLinkCardText(expected: String) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.Text).text)
        }

        fun checkTextLinkCardDestinationRoute(expected: DestinationRoute) {
            assertEquals(expected.screenName, (actualLinkCardResult as LinkCardDto.Text).destination.route)
        }

        fun checkFilterLinkCardText(expected: String) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.Filter).text)
        }

        fun checkFilterLinkCardDestinationRoute(expected: DestinationRoute) {
            assertEquals(expected.screenName, (actualLinkCardResult as LinkCardDto.Filter).destination.route)
        }

        fun checkFilterLinkCardParamsSize(expected: Int) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.Filter).params.size)
        }

        fun checkFilterLinkCardSubtitle(expected: String?) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.Filter).subTitle)
        }

        fun checkFilterLinkCardChangeAction(expected: LinkCardDto.Filter.Action?) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.Filter).changeAction)
        }

        fun checkFilterAttributeLinkCardText(expected: String) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.FilterAttribute).text)
        }

        fun checkFilterAttributeLinkCardDestinationRoute(expected: DestinationRoute) {
            assertEquals(expected.screenName, (actualLinkCardResult as LinkCardDto.FilterAttribute).destination.route)
        }

        fun checkFilterAttributeLinkCardIsSelected(expected: Boolean) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.FilterAttribute).isSelected)
        }

        fun checkFilterAttributeLinkCardSelectedValue(expected: String?) {
            assertEquals(expected, (actualLinkCardResult as LinkCardDto.FilterAttribute).selectedValue)
        }
    }
}