package features.srp

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.api.capi.CapiApiParams
import com.gumtree.mobile.api.capi.models.AdStatus
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.screens.layoutsData.SrpSortHeaderDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.srp.ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME
import com.gumtree.mobile.features.srp.SrpScreenUiConfiguration
import com.gumtree.mobile.features.srp.SrpSortHeaderFactory
import com.gumtree.mobile.responses.QueryParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DEFAULT_LOCATION_ID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.rawDataFactory.RawCapiAdsFactory
import tools.runUnitTest

class SrpSortHeaderFactoryTest {

    private val robot = Robot()

    @Test
    fun `should return sticky bar with sort header row expected data`() {
        runUnitTest(robot) {
            Given { stubRawAds(32) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        ApiQueryParams.SORT_TYPE to SrpScreenUiConfiguration.PRICE_DESCENDING_KEY,
                        ApiQueryParams.LOCATION_ID to "1234"
                    )
                )
            }
            Given { stubAnalyticsEventData(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            When { createSrpStickyBar() }
            Then { checkSortHeaderAmountOfResults("32 results") }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.DATE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.DATE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.PRICE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.PRICE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.PRICE_ASCENDING_KEY,
                    SrpScreenUiConfiguration.PRICE_ASCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY,
                    SrpScreenUiConfiguration.DISTANCE_ASCENDING_VALUE
                )
            }
            Then { checkSortHeaderCurrentlySelected(SrpScreenUiConfiguration.PRICE_DESCENDING_KEY) }
            Then { checkSortHeaderAnalyticsEventName(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
        }
    }

    @Test
    fun `should not have distance sort option when location is UK`() {
        runUnitTest(robot) {
            Given { stubRawAds(32) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        ApiQueryParams.LOCATION_ID to DEFAULT_LOCATION_ID
                    )
                )
            }
            Given { stubAnalyticsEventData(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            When { createSrpStickyBar() }
            Then { checkSortTypeNotInList(SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY) }
        }
    }

    @Test
    fun `should not use distance sort option when location is UK if already currently selected`() {
        runUnitTest(robot) {
            Given { stubRawAds(32) }
            Given { stubSearchParams(
                hashMapOf(
                    ApiQueryParams.SORT_TYPE to SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY,
                    ApiQueryParams.LOCATION_ID to DEFAULT_LOCATION_ID,
                    CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE to "true",
                )
            )}
            Given { stubAnalyticsEventData(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            When { createSrpStickyBar() }
            Then { checkSortTypeNotInList(SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY) }
            Then { checkSortHeaderCurrentlySelected(SrpScreenUiConfiguration.RELEVANCE_DESCENDING_KEY) }
        }
    }

    @Test
    fun `should not have distance sort option when locationId is null`() {
        runUnitTest(robot) {
            Given { stubRawAds(32) }
            Given {
                stubSearchParams(
                    hashMapOf(ApiQueryParams.LOCATION_ID to null)
                )
            }
            Given { stubAnalyticsEventData(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            When { createSrpStickyBar() }
            Then { checkSortTypeNotInList(SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY) }
        }
    }

    @Test
    fun `should return Most relevant first sort type header row expected data if hitMostRelevanceFlag is true`() {
        runUnitTest(robot) {
            Given { stubRawAds(32) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE to true.toString(),
                        ApiQueryParams.LOCATION_ID to "1234"
                    )
                )
            }
            Given { stubAnalyticsEventData(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            When { createSrpStickyBar() }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.RELEVANCE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.RELEVANCE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.DATE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.DATE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.PRICE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.PRICE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.PRICE_ASCENDING_KEY,
                    SrpScreenUiConfiguration.PRICE_ASCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY,
                    SrpScreenUiConfiguration.DISTANCE_ASCENDING_VALUE
                )
            }
            Then { checkSortHeaderCurrentlySelected(SrpScreenUiConfiguration.RELEVANCE_DESCENDING_KEY) }
        }
    }

    @Test
    fun `should not return Most relevant first sort type header row expected data if hitMostRelevanceFlag is false`() {
        runUnitTest(robot) {
            Given { stubRawAds(32) }
            Given {
                stubSearchParams(
                    hashMapOf(
                        CapiApiParams.IS_HIT_SHOW_MOST_RELEVANCE to false.toString(),
                        ApiQueryParams.LOCATION_ID to "1234"
                    )
                )
            }
            Given { stubAnalyticsEventData(ANALYTICS_SORT_SEARCH_RESULTS_EVENT_NAME) }
            When { createSrpStickyBar() }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.DATE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.DATE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.PRICE_DESCENDING_KEY,
                    SrpScreenUiConfiguration.PRICE_DESCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.PRICE_ASCENDING_KEY,
                    SrpScreenUiConfiguration.PRICE_ASCENDING_VALUE
                )
            }
            Then {
                checkSortHeaderSortType(
                    SrpScreenUiConfiguration.DISTANCE_ASCENDING_KEY,
                    SrpScreenUiConfiguration.DISTANCE_ASCENDING_VALUE
                )
            }
            Then { checkSortHeaderCurrentlySelected(SrpScreenUiConfiguration.DATE_DESCENDING_KEY) }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var actualSortHeaderResult: List<UiItem>

        private lateinit var rawAds: RawCapiAdList
        private lateinit var searchParams: QueryParams
        private lateinit var analyticsEventData: AnalyticsEventData

        private val testSubject = SrpSortHeaderFactory()

        fun stubRawAds(number: Int) {
            rawAds = RawCapiAdsFactory.createRawCapiAdList(number, listOf(AdStatus.ACTIVE))
        }

        fun stubSearchParams(params: QueryParams) {
            searchParams = params
        }

        fun stubAnalyticsEventData(eventName: String) {
            analyticsEventData = AnalyticsEventData(eventName = eventName)
        }

        fun createSrpStickyBar() {
            actualSortHeaderResult = testSubject.buildSrpSortHeader(rawAds, searchParams, analyticsEventData)
        }

        fun checkSortHeaderAmountOfResults(expected: String) {
            assertEquals(expected, (actualSortHeaderResult[0] as SrpSortHeaderDto).amountOfResults)
        }

        fun checkSortHeaderAnalyticsEventName(expected: String) {
            assertEquals(expected, (actualSortHeaderResult[0] as SrpSortHeaderDto).analyticsEventData?.eventName)
        }

        fun checkSortHeaderSortType(
            key: String,
            expected: String,
        ) {
            assertEquals(expected, (actualSortHeaderResult[0] as SrpSortHeaderDto).sortTypes[key])
        }

        fun checkSortTypeNotInList(
            key: String,
        ) {
            assertFalse((actualSortHeaderResult[0] as SrpSortHeaderDto).sortTypes.containsKey(key))
        }

        fun checkSortHeaderCurrentlySelected(expected: String) {
            assertEquals(expected, (actualSortHeaderResult[0] as SrpSortHeaderDto).currentlySelected)
        }
    }
}