package com.gumtree.mobile.features.vip

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.utils.extensions.data.toVipTitleCardDto

/**
 * Mapping the RawCapiAd data into VipTitleCardDto mobile application data
 */
class VipTitleMapper {

    fun map(
        rawData: RawCapiAd,
    ): RowLayout<UiItem> {
        val vipTitleCardDto = rawData.toVipTitleCardDto()
        return RowLayout(
            type = RowLayoutType.VIP_TITLE_ROW,
            data = listOf(vipTitleCardDto),
        )
    }
}
