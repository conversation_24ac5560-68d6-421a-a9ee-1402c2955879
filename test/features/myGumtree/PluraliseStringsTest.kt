package features.myGumtree

import com.gumtree.mobile.features.myGumtree.pluraliseString
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.When
import tools.runUnitTest

class PluraliseStringsTest {

    private val robot = Robot()

    @Test
    fun `should use singular string when number is 1`() {
        runUnitTest(robot) {
            When { pluraliseStringBasedOnNumber(1, " apple", " apples") }
            Then { checkPluralisedString("1 apple") }

            When { pluraliseStringBasedOnNumber(1, " singular text", " plural texts") }
            Then { checkPluralisedString("1 singular text") }
        }
    }

    @Test
    fun `should use plural string when number is NOT 1`() {
        runUnitTest(robot) {
            When { pluraliseStringBasedOnNumber(0, " result", " results") }
            Then { checkPluralisedString("0 results") }

            When { pluraliseStringBasedOnNumber(2, " apple", " apples") }
            Then { checkPluralisedString("2 apples") }

            When { pluraliseStringBasedOnNumber(5, " car", " cars") }
            Then { checkPluralisedString("5 cars") }

            When { pluraliseStringBasedOnNumber(100, " job", " jobs") }
            Then { checkPluralisedString("100 jobs") }

            When { pluraliseStringBasedOnNumber(234, " singular text", " plural texts") }
            Then { checkPluralisedString("234 plural texts") }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var actualPluralisedStringResult: String

        fun pluraliseStringBasedOnNumber(
            number: Int,
            singularString: String,
            pluralString: String,
        ) {
            actualPluralisedStringResult = pluraliseString(number, singularString, pluralString)
        }

        fun checkPluralisedString(expected: String) {
            assertEquals(expected, actualPluralisedStringResult)
        }
    }
}
