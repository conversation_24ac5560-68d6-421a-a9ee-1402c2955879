package features.reviews

import com.gumtree.mobile.features.reviews.CreateConversationReviewRequest
import com.gumtree.mobile.features.reviews.CreateReviewResponseDto
import com.gumtree.mobile.features.reviews.REVIEWS_CONVERSATION_PATH
import com.gumtree.mobile.features.reviews.ReviewsRepository
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.responses.ScreensFlowResponse
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.ID_PATH
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.server.plugins.*
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.dsl.module
import tools.DataFactory
import tools.defaultHttpClient
import tools.jsonHttpClient
import tools.routes.And
import tools.routes.BaseRouteRobot
import tools.routes.Given
import tools.routes.Then
import tools.routes.When
import tools.routes.runRouteTest
import tools.routes.runRouteTestForException
import tools.routes.runUnitTest

class ReviewsRouteTest {

    private val robot = Robot()

    private val module = module {
        single { robot.repository }
    }

    @Test
    fun `should complete GET conversation review flow screens request with success`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreensFlowData() }
            And { stubRepositoryReadConversationReviewFlowResponse() }
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = DataFactory.SOME_RATING,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    /*@Test
    fun `should complete GET conversation review flow screens request with error if rating is NOT provided`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreensFlowData() }
            And { stubRepositoryReadConversationReviewFlowResponse() }
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }*/

    @Test
    fun `should complete GET conversation review flow screens request with error`() = runRouteTestForException(module, UnauthorisedException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreensFlowData() }
            Given { stubRepositoryReadConversationReviewFlowResponseWithError(UnauthorisedException()) }
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET conversation review flow screens request with success if rating is NOT provided`() = runRouteTest(module) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreensFlowData() }
            And { stubRepositoryReadConversationReviewFlowResponse() }
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET conversation review flow screens request with error if rating is NOT valid`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            Given { stubScreensFlowData() }
            And { stubRepositoryReadConversationReviewFlowResponse() }
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = 10,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.OK) }
        }
    }

    @Test
    fun `should complete GET conversation review flow screens request with error if conversation id path value is NOT is expected format`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, defaultHttpClient()) {
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_AD_ID,
                    rating = 10,
                )
            }
        }
    }

    @Test
    fun `should complete GET conversation review flow screens request with error if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryConversationReviewFlowError(BadRequestException("Invalid data")) }
            When {
                getConversationReviewFlowScreens(
                    userEmail = DataFactory.SOME_USER_EMAIL,
                    userToken = DataFactory.SOME_USER_SECRET,
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = DataFactory.SOME_RATING,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST create conversation review request with success`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateConversationReviewResponse() }
            When {
                postCreateConversationReview(
                    conversationId = DataFactory.ANOTHER_CONVERSATION_ID,
                    rating = DataFactory.SOME_RATING,
                    review = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.Created) }
        }
    }

    @Test
    fun `should complete POST create conversation review request with BadRequest error if repository error`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            Given { stubRepositoryCreateConversationReviewError(BadRequestException("Invalid data")) }
            When {
                postCreateConversationReview(
                    conversationId = DataFactory.ANOTHER_CONVERSATION_ID,
                    rating = DataFactory.SOME_RATING,
                    review = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    @Test
    fun `should complete POST create conversation review request with exception when conversation id path value is NOT in expected format`() = runRouteTestForException(module, IllegalArgumentException::class) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                postCreateConversationReview(
                    conversationId = DataFactory.SOME_SAVED_SEARCH_ID,
                    rating = DataFactory.SOME_RATING,
                    review = null,
                )
            }
        }
    }

    @Test
    fun `should complete POST create conversation review request with BadRequest error when the request body is NOT valid`() = runRouteTest(module) {
        runUnitTest(robot, jsonHttpClient()) {
            When {
                postCreateConversationReview(
                    conversationId = DataFactory.SOME_CONVERSATION_ID,
                    rating = 10,
                    review = null,
                )
            }
            Then { checkResponseStatus(HttpStatusCode.BadRequest) }
        }
    }

    private class Robot: BaseRouteRobot() {
        val repository: ReviewsRepository = mockk(relaxed = true)

        private lateinit var actualResponse: HttpResponse
        private lateinit var screensFlowResponse: List<ScreenResponse>

        fun stubScreensFlowData() {
            screensFlowResponse = emptyList()
        }

        fun stubRepositoryReadConversationReviewFlowResponse() {
            coEvery {
                repository.readConversationReviewFlow(any(), any(), any(), any())
            } returns ScreensFlowResponse(
                flow = screensFlowResponse,
            )
        }

        fun stubRepositoryConversationReviewFlowError(error: Throwable) {
            coEvery {
                repository.readConversationReviewFlow(any(), any(), any(), any())
            } throws error
        }

        fun stubRepositoryReadConversationReviewFlowResponseWithError(error: Throwable) {
            coEvery {
                repository.readConversationReviewFlow(any(), any(), any(), any())
            }  throws error
        }

        fun stubRepositoryCreateConversationReviewResponse() {
            coEvery {
                repository.createConversationReview(any(), any(), any(), any(), any())
            } returns CreateReviewResponseDto(DataFactory.SOME_REVIEW_ID)
        }

        fun stubRepositoryCreateConversationReviewError(error: Throwable) {
            coEvery {
                repository.createConversationReview(any(), any(), any(), any(), any())
            } throws error
        }

        suspend fun getConversationReviewFlowScreens(
            userEmail: String,
            userToken: String,
            conversationId: String,
            rating: Int?,
        ) {
            actualResponse = client.get(REVIEWS_CONVERSATION_PATH.replace("{$ID_PATH}", conversationId)) {
                headers { append(ApiHeaderParams.AUTHORISATION_USER_EMAIL, userEmail) }
                headers { append(ApiHeaderParams.AUTHORISATION_USER_TOKEN, userToken) }
                rating?.let { parameter(ApiQueryParams.RATING, it) }
            }
        }

        suspend fun postCreateConversationReview(
            conversationId: String,
            rating: Int,
            review: CreateConversationReviewRequest.Review?,
        ) {
            actualResponse = client.post(REVIEWS_CONVERSATION_PATH.replace("{$ID_PATH}", conversationId)) {
                setBody(
                    CreateConversationReviewRequest(
                        rating = rating,
                        review = review,
                    )
                )
                contentType(ContentType.Application.Json)
            }
        }

        fun checkResponseStatus(expectedStatusCode: HttpStatusCode) {
            assertEquals(expectedStatusCode, actualResponse.status)
        }
    }
}