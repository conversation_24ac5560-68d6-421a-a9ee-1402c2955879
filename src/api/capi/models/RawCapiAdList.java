package api.capi.models;

import com.gumtree.mobile.api.capi.Namespaces;
import org.simpleframework.xml.Element;
import org.simpleframework.xml.ElementList;
import org.simpleframework.xml.Namespace;
import org.simpleframework.xml.NamespaceList;
import org.simpleframework.xml.Root;

import javax.annotation.Nullable;
import java.util.List;

@Root(name = "ads", strict = false)
@NamespaceList({
        @Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD),
        @Namespace(reference = Namespaces.TYPES, prefix = Namespaces.Prefix.TYPES),
})
@Namespace(reference = Namespaces.AD, prefix = Namespaces.Prefix.AD)
public class RawCapiAdList {

    @ElementList(required = false, inline = true, name = "ad", entry = "ad")
    @Namespace(reference = Namespaces.AD)
    @Nullable
    private List<RawCapiAd> mRawAds;

    @Element(required = false, name = "paging")
    @Namespace(reference = Namespaces.TYPES)
    public RawCapiPaging rawPaging;

    @Element(required = false, name = "ads-search-options")
    @Namespace(reference = Namespaces.AD)
    public RawCapiAdSearchOptions rawCapiAdSearchOptions;

    @Element(required = false, name = "ads-search-histograms")
    @Namespace(reference = Namespaces.AD)
    public RawCapiAdsSearchHistograms rawAdsSearchHistograms;

    @ElementList(required = false, name = "adSlots", entry = "adSlot")
    @Namespace(reference = Namespaces.AD)
    private List<RawCapiAdSlot> mRawAdSlots;

    @Nullable
    public List<RawCapiAd> getRawAds() {
        return mRawAds;
    }

    public List<RawCapiAdSlot> getRawAdSlots() {
        return mRawAdSlots;
    }

    public void setRawAds(@Nullable List<RawCapiAd> rawAds) {
        mRawAds = rawAds;
    }
}
