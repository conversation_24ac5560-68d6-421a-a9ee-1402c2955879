package features.vip.v2

import api.capi.models.RawCapiAd
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.Space
import com.gumtree.mobile.features.screens.Style
import com.gumtree.mobile.features.screens.createSpacing
import com.gumtree.mobile.features.screens.layoutsData.DividerCardDto
import com.gumtree.mobile.features.screens.layoutsData.OverviewCardDto
import com.gumtree.mobile.features.screens.layoutsData.PortfolioImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.PostPublisherInfoCardDto
import com.gumtree.mobile.features.screens.layoutsData.SellerLink
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VipImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.VipSectionTabsCardDto
import com.gumtree.mobile.features.vip.v2.VipAnalyticsProvider
import com.gumtree.mobile.features.vip.v2.VipUiOptimizeMapper
import com.gumtree.mobile.features.vip.v2.VipUiOptimizeProvider
import com.gumtree.mobile.requests.ClientSemantics
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.rawDataFactory.RawCapiAdsFactory

class VipUiOptimizeProviderTest {

    private lateinit var vipUiOptimizeMapper: VipUiOptimizeMapper
    private lateinit var vipAnalyticsProvider: VipAnalyticsProvider
    private lateinit var vipUiOptimizeProvider: VipUiOptimizeProvider
    private lateinit var rawAd: RawCapiAd
    private lateinit var clientSemantics: ClientSemantics

    @BeforeEach
    fun setup() {
        vipUiOptimizeMapper = mockk(relaxed = true)
        vipAnalyticsProvider = mockk(relaxed = true)
        vipUiOptimizeProvider = VipUiOptimizeProvider(vipUiOptimizeMapper, vipAnalyticsProvider)
        rawAd = RawCapiAdsFactory.createRawCapiAd()
        clientSemantics = ClientSemantics()
    }

    @Test
    fun `should return overview row with correct layout type`() {
        // Given
        val sellerLink = mockk<SellerLink>()
        val expectedRowLayout = RowLayout<UiItem>(
            type = RowLayoutType.OVERVIEW_ROW,
            data = listOf(mockk<OverviewCardDto>()),
            style = Style(spacing = createSpacing(topMargin = Space.SMALL))
        )
        every { vipUiOptimizeMapper.mapOverview(rawAd, sellerLink) } returns expectedRowLayout

        // When
        val result = vipUiOptimizeProvider.getVipOverviewRow(rawAd, sellerLink)

        // Then
        assertEquals(RowLayoutType.OVERVIEW_ROW, result.type)
        assertNotNull(result.data.first() as? OverviewCardDto)
    }

    @Test
    fun `should return portfolio image row with correct layout type`() {
        // Given
        val gamAdvertDto = mockk<VipImageCardDto>()
        val expectedRowLayout = RowLayout<UiItem>(
            type = RowLayoutType.PORTFOLIO_IMAGE_ROW,
            data = listOf(mockk<PortfolioImageCardDto>()),
            style = Style(spacing = createSpacing(topMargin = Space.SMALL))
        )
        every { vipUiOptimizeMapper.mapPortfolioImage(rawAd, gamAdvertDto, any()) } returns expectedRowLayout

        // When
        val result = vipUiOptimizeProvider.getVipPortfolioImageRow(rawAd, gamAdvertDto)

        // Then
        assertEquals(RowLayoutType.PORTFOLIO_IMAGE_ROW, result.type)
        assertNotNull(result.data.first() as? PortfolioImageCardDto)
    }

    @Test
    fun `should return section tabs row with correct layout type`() {
        // Given
        val tabIndexMap = mapOf("Overview" to 1, "Portfolio" to 2, "Services" to 3, "Description" to 4)
        val expectedRowLayout = RowLayout<UiItem>(
            type = RowLayoutType.VIP_SECTION_TABS_ROW,
            data = listOf(mockk<VipSectionTabsCardDto>()),
            style = Style(spacing = createSpacing(topMargin = Space.SMALL))
        )
        every { vipUiOptimizeMapper.mapSectionTabs(tabIndexMap) } returns expectedRowLayout

        // When
        val result = vipUiOptimizeProvider.getVipSectionTabsRow(tabIndexMap)

        // Then
        assertEquals(RowLayoutType.VIP_SECTION_TABS_ROW, result.type)
        assertNotNull(result.data.first() as? VipSectionTabsCardDto)
    }

    @Test
    fun `should return publisher info row with correct layout type`() {
        // Given
        val analyticsScreenParams = mapOf("param1" to "value1")
        val expectedRowLayout = RowLayout<UiItem>(
            type = RowLayoutType.POST_PUBLISHER_INFO_ROW,
            data = listOf(mockk<PostPublisherInfoCardDto>()),
            style = Style(spacing = createSpacing(topMargin = Space.SMALL))
        )
        every { vipUiOptimizeMapper.mapPublisherInfo(any(), any(), any()) } returns expectedRowLayout

        // When
        val result = vipUiOptimizeProvider.getVipPublisherInfo(rawAd)

        // Then
        assertEquals(RowLayoutType.POST_PUBLISHER_INFO_ROW, result.type)
        assertNotNull(result.data.first() as? PostPublisherInfoCardDto)
    }

    @Test
    fun `should return divider row with default spacing`() {
        // When
        val result = vipUiOptimizeProvider.createDividerRow()

        // Then
        assertEquals(RowLayoutType.DIVIDER_ROW, result.type)
        val dividerCard = result.data.first() as? DividerCardDto
        assertNotNull(dividerCard)
        assertEquals(DividerCardDto.Thickness.LIGHT, dividerCard?.data?.thickness)
        assertEquals(Space.SMALL, result.style?.spacing?.margins?.top)
        assertEquals(Space.SMALL, result.style?.spacing?.margins?.left)
        assertEquals(Space.SMALL, result.style?.spacing?.margins?.right)
        assertEquals(Space.SMALL, result.style?.spacing?.margins?.bottom)
    }

    @Test
    fun `should return divider row with custom spacing`() {
        // When
        val result = vipUiOptimizeProvider.createDividerRow(
            thickness = DividerCardDto.Thickness.MEDIUM,
            topMargin = Space.MEDIUM,
            leftMargin = Space.LARGE,
            rightMargin = Space.LARGE,
            bottomMargin = Space.X_LARGE
        )

        // Then
        assertEquals(RowLayoutType.DIVIDER_ROW, result.type)
        val dividerCard = result.data.first() as? DividerCardDto
        assertNotNull(dividerCard)
        assertEquals(DividerCardDto.Thickness.MEDIUM, dividerCard?.data?.thickness)
        assertEquals(Space.MEDIUM, result.style?.spacing?.margins?.top)
        assertEquals(Space.LARGE, result.style?.spacing?.margins?.left)
        assertEquals(Space.LARGE, result.style?.spacing?.margins?.right)
        assertEquals(Space.X_LARGE, result.style?.spacing?.margins?.bottom)
    }

    @Test
    fun `should return empty portfolio image row when no valid images`() {
        // Given
        val gamAdvertDto = mockk<VipImageCardDto>()
        val expectedRowLayout = RowLayout<UiItem>(
            type = RowLayoutType.PORTFOLIO_IMAGE_ROW,
            data = emptyList(),
            style = Style(spacing = createSpacing(topMargin = Space.SMALL))
        )
        every { vipUiOptimizeMapper.mapPortfolioImage(rawAd, gamAdvertDto, any()) } returns expectedRowLayout

        // When
        val result = vipUiOptimizeProvider.getVipPortfolioImageRow(rawAd, gamAdvertDto)

        // Then
        assertEquals(RowLayoutType.PORTFOLIO_IMAGE_ROW, result.type)
        assertTrue(result.data.isEmpty())
    }
} 
