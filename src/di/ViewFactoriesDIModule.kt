package com.gumtree.mobile.di

import com.gumtree.mobile.common.AdDetailsProvider
import com.gumtree.mobile.features.categories.CategoryAnalyticsProvider
import com.gumtree.mobile.features.screens.factories.ButtonsFactory
import com.gumtree.mobile.features.screens.factories.CategoryCardFactory
import com.gumtree.mobile.features.screens.factories.DropdownFactory
import com.gumtree.mobile.features.screens.factories.IconFactory
import com.gumtree.mobile.features.screens.factories.ImageFactory
import com.gumtree.mobile.features.screens.factories.InputFactory
import com.gumtree.mobile.features.screens.factories.LinkFactory
import com.gumtree.mobile.features.screens.factories.ReviewsChipsFactory
import com.gumtree.mobile.features.screens.factories.SnackbarFactory
import com.gumtree.mobile.features.screens.factories.SpaceRowFactory
import com.gumtree.mobile.features.screens.factories.StarsFactory
import com.gumtree.mobile.features.screens.factories.StepFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Create and inject instances of some standard UiItem Factories
 */
val viewFactoriesDIModule = module {
    factory { CategoryAnalyticsProvider() }
    factory { ButtonsFactory() }
    factory { SnackbarFactory() }
    factory { TitleFactory() }
    factory { ImageFactory() }
    factory { IconFactory() }
    factory { DropdownFactory() }
    factory { InputFactory() }
    factory { LinkFactory() }
    factory { ReviewsChipsFactory() }
    factory { StepFactory() }
    factory { StarsFactory() }
    factory { CategoryCardFactory(analyticsProvider = getFromKoin()) }

    factory { SpaceRowFactory() }
    factory { AdDetailsProvider() }
}
