package com.gumtree.mobile.api.userService.api

import com.gumtree.mobile.api.userService.UserServiceApiEndpointUrls
import com.gumtree.mobile.api.userService.UserServiceApiParams
import com.gumtree.mobile.api.userService.bodies.UserServiceActivateBody
import com.gumtree.mobile.api.userService.bodies.UserServiceChangePasswordBody
import com.gumtree.mobile.api.userService.bodies.UserServiceForgotPasswordBody
import com.gumtree.mobile.api.userService.bodies.UserServiceLoginRequestBody
import com.gumtree.mobile.api.userService.bodies.UserServiceLogoutRequestBody
import com.gumtree.mobile.api.userService.bodies.UserServiceSocialRegistrationBody
import com.gumtree.mobile.api.userService.models.RawUserServiceAccountDetails
import com.gumtree.mobile.api.userService.models.RawUserServiceAuthenticateCapiToken
import com.gumtree.mobile.api.userService.models.RawUserServiceRegisteredUser
import com.gumtree.mobile.api.userService.models.RawUserServiceTokenWrapper
import com.gumtree.mobile.api.userService.models.RawUserServiceUserDetails
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Path
import utils.ConvertHttpStatus

interface UserServiceApi {
    @POST(UserServiceApiEndpointUrls.AUTHENTICATE_CAPI_TOKEN_ENDPOINT)
    suspend fun authenticateCapiToken(
        @Header(UserServiceApiParams.AUTHORIZATION) capiToken: String,
    ): RawUserServiceAuthenticateCapiToken

    /**
     * Retrieves user details by email.
     *
     * Note: API returns 204 (No Content) with an empty body, for emails no longer in the system, causing thread blocking.
     * We convert 204 to 404 to prevent this issue without modifying the API contract.
     */
    @ConvertHttpStatus(from = 204, to = 404, endpoint = "UserServiceApiEndpointUrls.GET_USER_DETAILS_BY_EMAIL_ENDPOINT")
    @GET(UserServiceApiEndpointUrls.GET_USER_DETAILS_BY_EMAIL_ENDPOINT)
    suspend fun getUserDetails(@Path(UserServiceApiParams.EMAIL) email: String): RawUserServiceUserDetails

    @ConvertHttpStatus(
        from = 204,
        to = 404,
        endpoint = "UserServiceApiEndpointUrls.GET_USER_DETAILS_BY_USER_ID_ENDPOINT",
    )
    @GET(UserServiceApiEndpointUrls.GET_USER_DETAILS_BY_USER_ID_ENDPOINT)
    suspend fun getUserDetailsByUserId(@Path(UserServiceApiParams.USER_ID) userId: String): RawUserServiceUserDetails

    @GET(UserServiceApiEndpointUrls.GET_USER_ACCOUNTS_BY_ACCOUNT_ID_ENDPOINT)
    suspend fun getAccountsByAccountId(
        @Path(UserServiceApiParams.ACCOUNT_ID) accountId: String,
    ): RawUserServiceAccountDetails

    @POST(UserServiceApiEndpointUrls.AUTHENTICATE_CAPI_ENDPOINT)
    suspend fun authenticateUser(@Body requestBody: UserServiceLoginRequestBody): RawUserServiceTokenWrapper

    @POST(UserServiceApiEndpointUrls.LOGOUT_ENDPOINT)
    suspend fun unAuthenticateUser(@Body requestBody: UserServiceLogoutRequestBody): Response<Unit>

    @POST(UserServiceApiEndpointUrls.REGISTER)
    suspend fun register(@Body requestBody: UserServiceSocialRegistrationBody): RawUserServiceRegisteredUser?

    @POST(UserServiceApiEndpointUrls.ACTIVATE)
    suspend fun activate(@Body requestBody: UserServiceActivateBody): Response<Unit>

    @POST(UserServiceApiEndpointUrls.FORGOT_PASSWORD)
    suspend fun sendForgotPassword(@Body requestBody: UserServiceForgotPasswordBody): Response<Unit>

    @POST(UserServiceApiEndpointUrls.CHANGE_PASSWORD)
    suspend fun resetPassword(@Body requestBody: UserServiceChangePasswordBody): Response<Unit>
}
