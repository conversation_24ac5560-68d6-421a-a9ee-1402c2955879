package api.userProfile

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val USER_PROFILE_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "USER_PROFILE_API_BASE_URL"
const val USER_PROFILE_API_QA_PORT = 8090

/**
 * The UserProfile API base url manager
 */
class UserProfileApiBaseUrl : ApiBaseUrl() {

    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(
            USER_PROFILE_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY,
        ).toBaseUrl()
    } // see helm-chart/values.yaml

    override val localUrl: BaseUrl by lazy { LOCAL_HOST_BASE_URL.toBaseUrl() }

    override val localPort: Int = USER_PROFILE_API_QA_PORT
}
