package features.reportChat

import com.gumtree.mobile.api.coreChat.api.CoreChatStreamTokenResponse
import com.gumtree.mobile.api.reportChat.bodies.RawReportChatReason
import com.gumtree.mobile.api.reportChat.bodies.ReportChatRequestBody
import com.gumtree.mobile.api.reportChat.models.RawConversationReportId
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.common.UserProfileData
import com.gumtree.mobile.features.reportChat.DefaultReportChatRepository
import com.gumtree.mobile.features.reportChat.ReportChatBodyCreator
import com.gumtree.mobile.features.reportChat.ReportChatReason
import com.gumtree.mobile.features.reportChat.ReportChatRequest
import com.gumtree.mobile.features.reportChat.ReportChatService
import io.ktor.server.plugins.BadRequestException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.dataFactory.UserProfileDataFactory
import tools.rawDataFactory.UserServiceDataFactory
import tools.runUnitTest
import tools.runUnitTestForException

class DefaultReportChatRepositoryTest {
    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should create chat report with valid request and user profile data`() {
        runUnitTest(robot) {
            Given {
                stubReportChatRequest(
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT
                )
            }
            Given { stubUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubUserProfileData(UserProfileDataFactory.create()) }
            Given { stubBodyCreatorToReturnValidBody() }
            Given { stubCoreChatJwtGeneration(DataFactory.SOME_TOKEN) }
            Given { stubServiceToSucceed() }
            When { createChatReport() }
            Then { verifyBodyCreatorCalled() }
            Then { verifyCoreChatJwtGenerationCalled() }
            Then { verifyServiceCalled() }
        }
    }

    @Test
    fun `should throw BadRequestException when body creator returns null`() {
        runUnitTestForException(robot, BadRequestException::class) {
            Given {
                stubReportChatRequest(
                    "invalid_user_id",
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT
                )
            }
            Given { stubUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubUserProfileData(UserProfileDataFactory.create()) }
            Given { stubBodyCreatorToReturnNull() }
            When { createChatReport() }
            Then { verifyBodyCreatorCalled() }
            Then { verifyServiceNotCalled() }
        }
    }

    @Test
    fun `should propagate exceptions from service`() {
        runUnitTestForException(robot, RuntimeException::class) {
            Given {
                stubReportChatRequest(
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT
                )
            }
            Given { stubUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubUserProfileData(UserProfileDataFactory.create()) }
            Given { stubBodyCreatorToReturnValidBody() }
            Given { stubCoreChatJwtGeneration(DataFactory.SOME_TOKEN) }
            Given { stubServiceToThrowException() }
            When { createChatReport() }
            Then { verifyBodyCreatorCalled() }
            Then { verifyCoreChatJwtGenerationCalled() }
            Then { verifyServiceCalled() }
        }
    }

    @Test
    fun `should propagate exceptions from generateCoreChatJwt`() {
        runUnitTestForException(robot, RuntimeException::class) {
            Given {
                stubReportChatRequest(
                    DataFactory.SOME_USER_ID,
                    DataFactory.SOME_CONVERSATION_ID,
                    DataFactory.SOME_AD_ID,
                    ReportChatReason.FRAUD_SCAM,
                    DataFactory.SOME_COMMENT
                )
            }
            Given { stubUserEmail(DataFactory.SOME_USER_EMAIL) }
            Given { stubUserProfileData(UserProfileDataFactory.create()) }
            Given { stubBodyCreatorToReturnValidBody() }
            Given { stubCoreChatJwtGenerationFailure() }
            When { createChatReport() }
            Then { verifyBodyCreatorCalled() }
            Then { verifyCoreChatJwtGenerationCalled() }
            Then { verifyServiceNotCalled() }
        }
    }

    private class Robot : BaseRobot {
        private lateinit var reportChatRequest: ReportChatRequest
        private lateinit var userEmail: String
        private lateinit var userProfileData: UserProfileData
        private val mockReportChatService: ReportChatService = mockk(relaxed = true)
        private val mockReportChatBodyCreator: ReportChatBodyCreator = mockk(relaxed = true)
        private val testDispatcher: TestDispatcher = UnconfinedTestDispatcher()
        private val mockDispatcherProvider: DispatcherProvider = mockk {
            every { io } returns testDispatcher
        }
        private lateinit var testSubject: DefaultReportChatRepository

        override fun setup() {
            testSubject = DefaultReportChatRepository(
                reportChatService = mockReportChatService,
                reportChatBodyCreator = mockReportChatBodyCreator,
                dispatcherProvider = mockDispatcherProvider
            )
        }

        fun stubReportChatRequest(
            counterPartyUserId: String,
            conversationId: String,
            adId: String,
            reason: ReportChatReason,
            comment: String,
        ) {
            reportChatRequest = ReportChatRequest(
                counterPartyUserId = counterPartyUserId,
                conversationId = conversationId,
                adId = adId,
                reason = reason,
                comment = comment
            )
        }

        fun stubUserEmail(email: String) {
            userEmail = email
        }

        fun stubUserProfileData(userProfile: UserProfileData) {
            userProfileData = userProfile
        }

        fun stubBodyCreatorToReturnValidBody() {
            val body = ReportChatRequestBody(
                converseeUserId = DataFactory.SOME_USER_ID.toInt(),
                conversationId = DataFactory.SOME_CONVERSATION_ID,
                advertId = DataFactory.SOME_AD_ID.toInt(),
                reasons = listOf(RawReportChatReason.FRAUD_SCAM),
                comment = DataFactory.SOME_COMMENT
            )
            every { mockReportChatBodyCreator.createBody(reportChatRequest) } returns body
        }

        fun stubBodyCreatorToReturnNull() {
            every { mockReportChatBodyCreator.createBody(reportChatRequest) } returns null
        }

        fun stubGetUserDetails(userId: String) {
            coEvery { mockReportChatService.getUserDetails(userEmail) } returns UserServiceDataFactory.createRawUserDetails(
                userId = userId,
                userEmail = userEmail,
            )
        }

        fun stubGetUserDetailsFailure() {
            coEvery { mockReportChatService.getUserDetails(userEmail) } throws BadRequestException("Failure")
        }

        fun stubCoreChatJwtGeneration(token: String) {
            coEvery {
                mockReportChatService.generateCoreChatJwt(any())
            } returns CoreChatStreamTokenResponse(token)
        }

        fun stubCoreChatJwtGenerationFailure() {
            coEvery {
                mockReportChatService.generateCoreChatJwt(any())
            } throws RuntimeException("JWT generation failed")
        }

        fun stubServiceToSucceed() {
            coEvery {
                mockReportChatService.reportChat(
                    any(),
                    any()
                )
            } returns RawConversationReportId(DataFactory.SOME_CONVERSATION_ID)
        }

        fun stubServiceToThrowException() {
            coEvery { mockReportChatService.reportChat(any(), any()) } throws RuntimeException("Service error")
        }

        fun createChatReport() = runTest {
            testSubject.createChatReport(reportChatRequest, userProfileData)
        }

        fun verifyBodyCreatorCalled() {
            verify { mockReportChatBodyCreator.createBody(reportChatRequest) }
        }

        fun verifyGetUserDetailsCalled() {
            coVerify { mockReportChatService.getUserDetails(userEmail) }
        }

        fun verifyCoreChatJwtGenerationCalled() {
            coVerify { mockReportChatService.generateCoreChatJwt(any()) }
        }

        fun verifyServiceCalled() {
            coVerify { mockReportChatService.reportChat(any(), any()) }
        }

        fun verifyServiceNotCalled() {
            coVerify(exactly = 0) { mockReportChatService.reportChat(any(), any()) }
        }
    }
}
