package com.gumtree.mobile.features.chat

import com.gumtree.mobile.api.fullAdsSearch.models.RawFlatAd
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.features.categories.CategoriesTreeCache
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.CATEGORY_ID_KEY
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.CUSTOM_MESSAGE_TYPE_KEY
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.DEFAULT_MESSAGE_TYPE
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.JOBS_MESSAGE_TYPE
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.MOTORS_MESSAGE_TYPE
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.ORIGIN_KEY
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.ORIGIN_VALUE
import com.gumtree.mobile.features.chat.ChatMetaDataConstants.USER_AGENT_KEY
import com.gumtree.mobile.features.reviews.ReviewsAnalyticsProvider
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.layoutsData.StreamViewCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.requests.ClientSemantics
import com.gumtree.mobile.utils.extensions.data.getCategory
import com.gumtree.mobile.utils.runOrNull

class ChatMapper(
    private val chatAnalyticsProvider: ChatAnalyticsProvider,
    private val reviewsAnalyticsProvider: ReviewsAnalyticsProvider,
) {

    @Suppress("UseCheckOrError")
    fun map(
        conversationId: String,
        counterPartyName: String,
        counterPartyUserId: String,
        isCounterPartyBlocked: Boolean,
        isConversationAboutMyPosting: Boolean,
        rawAdDetails: RawFlatAd?,
        clientSemantics: ClientSemantics,
        canPostReview: Boolean?,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.STREAM_ROW,
            data = listOf(
                StreamViewCardDto(
                    conversationId = "${ChatScreenUiConfiguration.CONVERSATION_ID_PREFIX}$conversationId",
                    counterPartyInitial = counterPartyName.firstOrNull()?.toString().orEmpty(),
                    counterPartFirstName = counterPartyName,
                    counterPartyUserId = counterPartyUserId,
                    isCounterPartyBlocked = isCounterPartyBlocked,
                    analyticEventDataSentMessage = chatAnalyticsProvider.getChatMessageSentEvent(
                        isConversationAboutMyPosting,
                    ),
                    metadata = getMetaData(rawAdDetails, clientSemantics),
                    review = getReviewData(
                        counterPartyName,
                        canPostReview,
                        reviewsAnalyticsProvider.getStartReviewEvent(isConversationAboutMyPosting),
                    ),
                ),
            ),
        )
    }

    private fun getMetaData(
        rawAdDetails: RawFlatAd?,
        clientSemantics: ClientSemantics,
    ): Map<String, String> {
        val adCategoryId = rawAdDetails?.getCategory()?.id.orEmpty()
        return mapOf(
            ORIGIN_KEY to ORIGIN_VALUE,
            CUSTOM_MESSAGE_TYPE_KEY to getCustomMessageType(adCategoryId),
            CATEGORY_ID_KEY to adCategoryId,
            USER_AGENT_KEY to "${clientSemantics.platform}-${clientSemantics.appVersion}",
        )
    }

    private fun getCustomMessageType(id: String): String {
        return when {
            CategoriesTreeCache.isJobs(id) -> JOBS_MESSAGE_TYPE
            CategoriesTreeCache.isMotors(id) -> MOTORS_MESSAGE_TYPE
            else -> DEFAULT_MESSAGE_TYPE
        }
    }

    private fun getReviewData(
        counterPartyName: String,
        canPostReview: Boolean?,
        analyticsEventData: AnalyticsEventData,
    ): StreamViewCardDto.ReviewData? {
        return canPostReview?.let {
            runOrNull(it) {
                StreamViewCardDto.ReviewData(
                    title = "${ChatScreenUiConfiguration.CHAT_REVIEW_TITLE_TEXT} $counterPartyName",
                    analyticsEventData = analyticsEventData,
                )
            }
        }
    }
}
