package com.gumtree.mobile.features.categoryLanding

import api.capi.models.RawCapiAdList
import com.gumtree.mobile.common.FilterAttributeDefault
import com.gumtree.mobile.common.allCarsPriceFilterAttributeOptions
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import com.gumtree.mobile.common.buildFilterAttributeOptions
import com.gumtree.mobile.features.filters.FiltersCategoryAttributeDto
import com.gumtree.mobile.features.filters.FiltersScreenUiConfiguration
import com.gumtree.mobile.features.filters.getFilterAttributeValuesByRootAttributeValue
import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.screens.GridSizes
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.RowLayoutType
import com.gumtree.mobile.features.screens.factories.ButtonsFactory
import com.gumtree.mobile.features.screens.factories.DropdownFactory
import com.gumtree.mobile.features.screens.factories.ImageFactory
import com.gumtree.mobile.features.screens.factories.TitleFactory
import com.gumtree.mobile.features.screens.layoutsData.ButtonCardDto
import com.gumtree.mobile.features.screens.layoutsData.DropdownCardDto
import com.gumtree.mobile.features.screens.layoutsData.TitleCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.features.screens.layoutsData.VehicleBodyTypeCardDto
import com.gumtree.mobile.responses.ScreenType
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.DestinationRoute
import com.gumtree.mobile.utils.CategoryDefaults

/**
 * Provider of the Cars category landing screen Views
 */
class CarsCategoryLandingUIProvider(
    private val titleFactory: TitleFactory,
    private val imageFactory: ImageFactory,
    private val dropdownFactory: DropdownFactory,
    private val buttonsFactory: ButtonsFactory,
    private val categoryLandingSimilarItemsMapper: CategoryLandingSimilarItemsMapper,
) {

    /**
     * Get the Search section title RowLayout
     * @return - The title RowLayout
     */
    fun createImageRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.IMAGE_ROW,
            data = listOf(
                imageFactory.buildImageCardForAsset(
                    assetUrls = CategoryLandingScreenUiConfiguration.CARS_LANDING_SCREEN_TOP_IMAGE_ASSET_URLS,
                ),
            ),
        )
    }

    /**
     * Get the Search section title RowLayout
     * @return - The title RowLayout
     */
    fun createSearchSectionTitleRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.TITLE_ROW,
            data = listOf(
                titleFactory.buildCenterTitleCard(
                    titleText = CategoryLandingScreenUiConfiguration.FIND_YOUR_DREAM_CAR_TEXT,
                ),
            ),
        )
    }

    /**
     * Get the vehicle Make Dropdown RowLayout
     * @param selectedVehicleMake - the selected vehicle make option. If not provided in the request a default option shall be used
     * @param vehicleMakeAttribute - the vehicle make attribute data
     * @return - The Dropdown RowLayout
     */
    fun createVehicleMakeDropdownRow(
        selectedVehicleMake: String?,
        vehicleMakeAttribute: FiltersCategoryAttributeDto?,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.DROPDOWN_ROW,
            data = listOf(
                dropdownFactory.buildDropdownCard(
                    title = CategoryLandingScreenUiConfiguration.MAKE_DROPDOWN_TITLE_TEXT,
                    paramName = ApiQueryParams.VEHICLE_MAKE,
                    selectedOption = selectedVehicleMake ?: FilterAttributeDefault.ANY_MAKE.key,
                    dropdownOptions = buildFilterAttributeOptions(
                        vehicleMakeAttribute?.values,
                        FilterAttributeDefault.ANY_MAKE,
                    ),
                    dropdownChangeAction = DropdownCardDto.Action.REFRESH,
                ),
            ),
        )
    }

    /**
     * Get the vehicle Model Dropdown RowLayout
     * @param selectedVehicleModel - the selected model option. If not provided in the request a default option shall be used
     * @param selectedVehicleMake - the selected make option. If not provided in the request a default option shall be used
     * @param vehicleModelAttribute - the vehicle model attribute data
     * @return - The Dropdown RowLayout
     */
    fun createVehicleModelDropdownRow(
        selectedVehicleModel: String?,
        selectedVehicleMake: String?,
        vehicleModelAttribute: FiltersCategoryAttributeDto?,
    ): RowLayout<UiItem> {
        val allVehicleModels = vehicleModelAttribute.getFilterAttributeValuesByRootAttributeValue(selectedVehicleMake)
        return RowLayout(
            type = RowLayoutType.DROPDOWN_ROW,
            data = listOf(
                dropdownFactory.buildDropdownCard(
                    title = CategoryLandingScreenUiConfiguration.MODEL_DROPDOWN_TITLE_TEXT,
                    paramName = ApiQueryParams.VEHICLE_MODEL,
                    selectedOption = selectedVehicleModel ?: FilterAttributeDefault.ANY_MODEL.key,
                    dropdownOptions = buildFilterAttributeOptions(allVehicleModels, FilterAttributeDefault.ANY_MODEL),
                ),
            ),
        )
    }

    fun createPriceTitleRow(): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.TITLE_ROW,
            data = listOf(
                titleFactory.buildLeftTitleCard(
                    titleText = CategoryLandingScreenUiConfiguration.PRICE_DROPDOWN_TITLE_TEXT,
                    titleSize = TitleCardDto.Size.XX_SMALL,
                ),
            ),
        )
    }

    /**
     * Create the Filters Price double dropdown RowLayout
     * @param minPrice - the specified min price
     * @param maxPrice - the specified max price
     * @return - The Price double dropdown RowLayout
     */
    fun createPriceDoubleDropdownRow(
        minPrice: String,
        maxPrice: String,
    ): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.DOUBLE_DROPDOWN_ROW,
            data = listOf(
                dropdownFactory.buildDoubleDropdownCard(
                    title1 = FiltersScreenUiConfiguration.MIN_TEXT,
                    paramName1 = ApiQueryParams.MIN_PRICE,
                    selectedOption1 = minPrice,
                    dropdownOptions1 = buildFilterAttributeOptions(
                        allCarsPriceFilterAttributeOptions,
                        FilterAttributeDefault.NO_MIN,
                    ),
                    title2 = FiltersScreenUiConfiguration.MAX_TEXT,
                    paramName2 = ApiQueryParams.MAX_PRICE,
                    selectedOption2 = maxPrice,
                    dropdownOptions2 = buildFilterAttributeOptions(
                        allCarsPriceFilterAttributeOptions,
                        FilterAttributeDefault.NO_MAX,
                    ),
                    separatorText = FiltersScreenUiConfiguration.TO_TEXT,
                ),
            ),
        )
    }

    /**
     * Get the Search button RowLayout
     * @param analyticsEventData - the analytics event
     * @return - The Cars category landing screen Search button RowLayout
     */
    fun createSearchButtonRow(analyticsEventData: AnalyticsEventData): RowLayout<UiItem> {
        return RowLayout(
            type = RowLayoutType.BUTTON_ROW,
            data = listOf(
                buttonsFactory.buildPrimaryButtonCard(
                    buttonText = CategoryLandingScreenUiConfiguration.SEARCH_TEXT,
                    buttonSize = ButtonCardDto.Size.LARGE,
                    buttonDestination = DestinationRoute.SRP.build(
                        queryParams = mapOf(ApiQueryParams.SCREEN_TYPE to ScreenType.BRP.toString()),
                        analyticsEvent = analyticsEventData,
                    ),
                ),
            ),
        )
    }

    /**
     * Get the vehicle body type RowLayout
     * @return - The vehicle body RowLayout
     */
    fun createVehicleBodyTypeRow(
        vehicleBodyTypeAttribute: FiltersCategoryAttributeDto?,
        locationId: String?,
        locationType: LocationType,
        commonAnalyticsParams: Map<String, String>?,
        distance: String,
    ): RowLayout<UiItem> {
        val vehicleBodyTypeValues = buildVehicleBodyTypes(vehicleBodyTypeAttribute)
        return RowLayout(
            type = RowLayoutType.VEHICLE_BODY_TYPE_ROW,
            data = listOf(
                VehicleBodyTypeCardDto(
                    title = CategoryLandingScreenUiConfiguration.CARS_BY_TYPE_TEXT,
                    bodyTypes = vehicleBodyTypeValues.map {
                        VehicleBodyTypeCardDto.VehicleBodyType(
                            text = it.second.second,
                            iconType = it.second.first,
                            destination = DestinationRoute.SRP.build(
                                queryParams = mapOf(
                                    ApiQueryParams.CATEGORY_ID to CategoryDefaults.CARS.id,
                                    ApiQueryParams.LOCATION_ID to locationId,
                                    ApiQueryParams.LOCATION_TYPE to locationType.name,
                                    ApiQueryParams.VEHICLE_BODY_TYPE to it.first.second,
                                    ApiQueryParams.SCREEN_TYPE to ScreenType.BRP.toString(),
                                    ApiQueryParams.DISTANCE to distance,
                                ),
                                analyticsEvent = AnalyticsEventData(
                                    eventName = ANALYTICS_SUBMIT_CAR_SEARCH_EVENT_NAME,
                                    parameters = commonAnalyticsParams?.plus(
                                        CarsCategoryLandingAnalyticsProvider.getBodyTypeParams(it.first.second),
                                    ) ?: CarsCategoryLandingAnalyticsProvider.getBodyTypeParams(it.first.second),
                                ),
                            ),
                        )
                    },
                ),
            ),
        )
    }

    /**
     * Get Category similar items RowLayouts
     * @param rawSimilarAdsData - the raw similar listing data
     * @param similarItemsGridSize - the grid size
     * @return - The category similar items RowLayouts
     */
    fun createCategorySimilarItemsRow(
        rawSimilarAdsData: RawCapiAdList?,
        similarItemsGridSize: GridSizes,
    ): Array<RowLayout<UiItem>?> {
        return categoryLandingSimilarItemsMapper.map(rawSimilarAdsData, similarItemsGridSize)
    }

    @Suppress("MagicNumber")
    private fun buildVehicleBodyTypes(
        vehicleBodyTypeAttribute: FiltersCategoryAttributeDto?,
    ): List<Pair<Pair<Int, String>, Pair<VehicleBodyTypeCardDto.VehicleBodyType.Icon, String>>> {
        return vehicleBodyTypeAttribute?.values?.mapNotNull {
            when (it.value) {
                "hatchback" -> Pair(Pair(1, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.HATCHBACK, "Hatchback"))
                "saloon" -> Pair(Pair(2, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.SALOON, "Saloon"))
                "coupe" -> Pair(Pair(3, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.COUPE, "Coupe (2 doors)"))
                "light_4x4_utility" -> Pair(Pair(4, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.SUV, "SUV"))
                "pick_up" -> Pair(Pair(5, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.PICKUP, "Pick up"))
                "convertible" -> Pair(Pair(6, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.CONVERTIBLE, "Convertible"))
                "window_van" -> Pair(Pair(7, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.VAN, "Van"))
                "estate" -> Pair(Pair(8, it.value), Pair(VehicleBodyTypeCardDto.VehicleBodyType.Icon.ESTATE, "Estate"))
                else -> null
            }
        }
            ?.sortedBy { it.first.first }
            ?: emptyList()
    }
}
