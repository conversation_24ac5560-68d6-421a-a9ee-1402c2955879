package com.gumtree.mobile.features.myGumtree.v2

import com.gumtree.mobile.routes.API_V2_MAIN_PATH
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.routes.ID_PATH
import com.gumtree.mobile.utils.annotations.Versioning
import com.gumtree.mobile.utils.extensions.getOrEmpty
import com.gumtree.mobile.utils.extensions.getOrThrowUnauthorised
import com.gumtree.mobile.utils.extensions.readPagingParams
import com.gumtree.mobile.utils.extensions.readPathParam
import com.gumtree.mobile.utils.extensions.readQueryParam
import com.gumtree.mobile.utils.extensions.respondNoContent
import com.gumtree.mobile.utils.extensions.respondSuccess
import com.gumtree.mobile.utils.regexes.RegEx
import io.ktor.http.Headers
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import org.koin.ktor.ext.inject

const val CUSTOM_API_V2_MAIN_PATH = "$API_V2_MAIN_PATH"
const val MY_GUMTREE_PATH = "$API_V2_MAIN_PATH/my-gumtree"
const val MY_GUMTREE_SCREEN_PATH = "$MY_GUMTREE_PATH/screen"
const val MY_GUMTREE_AD_PATH = "$MY_GUMTREE_PATH/ad/{$ID_PATH}"
const val MY_GUMTREE_POTENTIAL_BUYER_PATH = "$MY_GUMTREE_PATH/potentialbuyer"
const val MY_GUMTREE_REPOST_PATH = "$MY_GUMTREE_AD_PATH/repost"
const val MY_GUMTREE_AD_POST_SUCCESS_PATH = "$MY_GUMTREE_PATH/adPostedAnalyticsEvent"

@Versioning(versionPath = CUSTOM_API_V2_MAIN_PATH, androidVersion = "10.1.6", iosVersion = "18.1.1")
fun Route.myGumtreeRoute() {
    val repository by inject<MyGumtreeRepository>()

    get(MY_GUMTREE_SCREEN_PATH) {
        val callHeaders: Headers = call.request.headers
        // if user not logged in, return non logged in screen?
        val email = callHeaders.getOrEmpty(ApiHeaderParams.AUTHORISATION_USER_EMAIL)
        val filterType = call.readQueryParam(ApiQueryParams.FILTER)
        val (pageNumber, pageSize) = call.readPagingParams()

        val response = repository.readScreen(callHeaders, filterType, email, pageNumber, pageSize)

        call.respondSuccess(response)
    }

    delete(MY_GUMTREE_AD_PATH) {
        val callHeaders: Headers = call.request.headers
        val userEmail: String = callHeaders[ApiHeaderParams.AUTHORISATION_USER_EMAIL].toString()
        val adId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        repository.delete(callHeaders, userEmail, adId)
        call.respondNoContent()
    }

    get(MY_GUMTREE_POTENTIAL_BUYER_PATH) {
        val callHeaders: Headers = call.request.headers
        val email = callHeaders.getOrThrowUnauthorised(ApiHeaderParams.AUTHORISATION_USER_EMAIL)
        val adId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        val response = repository.getPotentialBuyer(callHeaders, email, adId)

        call.respondSuccess(response)
    }

    post(MY_GUMTREE_REPOST_PATH) {
        val callHeaders: Headers = call.request.headers
        val adId = call.readPathParam(ID_PATH, RegEx.NUMERICAL_ID)
        repository.repostFreeAd(callHeaders, adId)
        call.respondNoContent()
    }

    get(MY_GUMTREE_AD_POST_SUCCESS_PATH) {
        val callHeaders = call.request.headers
        val adId = call.readQueryParam(ApiQueryParams.AD_ID, true)
        val postType = PostType.fromString(call.readQueryParam(ApiQueryParams.POST_TYPE, true))
        val isPaidAd = call.readQueryParam(ApiQueryParams.IS_PAID_AD).toBoolean()

        val response = repository.getAdPostedSuccessAnalyticsEvent(callHeaders, adId, postType, isPaidAd)
        call.respondSuccess(response)
    }
}
