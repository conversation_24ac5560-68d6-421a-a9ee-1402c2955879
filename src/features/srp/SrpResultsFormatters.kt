package com.gumtree.mobile.features.srp

import com.gumtree.mobile.common.DEFAULT_RESULTS_NUMBER_FORMAT
import com.gumtree.mobile.common.ResultsFormatter
import java.util.*
import kotlin.math.max

/**
 * Helper object to format the SRP sort header total search results text
 */
object SrpSortHeaderResultsFormatter : ResultsFormatter {

    override fun formatResults(results: String?): String {
        val number = max(results?.toDoubleOrNull() ?: 0.0, 0.0)
        val numberString = number.toResultsString(DEFAULT_RESULTS_NUMBER_FORMAT)
        val resultsString = "result".pluralize(number)

        return "$numberString $resultsString"
    }
}

/**
 * Helper object to format the SRP total search results text
 */
object SrpTotalResultsFormatter : ResultsFormatter {

    override fun formatResults(results: String?): String {
        val number = max(results?.toDoubleOrNull() ?: 0.0, 0.0)
        return number.toResultsString(DEFAULT_RESULTS_NUMBER_FORMAT)
    }
}

private fun Double.toResultsString(pattern: String) = String.format(Locale.ENGLISH, pattern, this)

private fun String.pluralize(count: Double): String {
    return when (count.toInt()) {
        1 -> this
        else -> this + 's'
    }
}
