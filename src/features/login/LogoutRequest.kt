package com.gumtree.mobile.features.login

import com.gumtree.mobile.requests.RequestBody
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LogoutRequest(
    @SerialName("userId")
    val userId: String,
    @SerialName("pushToken")
    val pushToken: String?,
) : RequestBody {

    /**
     * Only Logout request with some user ID are considered as a valid payload
     * If the user ID is empty we throw BadRequestException
     */
    override fun isValid(): Boolean {
        return userId.isNotEmpty()
    }
}
