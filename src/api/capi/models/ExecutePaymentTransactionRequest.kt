package com.gumtree.mobile.api.capi.models

import com.google.appengine.repackaged.com.google.gson.annotations.SerializedName

data class ExecutePaymentTransactionRequest(
    @SerializedName("account_id")
    val accountId: Long,
    @SerializedName("advert_id")
    val advertId: Long,
    @SerializedName("user_id")
    val userId: Long,
    @SerializedName("order_id")
    val orderId: Long,
    @SerializedName("email")
    val email: String,
    @SerializedName("platform_device")
    val platformDevice: String,
    @SerializedName("payment_details")
    val paymentDetails: PaymentDetails,
    @SerializedName("billing_address")
    val billingAddress: BillingAddress,
    @SerializedName("is_3ds2_enabled")
    val is3DS2Enabled: Boolean,
)

data class PaymentDetails(
    @SerializedName("device_data")
    val deviceData: String,
    @SerializedName("payment_method_nonce")
    val paymentMethodNonce: String,
    @SerializedName("total_including_vat")
    val totalIncludingVat: Long,
    @SerializedName("total_vat")
    val totalVat: Long,
)

data class BillingAddress(
    @SerializedName("first_name")
    val firstName: String = "",
    @SerializedName("last_name")
    val lastName: String = "",
    @SerializedName("street")
    val street: String = "",
    @SerializedName("postcode")
    val postCode: String = "",
    @SerializedName("town")
    val town: String = "",
    @SerializedName("country")
    val country: String = "",
)
