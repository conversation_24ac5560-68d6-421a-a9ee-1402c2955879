package features.blockUser

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.features.blockUser.BlockUserService
import com.gumtree.mobile.features.blockUser.DefaultBlockUserRepository
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import retrofit2.Response
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import utils.TestDispatcherProvider

class DefaultBlockUserRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use service to create block user`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulBlockUserHttpResult() }
            When { create(DataFactory.SOME_USER_ID) }
            Then { checkServiceCreateBlockUser(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should use service to delete block user`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulUnBlockUserHttpResult() }
            When { delete(DataFactory.SOME_USER_ID) }
            Then { checkServiceDeleteBlockUser(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should create authorised headers, call block user service in order when creating block user`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulBlockUserHttpResult() }
            When { create(DataFactory.SOME_USER_ID) }
            Then { checkCreateBlockUserActionsInOrder(DataFactory.SOME_USER_ID) }
        }
    }

    @Test
    fun `should create authorised headers, call block user service in order when deleting block user`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulUnBlockUserHttpResult() }
            When { delete(DataFactory.ANOTHER_USER_ID) }
            Then { checkDeleteBlockUserActionsInOrder(DataFactory.ANOTHER_USER_ID) }
        }
    }

    private class Robot: BaseRobot {
        private val blockUserService: BlockUserService = mockk(relaxed = true)
        private val headersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)
        private val authHeaders: ApiHeaders = mockk(relaxed = true)
        private val dispatcherProvider = TestDispatcherProvider()

        private lateinit var testSubject: DefaultBlockUserRepository

        override fun setup() {
            testSubject = DefaultBlockUserRepository(
                blockUserService,
                headersProvider,
                dispatcherProvider,
            )
        }

        fun stubAuthHeaders() {
            every { headersProvider.createAuthorisedHeaders(callHeaders) } returns authHeaders
        }

        fun stubSuccessfulBlockUserHttpResult() {
            coEvery { blockUserService.blockUser(any(), any()) } returns Response.success(Unit)
        }

        fun stubSuccessfulUnBlockUserHttpResult() {
            coEvery { blockUserService.unBlockUser(any(), any()) } returns Response.success(Unit)
        }

        suspend fun create(userIdToBlock: String) {
            testSubject.create(callHeaders, userIdToBlock)
        }

        suspend fun delete(userIdToUnBlock: String) {
            testSubject.delete(callHeaders, userIdToUnBlock)
        }

        fun checkServiceCreateBlockUser(userIdToBlock: String) {
            coVerify { blockUserService.blockUser(authHeaders, userIdToBlock) }
        }

        fun checkServiceDeleteBlockUser(userIdToUnBlock: String) {
            coVerify { blockUserService.unBlockUser(authHeaders, userIdToUnBlock) }
        }

        fun checkCreateBlockUserActionsInOrder(userIdToBlock: String) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                blockUserService.blockUser(authHeaders, userIdToBlock)
            }
        }

        fun checkDeleteBlockUserActionsInOrder(userIdToUnBlock: String) {
            coVerifyOrder {
                headersProvider.createAuthorisedHeaders(callHeaders)
                blockUserService.unBlockUser(authHeaders, userIdToUnBlock)
            }
        }
    }
}