@file:Suppress("EnumEntryNameCase", "EnumNaming")

package com.gumtree.mobile.api.locations.models

import com.google.gson.annotations.SerializedName

data class RawLocationSuggestions(
    @SerializedName("options")
    val options: List<RawLocation>,
)

data class RawNearestLocations(
    @SerializedName("locations")
    val locations: List<RawLocation>,
)

data class RawLocation(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("type")
    val type: Type,
    @SerializedName("location_id")
    val locationId: Int? = null,
    @SerializedName("leaf_location_id")
    val leafLocationId: Int? = null,
    @SerializedName("latitude")
    val latitude: Double? = null,
    @SerializedName("longitude")
    val longitude: Double? = null,
    @SerializedName("postcode")
    val postcode: String? = null,
) {
    enum class Type {
        postcode,
        outcode,
        location,
    }
}
