package com.gumtree.mobile.api.userProfile.api

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.papi.models.RawPapiUserProfile
import com.gumtree.mobile.api.userProfile.UserProfileApiParams.ACCOUNT_ID
import com.gumtree.mobile.api.userProfile.UserProfileApiParams.PUBLIC_ID
import com.gumtree.mobile.api.userProfile.UserProfileUrls.GET_BASE_USER_PROFILE_BY_ACCOUNT_ID_ENDPOINT
import com.gumtree.mobile.api.userProfile.UserProfileUrls.GET_BASE_USER_PROFILE_ENDPOINT
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.Path
import retrofit2.http.Query
import utils.ConvertHttpStatus

interface UserProfileApi {
    @ConvertHttpStatus(from = 204, to = 404)
    @GET(GET_BASE_USER_PROFILE_ENDPOINT)
    suspend fun getBaseUserProfile(
        @HeaderMap unAuthorisedHeaders: ApiHeaders,
        @Query(PUBLIC_ID) id: String?,
    ): RawPapiUserProfile

    @ConvertHttpStatus(from = 204, to = 404)
    @GET(GET_BASE_USER_PROFILE_BY_ACCOUNT_ID_ENDPOINT)
    suspend fun getBaseUserProfileByAccountId(
        @HeaderMap unAuthorisedHeaders: ApiHeaders,
        @Path(ACCOUNT_ID) accountId: String,
    ): RawPapiUserProfile
}
