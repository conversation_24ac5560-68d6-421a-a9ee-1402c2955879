package utils

import com.gumtree.mobile.utils.PageIndexes
import com.gumtree.mobile.utils.TailedPageSlicer
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTest

@ParallelTest
class TailedPageSlicerTest {

    private val robot = Robot()

    @Test
    fun `should return pageStarIndex equals to NULL and pageEndIndex equals to NULL`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(0) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }

            Given { stubPage(2) }
            Given { stubSize(24) }
            Given { stubTotalListSize(2) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }

            Given { stubPage(1) }
            Given { stubSize(24) }
            Given { stubTotalListSize(24) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }

            Given { stubPage(10) }
            Given { stubSize(24) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }
        }
    }

    @Test
    fun `should return pageStarIndex equals to 0 and pageEndIndex equals to totalListSize`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(10) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(9) }

            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(2) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(1) }

            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(19) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(18) }

            Given { stubPage(0) }
            Given { stubSize(100) }
            Given { stubTotalListSize(88) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(87) }
        }
    }

    @Test
    fun `should return pageStarIndex greater than 0 and pageEndIndex equals to size`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(15) }
            Given { stubTotalListSize(20) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(5) }
            Then { checkPageEndIndex(19) }

            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(30) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(6) }
            Then { checkPageEndIndex(29) }

            Given { stubPage(0) }
            Given { stubSize(30) }
            Given { stubTotalListSize(45) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(15) }
            Then { checkPageEndIndex(44) }
        }
    }

    @Test
    fun `should return pageStarIndex equals to 0 and pageEndIndex equals to size and totalListSize`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(20) }
            Given { stubTotalListSize(20) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(19) }

            Given { stubPage(0) }
            Given { stubSize(24) }
            Given { stubTotalListSize(24) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(23) }

            Given { stubPage(0) }
            Given { stubSize(30) }
            Given { stubTotalListSize(30) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(29) }

            Given { stubPage(0) }
            Given { stubSize(100) }
            Given { stubTotalListSize(100) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(99) }
        }
    }

    @Test
    fun `should return correct pageStarIndex and pageEndIndex equals to totalListSize`() {
        runUnitTest(robot) {
            Given { stubPage(1) }
            Given { stubSize(24) }
            Given { stubTotalListSize(29) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(4) }

            Given { stubPage(2) }
            Given { stubSize(24) }
            Given { stubTotalListSize(50) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(1) }

            Given { stubPage(3) }
            Given { stubSize(24) }
            Given { stubTotalListSize(81) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(8) }
        }
    }

    @Test
    fun `should return correct pageStarIndex and pageEndIndex for N pages in a row`() {
        runUnitTest(robot) {
            Given { stubPage(0) }
            Given { stubSize(30) }
            Given { stubTotalListSize(103) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(73) }
            Then { checkPageEndIndex(102) }

            Given { stubPage(1) }
            Given { stubSize(30) }
            Given { stubTotalListSize(103) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(43) }
            Then { checkPageEndIndex(72) }

            Given { stubPage(2) }
            Given { stubSize(30) }
            Given { stubTotalListSize(103) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(13) }
            Then { checkPageEndIndex(42) }

            Given { stubPage(3) }
            Given { stubSize(30) }
            Given { stubTotalListSize(103) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(0) }
            Then { checkPageEndIndex(12) }

            Given { stubPage(4) }
            Given { stubSize(30) }
            Given { stubTotalListSize(103) }
            When { getPageIndexes() }
            Then { checkPageStartIndex(null) }
            Then { checkPageEndIndex(null) }
        }
    }

    private class Robot: BaseRobot {

        private var page: Int = 0
        private var size: Int = 0
        private var totalListSize: Int = 0

        private lateinit var actualPageIndexes: PageIndexes

        private lateinit var testSubject: TailedPageSlicer

        fun stubPage(page: Int) {
            this.page = page
        }

        fun stubSize(size: Int) {
            this.size = size
        }

        fun stubTotalListSize(totalListSize: Int) {
            this.totalListSize = totalListSize
        }

        fun getPageIndexes() {
            testSubject = TailedPageSlicer(page, size, totalListSize)
            actualPageIndexes = testSubject.getPageIndexes()
        }

        fun checkPageStartIndex(expected: Int?) {
            assertEquals(expected, actualPageIndexes.first)
        }

        fun checkPageEndIndex(expected: Int?) {
            assertEquals(expected, actualPageIndexes.second)
        }
    }

}