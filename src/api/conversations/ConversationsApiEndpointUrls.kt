package com.gumtree.mobile.api.conversations

import com.gumtree.mobile.utils.annotations.NotCovered

@NotCovered
internal object ConversationsApiEndpointUrls {
    const val GET_USER_UNREAD_MESSAGES_ENDPOINT = "messages/unread/{${ConversationsApiParams.USER_ID}}"
    const val GET_USER_INFO_ENDPOINT = "users/{${ConversationsApiParams.USER_ID}}"
    const val GET_BLOCKED_USERS_ENDPOINT = "users/{${ConversationsApiParams.BLOCKER_ID}}/blocked-users"
    const val POST_BLOCK_USER = "users/block"
    const val DELETE_BLOCKED_USER_ENDPOINT = "users/{${ConversationsApiParams.BLOCKER_ID}}/blocked-users/{${ConversationsApiParams.BLOCKEE_ID}}"
    const val GET_USER_CONVERSATIONS_BY_CHANNEL_ID = "conversations/find-by-id/{${ConversationsApiParams.CHANNEL_ID}}"
    const val GET_USER_CONVERSATIONS_BY_USER_ID = "conversations/{${ConversationsApiParams.USER_ID}}"
    const val CONVERSATIONS_ENDPOINT = "conversations"
    const val MESSAGES_ENDPOINT = "messages"
    const val PUSH_NOTIFICATIONS_ENDPOINT = "push-notification/subscription"
}
