package com.gumtree.mobile.features.locations

import com.gumtree.mobile.api.locations.api.DEFAULT_LOCATION_SUGGESTIONS_TYPES
import com.gumtree.mobile.common.DispatcherProvider
import com.gumtree.mobile.utils.extensions.data.toSimpleLocationDto
import kotlinx.coroutines.withContext

/**
 * Handles all CRUD operations about the Locations feature
 */
interface LocationsRepository {
    /**
     * Find location suggestion (by location keyword)
     * @param keyword - the location search keyword
     * @param locationType - optional location type filter
     *
     * @return - all found location suggestions per the location keyword look up
     */
    suspend fun readLocationSuggestions(keyword: String, locationType: LocationType?): List<SimpleLocationDto>

    /**
     * Find the nearest location to specific point (lat and long)
     * @param latitude - The latitude
     * @param longitude - The longitude
     * @return The nearest location as a SimpleLocationDto
     */
    suspend fun getNearestLocation(latitude: String, longitude: String): SimpleLocationDto
}

class DefaultLocationsRepository(
    private val locationsService: LocationsService,
    private val locationsSuggestionsMapper: LocationsSuggestionsMapper,
    private val dispatcherProvider: DispatcherProvider,
) : LocationsRepository {

    override suspend fun readLocationSuggestions(keyword: String, locationType: LocationType?): List<SimpleLocationDto> {
        require(keyword.isNotBlank()) { "keyword is missing" }

        val types = locationType?.name?.lowercase() ?: DEFAULT_LOCATION_SUGGESTIONS_TYPES

        val rawLocationSuggestions =
            withContext(dispatcherProvider.io) { locationsService.getLocationSuggestions(keyword, types) }
        return locationsSuggestionsMapper.map(rawLocationSuggestions)
    }

    override suspend fun getNearestLocation(latitude: String, longitude: String): SimpleLocationDto {
        require(latitude.isNotBlank() && longitude.isNotBlank()) { "latitude or longitude is missing" }
        val rawLocation =
            withContext(dispatcherProvider.io) { locationsService.getLocationByLatLng(latitude, longitude) }
        return rawLocation.toSimpleLocationDto()
    }
}
