package features.settings

import com.gumtree.mobile.features.settings.SettingsScreenUiConfiguration
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import java.time.Year
import java.time.ZoneId

class SettingScreenUiConfigurationTest {

    private val robot = Robot()

    @Test
    fun `should return full settings copyright text`() = runTest {
        runUnitTest(robot) {
            When { getCopyrightText(DataFactory.SOME_APP_VERSION) }
            Then {
                checkCopyrightText(
                    "Copyright © Gumtree 2000 - ${Year.now(ZoneId.of("Europe/London")).value}" + "\nGumtree v${DataFactory.SOME_APP_VERSION}"
                )
            }
        }
    }

    @Test
    fun `should return settings copyright text without app version`() = runTest {
        runUnitTest(robot) {
            When { getCopyrightText(null) }
            Then {
                checkCopyrightText(
                    "Copyright © Gumtree 2000 - ${Year.now(ZoneId.of("Europe/London")).value}"
                )
            }
        }
    }

    private class Robot: BaseRobot {

        private lateinit var actualCopyrightText: String
        private val testSubject = SettingsScreenUiConfiguration

        fun getCopyrightText(appVersion: String?) {
            actualCopyrightText = testSubject.createCopyrightText(appVersion)
        }

        fun checkCopyrightText(expected: String) {
            assertEquals(expected, actualCopyrightText)
        }
    }
}