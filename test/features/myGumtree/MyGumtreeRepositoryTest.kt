package features.myGumtree

import com.gumtree.mobile.api.common.ApiHeaders
import com.gumtree.mobile.api.common.ApiHeadersProvider
import com.gumtree.mobile.features.myGumtree.DefaultMyGumtreeRepository
import com.gumtree.mobile.features.myGumtree.MyGumtreeAdsMapper
import com.gumtree.mobile.features.myGumtree.MyGumtreeRepository
import com.gumtree.mobile.features.myGumtree.MyGumtreeService
import com.gumtree.mobile.features.myGumtree.MyGumtreeTitleRowMapper
import com.gumtree.mobile.features.myGumtree.ProfileMapper
import com.gumtree.mobile.features.screens.PageCalculator
import com.gumtree.mobile.features.screens.RowLayout
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.responses.PortraitData
import com.gumtree.mobile.responses.ScreenResponse
import com.gumtree.mobile.routes.ApiHeaderParams
import com.gumtree.mobile.utils.extensions.getOrEmpty
import tools.layoutsDataFactory.MyGumtreeDataFactory
import tools.layoutsDataFactory.ProfileDataFactory
import io.ktor.http.*
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import retrofit2.Response
import tools.BaseRobot
import tools.DataFactory
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import utils.TestDispatcherProvider
import kotlin.test.assertEquals

class MyGumtreeRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should map all input data to correct screen response`() = runTest {
        val listings = MyGumtreeDataFactory.createListingsRows()
        val profileRow = ProfileDataFactory.createProfileRow(DataFactory.SOME_USER_DISPLAY_NAME)
        val titleRow = MyGumtreeTitleRowMapper().map()
        val nextPage = "nextPage"

        runUnitTest(robot) {
            Given {
                stubMyGumtreeAdsMapper(listings)
                stubProfileRowMapper(profileRow)
                stubTitleRowMapper(titleRow)
                stubNextPage(nextPage)
            }
            When { readScreen() }
            Then { checkScreenResponse(listings, profileRow, titleRow, nextPage) }
        }
    }

    @Test
    fun `should use service to delete user ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulDeleteUserAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkServiceDeleteUserAd(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should create authorisation headers when delete user ad`() = runTest {
        runUnitTest(robot) {
            Given { stubSuccessfulDeleteUserAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkProviderCreateAuthorisationHeaders() }
        }
    }

    @Test
    fun `should create authorised headers, call service in order when deleting user ad`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthHeaders() }
            Given { stubSuccessfulDeleteUserAdHttpResult() }
            When { delete(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
            Then { checkDeleteUserAdActionsInOrder(DataFactory.SOME_USER_EMAIL, DataFactory.SOME_AD_ID) }
        }
    }

    @Test
    fun `should authenticate when reposting`() = runTest {
        runUnitTest(robot) {
            Given { stubAdId(DataFactory.SOME_AD_ID) }
            Given { stubAuthHeaders() }
            When { repostFreeAd() }
            Then { checkRepostCalled() }
        }
    }

    private class Robot : BaseRobot {

        private lateinit var testSubject: MyGumtreeRepository
        private var service: MyGumtreeService = mockk(relaxed = true)

        private val authHeadersProvider: ApiHeadersProvider = mockk(relaxed = true)
        private val titleRowMapper: MyGumtreeTitleRowMapper = mockk(relaxed = true)
        private val myGumtreeAdsMapper: MyGumtreeAdsMapper = mockk(relaxed = true)
        private val profileMapper: ProfileMapper = mockk(relaxed = true)

        private val authHeaders: ApiHeaders = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true) {
            every { getOrEmpty(ApiHeaderParams.AUTHORISATION_USER_EMAIL) } returns DataFactory.SOME_USER_EMAIL
        }
        private val pageCalculator: PageCalculator = mockk {
            every { calculateNextPage(any(), any()) } returns ""
        }

        private lateinit var adId: String
        private var actualResponse: ScreenResponse? = null

        override fun setup() {
            testSubject = DefaultMyGumtreeRepository(
                authHeadersProvider,
                authHeadersProvider,
                service,
                myGumtreeAdsMapper,
                profileMapper,
                titleRowMapper,
                pageCalculator,
                TestDispatcherProvider(),
            )
        }

        fun stubAuthHeaders() {
            every { authHeadersProvider.createAuthorisedHeaders(callHeaders) } returns authHeaders
        }

        fun stubAdId(adId: String) {
            this.adId = adId
        }

        fun stubNextPage(nextPage: String) {
            every { pageCalculator.calculateNextPage(any(), any()) } returns nextPage
        }

        fun stubMyGumtreeAdsMapper(adRows: List<RowLayout<UiItem>>) {
            every { myGumtreeAdsMapper.map(any(), any()) } returns adRows
        }

        fun stubTitleRowMapper(titleRow: RowLayout<UiItem>) {
            every { titleRowMapper.map(any()) } returns titleRow
        }

        fun stubProfileRowMapper(profileRow: RowLayout<UiItem>) {
            every { profileMapper.map(any()) } returns profileRow
        }

        fun stubSuccessfulDeleteUserAdHttpResult() {
            coEvery { service.deleteUserAd(any(), any(), any()) } returns Response.success(Unit)
        }

        suspend fun readScreen() {
            actualResponse = testSubject.readScreen(callHeaders, "ALL_ADS", "", "20", "20")
        }

        suspend fun delete(
            userName: String,
            adId: String
        ) {
            testSubject.delete(callHeaders, userName, adId)
        }

        suspend fun repostFreeAd() {
            testSubject.repostFreeAd(callHeaders, adId)
        }

        fun checkRepostCalled() {
            coVerify { service.repostForFree(any(), adId) }
        }

        fun checkScreenResponse(
            portraitData: PortraitData,
            profileRow: RowLayout<UiItem>,
            titleRow: RowLayout<UiItem>,
            nextPage: String
        ) {
            assertEquals(portraitData, actualResponse?.portraitData)
            assertEquals(listOf(profileRow, titleRow), actualResponse?.stickyBar)
            assertEquals(nextPage, actualResponse?.nextPage)
        }

        fun checkServiceDeleteUserAd(
            expectedUserName: String,
            expectedAdId: String
        ) {
            coVerify { service.deleteUserAd(authHeaders, expectedUserName, expectedAdId) }
        }

        fun checkProviderCreateAuthorisationHeaders() {
            verify { authHeadersProvider.createAuthorisedHeaders(callHeaders) }
        }

        fun checkDeleteUserAdActionsInOrder(
            expectedUserName: String,
            expectedAdId: String
        ) {
            coVerifyOrder {
                authHeadersProvider.createAuthorisedHeaders(callHeaders)
                service.deleteUserAd(authHeaders, expectedUserName, expectedAdId)
            }
        }
    }
}