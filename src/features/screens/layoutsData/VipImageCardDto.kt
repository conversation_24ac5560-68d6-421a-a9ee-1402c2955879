package com.gumtree.mobile.features.screens.layoutsData

import com.gumtree.mobile.common.Image
import com.gumtree.mobile.common.analytics.AnalyticsEventData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("VIP_IMAGE_CARD")
data class VipImageCardDto(
    @SerialName("images")
    val images: List<Image>,
    @SerialName("gamAdvert")
    val gamAdvert: GAMAdvertDto? = null,
    @SerialName("analyticsEventData")
    val analyticsEventData: AnalyticsEventData? = null,
) : UiItem
