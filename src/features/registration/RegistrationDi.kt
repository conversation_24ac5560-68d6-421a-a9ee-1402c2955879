package com.gumtree.mobile.features.registration

import com.gumtree.mobile.utils.extensions.getFromKoin
import org.koin.dsl.module

/**
 * Provides all dependencies for User Registration feature
 */
val registrationModule = module {
    single { RegistrationService(userServiceApi = getFromKoin()) }
    single { RegistrationBodyCreator() }
    single { RegistrationActivationBodyCreator() }
    single { DateOfBirthValidator() }
    single<RegistrationRepository> {
        DefaultRegistrationRepository(
            registrationService = getFromKoin(),
            registrationBodyCreator = getFromKoin(),
            registrationActivationBodyCreator = getFromKoin(),
            dispatcherProvider = getFromKoin(),
            dateOfBirthValidator = getFromKoin(),
        )
    }
}
