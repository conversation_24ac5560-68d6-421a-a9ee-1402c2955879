package com.gumtree.mobile.features.locations

import com.gumtree.mobile.routes.API_V1_MAIN_PATH
import com.gumtree.mobile.routes.ApiQueryParams
import com.gumtree.mobile.utils.extensions.readQueryParam
import com.gumtree.mobile.utils.extensions.readQueryParamOrDefault
import com.gumtree.mobile.utils.extensions.respondSuccess
import io.ktor.server.application.call
import io.ktor.server.routing.Route
import io.ktor.server.routing.get
import org.koin.ktor.ext.inject

const val LOCATIONS_PATH = "$API_V1_MAIN_PATH/locations"
const val NEAREST_LOCATION_PATH = "$LOCATIONS_PATH/nearest"
const val LOCATIONS_SUGGESTIONS_PATH = "$LOCATIONS_PATH/suggestions"

fun Route.locationsRoute() {
    val repository by inject<LocationsRepository>()

    get(LOCATIONS_SUGGESTIONS_PATH) {
        val locationKeyword = call.readQueryParam(ApiQueryParams.KEYWORD, true)
        val locationType = call.readQueryParamOrDefault<String?>(ApiQueryParams.LOCATION_TYPE, null)?.let {
            LocationType.fromString(it)
        }

        val response = repository.readLocationSuggestions(locationKeyword, locationType)

        call.respondSuccess(response)
    }

    get(NEAREST_LOCATION_PATH) {
        val lat = call.readQueryParam(ApiQueryParams.LATITUDE, true)
        val lon = call.readQueryParam(ApiQueryParams.LONGITUDE, true)
        val response = repository.getNearestLocation(lat, lon)

        call.respondSuccess(response)
    }
}
