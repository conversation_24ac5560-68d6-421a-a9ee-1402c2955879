package api.seller

import com.gumtree.mobile.api.seller.SELLER_API_CONNECTION_POOL_KEEP_ALIVE_DURATION
import com.gumtree.mobile.api.seller.SELLER_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS
import com.gumtree.mobile.api.seller.SellerApiOkHttpConnectionPoolConfiguration
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Then
import tools.annotations.ParallelTest
import tools.runUnitTest
import java.util.concurrent.TimeUnit

@ParallelTest
class SellerApiOkHttpConnectionPoolConfigurationTest {

    private val robot = Robot()

    @Test
    fun `should return SellerApi OkHttp connection pool with expected settings`() {
        runUnitTest(robot) {
            Then { checkMaxIdleConnections(SELLER_API_CONNECTION_POOL_MAX_IDLE_CONNECTIONS) }
            Then { checkKeepAliveDuration(SELLER_API_CONNECTION_POOL_KEEP_ALIVE_DURATION) }
            Then { checkTimeUnit(TimeUnit.SECONDS) }
        }
    }

    private class Robot : BaseRobot {
        private val testSubject = SellerApiOkHttpConnectionPoolConfiguration()

        fun checkMaxIdleConnections(expected: Int) {
            assertEquals(expected, testSubject.maxIdleConnections)
        }

        fun checkKeepAliveDuration(expected: Long) {
            assertEquals(expected, testSubject.keepAliveDuration)
        }

        fun checkTimeUnit(expected: TimeUnit) {
            assertEquals(expected, testSubject.timeUnit)
        }
    }
}