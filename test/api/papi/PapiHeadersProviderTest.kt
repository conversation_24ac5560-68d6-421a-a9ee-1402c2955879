package api.papi

import com.google.appengine.repackaged.com.google.common.net.HttpHeaders
import com.gumtree.mobile.api.papi.*
import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.papi.PAPI_DEFAULT_LANGUAGE
import com.gumtree.mobile.api.papi.PapiHeadersProvider
import com.gumtree.mobile.responses.UnauthorisedException
import com.gumtree.mobile.routes.ApiHeaderParams
import io.ktor.client.utils.CacheControl
import io.ktor.http.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.runUnitTest
import tools.Then
import tools.When
import tools.annotations.ParallelTest
import tools.runUnitTestForException

@ParallelTest
class PapiHeadersProviderTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should return all default PAPI unauthorised headers`() {
        runUnitTest(robot) {
            When { createUnAuthorisedHeaders() }
            Then { checkHeader(ACCEPT_LANGUAGE, PAPI_DEFAULT_LANGUAGE) }
            Then { checkHeader(CONNECTION, HttpHeaders.KEEP_ALIVE) }
            Then { checkHeader(PROXY_CONNECTION, HttpHeaders.KEEP_ALIVE) }
            Then { checkHeader(PRAGMA, CacheControl.NO_CACHE) }
        }
    }

    @Test
    fun `should return all expected PAPI unauthorised headers`() {
        runUnitTest(robot) {
            Given { stubHeader(ApiHeaderParams.APP_VERSION, DataFactory.SOME_APP_VERSION) }
            Given { stubHeader(ApiHeaderParams.PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Given { stubHeader(ApiHeaderParams.UDID, DataFactory.SOME_UDID) }
            Given { stubHeader(ApiHeaderParams.EXPERIMENTS, DataFactory.SOME_EXPERIMENT) }
            Given { stubHeader(ApiHeaderParams.THREATMETRIX_SESSION, DataFactory.SOME_THREAT_METRIX_SESSION) }
            When { createUnAuthorisedHeaders() }
            Then { checkHeader(ECG_APP_VER, DataFactory.SOME_APP_VERSION) }
            Then { checkHeader(ECG_PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Then { checkHeader(ECG_UDID, DataFactory.SOME_UDID) }
            Then { checkHeader(X_ECG_EXPERIMENTS, DataFactory.SOME_EXPERIMENT) }
            Then { checkHeader(X_THREATMETRIX_SESSION_ID, DataFactory.SOME_THREAT_METRIX_SESSION) }
        }
    }

    @Test
    fun `should return all expected PAPI unauthorised headers without ECG_USER_AUTH header`() {
        runUnitTest(robot) {
            Given { stubHeader(ApiHeaderParams.APP_VERSION, DataFactory.SOME_APP_VERSION) }
            Given { stubHeader(ApiHeaderParams.PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Given { stubHeader(ApiHeaderParams.UDID, DataFactory.SOME_UDID) }
            Given { stubHeader(ApiHeaderParams.EXPERIMENTS, DataFactory.SOME_EXPERIMENT) }
            Given { stubHeader(ApiHeaderParams.THREATMETRIX_SESSION, DataFactory.SOME_THREAT_METRIX_SESSION) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            When { createUnAuthorisedHeaders(false) }
            Then { checkHeader(ECG_APP_VER, DataFactory.SOME_APP_VERSION) }
            Then { checkHeader(ECG_PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Then { checkHeader(ECG_UDID, DataFactory.SOME_UDID) }
            Then { checkHeader(X_ECG_EXPERIMENTS, DataFactory.SOME_EXPERIMENT) }
            Then { checkHeader(X_THREATMETRIX_SESSION_ID, DataFactory.SOME_THREAT_METRIX_SESSION) }
            Then { checkHeader(ECG_USER_AUTH, null) }
        }
    }

    @Test
    fun `should return all expected PAPI unauthorised headers as empty string even if they don't exist`() {
        runUnitTest(robot) {
            When { createUnAuthorisedHeaders() }
            Then { checkHeader(ECG_APP_VER, EMPTY_STRING) }
            Then { checkHeader(ECG_PLATFORM, EMPTY_STRING) }
            Then { checkHeader(ECG_UDID, EMPTY_STRING) }
            Then { checkHeader(X_ECG_EXPERIMENTS, EMPTY_STRING) }
            Then { checkHeader(X_THREATMETRIX_SESSION_ID, EMPTY_STRING) }
        }
    }

    @Test
    fun `should return all default PAPI authorised headers`() {
        runUnitTest(robot) {
            Given { stubHeader(ApiHeaderParams.APP_VERSION, DataFactory.SOME_APP_VERSION) }
            Given { stubHeader(ApiHeaderParams.PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            When { createAuthorisedHeaders() }
            Then { checkHeader(AUTHORIZATION, PAPI_DEFAULT_AUTHORIZATION) }
            Then { checkHeader(ECG_APP_VER, DataFactory.SOME_APP_VERSION) }
            Then { checkHeader(ECG_PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Then { checkHeader(ACCEPT_LANGUAGE, PAPI_DEFAULT_LANGUAGE) }
            Then { checkHeader(CONNECTION, HttpHeaders.KEEP_ALIVE) }
            Then { checkHeader(PROXY_CONNECTION, HttpHeaders.KEEP_ALIVE) }
            Then { checkHeader(PRAGMA, CacheControl.NO_CACHE) }
        }
    }

    @Test
    fun `should return all expected PAPI authorised headers as empty string even if they don't exist`() {
        runUnitTest(robot) {
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            When { createAuthorisedHeaders() }
            Then { checkHeader(AUTHORIZATION, PAPI_DEFAULT_AUTHORIZATION) }
            Then { checkHeader(ECG_APP_VER, EMPTY_STRING) }
            Then { checkHeader(ECG_PLATFORM, EMPTY_STRING) }
            Then { checkHeader(ECG_UDID, EMPTY_STRING) }
            Then { checkHeader(X_ECG_EXPERIMENTS, EMPTY_STRING) }
            Then { checkHeader(X_THREATMETRIX_SESSION_ID, EMPTY_STRING) }
        }
    }

    @Test
    fun `should return all expected PAPI authorised headers`() {
        runUnitTest(robot) {
            Given { stubHeader(ApiHeaderParams.APP_VERSION, DataFactory.SOME_APP_VERSION) }
            Given { stubHeader(ApiHeaderParams.PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Given { stubHeader(ApiHeaderParams.UDID, DataFactory.SOME_UDID) }
            Given { stubHeader(ApiHeaderParams.EXPERIMENTS, DataFactory.SOME_EXPERIMENT) }
            Given { stubHeader(ApiHeaderParams.THREATMETRIX_SESSION, DataFactory.SOME_THREAT_METRIX_SESSION) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            When { createAuthorisedHeaders() }
            Then { checkHeader(AUTHORIZATION, PAPI_DEFAULT_AUTHORIZATION) }
            Then { checkHeader(ECG_APP_VER, DataFactory.SOME_APP_VERSION) }
            Then { checkHeader(ECG_PLATFORM, DataFactory.SOME_APP_PLATFORM) }
            Then { checkHeader(ECG_UDID, DataFactory.SOME_UDID) }
            Then { checkHeader(X_ECG_EXPERIMENTS, DataFactory.SOME_EXPERIMENT) }
            Then { checkHeader(X_THREATMETRIX_SESSION_ID, DataFactory.SOME_THREAT_METRIX_SESSION) }
            Then { checkHeader(ECG_USER_AUTH, "email=\"${DataFactory.SOME_USER_EMAIL}\", token=\"${DataFactory.SOME_TOKEN}\"") }
        }
    }

    @Test
    fun `should throw UnauthorisedException if PAPI user email authorisation header is not provided`() {
        runUnitTestForException(robot, UnauthorisedException::class) {
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_TOKEN, DataFactory.SOME_TOKEN) }
            When { createAuthorisedHeaders() }
        }
    }

    @Test
    fun `should throw UnauthorisedException if PAPI user token authorisation header is not provided`() {
        runUnitTestForException(robot, UnauthorisedException::class) {
            Given { stubHeader(ApiHeaderParams.AUTHORISATION_USER_EMAIL, DataFactory.SOME_USER_EMAIL) }
            When { createAuthorisedHeaders() }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var headersBuilder: HeadersBuilder
        private lateinit var actualHeaders: Map<String, String>

        private val testSubject = PapiHeadersProvider()

        override fun setup() {
            headersBuilder = HeadersBuilder()
        }

        fun stubHeader(
            key: String,
            value: String,
        ) {
            headersBuilder.append(key, value)
        }

        fun createUnAuthorisedHeaders(addOptionalAuth: Boolean = true) {
            actualHeaders = testSubject.createUnAuthorisedHeaders(headersBuilder.build(), addOptionalAuth)
        }

        fun createAuthorisedHeaders() {
            actualHeaders = testSubject.createAuthorisedHeaders(headersBuilder.build())
        }

        fun checkHeader(
            key: String,
            expectedValue: String?,
        ) {
            assertEquals(expectedValue, actualHeaders[key])
        }
    }
}