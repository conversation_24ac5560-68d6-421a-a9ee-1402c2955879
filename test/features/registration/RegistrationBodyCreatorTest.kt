package features.registration

import com.gumtree.mobile.api.userService.bodies.UserServiceSocialRegistrationBody
import com.gumtree.mobile.features.registration.CreateRegistrationRequest
import com.gumtree.mobile.features.registration.RegistrationBodyCreator
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.DataFactory
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class RegistrationBodyCreatorTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should create new user registration body payload with email, password, name and marketing email opt in`() {
        runUnitTest(robot) {
            Given {
                stubCreateRegistrationRequest(
                    firstName = DataFactory.SOME_USER_FIRST_NAME,
                    lastName = DataFactory.SOME_USER_LAST_NAME,
                    userName = DataFactory.SOME_USER_EMAIL,
                    userSecret = DataFactory.SOME_USER_SECRET,
                    isMarketingOptIn = true,
                    dateOfBirth = DataFactory.SOME_DATE_OF_BIRTH,
                    postcode = DataFactory.SOME_LOCATION_ZIPCODE
                )
            }
            Given { stubThreatMetrixId(DataFactory.SOME_THREAT_METRIX_SESSION) }
            When { create() }
            Then { checkFirstName(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkLastName(DataFactory.SOME_USER_LAST_NAME) }
            Then { checkUserMarketingOptIn(true) }
            Then { checkUserName(DataFactory.SOME_USER_EMAIL) }
            Then { checkUserSecret(DataFactory.SOME_USER_SECRET) }
            Then { checkThreatMetrixSessionId(DataFactory.SOME_THREAT_METRIX_SESSION) }
            Then { checkDateOfBirth(DataFactory.SOME_DATE_OF_BIRTH) }
            Then { checkPostcode(DataFactory.SOME_LOCATION_ZIPCODE) }
        }
    }

    @Test
    fun `should create new user registration body payload with email, password, name and marketing email NOT opt in`() {
        runUnitTest(robot) {
            Given {
                stubCreateRegistrationRequest(
                    firstName = DataFactory.SOME_USER_FIRST_NAME,
                    lastName = DataFactory.SOME_USER_LAST_NAME,
                    userName = DataFactory.SOME_USER_EMAIL,
                    userSecret = DataFactory.SOME_USER_SECRET,
                    isMarketingOptIn = false,
                    dateOfBirth = DataFactory.SOME_DATE_OF_BIRTH,
                    postcode = DataFactory.SOME_LOCATION_ZIPCODE
                )
            }
            Given { stubThreatMetrixId(DataFactory.SOME_THREAT_METRIX_SESSION) }
            When { create() }
            Then { checkFirstName(DataFactory.SOME_USER_FIRST_NAME) }
            Then { checkLastName(DataFactory.SOME_USER_LAST_NAME) }
            Then { checkUserMarketingOptIn(false) }
            Then { checkUserName(DataFactory.SOME_USER_EMAIL) }
            Then { checkUserSecret(DataFactory.SOME_USER_SECRET) }
            Then { checkThreatMetrixSessionId(DataFactory.SOME_THREAT_METRIX_SESSION) }
            Then { checkDateOfBirth(DataFactory.SOME_DATE_OF_BIRTH) }
            Then { checkPostcode(DataFactory.SOME_LOCATION_ZIPCODE) }
        }
    }

    private class Robot: BaseRobot {
        private lateinit var userRegistrationBody: UserServiceSocialRegistrationBody
        private lateinit var createRegistrationRequest: CreateRegistrationRequest
        private var threatMetrixId: String? = null

        private lateinit var tested: RegistrationBodyCreator

        override fun setup() {
            tested = RegistrationBodyCreator()
        }

        fun stubCreateRegistrationRequest(
            firstName: String,
            lastName: String,
            userName: String,
            userSecret: String,
            isMarketingOptIn: Boolean,
            dateOfBirth: String,
            postcode: String,
        ) {
            createRegistrationRequest = CreateRegistrationRequest(
                firstName = firstName,
                lastName = lastName,
                userName = userName,
                userSecret = userSecret,
                isMarketingOptIn = isMarketingOptIn,
                postcode = postcode,
                dateOfBirth = dateOfBirth,
            )
        }

        fun stubThreatMetrixId(id: String) {
            this.threatMetrixId = id
        }

        fun create() {
            userRegistrationBody = tested.create(threatMetrixId, createRegistrationRequest)
        }

        fun checkUserName(expected: String) {
            assertEquals(expected, userRegistrationBody.username)
        }

        fun checkFirstName(expected: String) {
         assertEquals(expected, userRegistrationBody.firstName)
        }

        fun checkLastName(expected: String) {
            assertEquals(expected, userRegistrationBody.lastName)
        }

        fun checkUserSecret(expected: String) {
            assertEquals(expected, userRegistrationBody.accessToken)
        }

        fun checkUserMarketingOptIn(expected: Boolean) {
            assertEquals(expected, userRegistrationBody.optInMarketing)
        }

        fun checkThreatMetrixSessionId(expected: String) {
            assertEquals(expected, userRegistrationBody.threatMetrixSessionId)
        }

        fun checkDateOfBirth(expected: String) {
            assertEquals(expected, userRegistrationBody.dateOfBirth)
        }

        fun checkPostcode(expected: String) {
            assertEquals(expected, userRegistrationBody.postcode)
        }
    }
}