package features.srp

import api.capi.models.RawCapiAd
import com.gumtree.mobile.utils.extensions.isZero
import com.gumtree.mobile.utils.runOrNull
import common.AdjustTrackingData

class SrpAdjustTrackingDataProvider {

    fun getScreenTrackingData(rawAds: List<RawCapiAd>?, page: String): AdjustTrackingData? {
        return runOrNull(page.isZero()) {
            val topListingIds = rawAds?.mapNotNull { it.id }?.take(NUMBER_OF_IDS) ?: emptyList()

            AdjustTrackingData(
                TOKEN_SCREEN_EVENT,
                mapOf(LISTING_IDS_KEY to topListingIds.toString()),
            )
        }
    }

    fun getCallSellerEvent(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_PHONE_EVENT,
        getDefaultParameters(rawAdDetails),
    )

    fun getContactLinkEvent(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_CONTACT_LINK_EVENT,
        getDefaultParameters(rawAdDetails),
    )

    fun getStartChatEvent(rawAdDetails: RawCapiAd) = AdjustTrackingData(
        TOKEN_START_CHAT,
        getDefaultParameters(rawAdDetails),
    )

    private fun getDefaultParameters(rawAdDetails: RawCapiAd) = mapOf(
        LISTING_ID_KEY to rawAdDetails.id.orEmpty(),
    )

    companion object {
        const val TOKEN_SCREEN_EVENT = "c3snl8"
        const val TOKEN_PHONE_EVENT = "oo16qw"
        const val TOKEN_CONTACT_LINK_EVENT = "jk22e9"
        const val TOKEN_START_CHAT = "ir1bia"

        const val LISTING_IDS_KEY = "products"
        const val LISTING_ID_KEY = "product"

        private const val NUMBER_OF_IDS = 3
    }
}
