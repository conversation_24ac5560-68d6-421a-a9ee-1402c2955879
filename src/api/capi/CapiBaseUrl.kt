package com.gumtree.mobile.api.capi

import com.gumtree.mobile.api.common.ApiBaseUrl
import com.gumtree.mobile.api.common.BaseUrl
import com.gumtree.mobile.api.common.LOCAL_HOST_BASE_URL
import com.gumtree.mobile.utils.ApplicationEnvironment
import com.gumtree.mobile.utils.extensions.toBaseUrl

const val CAPI_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY = "CAPI_API_BASE_URL"
const val CAPI_API_QA_PORT = 8081

class CapiApiBaseUrl : ApiBaseUrl() {

    override val url: BaseUrl by lazy {
        ApplicationEnvironment.getProperty(
            CAPI_API_SYSTEM_ENVIRONMENT_PROPERTY_KEY,
        ).toBaseUrl()
    } // see helm-chart/values.yaml
    override val localUrl: BaseUrl by lazy { LOCAL_HOST_BASE_URL.toBaseUrl() }
    override val localPort: Int = CAPI_API_QA_PORT
}
