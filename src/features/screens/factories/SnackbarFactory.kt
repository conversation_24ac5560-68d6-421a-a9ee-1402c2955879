package com.gumtree.mobile.features.screens.factories

import com.gumtree.mobile.features.screens.layoutsData.SnackbarActionData
import com.gumtree.mobile.features.screens.layoutsData.SnackbarCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem

/**
 * Producing instances of SnackbarCardDto
 */
class SnackbarFactory {
    /**
     * Build SnackbarCardDto instance
     * @param snackbarMessage - the snackbar message
     * @param snackbarAction - the snackbar action if needed
     * @param snackbarActionData - the snackbar action data if needed
     * @return - an instance of SnackbarCardDto
     */
    fun buildSnackbarCard(
        snackbarMessage: String,
        snackbarAction: SnackbarCardDto.Action? = null,
        snackbarActionData: SnackbarActionData? = null,
    ): UiItem {
        return SnackbarCardDto(
            message = snackbarMessage,
            action = snackbarAction,
            actionText = snackbarAction?.actionText,
            actionData = snackbarActionData,
        )
    }
}
