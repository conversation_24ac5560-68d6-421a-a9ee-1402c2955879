package com.gumtree.mobile.features.syncPhoneVerifyResult

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SyncResponseDto(

    @SerialName("syncStatus")
    val syncStatus: <PERSON><PERSON><PERSON>,

    @SerialName("thankUrl")
    val thankUrl: String,

    @SerialName("payUrl")
    val payUrl: String,

    @SerialName("bizCode")
    val bizCode: String,
)
