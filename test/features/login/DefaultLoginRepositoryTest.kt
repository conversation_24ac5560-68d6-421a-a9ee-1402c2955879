package features.login

import com.gumtree.mobile.api.common.EMPTY_STRING
import com.gumtree.mobile.api.userService.bodies.UserServiceLoginRequestBody
import com.gumtree.mobile.api.userService.bodies.UserServiceSocialRegistrationBody
import com.gumtree.mobile.api.userService.models.RawUserServiceAuthProvider
import com.gumtree.mobile.common.PushNotificationsService
import com.gumtree.mobile.features.login.AuthenticationType
import com.gumtree.mobile.features.login.DefaultLoginRepository
import com.gumtree.mobile.features.login.LoginMapper
import com.gumtree.mobile.features.login.LoginService
import com.gumtree.mobile.features.login.LogoutRequest
import com.gumtree.mobile.responses.ConflictException
import io.ktor.http.*
import io.ktor.server.plugins.BadRequestException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import retrofit2.HttpException
import retrofit2.Response
import tools.BaseRobot
import tools.DataFactory
import tools.DataFactory.SOME_THREAT_METRIX_SESSION
import tools.DataFactory.SOME_TOKEN
import tools.DataFactory.SOME_USER_EMAIL
import tools.DataFactory.SOME_USER_SECRET
import tools.coroutines.Given
import tools.coroutines.Then
import tools.coroutines.When
import tools.coroutines.runUnitTest
import tools.coroutines.runUnitTestForException
import utils.TestDispatcherProvider

class DefaultLoginRepositoryTest {

    private val robot = Robot()

    @BeforeEach
    fun setup() {
        robot.setup()
    }

    @Test
    fun `should use service to send login request`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubAuthenticationType(null) }
            Given { stubOptInMarketing(false) }
            When { create() }
            Then {
                checkServiceLogin(
                    UserServiceLoginRequestBody(
                        RawUserServiceAuthProvider.GUMTREE,
                        SOME_USER_SECRET,
                        SOME_USER_EMAIL,
                        SOME_THREAT_METRIX_SESSION,
                    )
                )
            }
            Then { checkServiceGetUserDetails(SOME_USER_EMAIL) }
            Then { checkMapperMapsRawUserAuthentication() }
        }
    }

    @Test
    fun `should use service to create user facebook login`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubAuthenticationType("facebook") }
            Given { stubOptInMarketing(true) }
            When { create() }
            Then { checkServiceGetUserDetails(SOME_USER_EMAIL) }
            Then {
                checkServiceLogin(
                    UserServiceLoginRequestBody(
                        RawUserServiceAuthProvider.FACEBOOK,
                        SOME_USER_SECRET,
                        SOME_USER_EMAIL,
                        SOME_THREAT_METRIX_SESSION,
                    )
                )
            }
        }
    }

    @Test
    fun `should handle empty auth type`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubAuthenticationType(EMPTY_STRING) }
            Given { stubOptInMarketing(false) }
            When { create() }
            Then {
                checkServiceLogin(
                    UserServiceLoginRequestBody(
                        RawUserServiceAuthProvider.GUMTREE,
                        SOME_USER_SECRET,
                        SOME_USER_EMAIL,
                        SOME_THREAT_METRIX_SESSION,
                    )
                )
            }
        }
    }

    @Test
    fun `should call register and login when social registration = true`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubOptInMarketing(false) }
            Given { stubAuthenticationType("googleid") }
            Given { stubSocialRegistration(true) }
            When { create() }
            Then {
                checkServiceRegister(
                    UserServiceSocialRegistrationBody(
                        accessToken = SOME_USER_SECRET,
                        username = SOME_USER_EMAIL,
                        threatMetrixSessionId = SOME_THREAT_METRIX_SESSION,
                        authProvider = RawUserServiceAuthProvider.GOOGLEID,
                        optInMarketing = false
                    )
                )
                checkServiceLogin(
                    UserServiceLoginRequestBody(
                        RawUserServiceAuthProvider.GOOGLEID,
                        SOME_USER_SECRET,
                        SOME_USER_EMAIL,
                        SOME_THREAT_METRIX_SESSION,
                    )
                )
            }
        }
    }

    @Test
    fun `should call login service and map raw user authentication data in order when creating user login`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubOptInMarketing(false) }
            Given { stubAuthenticationType(null) }
            When { create() }
            Then { checkCreateLoginActionsInOrder() }
        }
    }

    @Test
    fun `should throw 403 if social login returns 403 twice in a row`() = runTest {
        runUnitTestForException(robot, HttpException::class) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubAuthenticationType("facebook") }
            Given { stubOptInMarketing(false) }
            Given { stubLoginForbiddenHttpResponse() }
            When { create() }
            Then {
                checkServiceLogin(
                    UserServiceLoginRequestBody(
                        RawUserServiceAuthProvider.FACEBOOK,
                        SOME_USER_SECRET,
                        SOME_USER_EMAIL,
                        SOME_THREAT_METRIX_SESSION,
                    ),
                    times = 2
                )
            }
            Then {
                checkServiceRegister(
                    UserServiceSocialRegistrationBody(
                        accessToken = SOME_USER_SECRET,
                        username = SOME_USER_EMAIL,
                        threatMetrixSessionId = SOME_THREAT_METRIX_SESSION,
                        authProvider = RawUserServiceAuthProvider.FACEBOOK,
                        optInMarketing = false
                    )
                )
            }
        }
    }

    @Test
    fun `should not attempt to register user when 403 with GUMTREE auth type`() = runTest {
        runUnitTestForException(robot, HttpException::class) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserSecret(SOME_USER_SECRET) }
            Given { stubThreatMetrixSessionId(SOME_THREAT_METRIX_SESSION) }
            Given { stubAuthenticationType("GUMTREE") }
            Given { stubOptInMarketing(false) }
            Given { stubLoginForbiddenHttpResponse() }
            When { create() }
            Then {
                checkServiceLogin(
                    UserServiceLoginRequestBody(
                        RawUserServiceAuthProvider.GUMTREE,
                        SOME_USER_SECRET,
                        SOME_USER_EMAIL,
                        SOME_THREAT_METRIX_SESSION,
                    )
                )
            }
        }
    }

    @Test
    fun `should create authorised headers, call notification service in order when deleting user login (logout)`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthorization(DataFactory.SOME_STREAM_TOKEN) }
            Given { stubHasExistingPushNotificationSubscription(true) }
            Given { stubUserToken(DataFactory.SOME_TOKEN) }
            Given { stubLogoutRequest(DataFactory.SOME_USER_ID, DataFactory.SOME_PUSH_TOKEN) }
            Given { stubSuccessfulUnAuthenticateUserHttpResult() }
            When { delete() }
            Then { checkDeleteLoginActionsInOrder() }
        }
    }

    @Test
    fun `should NOT unsubscribe when logout without push token`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthorization(DataFactory.SOME_STREAM_TOKEN) }
            Given { stubUserToken(DataFactory.SOME_TOKEN) }
            Given { stubLogoutRequest(DataFactory.SOME_USER_ID, null) }
            Given { stubSuccessfulUnAuthenticateUserHttpResult() }
            When { delete() }
            Then { checkPushNotificationsUnSubscribeNotCalled() }
        }
    }

    @Test
    fun `should NOT unsubscribe when logout without existing push notification subscription`() = runTest {
        runUnitTest(robot) {
            Given { stubAuthorization(DataFactory.SOME_STREAM_TOKEN) }
            Given { stubHasExistingPushNotificationSubscription(false) }
            Given { stubUserToken(DataFactory.SOME_TOKEN) }
            Given { stubLogoutRequest(DataFactory.SOME_USER_ID, DataFactory.SOME_PUSH_TOKEN) }
            Given { stubSuccessfulUnAuthenticateUserHttpResult() }
            When { delete() }
            Then { checkPushNotificationsUnSubscribeNotCalled() }
        }
    }

    @Test
    fun `should use service when exchanging user email and token for fresh login data`() = runTest {
        runUnitTest(robot) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserToken(SOME_TOKEN) }
            When { exchange() }
            Then { checkServiceGetUserDetails(SOME_USER_EMAIL) }
            Then { checkMapperMapsRawUserAuthentication() }
        }
    }

    @Test
    fun `should throw ConflictException when user email is NOT provided`() = runTest {
        runUnitTestForException(robot, ConflictException::class) {
            Given { stubUserName(EMPTY_STRING) }
            Given { stubUserToken(SOME_TOKEN) }
            When { exchange() }
        }
    }

    @Test
    fun `should throw ConflictException when get user user details BE request has error`() = runTest {
        runUnitTestForException(robot, ConflictException::class) {
            Given { stubUserName(SOME_USER_EMAIL) }
            Given { stubUserToken(SOME_TOKEN) }
            Given { stubGetUserDetailsError(BadRequestException("error")) }
            When { exchange() }
        }
    }

    private class Robot : BaseRobot {
        private val pushNotificationsService: PushNotificationsService = mockk(relaxed = true)
        private val loginService: LoginService = mockk(relaxed = true)
        private val loginMapper: LoginMapper = mockk(relaxed = true)
        private val callHeaders: Headers = mockk(relaxed = true)

        private lateinit var userName: String
        private lateinit var userSecret: String
        private lateinit var userToken: String
        private lateinit var authorization: String
        private lateinit var threatMetrixSessionId: String
        private lateinit var logoutRequest: LogoutRequest
        private var optInMarketing: Boolean? = null
        private var authenticationType: AuthenticationType? = null
        private var socialRegistration: Boolean = false
        private val forbiddenHttpException: HttpException = mockk(relaxed = true) {
            every { code() } returns 403
        }

        private lateinit var testSubject: DefaultLoginRepository

        override fun setup() {
            testSubject = DefaultLoginRepository(
                loginService,
                pushNotificationsService,
                loginMapper,
                TestDispatcherProvider(),
            )
        }

        fun stubUserName(email: String) {
            userName = email
        }

        fun stubAuthorization(auth: String) {
            authorization = auth
            coEvery { pushNotificationsService.createAuthorization(any(), any()) } returns authorization
        }

        fun stubHasExistingPushNotificationSubscription(hasSubscription: Boolean) {
            coEvery { pushNotificationsService.hasExistingPushNotificationSubscription(any(), any(), any()) } returns hasSubscription
        }

        fun stubUserSecret(secret: String) {
            userSecret = secret
        }

        fun stubSocialRegistration(socialRegistration: Boolean) {
            this.socialRegistration = socialRegistration
        }

        fun stubThreatMetrixSessionId(sessionId: String) {
            threatMetrixSessionId = sessionId
        }

        fun stubUserToken(token: String) {
            userToken = token
        }

        fun stubAuthenticationType(authenticationType: String?) {
            this.authenticationType = AuthenticationType.fromString(authenticationType)
        }

        fun stubOptInMarketing(optInMarketing: Boolean) {
            this.optInMarketing = optInMarketing
        }

        fun stubLogoutRequest(
            userId: String,
            pushToken: String?,
        ) {
            logoutRequest = LogoutRequest(
                userId = userId,
                pushToken = pushToken
            )
        }

        fun stubSuccessfulUnAuthenticateUserHttpResult() {
            coEvery { loginService.unAuthenticateUser(any()) } returns Response.success(Unit)
        }

        fun stubLoginForbiddenHttpResponse() {
            coEvery { loginService.authenticateUser(any()) } throws forbiddenHttpException
        }

        fun stubGetUserDetailsError(error: Throwable) {
            coEvery { loginService.getUserDetails(any()) } throws error
        }

        suspend fun create() {
            testSubject.create(
                userName,
                userSecret,
                threatMetrixSessionId,
                authenticationType!!,
                socialRegistration,
                optInMarketing!!,
            )
        }

        suspend fun delete() {
            testSubject.delete(callHeaders, userToken, logoutRequest)
        }

        suspend fun exchange() {
            testSubject.exchange(callHeaders, userName, userToken)
        }

        fun checkServiceLogin(loginRequest: UserServiceLoginRequestBody, times: Int = 1) {
            coVerify(exactly = times) { loginService.authenticateUser(loginRequest) }
        }

        fun checkServiceRegister(request: UserServiceSocialRegistrationBody) {
            coVerify { loginService.register(request) }
        }

        fun checkServiceGetUserDetails(email: String) {
            coVerify { loginService.getUserDetails(email) }
        }

        fun checkMapperMapsRawUserAuthentication() {
            verify { loginMapper.map(any(), any(), any()) }
        }

        fun checkCreateLoginActionsInOrder() {
            coVerifyOrder {
                loginService.authenticateUser(any())
                loginService.getUserDetails(any())
                loginMapper.map(any(), any(), any())
            }
        }

        fun checkDeleteLoginActionsInOrder() {
            coVerifyOrder {
                pushNotificationsService.createAuthorization(callHeaders, logoutRequest.userId)
                pushNotificationsService.hasExistingPushNotificationSubscription(authorization, logoutRequest.pushToken!!, logoutRequest.userId)
                pushNotificationsService.unsubscribe(any(), any(), any())
                loginService.unAuthenticateUser(any())
            }
        }

        fun checkPushNotificationsUnSubscribeNotCalled() {
            coVerify(exactly = 0) { pushNotificationsService.unsubscribe(any(), any(), any()) }
        }

    }
}
