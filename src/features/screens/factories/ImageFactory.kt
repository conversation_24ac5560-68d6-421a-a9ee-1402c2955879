package com.gumtree.mobile.features.screens.factories

import com.gumtree.mobile.common.Image
import com.gumtree.mobile.features.screens.layoutsData.ImageCardDto
import com.gumtree.mobile.features.screens.layoutsData.UiItem
import com.gumtree.mobile.utils.extensions.mapAndDistinct

/**
 * Producing instances of ImageCardDto
 */
class ImageFactory {

    /**
     * Build ImageCardDto.Default instance
     * @param assetUrls - the asset urls, usually the new apps assets are stored/uploaded into GCP buckets
     * @return - an instance of ImageCardDto.Default
     */
    fun buildImageCardForAsset(assetUrls: List<String>): UiItem {
        return ImageCardDto.Default(images = assetUrls.mapAndDistinct { Image.mapAssetUrlToImage(it) })
    }

    /**
     * Build ImageCardDto.Local instance
     * @param resource - the image resource
     * @return - an instance of ImageCardDto.Local
     */
    fun buildLocalImageCard(resource: ImageCardDto.Local.Resource): UiItem {
        return ImageCardDto.Local(resource = resource)
    }
}
