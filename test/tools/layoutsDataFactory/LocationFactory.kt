package tools.layoutsDataFactory

import com.gumtree.mobile.features.locations.LocationType
import com.gumtree.mobile.features.locations.SimpleLocationDto
import tools.DataFactory


object LocationFactory {

    /**
     * Generate a SimpleLocationDto instance
     * @param locationId - the location ID
     * @param locationName - the location name
     * @param locationType - the location type
     * @param locationLatitude - the location latitude
     * @param locationLongitude - the location longitude
     * @return - The SimpleLocationDto instance
     */
    fun createLocation(
        locationId: String = DataFactory.anyString(),
        locationName: String = DataFactory.anyString(),
        locationType: LocationType = DataFactory.anyEnumValue(LocationType::class.java),
        locationLatitude: Double? = null,
        locationLongitude: Double? = null
    ): SimpleLocationDto {
        return SimpleLocationDto(
            id = locationId,
            name = locationName,
            type = locationType,
            latitude = locationLatitude,
            longitude = locationLongitude
        )
    }
}
