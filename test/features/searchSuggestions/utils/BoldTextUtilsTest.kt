package features.searchSuggestions.utils

import com.gumtree.mobile.features.searchSuggestions.utils.BoldTextDisplayString
import com.gumtree.mobile.features.searchSuggestions.utils.BoldTextUtils
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import tools.BaseRobot
import tools.Given
import tools.Then
import tools.When
import tools.runUnitTest

class BoldTextUtilsTest {
    val robot = Robot()

    @Test
    fun `should have empty prefix and suffix when exact match`() {
        runUnitTest(robot) {
            Given { stubSuggestionText("iphone") }
            Given { stubSearchTerm("iphone") }
            When { getBoldTextFromSuggestion() }
            Then { checkBoldTextString(BoldTextDisplayString("iphone", "", "")) }
        }
    }

    @Test
    fun `should prefix when search term is not at the beginning`() {
        runUnitTest(robot) {
            Given { stubSuggestionText("black iphone") }
            Given { stubSearchTerm("iphone") }
            When { getBoldTextFromSuggestion() }
            Then { checkBoldTextString(BoldTextDisplayString("iphone", "black ", "")) }
        }
    }

    @Test
    fun `should suffix when search term is not at the end`() {
        runUnitTest(robot) {
            Given { stubSuggestionText("iphone case") }
            Given { stubSearchTerm("iphone") }
            When { getBoldTextFromSuggestion() }
            Then { checkBoldTextString(BoldTextDisplayString("iphone", "", " case")) }
        }
    }

    @Test
    fun `should prefix and suffix when search term is in the middle`() {
        runUnitTest(robot) {
            Given { stubSuggestionText("black iphone case") }
            Given { stubSearchTerm("iphone") }
            When { getBoldTextFromSuggestion() }
            Then { checkBoldTextString(BoldTextDisplayString("iphone", "black ", " case")) }
        }
    }

    inner class Robot : BaseRobot {
        private lateinit var searchTerm: String
        private lateinit var suggestionText: String

        private lateinit var actualResult: BoldTextDisplayString

        fun stubSuggestionText(suggestionText: String) {
            this.suggestionText = suggestionText
        }

        fun stubSearchTerm(searchTerm: String) {
            this.searchTerm = searchTerm
        }

        fun getBoldTextFromSuggestion() {
            actualResult = BoldTextUtils.getBoldTextFromSuggestion(suggestionText, searchTerm)
        }

        fun checkBoldTextString(expected: BoldTextDisplayString) {
            assertEquals(expected, actualResult)
        }
    }
}
